import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom'; // Import router hooks
import {
  X,
  Save,
  Upload,
  Calendar,
  DollarSign,
  Percent,
  Clock,
  Users,
  FileText,
  CheckSquare,
  AlertCircle,
  Briefcase,
  Package,
  Building,
  Tag,
  CreditCard,
  ClipboardList,
  User,
  Globe,
  Server,
  Repeat,
  Key,
  Cloud,
  Wrench,
  LifeBuoy,
  TrendingUp,
  ShoppingCart,
  Settings,
  MessageSquare,
  Map,
  Layers,
  Plus,
  Monitor,
  Printer,
  Phone,
  Wifi,
  Shield,
  Database,
  File,
  Tablet,
  Keyboard,
  Mouse,
  Tv,
  HelpCircle,
  Laptop,
  AlertOctagon,
  HardDrive,
  Cpu,
  PlugZap,
  Settings2,
  RefreshCw,
  Eye,
  AlertTriangle,
  CheckCircle,
  Trash2,
} from 'lucide-react';
import { format, addDays } from 'date-fns';
import { toast } from 'react-hot-toast'; // Import toast
import AssetSoftwareService from '../services/AssetSoftwareService'; // Import service
import api from '../services/api'; // Import API service properly

// Define the interface for the invoice data
interface BillingInvoice {
  id: string;
  invoiceNumber: string;
  vendorName: string;
  serviceProduct: string;
  department: string;
  billingCategory: string;
  invoiceDate: string;
  dueDate: string;
  amount: number | string;
  currency: string;
  tax: number | string;
  totalAmount: number;
  paymentMethod: string;
  accountTitle?: string;
  mobileNumber?: string;
  checkNumber?: string;
  bankName?: string;
  receivedBy?: string;
  receiptNumber?: string;
  paymentNotes?: string;
  invoiceFileUrl?: string;
  accountNumber?: string; // Added for Bank Transfer
  cardNumber?: string; // Added for Credit Card
  expiryDate?: string; // Added for Credit Card
  cvv?: string; // Added for Credit Card
  notes: string;
  isRecurring: boolean;
  billingFrequency?: 'Monthly' | 'Yearly' | 'One-Time';
  reminderDays?: number;
  linkedAssets?: AssetItem[];
  linkedSoftware?: SoftwareItem[];
  assignedUsers?: UserItem[];
  approvalStatus: 'Pending' | 'Approved' | 'Rejected';
  createdBy: string;
  approvedBy?: string;
  lastModifiedBy: string;
  lastModifiedDate: string;
  showDeptDropdown: boolean;
  showCategoryDropdown: boolean;
}

// Define interfaces for linked items
interface AssetItem {
  id: string;
  name: string;
  category?: string;
  department?: string;
  assetType?: string;
}

interface SoftwareItem {
  id: string;
  name: string;
  category?: string;
  department?: string;
}

interface UserItem {
  id: string;
  name: string;
  department?: string;
  project?: string;
}

// Define a minimal Vendor interface for the fetch response
interface Vendor { 
  id: string;
  vendorName: string; 
  // Add other fields if needed, but vendorName is the minimum
}

// Ensure we always have a proper array for iteration
const ensureArray = <T,>(arr: T[] | undefined | null): T[] => {
  return Array.isArray(arr) ? arr : [];
};

// Add this before the component to declare the type on window
declare global {
  interface Window {
    assetDropdownTimeout: number | NodeJS.Timeout | undefined;
    softwareDropdownTimeout?: number;
    userDropdownTimeout?: number;
  }
}

// Custom hook to handle clicks outside of components
const useClickOutside = (handler: () => void) => {
  const ref = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        handler();
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [handler]);
  
  return ref;
};

// Add this function near the top of the component, after the imports and before the component definition
const getFileUrl = (url: string | undefined) => {
  // Handle undefined URLs
  if (!url) {
    console.warn('Attempted to get file URL for undefined value');
    return '';
  }

  // Extract the file name from the URL
  const fileName = url.split('/').pop();
  if (!fileName) {
    console.warn('Could not extract filename from URL:', url);
    return url; // Return original if we can't extract filename
  }

  // Check if the URL is already absolute
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // Get the API base URL from our configuration
  let baseUrl = '';

  // Determine if we're in production or development
  const isProduction = !window.location.hostname.includes('localhost');

  if (isProduction) {
    // In production, use relative path
    baseUrl = '';
  } else {
    // In development, use the API server URL
    baseUrl = 'http://localhost:5000';
  }

  // Try multiple possible paths since server routing might be inconsistent
  const possiblePaths = [
    url, // Original path
    `/uploads/invoices/${fileName}`, // Standard invoices directory
    `/uploads/it-invoices/${fileName}`, // IT invoices directory
    `/public/uploads/invoices/${fileName}`, // Alternative path with public prefix
    `/public/uploads/it-invoices/${fileName}`, // Alternative path with public prefix
  ];

  // Log the paths we're going to try
  console.log('Trying the following file paths:', {
    originalUrl: url,
    possiblePaths: possiblePaths.map(p => baseUrl + p),
  });

  // Make sure URL starts with a slash
  const normalizedUrl = url.startsWith('/') ? url : `/${url}`;

  // For development, we'll try the first path
  return `${baseUrl}${normalizedUrl}`;
};

// Add this debug utility function near getFileUrl
const debugFileUrl = (url: string | undefined, label: string = 'File URL') => {
  if (!url) {
    console.log(`[${label}]`, {
      original: url,
      error: 'URL is undefined',
    });
    return '';
  }

  const isProduction = !window.location.hostname.includes('localhost');
  console.log(`[${label}]`, {
    original: url,
    isAbsolute: url.startsWith('http'),
    isRelativeWithSlash: url.startsWith('/') && !url.startsWith('//'),
    isBlob: url.startsWith('blob:'),
    transformed: getFileUrl(url),
    environment: isProduction ? 'production' : 'development',
    hostname: window.location.hostname,
  });
  return url;
};

// Helper function to check if a file exists at a given URL
const checkFileExistence = async (url: string): Promise<string | null> => {
  try {
    console.log(`Checking file existence at: ${url}`);

    // Simply try a GET request with range to minimize transfer
    const getResponse = await fetch(url, {
      headers: {
        Range: 'bytes=0-0',
      },
    });

    if (getResponse.ok || getResponse.status === 206) {
      console.log(`✅ File exists at: ${url}`);
      return url;
    } else {
      console.log(`❌ File not found at: ${url}`);
      return null;
    }
  } catch (error) {
    console.log(`⚠️ Error checking file at ${url}:`, error);
    return null;
  }
};

// Add this function to find a working URL from multiple possibilities
const findWorkingFileUrl = async (url: string | undefined): Promise<string> => {
  if (!url) return '';

  // Extract the file name from the URL
  const fileName = url.split('/').pop();
  if (!fileName) return url;

  // Get base URL for development
  const baseUrl = window.location.hostname.includes('localhost') ? 'http://localhost:5000' : '';

  // Log the original URL for debugging
  console.log('Finding working URL for:', url);

  // PRIORITY 0: Try direct download endpoint - this is most likely to work with CORS issues
  const downloadEndpoint = `${baseUrl}/api/download/file/${fileName}`;
  console.log('Trying direct download endpoint first:', downloadEndpoint);

  // Don't try to validate with fetch - just return this URL as it's the most reliable
  // and should be opened directly by the browser without CORS checks
  return downloadEndpoint;
};

const BillingInvoiceForm: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;
  const initialInvoiceData = isEditMode ? (location.state?.invoice as BillingInvoice) : null;

  // Create refs for the dropdown containers
  const assetDropdownRef = useRef<HTMLDivElement>(null);
  const softwareDropdownRef = useRef<HTMLDivElement>(null);
  const userDropdownRef = useRef<HTMLDivElement>(null);
  
  // Setup click outside handlers with memorized callbacks to avoid infinite rerenders
  const closeAssetDropdown = useCallback(() => {
    setShowAssetDropdown(false);
  }, []);
  
  const closeSoftwareDropdown = useCallback(() => {
    setShowSoftwareDropdown(false);
  }, []);
  
  const closeUserDropdown = useCallback(() => {
    setShowUserDropdown(false);
  }, []);

  // Use the custom hooks to handle clicks outside each dropdown
  const assetClickOutsideRef = useClickOutside(closeAssetDropdown);
  const softwareClickOutsideRef = useClickOutside(closeSoftwareDropdown);
  const userClickOutsideRef = useClickOutside(closeUserDropdown);

  // Initial form state setup
  const getInitialFormData = (): BillingInvoice => {
    if (isEditMode && initialInvoiceData) {
      // Make sure we properly extract and initialize all associated entities
      console.log('Initializing form with edit data:', initialInvoiceData);
      
      // Create a deep copy with all required fields
      const formDataWithDefaults = {
        ...initialInvoiceData,
        // Ensure arrays are properly initialized - this is critical
        linkedAssets: Array.isArray(initialInvoiceData.linkedAssets)
          ? initialInvoiceData.linkedAssets
          : [],
        linkedSoftware: Array.isArray(initialInvoiceData.linkedSoftware)
          ? initialInvoiceData.linkedSoftware
          : [],
        assignedUsers: Array.isArray(initialInvoiceData.assignedUsers)
          ? initialInvoiceData.assignedUsers
          : [],
        // Ensure notes field exists
        notes: initialInvoiceData.notes || '',
        // Ensure payment detail fields are initialized
        accountTitle: initialInvoiceData.accountTitle || '',
        mobileNumber: initialInvoiceData.mobileNumber || '',
        checkNumber: initialInvoiceData.checkNumber || '',
        bankName: initialInvoiceData.bankName || '',
        receivedBy: initialInvoiceData.receivedBy || '',
        receiptNumber: initialInvoiceData.receiptNumber || '',
        paymentNotes: initialInvoiceData.paymentNotes || '',
        accountNumber: initialInvoiceData.accountNumber || '', // Added for Bank Transfer
        cardNumber: initialInvoiceData.cardNumber || '', // Added for Credit Card
        expiryDate: initialInvoiceData.expiryDate || '', // Added for Credit Card
        cvv: initialInvoiceData.cvv || '', // Added for Credit Card
        // Ensure recurring billing fields are properly initialized
        isRecurring: Boolean(initialInvoiceData.isRecurring),
        billingFrequency:
          initialInvoiceData.billingFrequency ||
          (initialInvoiceData.isRecurring ? 'Monthly' : 'One-Time'),
        reminderDays: parseInt(String(initialInvoiceData.reminderDays), 10) || 3,
      };
      
      console.log('Form data initialized with:', {
        id: formDataWithDefaults.id,
        notes: formDataWithDefaults.notes,
        isRecurring: formDataWithDefaults.isRecurring,
        billingFrequency: formDataWithDefaults.billingFrequency,
        reminderDays: formDataWithDefaults.reminderDays,
        paymentMethod: formDataWithDefaults.paymentMethod,
        hasPaymentDetails: !!(
          formDataWithDefaults.accountTitle ||
          formDataWithDefaults.mobileNumber ||
          formDataWithDefaults.checkNumber ||
          formDataWithDefaults.bankName ||
          formDataWithDefaults.receivedBy ||
          formDataWithDefaults.receiptNumber ||
          formDataWithDefaults.paymentNotes
        ),
        hasLinkedAssets: formDataWithDefaults.linkedAssets.length > 0,
        hasLinkedSoftware: formDataWithDefaults.linkedSoftware.length > 0,
        hasAssignedUsers: formDataWithDefaults.assignedUsers.length > 0,
      });
      
      return formDataWithDefaults;
    }
    
    // Default values for a new invoice
    return {
      id: '', // Will be set on save for new invoices
      invoiceNumber: '',
      vendorName: '',
      serviceProduct: '',
      department: '',
      billingCategory: '',
      invoiceDate: format(new Date(), 'yyyy-MM-dd'),
      dueDate: format(addDays(new Date(), 30), 'yyyy-MM-dd'),
      amount: 0,
      currency: 'USD',
      tax: 0,
      totalAmount: 0,
      paymentMethod: '',
      // Initialize payment detail fields
      accountTitle: '',
      mobileNumber: '',
      checkNumber: '',
      bankName: '',
      receivedBy: '',
      receiptNumber: '',
      paymentNotes: '',
      invoiceFileUrl: '',
      accountNumber: '', // Added for Bank Transfer
      cardNumber: '', // Added for Credit Card
      expiryDate: '', // Added for Credit Card
      cvv: '', // Added for Credit Card
      notes: '',
      isRecurring: false,
      billingFrequency: 'One-Time',
      reminderDays: 3,
      linkedAssets: [],
      linkedSoftware: [],
      assignedUsers: [],
      approvalStatus: 'Pending',
      createdBy: 'Current User', // Replace with actual user later
      approvedBy: '',
      lastModifiedBy: 'Current User', // Replace with actual user later
      lastModifiedDate: new Date().toISOString(),
      showDeptDropdown: false,
      showCategoryDropdown: false,
    };
  };

  const [formData, setFormData] = useState<BillingInvoice>(getInitialFormData());
  const [loading, setLoading] = useState(false); // Add loading state for async operations
  const [isSubmitting, setIsSubmitting] = useState(false); // Additional state for form submission process
  const [vendorList, setVendorList] = useState<string[]>([]); // State for vendors
  const [vendorsLoading, setVendorsLoading] = useState(true); // Separate loading state for vendors
  
  // States for assets, software, and users
  const [assets, setAssets] = useState<any[]>([]);
  const [software, setSoftware] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [assetsLoading, setAssetsLoading] = useState(true);
  const [softwareLoading, setSoftwareLoading] = useState(true);
  const [usersLoading, setUsersLoading] = useState(true);

  // States for searching and filtering options
  const [assetSearch, setAssetSearch] = useState('');
  const [softwareSearch, setSoftwareSearch] = useState('');
  const [userSearch, setUserSearch] = useState('');
  const [filteredAssets, setFilteredAssets] = useState<any[]>([]);
  const [filteredSoftware, setFilteredSoftware] = useState<any[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<any[]>([]);
  const [showAssetDropdown, setShowAssetDropdown] = useState(false);
  const [showSoftwareDropdown, setShowSoftwareDropdown] = useState(false);
  const [showUserDropdown, setShowUserDropdown] = useState(false);

  // Add these state variables to track selected item index
  const [selectedAssetIndex, setSelectedAssetIndex] = useState<number>(-1);
  const [selectedSoftwareIndex, setSelectedSoftwareIndex] = useState<number>(-1);
  const [selectedUserIndex, setSelectedUserIndex] = useState<number>(-1);

  // Add a new state variable to track upload errors
  // Add this near your other state variables
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Store the original file for retry functionality
  const [fileForRetry, setFileForRetry] = useState<File | null>(null);

  // Add state to track if a file is available for viewing
  const [hasAttachedFile, setHasAttachedFile] = useState(false);

  // Add useEffect to keep hasAttachedFile in sync with formData.invoiceFileUrl
  useEffect(() => {
    // Only update if formData has been initialized
    if (formData) {
      console.log('Checking file URL state:', !!formData.invoiceFileUrl, formData.invoiceFileUrl);
      setHasAttachedFile(!!formData.invoiceFileUrl);
    }
  }, [formData?.invoiceFileUrl]);

  // If in edit mode but no initial data was passed (e.g., page refresh),
  // fetch the data here using invoiceId.
  useEffect(() => {
    if (isEditMode) {
      console.log('Edit mode detected, initialInvoiceData:', initialInvoiceData);

      // If we have initialInvoiceData directly from navigation state
      if (initialInvoiceData) {
        console.log('Using initialInvoiceData from navigation state');
        setFormData(initialInvoiceData);

        // Check if there's a file URL
        if (initialInvoiceData.invoiceFileUrl) {
          console.log('Invoice has attached file:', initialInvoiceData.invoiceFileUrl);
          setHasAttachedFile(true);
        }
      }
      
      // Whether we have initialInvoiceData or not, always fetch fresh data in edit mode
      // This ensures we always have the latest data and fixes potential issues with stale data
        const fetchInvoiceData = async () => {
          setLoading(true);
          try {
          console.log(`Fetching invoice data for ID: ${id}`);
          // Ensure token is properly set for authorization
          const token = localStorage.getItem('authToken');
          
          // Add extra headers for authorization as a fallback
          const headers = token ? { Authorization: `Bearer ${token}` } : {};
          const response = await api.get(`/billing-invoices/${id}`, {
            headers,
            withCredentials: true,
          });
            
            if (response.data.success) {
              const invoiceData = response.data.data;
            
            // Log complete invoice data to diagnose the issue
            console.log('Raw invoice data from API:', JSON.stringify(invoiceData, null, 2));
            
            // Handle different response structures
            let processedData = invoiceData;
            
            // Check if the data is nested in a 'data' property
            if (invoiceData.data && typeof invoiceData.data === 'object') {
              console.log('Data is nested in a data property, using that instead');
              processedData = invoiceData.data;
            }
            
            // Check if the data is nested in an 'invoice' property
            if (invoiceData.invoice && typeof invoiceData.invoice === 'object') {
              console.log('Data is nested in an invoice property, using that instead');
              processedData = invoiceData.invoice;
            }

            // Special case: Set the invoiceFileUrl to the it-invoices folder
            console.log('Setting the invoiceFileUrl to the it-invoices folder');
            processedData.invoiceFileUrl = `/uploads/it-invoices/${processedData.id}.pdf`;
            
            // Ensure the invoice data has the association arrays initialized and in correct format
            if (!processedData.linkedAssets || !Array.isArray(processedData.linkedAssets)) {
              console.log('Fixing linkedAssets array format');
              processedData.linkedAssets = [];
            }
            
            if (!processedData.linkedSoftware || !Array.isArray(processedData.linkedSoftware)) {
              console.log('Fixing linkedSoftware array format');
              processedData.linkedSoftware = [];
            }
            
            if (!processedData.assignedUsers || !Array.isArray(processedData.assignedUsers)) {
              console.log('Fixing assignedUsers array format');
              processedData.assignedUsers = [];
            }
            
            // Make sure notes field exists (it might be null or undefined)
            if (processedData.notes === undefined || processedData.notes === null) {
              console.log('Notes field is missing or null, checking alternative locations');
              
              // Check if notes might be in a different location in the response
              if (processedData.note) {
                console.log('Found notes in "note" field instead of "notes"');
                processedData.notes = processedData.note;
              } else if (processedData.description) {
                console.log('Found notes in "description" field');
                processedData.notes = processedData.description;
              } else if (invoiceData.notes) {
                console.log('Found notes in original invoiceData');
                processedData.notes = invoiceData.notes;
              } else if (invoiceData.note) {
                console.log('Found notes in original invoiceData as "note"');
                processedData.notes = invoiceData.note;
              } else if (invoiceData.description) {
                console.log('Found notes in original invoiceData as "description"');
                processedData.notes = invoiceData.description;
              } else if (processedData.comments) {
                console.log('Found notes in "comments" field');
                processedData.notes = processedData.comments;
              } else if (invoiceData.comments) {
                console.log('Found notes in original invoiceData as "comments"');
                processedData.notes = invoiceData.comments;
              } else {
                console.log(
                  'No notes field found in any expected location, initializing as empty string'
                );
                processedData.notes = '';
              }
            } else {
              console.log(`Notes field found with value: "${processedData.notes}"`);
            }
            
            // Ensure recurring billing fields are properly initialized
            // isRecurring should be a boolean
            processedData.isRecurring = Boolean(processedData.isRecurring);
            console.log(`isRecurring initialized as: ${processedData.isRecurring}`);
            
            // billingFrequency should be one of the valid options
            if (
              !processedData.billingFrequency ||
              !['Monthly', 'Yearly', 'One-Time'].includes(processedData.billingFrequency)
            ) {
              processedData.billingFrequency = processedData.isRecurring ? 'Monthly' : 'One-Time';
              console.log(`billingFrequency initialized as: ${processedData.billingFrequency}`);
            }
            
            // reminderDays should be a number
            if (processedData.reminderDays === undefined || processedData.reminderDays === null) {
              processedData.reminderDays = 3; // Default value
              console.log('reminderDays initialized to default value: 3');
            } else {
              // Ensure it's a number
              processedData.reminderDays = parseInt(String(processedData.reminderDays), 10) || 3;
              console.log(`reminderDays initialized as: ${processedData.reminderDays}`);
            }
            
            // Initialize payment detail fields if they don't exist
            processedData.accountTitle = processedData.accountTitle || '';
            processedData.mobileNumber = processedData.mobileNumber || '';
            processedData.checkNumber = processedData.checkNumber || '';
            processedData.bankName = processedData.bankName || '';
            processedData.receivedBy = processedData.receivedBy || '';
            processedData.receiptNumber = processedData.receiptNumber || '';
            processedData.paymentNotes = processedData.paymentNotes || '';
            processedData.accountNumber = processedData.accountNumber || ''; // Added
            processedData.cardNumber = processedData.cardNumber || ''; // Added
            processedData.expiryDate = processedData.expiryDate || ''; // Added
            processedData.cvv = processedData.cvv || ''; // Added
            
            console.log('Processed invoice data before setting state:', {
              id: processedData.id,
              notes: processedData.notes,
              isRecurring: processedData.isRecurring,
              billingFrequency: processedData.billingFrequency,
              reminderDays: processedData.reminderDays,
              paymentMethod: processedData.paymentMethod,
              linkedAssets: processedData.linkedAssets,
              linkedSoftware: processedData.linkedSoftware,
              assignedUsers: processedData.assignedUsers,
            });
            
            // Special handling for possible array format issues
            // Some APIs return arrays as null, empty objects, or with non-standard structure
            // Ensure proper array structures for all linked entities
            
            // Fix for linkedAssets that might be string IDs or malformed objects
            if (processedData.linkedAssets && Array.isArray(processedData.linkedAssets)) {
              console.log('Processing linkedAssets array:', processedData.linkedAssets);
              
              // If we have simple string IDs or malformed objects, try to convert them to proper objects
              processedData.linkedAssets = processedData.linkedAssets.map((asset: any) => {
                // If asset is just a string ID
                if (typeof asset === 'string') {
                  console.log(`Converting asset ID string to object: ${asset}`);
                  return { id: asset, name: `Asset ${asset}`, assetType: '' };
                }
                
                // If asset is an object but missing required fields
                if (typeof asset === 'object') {
                  return {
                    id:
                      asset.id || asset._id || `unknown-${Math.random().toString(36).substring(7)}`,
                    name: asset.name || asset.assetName || 'Unknown Asset',
                    category: asset.category || '',
                    department: asset.department || '',
                    assetType: asset.assetType || asset.type || '',
                  };
                }
                
                return asset;
              });
            }
            
            // Fix for linkedSoftware that might be string IDs or malformed objects
            if (processedData.linkedSoftware && Array.isArray(processedData.linkedSoftware)) {
              console.log('Processing linkedSoftware array:', processedData.linkedSoftware);
              
              // If we have simple string IDs or malformed objects, try to convert them to proper objects
              processedData.linkedSoftware = processedData.linkedSoftware.map((software: any) => {
                // If software is just a string ID
                if (typeof software === 'string') {
                  console.log(`Converting software ID string to object: ${software}`);
                  return { id: software, name: `Software ${software}` };
                }
                
                // If software is an object but missing required fields
                if (typeof software === 'object') {
                  return {
                    id:
                      software.id ||
                      software._id ||
                      `unknown-${Math.random().toString(36).substring(7)}`,
                    name: software.name || software.softwareName || 'Unknown Software',
                    category: software.category || '',
                    department: software.department || '',
                  };
                }
                
                return software;
              });
            }
            
            // Fix for assignedUsers that might be string IDs or malformed objects
            if (processedData.assignedUsers && Array.isArray(processedData.assignedUsers)) {
              console.log('Processing assignedUsers array:', processedData.assignedUsers);
              
              // If we have simple string IDs or malformed objects, try to convert them to proper objects
              processedData.assignedUsers = processedData.assignedUsers.map((user: any) => {
                // If user is just a string ID
                if (typeof user === 'string') {
                  console.log(`Converting user ID string to object: ${user}`);
                  return { id: user, name: `User ${user}` };
                }
                
                // If user is an object but missing required fields
                if (typeof user === 'object') {
                  return {
                    id:
                      user.id ||
                      user._id ||
                      user.userId ||
                      `unknown-${Math.random().toString(36).substring(7)}`,
                    name: user.name || user.userName || user.fullName || 'Unknown User',
                    department: user.department || '',
                    project: user.project || '',
                  };
                }
                
                return user;
              });
            }
            
            console.log('Final processed data with fixed arrays:', {
              linkedAssets: processedData.linkedAssets,
              linkedSoftware: processedData.linkedSoftware,
              assignedUsers: processedData.assignedUsers,
            });
            
            // Set form data after processing
            setFormData(processedData);

            // After processing the data, check for file URL
            if (processedData.invoiceFileUrl) {
              console.log('Invoice has attached file:', processedData.invoiceFileUrl);
              console.log('File URL type:', typeof processedData.invoiceFileUrl);
              console.log('File URL content check:', {
                isEmpty: !processedData.invoiceFileUrl,
                isBlob: processedData.invoiceFileUrl.startsWith('blob:'),
                urlLength: processedData.invoiceFileUrl.length,
                fileName: processedData.invoiceFileUrl.split('/').pop(),
              });
              setHasAttachedFile(true);
            } else {
              console.log('No invoice file URL found in the data');
              setHasAttachedFile(false);

              // Check if we might have a file URL in another format or property
              for (const key of Object.keys(processedData)) {
                if (
                  typeof processedData[key] === 'string' &&
                  (key.toLowerCase().includes('file') ||
                    key.toLowerCase().includes('attachment') ||
                    key.toLowerCase().includes('document')) &&
                  processedData[key] !== ''
                ) {
                  console.log(`Found possible file URL in field '${key}':`, processedData[key]);
                  // If we find a potential file URL, set it in the formData
                  if (!processedData.invoiceFileUrl) {
                    console.log(`Setting invoiceFileUrl from ${key}`);
                    processedData.invoiceFileUrl = processedData[key];
                    setHasAttachedFile(true);
                  }
                }
              }

              // Special case: Check if the file might be in a different location
              // This is to handle files stored in the /uploads/it-invoices folder
              if (!processedData.invoiceFileUrl && processedData.id) {
                console.log(
                  'Checking for file in alternate locations based on ID:',
                  processedData.id
                );

                // Try the it-invoices folder which the user mentioned
                processedData.invoiceFileUrl = `/uploads/it-invoices/${processedData.id}.pdf`;
                console.log(
                  'Setting file URL to it-invoices folder:',
                  processedData.invoiceFileUrl
                );
                setHasAttachedFile(true);
              }
            }

            setLoading(false);
          } else {
            toast.error('Failed to load invoice data: ' + response.data.message);
              navigate('/it-billing/billing-invoice'); // Redirect if fetch fails
            }
          } catch (error) {
          console.error('Error fetching invoice data:', error);
          toast.error('Failed to load invoice data. Please try again.');
            navigate('/it-billing/billing-invoice'); // Redirect if fetch fails
          } finally {
            setLoading(false);
          }
        };
        
        fetchInvoiceData();
      }
  }, [isEditMode, id, navigate]);

  // Fetch vendor list from API on mount
  useEffect(() => {
    const fetchVendors = async () => {
      setVendorsLoading(true);
      try {
        console.log('Fetching vendors from API...');
        const response = await api.get<Vendor[]>('/vendors'); // Fetch Vendor[]
        
        if (response.data && Array.isArray(response.data)) {
          // Extract just the names and sort them
          const names = response.data.map(vendor => vendor.vendorName).sort();
          setVendorList(names);
          console.log('Vendors loaded from API.');
        } else {
          console.error('Invalid vendor data received:', response.data);
          toast.error('Received invalid vendor data.');
          setVendorList([]); // Set empty list on error
        }
      } catch (error) {
        console.error('Error fetching vendors:', error);
        toast.error('Failed to load vendors. Please try again later.');
        setVendorList([]); // Set empty list on error
      } finally {
        setVendorsLoading(false);
      }
    };

    fetchVendors();
  }, []); // Empty dependency array means run once on mount

  // Fetch assets, software, and users from APIs
  useEffect(() => {
    // Fetch assets
    const fetchAssets = async () => {
      setAssetsLoading(true);
      try {
        const options = await AssetSoftwareService.getAssetOptions();
        // Filter out category headers and None option
        const filteredAssets = options.filter(
          (option: any) =>
          !option.disabled && option.value !== 'None' && !option.value.startsWith('category-')
        );
        setAssets(filteredAssets);
      } catch (error) {
        console.error('Error fetching assets:', error);
        toast.error('Failed to load assets data.');
        setAssets([]);
      } finally {
        setAssetsLoading(false);
      }
    };

    // Fetch software
    const fetchSoftware = async () => {
      setSoftwareLoading(true);
      try {
        const options = await AssetSoftwareService.getSoftwareOptions();
        // Filter out category headers and None option
        const filteredSoftware = options.filter(
          (option: any) =>
          !option.disabled && option.value !== 'None' && !option.value.startsWith('category-')
        );
        setSoftware(filteredSoftware);
      } catch (error) {
        console.error('Error fetching software:', error);
        toast.error('Failed to load software data.');
        setSoftware([]);
      } finally {
        setSoftwareLoading(false);
      }
    };

    // Fetch users
    const fetchUsers = async () => {
      setUsersLoading(true);
      try {
        const response = await api.get('/users', { 
          params: { limit: 100 }, // Get a large number of users
        });
        
        if (response.data && response.data.users && Array.isArray(response.data.users)) {
          const userOptions = response.data.users.map((user: any) => ({
            value: user.id,
            label: `${user.name} | ${user.department} | ${user.project || 'N/A'}`,
          }));
          setUsers(userOptions);
        } else {
          console.error('Invalid user data received');
          setUsers([]);
        }
      } catch (error) {
        console.error('Error fetching users:', error);
        toast.error('Failed to load users data.');
        setUsers([]);
      } finally {
        setUsersLoading(false);
      }
    };

    fetchAssets();
    fetchSoftware();
    fetchUsers();
  }, []);

  // Updated department list with proper capitalization, full names, and icons
  const departments = [
    { id: 'Information Technology', icon: 'Server' },
    { id: 'Human Resources', icon: 'Users' },
    { id: 'Finance', icon: 'DollarSign' },
    { id: 'Marketing', icon: 'TrendingUp' },
    { id: 'Sales', icon: 'ShoppingCart' },
    { id: 'Operations', icon: 'Settings' },
    { id: 'Customer Service Department', icon: 'MessageSquare' },
    { id: 'Land Department', icon: 'Map' },
    { id: 'Legal', icon: 'FileText' },
    { id: 'Management', icon: 'Briefcase' },
    { id: 'Planning and Development', icon: 'Layers' },
  ];

  // Updated billing categories with icons mapping
  const billingCategories = [
    { id: 'Software', icon: 'FileText' },
    { id: 'Hardware', icon: 'Server' },
    { id: 'Internet', icon: 'Globe' },
    { id: 'Subscription', icon: 'Repeat' },
    { id: 'Licensing', icon: 'Key' },
    { id: 'Domain', icon: 'Globe' },
    { id: 'Cloud Services', icon: 'Cloud' },
    { id: 'Consulting', icon: 'Users' },
    { id: 'Maintenance', icon: 'Wrench' },
    { id: 'Support', icon: 'LifeBuoy' },
  ];

  // Updated payment methods - remove PayPal
  const paymentMethods = [
    'Bank Transfer',
    'Credit Card',
    'Cash',
    'JazzCash',
    'EasyPaisa',
    'Check',
    'Other',
  ];
  const currencies = ['USD', 'PKR', 'EUR', 'GBP'];
  const billingFrequencies = ['Monthly', 'Yearly', 'One-Time'];
  const approvalStatuses = ['Pending', 'Approved', 'Rejected'];

  // Update the Amount field to be dependent on currency
  useEffect(() => {
    // Recalculate total whenever amount, tax, or currency changes
    const amountValue =
      typeof formData.amount === 'string' ? parseFloat(formData.amount) || 0 : formData.amount;
    const taxValue =
      typeof formData.tax === 'string' ? parseFloat(formData.tax) || 0 : formData.tax;
    const total = amountValue * (1 + taxValue / 100);
    setFormData((prev: BillingInvoice) => ({ ...prev, totalAmount: total }));
  }, [formData.amount, formData.tax, formData.currency]);

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      setFormData((prev: BillingInvoice) => ({ ...prev, [name]: checkbox.checked }));
    } else if (type === 'number') {
      // Special handling for amount and tax fields to allow empty values
      if ((name === 'amount' || name === 'tax') && value === '') {
        setFormData((prev: BillingInvoice) => ({ ...prev, [name]: '' }));
      } else {
      setFormData((prev: BillingInvoice) => ({ ...prev, [name]: parseFloat(value) || 0 }));
      }
    } else {
      setFormData((prev: BillingInvoice) => ({ ...prev, [name]: value }));
    }
  };

  // Add debug logging specifically for notes
  useEffect(() => {
    if (isEditMode) {
      console.log('Current notes value in form data:', {
        notes: formData.notes,
        notesType: typeof formData.notes,
        notesLength: formData.notes ? formData.notes.length : 0,
        notesEmpty: !formData.notes,
        notesUndefined: formData.notes === undefined,
        notesNull: formData.notes === null,
      });
    }
  }, [formData.notes, isEditMode]);

  // Update the handleChange function to better handle the notes field
  const originalHandleChange = handleChange;
  const enhancedHandleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    
    // Special handling for notes field
    if (name === 'notes') {
      console.log(`Notes field change detected: ${value}`);
    }
    
    // Special handling for isRecurring checkbox
    if (name === 'isRecurring') {
      const isChecked = (e.target as HTMLInputElement).checked;
      console.log(`isRecurring changed to: ${isChecked}`);
      
      // If checked and billingFrequency is One-Time, set to Monthly as default for recurring
      if (isChecked && formData.billingFrequency === 'One-Time') {
        setFormData(prev => ({
          ...prev,
          isRecurring: isChecked,
          billingFrequency: 'Monthly',
        }));
        return; // Skip original handler since we've updated form data
      }
      
      // If unchecked, set billingFrequency to One-Time
      if (!isChecked) {
        setFormData(prev => ({
          ...prev,
          isRecurring: isChecked,
          billingFrequency: 'One-Time',
        }));
        return; // Skip original handler since we've updated form data
      }
    }
    
    originalHandleChange(e);
  };

  // Handle multi-select changes
  const handleMultiSelectChange = (
    e: React.ChangeEvent<HTMLSelectElement>,
    field: keyof BillingInvoice
  ) => {
    const options = e.target.options;
    const selectedValues: string[] = [];
    
    for (let i = 0; i < options.length; i++) {
      if (options[i].selected) {
        selectedValues.push(options[i].value);
      }
    }
    
    setFormData((prev: BillingInvoice) => ({ ...prev, [field]: selectedValues }));
  };

  // Handle file upload - updated with better authentication handling
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    // Get the file or reset if no file
    const file = e.target.files && e.target.files.length > 0 ? e.target.files[0] : null;
    
    // If no file selected (user clicked cancel), clear the current file
    if (!file) {
      setFormData(prev => ({ ...prev, invoiceFileUrl: '' }));
      return;
    }
    
    // Store the file for retry functionality
    setFileForRetry(file);

    // Continue with upload process
    await processFileUpload(file);
  };

  // Process file upload - separate function for retries
  const processFileUpload = async (file: File) => {
    // Check file size (10MB limit)
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    if (file.size > MAX_FILE_SIZE) {
      toast.error(`File is too large. Maximum size is 10MB.`);
      return;
    }
    
    // Check file type
    const allowedTypes = [
      'application/pdf', 
      'image/jpeg', 
      'image/png', 
      'image/jpg',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
      'application/msword', // doc
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
      'application/vnd.ms-excel', // xls
    ];
    
    if (!allowedTypes.includes(file.type)) {
      toast.error(`Invalid file type. Only PDF, images, and office documents are allowed.`);
      return;
    }
    
    // Create a temporary object URL for preview
    const tempURL = URL.createObjectURL(file);
    setFormData(prev => ({ ...prev, invoiceFileUrl: tempURL }));
    
    // Show uploading toast
    const toastId = toast.loading('Uploading file...');
    setUploadError(null);
    setIsRetrying(false);
    
    try {
      console.log('Uploading file...');
      console.log('File type:', file.type);
      console.log('File size:', file.size);
      
      // Create form data for upload
      const formData = new FormData();
      formData.append('file', file);

      // Log formData contents for debugging
      for (const pair of (formData as any).entries()) {
        console.log('FormData content:', pair[0], pair[1]);
      }

      // Use the api service for the upload with specific options
      const response = await api.post('/uploads/invoice', formData, {
        headers: {
          // Don't set Content-Type here - axios will set it with the boundary
          // Let the interceptor handle auth token
        },
        timeout: 60000, // 60 second timeout for large files
        withCredentials: true, // Include credentials for cross-origin requests if needed
      });
      
      console.log('File upload response:', response);

      // Dismiss the loading toast
      toast.dismiss(toastId);
      
      if (response.data && response.data.fileUrl) {
        // Revoke the temporary URL since we don't need it anymore
        URL.revokeObjectURL(tempURL);
        
        // Get the permanent URL from the response
        const fileUrl = response.data.fileUrl;

        // Fix potential formatting issues with the URL
        // Make sure it starts with a proper slash if needed
        const formattedUrl = fileUrl.startsWith('http')
          ? fileUrl
          : fileUrl.startsWith('/')
            ? fileUrl
            : `/${fileUrl}`;

        console.log('File upload successful, received URL:', fileUrl);
        console.log('Formatted URL for storage:', formattedUrl);

        // Store the actual file name for better error handling later
        const fileName = formattedUrl.split('/').pop();
        console.log('Extracted file name:', fileName);

        // Try to verify the file exists by making a HEAD request
        try {
          const baseUrl = window.location.hostname.includes('localhost')
            ? 'http://localhost:5000'
            : '';
          const fullUrl = `${baseUrl}${formattedUrl}`;
          console.log('Checking if file exists at:', fullUrl);

          const response = await fetch(fullUrl, { method: 'HEAD' });
          console.log('File check response:', response.status, response.ok);

          if (!response.ok) {
            console.warn('Warning: File URL may be incorrect, received status:', response.status);
            // Try alternative paths
            console.log('Will try alternative paths when viewing the file');
          }
        } catch (error) {
          console.warn('Error checking file existence:', error);
        }
        
        // Update form data with the permanent URL
        setFormData(prev => ({ ...prev, invoiceFileUrl: formattedUrl }));
        
        // Show success message
        toast.success('File uploaded successfully');
      } else {
        // Revoke the temporary URL on error
        URL.revokeObjectURL(tempURL);
        
        throw new Error(response.data?.message || 'Upload failed - Invalid server response');
      }
    } catch (error: any) {
      // Dismiss the loading toast
      toast.dismiss(toastId);

      console.error('File upload error:', error);
      
      // Generate a meaningful error message
      let errorMessage = 'Failed to upload file';

      // Check for auth error specifically
      if (error.response?.status === 401) {
        errorMessage = 'Your session has expired. Please try again using the Retry button.';
        setUploadError(errorMessage);

        // Keep the blob URL temporarily while showing the error
        toast.error(errorMessage, {
          duration: 5000,
          position: 'top-center',
        });
      } else if (error.response?.status === 403) {
        errorMessage = 'You do not have permission to upload files.';
        setUploadError(errorMessage);
        toast.error(errorMessage);

        // Revoke the temporary URL
        URL.revokeObjectURL(tempURL);
        setFormData(prev => ({ ...prev, invoiceFileUrl: '' }));
      } else if (error.response?.status === 413) {
        // Payload too large
        errorMessage = 'File is too large for server to process.';
        setUploadError(errorMessage);
        toast.error(errorMessage);

        // Revoke the temporary URL
        URL.revokeObjectURL(tempURL);
        setFormData(prev => ({ ...prev, invoiceFileUrl: '' }));
      } else if (error.response) {
        // Server responded with an error status
        errorMessage += `: ${error.response.status} - ${error.response.data?.message || error.response.statusText}`;
        setUploadError(errorMessage);
        toast.error(errorMessage);

        // Revoke the temporary URL on error
        URL.revokeObjectURL(tempURL);

        // Clear the temporary URL on error
        setFormData(prev => ({ ...prev, invoiceFileUrl: '' }));
      } else if (error.request) {
        // Request was made but no response received
        errorMessage += ': No response from server';
        setUploadError(errorMessage);
        toast.error(errorMessage);

        // Revoke the temporary URL on error
        URL.revokeObjectURL(tempURL);

        // Keep the temp URL for retry possibility
      } else {
        // Error setting up the request
        errorMessage += `: ${error.message}`;
        setUploadError(errorMessage);
      toast.error(errorMessage);
      
      // Revoke the temporary URL on error
      URL.revokeObjectURL(tempURL);
      
      // Clear the temporary URL on error
      setFormData(prev => ({ ...prev, invoiceFileUrl: '' }));
      }
    }
  };

  // Add retry upload function with real retry logic
  const retryFileUpload = () => {
    if (fileForRetry) {
      // Retry with the stored file
      processFileUpload(fileForRetry);
    } else if (fileInputRef.current) {
      // If no stored file, prompt user to select a new file
      fileInputRef.current.click();
    }

    setIsRetrying(true);
  };

  // Handle form submission - updated to use API with better validation
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setIsSubmitting(true);

    try {
      // Validate required fields
      const requiredFields = [
        'invoiceNumber', 
        'vendorName', 
        'serviceProduct', 
        'department', 
        'billingCategory',
        'invoiceDate',
        'dueDate',
        'amount',
        'currency',
        'totalAmount',
      ];
      
      const missingFields = requiredFields.filter(
        field => !formData[field as keyof BillingInvoice]
      );
      
      if (missingFields.length > 0) {
        toast.error(`Missing required fields: ${missingFields.join(', ')}`);
        setLoading(false);
        setIsSubmitting(false);
        return;
      }

      // Check if the form has an invoiceFileUrl that's temporary (starts with blob:)
      // If so, show a warning because the file upload hasn't completed
      if (formData.invoiceFileUrl && formData.invoiceFileUrl.startsWith('blob:')) {
        toast.error('Please wait for the file to finish uploading or remove it before submitting');
        setLoading(false);
        setIsSubmitting(false);
        return;
      }

      // Convert dates to MySQL-compatible format (YYYY-MM-DD)
      // MySQL 'date' type requires this exact format without time component
      const formattedInvoiceDate = new Date(formData.invoiceDate).toISOString().split('T')[0];
      const formattedDueDate = new Date(formData.dueDate).toISOString().split('T')[0];
      
      // Log the formatted dates to verify
      console.log('Formatted dates:', {
        original: {
          invoiceDate: formData.invoiceDate,
          dueDate: formData.dueDate,
        },
        formatted: {
          invoiceDate: formattedInvoiceDate,
          dueDate: formattedDueDate,
        },
      });

      // Safely format linked items - if any of these are undefined or not arrays, set them as empty arrays
      const safeLinkedAssets = Array.isArray(formData.linkedAssets) ? formData.linkedAssets : [];
      const safeLinkedSoftware = Array.isArray(formData.linkedSoftware)
        ? formData.linkedSoftware
        : [];
      const safeAssignedUsers = Array.isArray(formData.assignedUsers) ? formData.assignedUsers : [];

      // Prepare a more simplified data object for submission to avoid potential issues
      const minimalSubmissionData: any = {
        invoiceNumber: formData.invoiceNumber || '',
        vendorName: formData.vendorName || '',
        serviceProduct: formData.serviceProduct || '',
        department: formData.department || '',
        billingCategory: formData.billingCategory || '',
        // Use MySQL-compatible date format (YYYY-MM-DD) without time component
        invoiceDate: formattedInvoiceDate,
        dueDate: formattedDueDate,
        amount: Number(formData.amount) || 0,
        tax: Number(formData.tax) || 0,
        totalAmount: Number(formData.totalAmount) || 0,
        paymentMethod: formData.paymentMethod || '',
        notes: formData.notes || '',
        isRecurring: Boolean(formData.isRecurring),
        billingFrequency: formData.billingFrequency,
        reminderDays: formData.reminderDays,
        linkedAssets: safeLinkedAssets.map(asset => ({
          id: asset.id || '',
          name: asset.name || 'Unknown Asset',
        })),
        linkedSoftware: safeLinkedSoftware.map(sw => ({
          id: sw.id || '',
          name: sw.name || 'Unknown Software',
        })),
        assignedUsers: safeAssignedUsers.map(user => ({
          id: user.id || '',
          name: user.name || 'Unknown User',
        })),
        approvalStatus: formData.approvalStatus || 'Pending',
        createdBy: formData.createdBy || 'Current User',
        approvedBy: formData.approvedBy || '',
        lastModifiedBy: formData.lastModifiedBy || 'Current User',
        lastModifiedDate: new Date().toISOString(),
        showDeptDropdown: formData.showDeptDropdown,
        showCategoryDropdown: formData.showCategoryDropdown,
      };
      
      if (isEditMode) {
        minimalSubmissionData.id = formData.id;
      }

      // Make sure we're capturing the file URL properly
      console.log('File URL during submission:', {
        originalUrl: formData.invoiceFileUrl,
        isTemporary: formData.invoiceFileUrl?.startsWith('blob:'),
        willBeIncluded: !!(formData.invoiceFileUrl && !formData.invoiceFileUrl.startsWith('blob:')),
      });
      
      // Only include optional fields if they exist and are valid
      if (formData.invoiceFileUrl && !formData.invoiceFileUrl.startsWith('blob:')) {
        minimalSubmissionData.invoiceFileUrl = formData.invoiceFileUrl;
        console.log('Including file URL in submission:', minimalSubmissionData.invoiceFileUrl);
      } else if (hasAttachedFile && !formData.invoiceFileUrl) {
        // This is a special case - we know there should be a file but the URL is missing
        console.log('File attachment state indicates a file should be present, but URL is missing');
      }
      
      if (formData.billingFrequency) {
        minimalSubmissionData.billingFrequency = formData.billingFrequency;
      }
      
      if (formData.reminderDays && formData.reminderDays > 0) {
        minimalSubmissionData.reminderDays = formData.reminderDays;
      }
      
      // Only include linked items if they're valid arrays with actual items
      if (Array.isArray(safeLinkedAssets) && safeLinkedAssets.length > 0) {
        minimalSubmissionData.linkedAssets = safeLinkedAssets.map(asset => ({
          id: asset.id || '',
          name: asset.name || 'Unknown Asset',
        }));
      }
      
      if (Array.isArray(safeLinkedSoftware) && safeLinkedSoftware.length > 0) {
        minimalSubmissionData.linkedSoftware = safeLinkedSoftware.map(sw => ({
          id: sw.id || '',
          name: sw.name || 'Unknown Software',
        }));
      }
      
      if (Array.isArray(safeAssignedUsers) && safeAssignedUsers.length > 0) {
        minimalSubmissionData.assignedUsers = safeAssignedUsers.map(user => ({
          id: user.id || '',
          name: user.name || 'Unknown User',
        }));
      }
      
      console.log(
        'Submitting minimal invoice data:',
        JSON.stringify(minimalSubmissionData, null, 2)
      );
      
      // Create an ultra-minimal test submission with just required fields
      // Ensure all strings are actually strings and numbers are numbers
      const testSubmission = {
        invoiceNumber: String(formData.invoiceNumber || ''),
        vendorName: String(formData.vendorName || ''),
        serviceProduct: String(formData.serviceProduct || ''),
        department: String(formData.department || ''),
        billingCategory: String(formData.billingCategory || ''),
        // Use YYYY-MM-DD format only, not ISO strings
        invoiceDate: formattedInvoiceDate,
        dueDate: formattedDueDate,
        amount: parseFloat(String(formData.amount || 0)),
        tax: parseFloat(String(formData.tax || 0)),
        totalAmount: parseFloat(String(formData.totalAmount || 0)),
        approvalStatus: 'Pending', // Use quotes to ensure it's a string
        createdBy: 'Current User', // Use quotes to ensure it's a string
        lastModifiedBy: 'Current User', // Use quotes to ensure it's a string
        // Use YYYY-MM-DD format for lastModifiedDate
        lastModifiedDate: new Date().toISOString().split('T')[0],
        paymentMethod: String(formData.paymentMethod || ''),
        isRecurring: Boolean(formData.isRecurring),
        // Include recurring billing fields
        billingFrequency: formData.billingFrequency || 'One-Time',
        reminderDays: parseInt(String(formData.reminderDays), 10) || 3,
        notes: String(formData.notes || ''), // Ensure notes is included
      };
      
      // Log dates specifically to help debug time-related issues
      console.log('Date formats being sent:', {
        invoiceDate: testSubmission.invoiceDate,
        dueDate: testSubmission.dueDate,
        lastModifiedDate: testSubmission.lastModifiedDate,
        currentISOString: new Date().toISOString(),
        invoiceDateFromForm: formData.invoiceDate,
        dueDateFromForm: formData.dueDate,
      });
      
      // Validate there are no circular references in the data
      const validateSubmissionData = (data: any) => {
        try {
          JSON.stringify(data);
          return true;
        } catch (error) {
          console.error('Circular reference detected in submission data:', error);
          return false;
        }
      };
      
      // Check data validity
      if (!validateSubmissionData(testSubmission)) {
        toast.error('Could not process form data: Invalid data structure');
        setLoading(false);
        setIsSubmitting(false);
        return;
      }
      
      // Use the api service instead of direct axios
      let response;
      try {
        // First try with ultra-minimal data but a completely different approach
        // Create a version with minimal fields and MySQL-compatible date formats
        const basicSubmissionData = {
          invoiceNumber: String(formData.invoiceNumber || ''),
          vendorName: String(formData.vendorName || ''),
          serviceProduct: String(formData.serviceProduct || ''),
          department: String(formData.department || ''),
          billingCategory: String(formData.billingCategory || ''),
          // Use only YYYY-MM-DD format for MySQL compatibility
          invoiceDate: formattedInvoiceDate,
          dueDate: formattedDueDate,
          amount: parseFloat(String(formData.amount || 0)),
          tax: parseFloat(String(formData.tax || 0)),
          totalAmount: parseFloat(String(formData.totalAmount || 0)),
          // Try using the enum value that MySQL expects
          approvalStatus: 'PENDING', // Try uppercase
          // Include only minimal required fields - no timestamps or other complex fields
          paymentMethod: String(formData.paymentMethod || ''),
          // Include payment detail fields based on payment method
          accountTitle: formData.accountTitle || '',
          mobileNumber: formData.mobileNumber || '',
          checkNumber: formData.checkNumber || '',
          bankName: formData.bankName || '',
          receivedBy: formData.receivedBy || '',
          receiptNumber: formData.receiptNumber || '',
          paymentNotes: formData.paymentNotes || '',
          accountNumber: formData.accountNumber || '', // Added for Bank Transfer
          cardNumber: formData.cardNumber || '', // Added for Credit Card
          expiryDate: formData.expiryDate || '', // Added for Credit Card
          cvv: formData.cvv || '', // Added for Credit Card
          isRecurring: Boolean(formData.isRecurring),
          billingFrequency: formData.billingFrequency || 'One-Time',
          reminderDays: formData.reminderDays,
          notes: formData.notes || '',
          // Include associations in a format the API can handle
          linkedAssets: safeLinkedAssets.map(asset => ({
            id: asset.id || '',
            name: asset.name || 'Unknown Asset',
          })),
          linkedSoftware: safeLinkedSoftware.map(sw => ({
            id: sw.id || '',
            name: sw.name || 'Unknown Software',
          })),
          assignedUsers: safeAssignedUsers.map(user => ({
            id: user.id || '',
            name: user.name || 'Unknown User',
          })),
        };
        
        console.log(
          'Submitting data with MySQL-compatible dates:',
          JSON.stringify(basicSubmissionData, null, 2)
        );
        
        // Remove debug alert
        
      if (isEditMode) {
          console.log(`Updating invoice ${formData.id} with fixed date formats`);
          // Ensure token is properly set for authorization
          const token = localStorage.getItem('authToken');
          console.log('Using token for authorization:', token ? 'Token exists' : 'No token found');
          
          // Add extra headers for authorization as a fallback
          const headers = token ? { Authorization: `Bearer ${token}` } : {};
          response = await api.put(`/billing-invoices/${formData.id}`, basicSubmissionData, { 
            headers,
            // Signal that this request should not trigger automatic logout
            withCredentials: true,
          });
        } else {
          console.log('Creating new invoice with fixed date formats');
          // Ensure token is properly set for authorization
          const token = localStorage.getItem('authToken');
          console.log('Using token for authorization:', token ? 'Token exists' : 'No token found');
          
          // Add extra headers for authorization as a fallback
          const headers = token ? { Authorization: `Bearer ${token}` } : {};
          response = await api.post('/billing-invoices', basicSubmissionData, { 
            headers,
            // Signal that this request should not trigger automatic logout
            withCredentials: true,
          });
        }
        
        console.log('API response received:', {
          status: response.status,
          statusText: response.statusText,
          data: response.data,
        });
        
        // If we get here, the test submission worked, so we can update with full data
        // But for now, let's just proceed with the success path
        toast.success(`Invoice ${isEditMode ? 'updated' : 'added'} successfully!`);
        
        // Add a slight delay before navigating to ensure the toast is shown
        setTimeout(() => {
        navigate('/it-billing/billing-invoicing'); // Correct the navigation path
        }, 1000);
        
        return; // Exit early since we succeeded
        
        /* Commented out full submission for now
        if (isEditMode) {
          response = await api.put(`/billing-invoices/${formData.id}`, minimalSubmissionData);
        } else {
          response = await api.post('/billing-invoices', minimalSubmissionData);
      }

      console.log('Invoice submission response:', response);

        // Check for success property or status code
        if (response.data?.success || response.status === 200 || response.status === 201) {
      toast.success(`Invoice ${isEditMode ? 'updated' : 'added'} successfully!`);
          navigate('/it-billing/billing-invoicing'); // Navigate back to the list view
      } else {
          throw new Error(response.data?.message || 'Operation failed with unknown error');
        }
        */
      } catch (apiError: any) {
        console.error('API Error Details:', {
          status: apiError.response?.status,
          statusText: apiError.response?.statusText,
          data: apiError.response?.data,
          serverMessage: apiError.response?.data?.message,
          error: apiError.response?.data?.error,
          detailedError: apiError.response?.data?.detailedError,
          headers: apiError.response?.headers,
          config: {
            url: apiError.config?.url,
            method: apiError.config?.method,
            data: apiError.config?.data, // Log the actual data sent
            headers: apiError.config?.headers,
          },
        });
        
        // Try to extract useful information from HTML error responses
        if (
          typeof apiError.response?.data === 'string' &&
          apiError.response.data.includes('<!DOCTYPE html>')
        ) {
          // This might be an HTML error page
          try {
            // Try to extract the useful parts of the error message
            const htmlContent = apiError.response.data;
            const titleMatch = htmlContent.match(/<title>(.*?)<\/title>/);
            const bodyMatch = htmlContent.match(/<body[^>]*>([\s\S]*?)<\/body>/);
            
            console.error('Server returned HTML error:', {
              title: titleMatch ? titleMatch[1] : 'Unknown error',
              body: bodyMatch
                ? bodyMatch[1]
                    .replace(/<[^>]*>/g, ' ')
                    .replace(/\s+/g, ' ')
                    .trim()
                : 'No body content',
            });
          } catch (parseError) {
            console.error('Failed to parse HTML error response');
          }
        }
        
        throw apiError; // Re-throw for the outer catch block
      }
    } catch (error: any) {
      console.error('Error submitting invoice:', error);
      toast.error(`Failed to ${isEditMode ? 'update' : 'create'} invoice. Please try again.`);
    } finally {
      setLoading(false);
      setIsSubmitting(false);
    }
  };

  // Handle cancellation
  const handleCancel = () => {
    navigate('/it-billing/billing-invoicing'); // Correct the navigation path
  };

  // Filter assets based on search input
  useEffect(() => {
    if (assetSearch.trim() === '') {
      setFilteredAssets([]);
      return;
    }
    
    const searchLower = assetSearch.toLowerCase();
    const filtered = assets.filter(
      asset => asset.label && asset.label.toLowerCase().includes(searchLower)
    );
    setFilteredAssets(filtered.slice(0, 20)); // Increase limit to 20 results
  }, [assetSearch, assets]);

  // Filter software based on search input
  useEffect(() => {
    if (softwareSearch.trim() === '') {
      setFilteredSoftware([]);
      return;
    }
    
    const searchLower = softwareSearch.toLowerCase();
    const filtered = software.filter(
      sw => sw.label && sw.label.toLowerCase().includes(searchLower)
    );
    setFilteredSoftware(filtered.slice(0, 20)); // Increase limit to 20 results
  }, [softwareSearch, software]);

  // Filter users based on search input
  useEffect(() => {
    if (userSearch.trim() === '') {
      setFilteredUsers([]);
      return;
    }
    
    const searchLower = userSearch.toLowerCase();
    const filtered = users.filter(
      user => user.label && user.label.toLowerCase().includes(searchLower)
    );
    setFilteredUsers(filtered.slice(0, 20)); // Increase limit to 20 results
  }, [userSearch, users]);

  // Add asset to linked assets
  const handleAddAsset = (asset: any) => {
    // Extract information from the asset
    const assetInfo = asset.label.split('|').map((part: string) => part.trim());
    let assetNamePart = assetInfo[0] || '';
    const assetDept = assetInfo[1] || '';
    
    // Remove any asset ID in parentheses from the name - handle more formats
    assetNamePart = assetNamePart.replace(/\s*\([A-Za-z]+-\d+\)/, '').trim();
    
    // Try to extract category from metadata or from asset name
    let category = '';
    
    // Determine asset type - this is critical for icon selection
    let assetType = '';
    
    // First check metadata for asset type
    if (asset.metadata && asset.metadata.assetType) {
      assetType = asset.metadata.assetType;
    }
    
    // Get category from metadata if available
    if (asset.metadata && asset.metadata.category) {
      category = asset.metadata.category;
    }
    
    // If no asset type determined yet, try to infer from name
    if (!assetType) {
      const label = assetNamePart.toLowerCase();
      
      // Check for specific asset types in the name
      if (label.includes('laptop')) {
        assetType = 'laptop';
      } else if (label.includes('desktop')) {
        assetType = 'desktop';
      } else if (label.includes('tablet')) {
        assetType = 'tablet';
      } else if (label.includes('monitor') || label.includes('display')) {
        assetType = 'monitor';
      } else if (label.includes('keyboard')) {
        assetType = 'keyboard';
      } else if (label.includes('mouse')) {
        assetType = 'mouse';
      } else if (
        label.includes('printer') ||
        label.includes('plotter') ||
        label.includes('laser printer') ||
        (label.includes('hp') && label.match(/\b\d{4}\b/))
      ) {
        assetType = 'printer';
      } else if (label.includes('server')) {
        assetType = 'server';
      } else if (label.includes('phone')) {
        assetType = 'phone';
      } else if (
        label.includes('network') ||
        label.includes('router') ||
        label.includes('switch') ||
        label.includes('access point') ||
        label.includes('ap')
      ) {
        assetType = 'network';
      } 
      // Brand-based detection for common manufacturers
      else if (label.includes('dell') || label.includes('hp') || label.includes('lenovo')) {
        // These brands make various products, but most commonly computers
        if (
          label.includes('latitude') ||
          label.includes('thinkpad') ||
          label.includes('elitebook') ||
          label.includes('probook')
        ) {
          assetType = 'laptop';
        } else if (
          label.includes('optiplex') ||
          label.includes('thinkcentre') ||
          label.includes('prodesk') ||
          label.includes('elitedesk')
        ) {
          assetType = 'desktop';
        }
      } else if (
        label.includes('brother') ||
        label.includes('canon') ||
        label.includes('epson') ||
        label.includes('xerox')
      ) {
        assetType = 'printer';
      } else if (
        label.includes('cisco') ||
        label.includes('meraki') ||
        label.includes('ubiquiti') ||
        label.includes('netgear')
      ) {
        assetType = 'network';
      }
    }
    
    // If no category from metadata, try to extract from name
    if (!category) {
      // Try to extract from name - assume format like "Category - Name"
      const nameParts = assetNamePart.split('-');
      if (nameParts.length > 1) {
        category = nameParts[0].trim();
      }
    }
    
    // Create a new asset item with full information
    const newAssetItem: AssetItem = {
      id: asset.value,
      name: assetNamePart,
      category: category,
      department: assetDept,
      assetType: assetType, // Always include the asset type
    };
    
    // Skip if already added (check by ID)
    if (ensureArray(formData.linkedAssets).some(item => item.id === newAssetItem.id)) {
      return;
    }
    
    setFormData(prev => ({
      ...prev,
      linkedAssets: [...ensureArray(prev.linkedAssets), newAssetItem],
    }));
    
    setAssetSearch('');
    setShowAssetDropdown(false);
  };

  // Add software to linked software
  const handleAddSoftware = (sw: any) => {
    // Extract information from the software
    const softwareInfo = sw.label.split('|').map((part: string) => part.trim());
    const softwareNamePart = softwareInfo[0] || '';
    const softwareDept = softwareInfo[1] || '';
    
    // Create a new software item with full information
    const newSoftwareItem: SoftwareItem = {
      id: sw.value,
      name: softwareNamePart,
      category: sw.metadata?.category || '',
      department: softwareDept,
    };
    
    // Skip if already added (check by ID)
    if (ensureArray(formData.linkedSoftware).some(item => item.id === newSoftwareItem.id)) {
      return;
    }
    
    setFormData(prev => ({
      ...prev,
      linkedSoftware: [...ensureArray(prev.linkedSoftware), newSoftwareItem],
    }));
    
    setSoftwareSearch('');
    setShowSoftwareDropdown(false);
  };

  // Add user to assigned users
  const handleAddUser = (user: any) => {
    // Extract information from the user
    const userInfo = user.label.split('|').map((part: string) => part.trim());
    const userName = userInfo[0] || '';
    const userDept = userInfo[1] || '';
    const userProject = userInfo[2] || '';
    
    // Create a new user item with full information
    const newUserItem: UserItem = {
      id: user.value,
      name: userName,
      department: userDept,
      project: userProject,
    };
    
    // Skip if already added (check by ID)
    if (ensureArray(formData.assignedUsers).some(item => item.id === newUserItem.id)) {
      return;
    }
    
    setFormData(prev => ({
      ...prev,
      assignedUsers: [...ensureArray(prev.assignedUsers), newUserItem],
    }));
    
    setUserSearch('');
    setShowUserDropdown(false);
  };

  // Function to remove an asset from the linked assets
  const handleRemoveAsset = (index: number) => {
    const updatedAssets = ensureArray(formData.linkedAssets).filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, linkedAssets: updatedAssets }));
  };

  // Function to remove software from the linked software
  const handleRemoveSoftware = (index: number) => {
    const updatedSoftware = ensureArray(formData.linkedSoftware).filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, linkedSoftware: updatedSoftware }));
  };

  // Function to remove a user from the assigned users
  const handleRemoveUser = (index: number) => {
    const updatedUsers = ensureArray(formData.assignedUsers).filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, assignedUsers: updatedUsers }));
  };

  // Helper function to determine asset icon based on type and label
  const getAssetIcon = (type: string = '', label: string = '') => {
    const source = type?.toLowerCase().trim() || label?.toLowerCase().trim() || '';
    
    // Use more visually distinctive icons and ensure specific matches are prioritized
    // Specific product matches first
    if (source.includes('laser printer')) return Printer; 
    if (source.includes('m202') || source.includes('6210')) return Printer;
    
    // Then check general device types
    if (source.includes('laptop')) return Laptop;
    if (source.includes('desktop')) return Monitor;
    if (source.includes('tablet')) return Tablet;
    if (source.includes('printer') || source.includes('plotter')) return Printer;
    if (source.includes('monitor') || source.includes('display')) return Tv;
    if (source.includes('keyboard')) return Keyboard;
    if (source.includes('mouse')) return Mouse;
    if (source.includes('server')) return Server;
    if (source.includes('phone')) return Phone;
    if (source.includes('router') || source.includes('modem') || source.includes('switch'))
      return Wifi;
    if (source.includes('network')) return Globe;
    if (source.includes('firewall')) return Shield;
    if (
      source.includes('drive') ||
      source.includes('storage') ||
      source.includes('disk') ||
      source.includes('hdd') ||
      source.includes('ssd')
    )
      return HardDrive;
    if (source.includes('processor') || source.includes('cpu')) return Cpu;
    if (source.includes('power') || source.includes('ups') || source.includes('battery'))
      return PlugZap;
    if (
      source.includes('peripheral') ||
      source.includes('accessory') ||
      source.includes('component')
    )
      return Settings2;
    
    // Make sure we're not missing any obvious category
    if (source.includes('wireless')) return Wifi;
    if (source.includes('scanner')) return Printer;
    if (source.includes('gaming')) return Laptop;
    if (source.includes('workstation')) return Monitor;
    
    // Brand-based fallbacks if we couldn't match by type
    if (source.includes('hp') || source.includes('dell') || source.includes('lenovo')) {
      if (
        source.includes('pavilion') ||
        source.includes('thinkpad') ||
        source.includes('latitude')
      ) {
        return Laptop; // These are typically laptop models
      }
      if (
        source.includes('optiplex') ||
        source.includes('precision') ||
        source.includes('thinkcentre')
      ) {
        return Monitor; // These are typically desktop models
      }
    }
    
    // Default fallback - changed from Package to HardDrive
    console.log('Using default icon for: ', source); // Add debug logging
    return HardDrive;
  };
  
  // Helper function to determine icon color based on asset type/name
  const getAssetIconColor = (type: string = '', label: string = '') => {
    const source = type?.toLowerCase().trim() || label?.toLowerCase().trim() || '';
    
    // Specific matches first (matching the order in getAssetIcon)
    if (source.includes('laser printer')) return 'text-green-700'; // Darker green for laser printers
    if (source.includes('m202') || source.includes('6210')) return 'text-green-700';
    
    // Then check general device types with distinct colors
    if (source.includes('laptop')) return 'text-blue-500';
    if (source.includes('desktop')) return 'text-blue-800'; // Darker blue for desktops
    if (source.includes('tablet')) return 'text-cyan-600';
    if (source.includes('printer') || source.includes('plotter')) return 'text-green-600';
    if (source.includes('monitor') || source.includes('display')) return 'text-teal-600';
    if (source.includes('keyboard')) return 'text-gray-700'; // Darker gray
    if (source.includes('mouse')) return 'text-gray-600';
    if (source.includes('server')) return 'text-purple-600';
    if (source.includes('phone')) return 'text-orange-500';
    if (source.includes('router') || source.includes('modem') || source.includes('switch'))
      return 'text-indigo-600';
    if (source.includes('network')) return 'text-indigo-400'; // Lighter indigo
    if (source.includes('firewall')) return 'text-red-500';
    if (
      source.includes('drive') ||
      source.includes('storage') ||
      source.includes('disk') ||
      source.includes('hdd') ||
      source.includes('ssd')
    )
      return 'text-amber-600';
    if (source.includes('processor') || source.includes('cpu')) return 'text-purple-500';
    if (source.includes('power') || source.includes('ups') || source.includes('battery'))
      return 'text-yellow-500';
    if (source.includes('peripheral') || source.includes('accessory')) return 'text-gray-500';
    
    // Additional categories
    if (source.includes('wireless')) return 'text-sky-500';
    if (source.includes('scanner')) return 'text-emerald-500';
    if (source.includes('gaming')) return 'text-pink-500';
    if (source.includes('workstation')) return 'text-blue-700';
    
    // Default fallback
    return 'text-gray-400';
  };

  // Process associations when the necessary data is loaded
  useEffect(() => {
    // Only proceed if we're in edit mode and have loaded all the necessary data
    if (isEditMode && !assetsLoading && !softwareLoading && !usersLoading) {
      console.log('Processing associations for edit mode');
      
      // Process the existing form data to ensure associations are properly set up
      setFormData(prevFormData => {
        const processedData = { ...prevFormData };
        
        // Process linkedAssets if they're in the wrong format
        if (processedData.linkedAssets) {
          if (processedData.linkedAssets.length > 0) {
            // Check if we need to convert string IDs to objects
            if (typeof processedData.linkedAssets[0] === 'string') {
              processedData.linkedAssets = (processedData.linkedAssets as unknown as string[]).map(
                id => {
                const assetItem = assets.find(a => a.value === id);
                let name = 'Unknown Asset';
                let dept = '';
                let category = '';
                let assetType = '';
                
                if (assetItem?.label) {
                  const assetInfo = assetItem.label.split('|').map((part: string) => part.trim());
                  name = assetInfo[0] || 'Unknown Asset';
                  dept = assetInfo[1] || '';
                  
                  // Try to extract category and type
                  if (assetItem.metadata) {
                    category = assetItem.metadata.category || '';
                    assetType = assetItem.metadata.assetType || '';
                  } else {
                    const nameParts = name.split('-');
                    if (nameParts.length > 1) {
                      category = nameParts[0].trim();
                    }
                  }
                }
                
                return {
                  id: id,
                  name: name, 
                  category: category,
                  department: dept,
                    assetType: assetType,
                } as AssetItem;
                }
              );
            }
          } else {
            // Ensure it's an empty array, not null or undefined
            processedData.linkedAssets = [];
          }
        } else {
          processedData.linkedAssets = [];
        }
        
        // Process linkedSoftware if they're in the wrong format
        if (processedData.linkedSoftware) {
          if (processedData.linkedSoftware.length > 0) {
            // Check if we need to convert string IDs to objects
            if (typeof processedData.linkedSoftware[0] === 'string') {
              processedData.linkedSoftware = (
                processedData.linkedSoftware as unknown as string[]
              ).map(id => {
                const softwareItem = software.find(s => s.value === id);
                let name = 'Unknown Software';
                let dept = '';
                let category = '';
                
                if (softwareItem?.label) {
                  const softwareInfo = softwareItem.label
                    .split('|')
                    .map((part: string) => part.trim());
                  name = softwareInfo[0] || 'Unknown Software';
                  dept = softwareInfo[1] || '';
                  category = softwareItem.metadata?.category || '';
                }
                
                return {
                  id: id,
                  name: name,
                  category: category,
                  department: dept,
                } as SoftwareItem;
              });
            }
          } else {
            // Ensure it's an empty array, not null or undefined
            processedData.linkedSoftware = [];
          }
        } else {
          processedData.linkedSoftware = [];
        }
        
        // Process assignedUsers if they're in the wrong format
        if (processedData.assignedUsers) {
          if (processedData.assignedUsers.length > 0) {
            // Check if we need to convert string IDs to objects
            if (typeof processedData.assignedUsers[0] === 'string') {
              processedData.assignedUsers = (
                processedData.assignedUsers as unknown as string[]
              ).map(id => {
                const userItem = users.find(u => u.value === id);
                let name = 'Unknown User';
                let dept = '';
                let project = '';
                
                if (userItem?.label) {
                  const userInfo = userItem.label.split('|').map((part: string) => part.trim());
                  name = userInfo[0] || 'Unknown User';
                  dept = userInfo[1] || '';
                  project = userInfo[2] || '';
                }
                
                return {
                  id: id,
                  name: name,
                  department: dept,
                  project: project,
                } as UserItem;
              });
            }
          } else {
            // Ensure it's an empty array, not null or undefined
            processedData.assignedUsers = [];
          }
        } else {
          processedData.assignedUsers = [];
        }
        
        console.log('Processed associations:', {
          assets: processedData.linkedAssets?.length || 0,
          software: processedData.linkedSoftware?.length || 0, 
          users: processedData.assignedUsers?.length || 0,
        });
        
        return processedData;
      });
    }
  }, [isEditMode, assets, software, users, assetsLoading, softwareLoading, usersLoading]);

  // Add debug logging in useEffect hooks
  // Debug logging for linked entities
  useEffect(() => {
    if (isEditMode) {
      console.log('Current form data linked entities:', {
        linkedAssets: formData.linkedAssets,
        linkedSoftware: formData.linkedSoftware,
        assignedUsers: formData.assignedUsers,
        notes: formData.notes,
      });
    }
  }, [
    formData.linkedAssets,
    formData.linkedSoftware,
    formData.assignedUsers,
    formData.notes,
    isEditMode,
  ]);

  // Add a special useEffect to ensure notes are properly loaded when the form mounts
  useEffect(() => {
    if (isEditMode && formData.id) {
      console.log('Form mounted in edit mode, ensuring notes are properly initialized');
      // Force a refresh of the form data to ensure notes are properly loaded
      setFormData(prev => ({
        ...prev,
        notes: prev.notes || '', // Ensure notes is never undefined or null
      }));
    }
  }, [isEditMode, formData.id]);

  // Remove the outer modal div. Wrap content in a standard page container.
  return (
    <div className="p-6"> 
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl mx-auto mb-8">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
            <FileText className="h-6 w-6" />
            {isEditMode ? 'Edit Invoice' : 'Add New Invoice'} 
          </h3>
          {/* Cancel button can be moved to the bottom with Save */}
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Invoice Number */}
            <div>
              <label
                htmlFor="invoiceNumber"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                Invoice Number <span className="text-red-500">*</span>
              </label>
                <input
                  type="text"
                  name="invoiceNumber"
                  id="invoiceNumber"
                  value={formData.invoiceNumber}
                  onChange={enhancedHandleChange}
                className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="INV-2023-001"
                  required
                  disabled={loading} // Disable fields when loading
                />
            </div>

            {/* Vendor Name */}
            <div>
              <label
                htmlFor="vendorName"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
              >
                <Briefcase className="h-4 w-4" />
                Vendor Name <span className="text-red-500">*</span>
              </label>
              <select
                name="vendorName"
                id="vendorName"
                value={formData.vendorName}
                onChange={enhancedHandleChange}
                className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                required
                disabled={loading || vendorsLoading} // Disable while loading/empty
              >
                <option value="">{vendorsLoading ? 'Loading vendors...' : 'Select Vendor'}</option>
                {!vendorsLoading &&
                  vendorList.map(vendor => (
                    <option key={vendor} value={vendor}>
                      {vendor}
                    </option>
                ))}
              </select>
              {vendorsLoading && (
                <p className="text-xs text-gray-500 mt-1">Loading available vendors...</p>
              )}
              {!vendorsLoading && vendorList.length === 0 && (
                <p className="text-xs text-red-500 mt-1">Could not load vendors.</p>
              )}
            </div>

            {/* Service/Product */}
            <div>
              <label
                htmlFor="serviceProduct"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
              >
                <Settings2 className="h-4 w-4" />
                Service/Product <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="serviceProduct"
                id="serviceProduct"
                value={formData.serviceProduct}
                onChange={enhancedHandleChange}
                className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="e.g., Office 365 License"
                required
                disabled={loading}
              />
            </div>

            {/* Department - Custom Dropdown with Icons */}
            <div>
              <label
                htmlFor="department"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
              >
                {formData.department ? (
                  (() => {
                    const dept = departments.find(d => d.id === formData.department);
                    if (!dept) return <Building className="h-4 w-4" />;
                    
                    // Get the icon component based on the selected department
                    const IconComponent =
                      dept.icon === 'Server'
                        ? Server
                        : dept.icon === 'Users'
                          ? Users
                          : dept.icon === 'DollarSign'
                            ? DollarSign
                            : dept.icon === 'TrendingUp'
                              ? TrendingUp
                              : dept.icon === 'ShoppingCart'
                                ? ShoppingCart
                                : dept.icon === 'Settings'
                                  ? Settings
                                  : dept.icon === 'MessageSquare'
                                    ? MessageSquare
                                    : dept.icon === 'Map'
                                      ? Map
                                      : dept.icon === 'FileText'
                                        ? FileText
                                        : dept.icon === 'Briefcase'
                                          ? Briefcase
                                          : dept.icon === 'Layers'
                                            ? Layers
                                            : Building;
                    
                    return <IconComponent className="h-4 w-4" />;
                  })() 
                ) : (
                  <Building className="h-4 w-4" />
                )}
                Department <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <button
                  type="button"
                  onClick={() => {
                    if (!loading) {
                      setFormData(prev => ({ 
                        ...prev, 
                        showDeptDropdown: !prev.showDeptDropdown,
                      }));
                    }
                  }}
                  className="block w-full pl-3 pr-10 py-2 text-left border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  disabled={loading}
                >
                  {formData.department ? (
                    <div className="flex items-center">
                      {(() => {
                        const dept = departments.find(d => d.id === formData.department);
                        if (!dept) return <Building className="h-4 w-4 mr-2" />;
                        
                        // Get the icon component based on the selected department
                        const IconComponent =
                          dept.icon === 'Server'
                            ? Server
                            : dept.icon === 'Users'
                              ? Users
                              : dept.icon === 'DollarSign'
                                ? DollarSign
                                : dept.icon === 'TrendingUp'
                                  ? TrendingUp
                                  : dept.icon === 'ShoppingCart'
                                    ? ShoppingCart
                                    : dept.icon === 'Settings'
                                      ? Settings
                                      : dept.icon === 'MessageSquare'
                                        ? MessageSquare
                                        : dept.icon === 'Map'
                                          ? Map
                                          : dept.icon === 'FileText'
                                            ? FileText
                                            : dept.icon === 'Briefcase'
                                              ? Briefcase
                                              : dept.icon === 'Layers'
                                                ? Layers
                                                : Building;
                        
                        return <IconComponent className="h-4 w-4 mr-2" />;
                      })()}
                      {formData.department}
                    </div>
                  ) : (
                    <span className="text-gray-400">Select Department</span>
                  )}
                </button>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <svg
                    className="h-5 w-5 text-gray-400"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                
                {formData.showDeptDropdown && (
                  <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                    <div className="py-1">
                      {departments.map(dept => {
                        // Get the icon component for this department
                        const IconComponent =
                          dept.icon === 'Server'
                            ? Server
                            : dept.icon === 'Users'
                              ? Users
                              : dept.icon === 'DollarSign'
                                ? DollarSign
                                : dept.icon === 'TrendingUp'
                                  ? TrendingUp
                                  : dept.icon === 'ShoppingCart'
                                    ? ShoppingCart
                                    : dept.icon === 'Settings'
                                      ? Settings
                                      : dept.icon === 'MessageSquare'
                                        ? MessageSquare
                                        : dept.icon === 'Map'
                                          ? Map
                                          : dept.icon === 'FileText'
                                            ? FileText
                                            : dept.icon === 'Briefcase'
                                              ? Briefcase
                                              : dept.icon === 'Layers'
                                                ? Layers
                                                : Building;
                        
                        return (
                          <div
                            key={dept.id}
                            className={`cursor-pointer select-none relative py-2 pl-3 pr-9 flex items-center hover:bg-gray-100 dark:hover:bg-gray-700 ${
                              formData.department === dept.id
                                ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-300'
                                : ''
                            }`}
                            onClick={() => {
                              handleChange({ 
                                target: { name: 'department', value: dept.id },
                              } as React.ChangeEvent<HTMLSelectElement>);
                              setFormData(prev => ({ ...prev, showDeptDropdown: false }));
                            }}
                          >
                            <IconComponent className="h-4 w-4 mr-2" />
                            <span className="block truncate">{dept.id}</span>
                            {formData.department === dept.id && (
                              <span className="absolute inset-y-0 right-0 flex items-center pr-4">
                                <CheckSquare className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                              </span>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
              
              {/* Hidden select for form submission */}
              <input type="hidden" name="department" value={formData.department} required />
            </div>

            {/* Billing Category - Custom Dropdown with Icons */}
            <div>
              <label
                htmlFor="billingCategory"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
              >
                {formData.billingCategory ? (
                  (() => {
                    const category = billingCategories.find(c => c.id === formData.billingCategory);
                    if (!category) return <Tag className="h-4 w-4" />;
                    
                    // Get the icon component based on the selected category
                    const IconComponent = 
                      category.icon === 'FileText'
                        ? FileText
                        : category.icon === 'Server'
                          ? Server
                          : category.icon === 'Globe'
                            ? Globe
                            : category.icon === 'Repeat'
                              ? Repeat
                              : category.icon === 'Key'
                                ? Key
                                : category.icon === 'Cloud'
                                  ? Cloud
                                  : category.icon === 'Users'
                                    ? Users
                                    : category.icon === 'Wrench'
                                      ? Wrench
                                      : category.icon === 'LifeBuoy'
                                        ? LifeBuoy
                                        : Tag;
                    
                    return <IconComponent className="h-4 w-4" />;
                  })() 
                ) : (
                  <Tag className="h-4 w-4" />
                )}
                Billing Category <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <button
                  type="button"
                  onClick={() => {
                    if (!loading) {
                      setFormData(prev => ({ 
                        ...prev, 
                        showCategoryDropdown: !prev.showCategoryDropdown,
                      }));
                    }
                  }}
                  className="block w-full pl-3 pr-10 py-2 text-left border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  disabled={loading}
                >
                  {formData.billingCategory ? (
                    <div className="flex items-center">
                      {(() => {
                        const category = billingCategories.find(
                          c => c.id === formData.billingCategory
                        );
                        if (!category) return <Tag className="h-4 w-4 mr-2" />;
                        
                        // Get the icon component based on the selected category
                        const IconComponent = 
                          category.icon === 'FileText'
                            ? FileText
                            : category.icon === 'Server'
                              ? Server
                              : category.icon === 'Globe'
                                ? Globe
                                : category.icon === 'Repeat'
                                  ? Repeat
                                  : category.icon === 'Key'
                                    ? Key
                                    : category.icon === 'Cloud'
                                      ? Cloud
                                      : category.icon === 'Users'
                                        ? Users
                                        : category.icon === 'Wrench'
                                          ? Wrench
                                          : category.icon === 'LifeBuoy'
                                            ? LifeBuoy
                                            : Tag;
                        
                        return <IconComponent className="h-4 w-4 mr-2" />;
                      })()}
                      {formData.billingCategory}
                    </div>
                  ) : (
                    <span className="text-gray-400">Select Category</span>
                  )}
                </button>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <svg
                    className="h-5 w-5 text-gray-400"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                
                {formData.showCategoryDropdown && (
                  <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                    <div className="py-1">
                      {billingCategories.map(category => {
                        // Get the icon component for this category
                        const IconComponent = 
                          category.icon === 'FileText'
                            ? FileText
                            : category.icon === 'Server'
                              ? Server
                              : category.icon === 'Globe'
                                ? Globe
                                : category.icon === 'Repeat'
                                  ? Repeat
                                  : category.icon === 'Key'
                                    ? Key
                                    : category.icon === 'Cloud'
                                      ? Cloud
                                      : category.icon === 'Users'
                                        ? Users
                                        : category.icon === 'Wrench'
                                          ? Wrench
                                          : category.icon === 'LifeBuoy'
                                            ? LifeBuoy
                                            : Tag;
                        
                        return (
                          <div
                            key={category.id}
                            className={`cursor-pointer select-none relative py-2 pl-3 pr-9 flex items-center hover:bg-blue-50 hover:text-blue-700 transition-colors duration-200 ${
                              formData.billingCategory === category.id
                                ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-300'
                                : ''
                            }`}
                            onClick={() => {
                              handleChange({ 
                                target: { name: 'billingCategory', value: category.id },
                              } as React.ChangeEvent<HTMLSelectElement>);
                              setFormData(prev => ({ ...prev, showCategoryDropdown: false }));
                            }}
                          >
                            <IconComponent className="h-4 w-4 mr-2" />
                            <span className="block truncate">{category.id}</span>
                            {formData.billingCategory === category.id && (
                              <span className="absolute inset-y-0 right-0 flex items-center pr-4">
                                <CheckSquare className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                              </span>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
              
              {/* Hidden select for form submission */}
              <input
                type="hidden"
                name="billingCategory"
                value={formData.billingCategory}
                required
              />
            </div>

            {/* Invoice Date */}
            <div>
              <label
                htmlFor="invoiceDate"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
              >
                <Calendar className="h-4 w-4" />
                Invoice Date <span className="text-red-500">*</span>
              </label>
                <input
                  type="date"
                  name="invoiceDate"
                  id="invoiceDate"
                  value={formData.invoiceDate}
                  onChange={enhancedHandleChange}
                className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  required
                  disabled={loading}
                />
            </div>

            {/* Due Date */}
            <div>
              <label
                htmlFor="dueDate"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
              >
                <Calendar className="h-4 w-4" />
                Due Date <span className="text-red-500">*</span>
              </label>
                <input
                  type="date"
                  name="dueDate"
                  id="dueDate"
                  value={formData.dueDate}
                  onChange={enhancedHandleChange}
                className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  required
                  disabled={loading}
                />
              </div>

            {/* Currency - Moving this before Amount */}
            <div>
              <label
                htmlFor="currency"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
              >
                <DollarSign className="h-4 w-4" />
                Currency <span className="text-red-500">*</span>
              </label>
              <select
                name="currency"
                id="currency"
                value={formData.currency}
                onChange={enhancedHandleChange}
                className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                required
                disabled={loading}
              >
                <option value="">Select Currency</option>
                {currencies.map(curr => (
                  <option key={curr} value={curr}>
                    {curr}
                  </option>
                ))}
              </select>
            </div>

            {/* Amount - Updated design to match other fields */}
            <div>
              <label
                htmlFor="amount"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
              >
                <DollarSign className="h-4 w-4" />
                Amount <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type="number"
                  name="amount"
                  id="amount"
                  value={formData.amount}
                  onChange={enhancedHandleChange}
                  step="0.01"
                  min="0"
                  className="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder={`0.00 ${formData.currency}`}
                  required
                  disabled={loading}
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">{formData.currency}</span>
                </div>
              </div>
            </div>

            {/* Tax */}
            <div>
              <label
                htmlFor="tax"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
              >
                <Percent className="h-4 w-4" />
                Tax (%)
              </label>
                <input
                  type="number"
                  name="tax"
                  id="tax"
                  value={formData.tax}
                  onChange={enhancedHandleChange}
                  step="0.01"
                  min="0"
                className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="0.00"
                  disabled={loading}
                />
            </div>

            {/* Total Amount - Updated to match amount field styling */}
            <div>
              <label
                htmlFor="totalAmount"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
              >
                <DollarSign className="h-4 w-4" />
                Total Amount
              </label>
              <div className="relative">
                 <input
                  type="text"
                  name="totalAmount"
                  id="totalAmount"
                  value={formData.amount ? Number(formData.totalAmount).toFixed(2) : ''}
                  readOnly
                  className="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-gray-100 dark:bg-gray-600 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none sm:text-sm"
                  placeholder="0.00"
                  disabled
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">{formData.currency}</span>
                </div>
              </div>
            </div>

            {/* Payment Method */}
            <div>
              <label
                htmlFor="paymentMethod"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
              >
                <CreditCard className="h-4 w-4" />
                Payment Method
              </label>
               <select
                name="paymentMethod"
                id="paymentMethod"
                value={formData.paymentMethod}
                onChange={enhancedHandleChange}
                className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                disabled={loading}
              >
                <option value="">Select Payment Method</option>
                 {paymentMethods.map(method => (
                  <option key={method} value={method}>
                    {method}
                  </option>
                ))}
              </select>
            </div>

            {/* Conditional Payment Details based on selected payment method */}
            {formData.paymentMethod && (
              <div>
                <label
                  htmlFor="paymentDetails"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  Payment Details
                </label>
                {formData.paymentMethod === 'Bank Transfer' && (
                  <div className="space-y-3">
                    <input
                      type="text"
                      name="accountTitle"
                      value={formData.accountTitle || ''}
                      onChange={enhancedHandleChange}
                      placeholder="Account Title"
                      className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      disabled={loading}
                    />
                    <input
                      type="text"
                      name="accountNumber"
                      value={formData.accountNumber || ''}
                      onChange={enhancedHandleChange}
                      placeholder="Account Number"
                      className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      disabled={loading}
                    />
                    <input
                      type="text"
                      name="bankName"
                      value={formData.bankName || ''}
                      onChange={enhancedHandleChange}
                      placeholder="Bank Name"
                      className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      disabled={loading}
                    />
                  </div>
                )}
                {formData.paymentMethod === 'Credit Card' && (
                  <div className="space-y-3">
                    <input
                      type="text"
                      name="cardNumber"
                      value={formData.cardNumber || ''}
                      onChange={enhancedHandleChange}
                      placeholder="Card Number"
                      className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      disabled={loading}
                    />
                    <div className="grid grid-cols-2 gap-3">
                      <input
                        type="text"
                        name="expiryDate"
                        value={formData.expiryDate || ''}
                        onChange={enhancedHandleChange}
                        placeholder="MM/YY"
                        className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        disabled={loading}
                      />
                      <input
                        type="text"
                        name="cvv"
                        value={formData.cvv || ''}
                        onChange={enhancedHandleChange}
                        placeholder="CVV"
                        className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        disabled={loading}
                      />
                    </div>
                  </div>
                )}
                {(formData.paymentMethod === 'JazzCash' ||
                  formData.paymentMethod === 'EasyPaisa') && (
                  <div className="space-y-3">
                    <input
                      type="text"
                      name="accountTitle"
                      value={formData.accountTitle || ''}
                      onChange={enhancedHandleChange}
                      placeholder="Account Title"
                      className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      disabled={loading}
                    />
                    <input
                      type="text"
                      name="mobileNumber"
                      value={formData.mobileNumber || ''}
                      onChange={enhancedHandleChange}
                      placeholder="Mobile Number"
                      className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      disabled={loading}
                    />
                  </div>
                )}
                {formData.paymentMethod === 'Check' && (
                  <div className="space-y-3">
                    <input
                      type="text"
                      name="checkNumber"
                      value={formData.checkNumber || ''}
                      onChange={enhancedHandleChange}
                      placeholder="Check Number"
                      className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      disabled={loading}
                    />
                    <input
                      type="text"
                      name="bankName"
                      value={formData.bankName || ''}
                      onChange={enhancedHandleChange}
                      placeholder="Bank Name"
                      className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      disabled={loading}
                    />
                  </div>
                )}
                {formData.paymentMethod === 'Cash' && (
                  <div className="space-y-3">
                    <input
                      type="text"
                      name="receivedBy"
                      value={formData.receivedBy || ''}
                      onChange={enhancedHandleChange}
                      placeholder="Received By"
                      className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      disabled={loading}
                    />
                    <input
                      type="text"
                      name="receiptNumber"
                      value={formData.receiptNumber || ''}
                      onChange={enhancedHandleChange}
                      placeholder="Receipt Number"
                      className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      disabled={loading}
                    />
                  </div>
                )}
                {formData.paymentMethod === 'Other' && (
                  <textarea
                    name="paymentNotes"
                    value={formData.paymentNotes || ''}
                    onChange={enhancedHandleChange}
                    placeholder="Specify payment details here..."
                    rows={3}
                    className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    disabled={loading}
                  ></textarea>
                )}
              </div>
            )}
          </div>

          {/* Recurring Billing Options */}
          <div className="mt-8 border-t border-gray-200 dark:border-gray-700 pt-6">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <ClipboardList className="h-5 w-5" />
              Recurring Billing
            </h4>
             <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
               <div className="flex items-center">
                 <input
                   id="isRecurring"
                   name="isRecurring"
                   type="checkbox"
                   checked={formData.isRecurring}
                   onChange={enhancedHandleChange}
                   className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                   disabled={loading}
                 />
                <label
                  htmlFor="isRecurring"
                  className="ml-2 block text-sm text-gray-900 dark:text-gray-300 flex items-center gap-2"
                >
                  <Clock className="h-4 w-4" />
                   Is this a recurring invoice?
                 </label>
               </div>

              {formData.isRecurring && (
                <>
                  <div>
                    <label
                      htmlFor="billingFrequency"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
                    >
                      <ClipboardList className="h-4 w-4" />
                      Frequency
                    </label>
                    <select
                      name="billingFrequency"
                      id="billingFrequency"
                      value={formData.billingFrequency}
                      onChange={enhancedHandleChange}
                      className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      required={formData.isRecurring}
                      disabled={loading}
                    >
                      {billingFrequencies.map(freq => (
                        <option key={freq} value={freq}>
                          {freq}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label
                      htmlFor="reminderDays"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
                    >
                      <Clock className="h-4 w-4" />
                      Reminder (Days Before Due)
                    </label>
                      <input
                        type="number"
                        name="reminderDays"
                        id="reminderDays"
                        value={formData.reminderDays}
                        onChange={enhancedHandleChange}
                        min="0"
                      className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        disabled={loading}
                      />
                  </div>
                </>
              )}
             </div>
           </div>

          {/* Links and Associations */}
          <div className="mt-8 border-t border-gray-200 dark:border-gray-700 pt-6">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Users className="h-5 w-5" />
              Associations
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Linked Assets - Simple Search Field with Plus Button */}
              <div>
                <label
                  htmlFor="linkedAssets"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
                >
                  <HardDrive className="h-4 w-4" />
                  Linked Assets
                </label>
                <div className="relative" ref={assetClickOutsideRef}>
                  <div className="flex">
                    <div className="relative flex-grow">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <HardDrive className="h-4 w-4" />
                      </div>
                      <input
                        type="text"
                        value={assetSearch}
                        onChange={e => {
                          setAssetSearch(e.target.value);
                          if (e.target.value.trim() !== '') {
                            setShowAssetDropdown(true);
                          }
                        }}
                        onClick={() => {
                          // Open dropdown on click, keeping any existing results
                          setShowAssetDropdown(true);
                          if (assetSearch.trim() === '' && filteredAssets.length === 0) {
                            // Load initial assets when clicking with empty search
                            setFilteredAssets(
                              assets
                                .filter(
                                  asset =>
                              !asset.disabled && 
                              asset.value !== 'None' && 
                              !asset.value.startsWith('category-')
                                )
                                .slice(0, 50)
                            );
                          }
                        }}
                        onKeyDown={e => {
                          // Add keyboard navigation
                          if (showAssetDropdown && filteredAssets.length > 0) {
                            if (e.key === 'ArrowDown') {
                              e.preventDefault();
                              setSelectedAssetIndex(prev => 
                                prev < filteredAssets.length - 1 ? prev + 1 : 0
                              );
                            } else if (e.key === 'ArrowUp') {
                              e.preventDefault();
                              setSelectedAssetIndex(prev => 
                                prev > 0 ? prev - 1 : filteredAssets.length - 1
                              );
                            } else if (e.key === 'Enter' && selectedAssetIndex >= 0) {
                              e.preventDefault();
                              handleAddAsset(filteredAssets[selectedAssetIndex]);
                              // Keep focus on input field
                              e.currentTarget.focus();
                            } else if (e.key === 'Escape') {
                              e.preventDefault();
                              setShowAssetDropdown(false);
                            } else if (e.key === 'Tab') {
                              // Close dropdown on tab
                              setShowAssetDropdown(false);
                            }
                          }
                        }}
                        placeholder="Search and select an asset..."
                        className="block w-full pl-9 pr-3 py-2 border border-gray-300 rounded-l-md shadow-sm bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        disabled={loading || assetsLoading}
                      />
                      {showAssetDropdown && filteredAssets.length > 0 && (
                        <div className="asset-dropdown-menu absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                          {filteredAssets.map((asset, index) => {
                            // Use the helper function to determine icon
                            const AssetIcon = getAssetIcon(asset.metadata?.assetType, asset.label);
                            
                            // Extract category from metadata or infer from name
                            let category = '';
                            if (asset.metadata?.category) {
                              category = asset.metadata.category;
                            } else {
                              // Try to infer from name
                              const parts = asset.label.split('-');
                              if (parts.length > 1) {
                                category = parts[0].trim();
                              }
                            }
                            
                            // Remove asset ID from displayed name - handle more formats
                            const displayName = asset.label.replace(/\s*\([A-Za-z]+-\d+\)/, '');
                            
                            const isSelected = selectedAssetIndex === index;
                            
                            return (
                              <div
                                key={asset.value}
                                className={`cursor-pointer select-none relative py-2 pl-3 pr-9 ${
                                  isSelected
                                    ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100'
                                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                                }`}
                                onClick={() => {
                                  handleAddAsset(asset);
                                  // Keep input focused after selection
                                  const inputField =
                                    assetClickOutsideRef.current?.querySelector('input');
                                  if (inputField) {
                                    inputField.focus();
                                  }
                                }}
                                onMouseEnter={() => {
                                  // Update the selected index on hover
                                  setSelectedAssetIndex(index);
                                }}
                              >
                                <div className="flex items-center">
                                  {/* Pre-rendering asset icon */}
                                  <div className="text-blue-500 w-5 h-5 mr-2 flex items-center justify-center">
                                    <AssetIcon className="h-4 w-4" />
                                  </div>
                                  <div>
                                    {category && (
                                      <span className="font-semibold mr-1">{category}:</span>
                                    )}
                                    <span className="font-normal">{displayName}</span>
                                  </div>
              </div>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        // Always show dropdown on search button click
                        setShowAssetDropdown(true);
                        setSelectedAssetIndex(-1);
                        
                        // Show all assets or filtered assets based on search text
                        if (assetSearch.trim() === '') {
                          // When empty, show up to 50 assets
                          setFilteredAssets(
                            assets
                              .filter(
                                asset =>
                            // Skip category headers, disabled items, and 'None' option
                            !asset.disabled && 
                            asset.value !== 'None' && 
                            !asset.value.startsWith('category-')
                              )
                              .slice(0, 50)
                          );
                        }
                        
                        // Focus the input field
                        const inputField = assetClickOutsideRef.current?.querySelector('input');
                        if (inputField) {
                          inputField.focus();
                        }
                      }}
                      className="inline-flex items-center justify-center px-3 py-2 border border-l-0 border-gray-300 text-sm font-medium rounded-r-md text-gray-700 bg-gray-50 hover:bg-gray-100 hover:text-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      disabled={loading || assetsLoading}
                    >
                      Search
                    </button>
                  </div>
                </div>
                {/* Display selected assets */}
                {ensureArray(formData.linkedAssets).length > 0 ? (
                  <div className="mt-2">
                    <div className="flex flex-wrap gap-2">
                      {ensureArray(formData.linkedAssets).map((asset, idx) => {
                        // Use the helper functions to determine icon and color
                        const IconComponent = getAssetIcon(asset.assetType, asset.name);
                        const iconColor = getAssetIconColor(asset.assetType, asset.name);
                        
                        return (
                          <div 
                            key={asset.id} 
                            className="flex items-center justify-between bg-white p-2 rounded border border-gray-200"
                          >
                            <div className="flex items-center gap-2">
                              <div className="text-lg">
                                <IconComponent className={`h-5 w-5 ${iconColor}`} />
                              </div>
                              <div>
                                <div className="font-medium">{asset.name}</div>
                                {asset.category && (
                                  <div className="text-xs text-gray-500">{asset.category}</div>
                                )}
                                {asset.department && (
                                  <div className="text-xs text-gray-500">{asset.department}</div>
                                )}
                              </div>
                            </div>
                            <button
                              type="button"
                              onClick={() => handleRemoveAsset(idx)}
                              className="text-red-500 hover:text-red-700 transition-colors p-1.5 rounded-full hover:bg-red-50"
                              aria-label="Remove asset"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ) : (
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Link assets required for this invoice
                  </p>
                )}
              </div>

              {/* Linked Software - Simple Search Field with Plus Button */}
              <div>
                <label
                  htmlFor="linkedSoftware"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  Linked Software
                </label>
                <div
                  className="relative"
                  ref={softwareClickOutsideRef}
                  onMouseLeave={() => {
                    // Use a longer timeout to prevent dropdown from closing too quickly
                    setTimeout(() => {
                      if (
                        !document.activeElement ||
                        (!document.activeElement.classList.contains('software-dropdown-menu') &&
                          !document.activeElement.closest('.software-dropdown-menu'))
                      ) {
                        setShowSoftwareDropdown(false);
                      }
                    }, 800); // Increased timeout for better interaction
                  }}
                >
                  <div className="flex">
                    <div className="relative flex-grow">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FileText className="h-4 w-4 text-gray-400" />
                      </div>
                      <input
                        type="text"
                        value={softwareSearch}
                        onChange={e => {
                          setSoftwareSearch(e.target.value);
                          if (e.target.value.trim() !== '') {
                            setShowSoftwareDropdown(true);
                          }
                        }}
                        onFocus={() => setShowSoftwareDropdown(true)}
                        onBlur={e => {
                          // Only hide dropdown if focus doesn't move to an item in the dropdown
                          setTimeout(() => {
                            if (
                              !document.activeElement ||
                              (!document.activeElement.classList.contains(
                                'software-dropdown-menu'
                              ) &&
                                !document.activeElement.closest('.software-dropdown-menu'))
                            ) {
                              setShowSoftwareDropdown(false);
                            }
                          }, 800); // Increased timeout for better interaction
                        }}
                        onKeyDown={e => {
                          // Add keyboard navigation
                          if (showSoftwareDropdown && filteredSoftware.length > 0) {
                            if (e.key === 'ArrowDown') {
                              e.preventDefault();
                              setSelectedSoftwareIndex(prev => 
                                prev < filteredSoftware.length - 1 ? prev + 1 : 0
                              );
                            } else if (e.key === 'ArrowUp') {
                              e.preventDefault();
                              setSelectedSoftwareIndex(prev => 
                                prev > 0 ? prev - 1 : filteredSoftware.length - 1
                              );
                            } else if (e.key === 'Enter' && selectedSoftwareIndex >= 0) {
                              e.preventDefault();
                              handleAddSoftware(filteredSoftware[selectedSoftwareIndex]);
                              // Keep focus on input field
                              e.currentTarget.focus();
                            } else if (e.key === 'Escape') {
                              e.preventDefault();
                              setShowSoftwareDropdown(false);
                            } else if (e.key === 'Tab') {
                              // Close dropdown on tab
                              setShowSoftwareDropdown(false);
                            }
                          }
                        }}
                        placeholder="Search and select software..."
                        className="block w-full pl-9 pr-3 py-2 border border-gray-300 rounded-l-md shadow-sm bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        disabled={loading || softwareLoading}
                      />
                      {showSoftwareDropdown && filteredSoftware.length > 0 && (
                        <div 
                          className="software-dropdown-menu absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm" 
                          tabIndex={-1}
                          onMouseDown={e => {
                            // Prevent dropdown from closing when clicking inside
                            e.preventDefault();
                          }}
                          onClick={e => {
                            // Prevent click from propagating to document
                            e.stopPropagation();
                          }}
                          onMouseEnter={() => {
                            // Clear any pending timeout when mouse enters the dropdown
                            if (window.softwareDropdownTimeout) {
                              clearTimeout(window.softwareDropdownTimeout);
                            }
                          }}
                        >
                          {filteredSoftware.map((sw, index) => {
                            // Determine icon based on software type
                            let SoftwareIcon = FileText;
                            if (sw.label.toLowerCase().includes('firewall')) SoftwareIcon = Shield;
                            else if (sw.label.toLowerCase().includes('helpdesk'))
                              SoftwareIcon = LifeBuoy;
                            else if (sw.label.toLowerCase().includes('kaspersky'))
                              SoftwareIcon = Shield;
                            else if (
                              sw.label.toLowerCase().includes('antivirus') ||
                              sw.label.toLowerCase().includes('security')
                            )
                              SoftwareIcon = Shield;
                            else if (sw.label.toLowerCase().includes('license')) SoftwareIcon = Key;
                            else if (
                              sw.label.toLowerCase().includes('cloud') ||
                              sw.label.toLowerCase().includes('saas')
                            )
                              SoftwareIcon = Cloud;
                            else if (
                              sw.label.toLowerCase().includes('windows') ||
                              sw.label.toLowerCase().includes('os')
                            )
                              SoftwareIcon = Monitor;
                            else if (
                              sw.label.toLowerCase().includes('office') ||
                              sw.label.toLowerCase().includes('productivity')
                            )
                              SoftwareIcon = File;
                            else if (sw.label.toLowerCase().includes('database'))
                              SoftwareIcon = Database;
                            
                            return (
                              <div
                                key={sw.value}
                                className={`cursor-pointer select-none relative py-2 pl-3 pr-9 ${
                                  selectedSoftwareIndex === index
                                    ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100'
                                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                                }`}
                                onClick={() => {
                                  handleAddSoftware(sw);
                                  // Keep input focused after selection
                                  const inputField =
                                    softwareClickOutsideRef.current?.querySelector('input');
                                  if (inputField) {
                                    inputField.focus();
                                  }
                                }}
                                onMouseEnter={() => {
                                  // Update the selected index on hover
                                  setSelectedSoftwareIndex(index);
                                }}
                              >
                                <div className="flex items-center">
                                  <SoftwareIcon className="h-4 w-4 mr-2 text-blue-500" />
                                  <span className="font-normal block truncate">{sw.label}</span>
              </div>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        // Always show dropdown on search button click
                        setShowSoftwareDropdown(true);
                        setSelectedSoftwareIndex(-1);
                        
                        // Show all software or filtered software based on search text
                        if (softwareSearch.trim() === '') {
                          // When empty, show up to 50 software items
                          setFilteredSoftware(
                            software
                              .filter(
                                sw =>
                            // Skip category headers, disabled items, and 'None' option
                            !sw.disabled && 
                            sw.value !== 'None' && 
                            !sw.value.startsWith('category-')
                              )
                              .slice(0, 50)
                          );
                        }
                        
                        // Focus the input field
                        const inputField = softwareClickOutsideRef.current?.querySelector('input');
                        if (inputField) {
                          inputField.focus();
                        }
                      }}
                      className="inline-flex items-center justify-center px-3 py-2 border border-l-0 border-gray-300 text-sm font-medium rounded-r-md text-gray-700 bg-gray-50 hover:bg-gray-100 hover:text-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      disabled={loading || softwareLoading}
                    >
                      Search
                    </button>
                  </div>
                </div>
                {/* Display selected software */}
                {ensureArray(formData.linkedSoftware).length > 0 ? (
                  <div className="mt-2">
                    <div className="flex flex-wrap gap-2">
                      {ensureArray(formData.linkedSoftware).map((softwareItem, index) => {
                        // Determine icon based on software name/category
                        let SoftwareIcon = FileText;
                        if (softwareItem.name) {
                          const name = softwareItem.name.toLowerCase();
                          if (name.includes('firewall')) SoftwareIcon = Shield;
                          else if (name.includes('helpdesk')) SoftwareIcon = LifeBuoy;
                          else if (name.includes('kaspersky')) SoftwareIcon = Shield;
                          else if (name.includes('antivirus') || name.includes('security'))
                            SoftwareIcon = Shield;
                          else if (name.includes('license')) SoftwareIcon = Key;
                          else if (name.includes('cloud') || name.includes('saas'))
                            SoftwareIcon = Cloud;
                          else if (name.includes('windows') || name.includes('os'))
                            SoftwareIcon = Monitor;
                          else if (name.includes('office') || name.includes('productivity'))
                            SoftwareIcon = File;
                          else if (name.includes('database')) SoftwareIcon = Database;
                        }
                        
                        return (
                          <div
                            key={index}
                            className="inline-flex items-center bg-blue-50 border border-blue-200 rounded px-2 py-1 text-xs text-blue-800"
                          >
                            <SoftwareIcon className="h-4 w-4 mr-1 text-blue-500" />
                            {softwareItem.category && (
                              <span className="font-semibold mr-1">{softwareItem.category}:</span>
                            )}
                            <span>{softwareItem.name}</span>
                            {softwareItem.department && (
                              <span className="ml-1 text-blue-600">
                                | {softwareItem.department}
                              </span>
                            )}
                            <button
                              type="button"
                              onClick={() => handleRemoveSoftware(index)}
                              className="ml-1 text-blue-500 hover:text-blue-700"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ) : (
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Link software licenses related to this invoice
                  </p>
                )}
              </div>

              {/* Assigned Users - Simple Search Field with Search Button */}
              <div>
                <label
                  htmlFor="assignedUsers"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
                >
                  <User className="h-4 w-4" />
                  Assigned Users
                </label>
                <div
                  className="relative"
                  ref={userClickOutsideRef}
                  onMouseLeave={() => {
                    // Use a longer timeout to prevent dropdown from closing too quickly
                    setTimeout(() => {
                      if (
                        !document.activeElement ||
                        (!document.activeElement.classList.contains('user-dropdown-menu') &&
                          !document.activeElement.closest('.user-dropdown-menu'))
                      ) {
                        setShowUserDropdown(false);
                      }
                    }, 800); // Increased timeout for better interaction
                  }}
                >
                  <div className="flex">
                    <div className="relative flex-grow">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="h-4 w-4 text-gray-400" />
                      </div>
                      <input
                        type="text"
                        value={userSearch}
                        onChange={e => {
                          setUserSearch(e.target.value);
                          if (e.target.value.trim() !== '') {
                            setShowUserDropdown(true);
                          }
                        }}
                        onFocus={() => setShowUserDropdown(true)}
                        onBlur={e => {
                          // Only hide dropdown if focus doesn't move to an item in the dropdown
                          setTimeout(() => {
                            if (
                              !document.activeElement ||
                              (!document.activeElement.classList.contains('user-dropdown-menu') &&
                                !document.activeElement.closest('.user-dropdown-menu'))
                            ) {
                              setShowUserDropdown(false);
                            }
                          }, 800); // Increased timeout for better interaction
                        }}
                        onKeyDown={e => {
                          // Add keyboard navigation
                          if (showUserDropdown && filteredUsers.length > 0) {
                            if (e.key === 'ArrowDown') {
                              e.preventDefault();
                              setSelectedUserIndex(prev => 
                                prev < filteredUsers.length - 1 ? prev + 1 : 0
                              );
                            } else if (e.key === 'ArrowUp') {
                              e.preventDefault();
                              setSelectedUserIndex(prev => 
                                prev > 0 ? prev - 1 : filteredUsers.length - 1
                              );
                            } else if (e.key === 'Enter' && selectedUserIndex >= 0) {
                              e.preventDefault();
                              handleAddUser(filteredUsers[selectedUserIndex]);
                              // Keep focus on input field
                              e.currentTarget.focus();
                            } else if (e.key === 'Escape') {
                              e.preventDefault();
                              setShowUserDropdown(false);
                            } else if (e.key === 'Tab') {
                              // Close dropdown on tab
                              setShowUserDropdown(false);
                            }
                          }
                        }}
                        placeholder="Search and select a user..."
                        className="block w-full pl-9 pr-3 py-2 border border-gray-300 rounded-l-md shadow-sm bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        disabled={loading || usersLoading}
                      />
                      {showUserDropdown && filteredUsers.length > 0 && (
                        <div 
                          className="user-dropdown-menu absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm" 
                          tabIndex={-1}
                          onMouseDown={e => {
                            // Prevent dropdown from closing when clicking inside
                            e.preventDefault();
                          }}
                          onClick={e => {
                            // Prevent click from propagating to document
                            e.stopPropagation();
                          }}
                          onMouseEnter={() => {
                            // Clear any pending timeout when mouse enters the dropdown
                            if (window.userDropdownTimeout) {
                              clearTimeout(window.userDropdownTimeout);
                            }
                          }}
                        >
                          {filteredUsers.map((user, index) => {
                            // Use Users icon for all users
                            const UserIcon = Users;
                            
                            return (
                              <div
                                key={user.value}
                                className={`cursor-pointer select-none relative py-2 pl-3 pr-9 ${
                                  selectedUserIndex === index
                                    ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100'
                                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                                }`}
                                onClick={() => {
                                  handleAddUser(user);
                                  // Keep input focused after selection
                                  const inputField =
                                    userClickOutsideRef.current?.querySelector('input');
                                  if (inputField) {
                                    inputField.focus();
                                  }
                                }}
                                onMouseEnter={() => {
                                  // Update the selected index on hover
                                  setSelectedUserIndex(index);
                                }}
                              >
                                <div className="flex items-center">
                                  <UserIcon className="h-4 w-4 mr-2 text-blue-500" />
                                  <span className="font-normal block truncate">{user.label}</span>
              </div>
            </div>
                            );
                          })}
          </div>
                      )}
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        // Always show dropdown on search button click
                        setShowUserDropdown(true);
                        setSelectedUserIndex(-1);
                        
                        // Show all users or filtered users based on search text
                        if (userSearch.trim() === '') {
                          // When empty, show up to 50 users
                          setFilteredUsers(users.slice(0, 50));
                        }
                        
                        // Focus the input field
                        const inputField = userClickOutsideRef.current?.querySelector('input');
                        if (inputField) {
                          inputField.focus();
                        }
                      }}
                      className="inline-flex items-center justify-center px-3 py-2 border border-l-0 border-gray-300 text-sm font-medium rounded-r-md text-gray-700 bg-gray-50 hover:bg-gray-100 hover:text-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      disabled={loading || usersLoading}
                    >
                      Search
                    </button>
                  </div>
                </div>
                {/* Display selected users */}
                <div className="mt-1">
                {ensureArray(formData.assignedUsers).length > 0 && (
                  <div className="mt-2">
                    <div className="flex flex-wrap gap-2">
                      {ensureArray(formData.assignedUsers).map((userItem, index) => {
                        // Use Users icon for all users
                        const UserIcon = User;
                        
                        return (
                            <div
                              key={index}
                              className="inline-flex items-center bg-blue-50 border border-blue-200 rounded px-2 py-1 text-xs text-blue-800"
                            >
                            <UserIcon className="h-4 w-4 mr-1 text-blue-500" />
                            <span>{userItem.name}</span>
                              {userItem.department && (
                                <span className="ml-1 text-blue-600">| {userItem.department}</span>
                              )}
                              {userItem.project && (
                                <span className="ml-1 text-blue-500">| {userItem.project}</span>
                              )}
                            <button
                              type="button"
                              onClick={() => handleRemoveUser(index)}
                              className="ml-1 text-blue-500 hover:text-blue-700"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
                  {ensureArray(formData.assignedUsers).length === 0 && (
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Assign users responsible for this invoice
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Notes and Attachments - Reordered */}
          <div className="mt-8 border-t border-gray-200 dark:border-gray-700 pt-6">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Attachments & Notes
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
              {/* File Upload - Professional Design with Browse button on left */}
              <div>
                <div className="sm:col-span-6 mt-4">
                  <div className="flex items-center mb-2">
                    <FileText className="h-5 w-5 text-gray-500 dark:text-gray-400 mr-2" />
                    <label
                      htmlFor="invoiceFile"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      Attach Invoice
                    </label>
                  </div>
                  <div className="flex flex-col">
                    {/* Modern drag & drop file upload area */}
                    <div
                      className={`relative border-2 border-dashed rounded-lg p-6 mt-1 
                      ${formData.invoiceFileUrl ? 'bg-blue-50 border-blue-300 dark:bg-blue-900/20 dark:border-blue-700' : 'border-gray-300 dark:border-gray-700 hover:border-blue-400 dark:hover:border-blue-600'}
                      transition-colors cursor-pointer`}
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <input 
                        type="file"
                        id="invoiceFile" 
                        name="invoiceFile" 
                        onChange={handleFileUpload} 
                        accept=".pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg"
                        className="sr-only"
                        ref={fileInputRef}
                        disabled={loading || isSubmitting}
                      />

                      <div className="text-center flex flex-col items-center">
                        {!formData.invoiceFileUrl ? (
                          // Empty state - no file attached
                          <>
                            <Upload className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-3" />
                            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              Drag and drop your file here, or{' '}
                              <span className="text-blue-500">browse</span>
                            </p>
                            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                              Supported formats: PDF, Word, Excel, PNG, JPG (Max: 10MB)
                            </p>
                          </>
                        ) : (
                          // File attached state
                          <>
                            <div className="flex items-center justify-center">
                              {formData.invoiceFileUrl.startsWith('blob:') ? (
                                // Loading state while uploading
                                <div className="flex items-center">
                                  <RefreshCw className="h-8 w-8 text-blue-500 animate-spin mr-3" />
                                  <div className="text-left">
                                    <p className="font-medium text-gray-700 dark:text-gray-300">
                                      Uploading file...
                                    </p>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                      {formData.invoiceFileUrl.split('/').pop() || 'invoice.pdf'}
                                    </p>
                                  </div>
                                </div>
                              ) : (
                                // Success state
                                <div className="flex items-center">
                                  <FileText className="h-8 w-8 text-blue-500 mr-3" />
                                  <div className="text-left">
                                    <div className="flex items-center">
                                      <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                                      <p className="font-medium text-gray-700 dark:text-gray-300">
                                        File attached successfully
                                      </p>
                                    </div>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                      {formData.invoiceFileUrl.split('/').pop() || 'invoice.pdf'}
                                    </p>
                                  </div>
                                </div>
                              )}
                            </div>

                            <div className="flex mt-4 space-x-3">
                              {formData.invoiceFileUrl &&
                                !formData.invoiceFileUrl.startsWith('blob:') && (
                                  <a
                                    href={`${window.location.hostname.includes('localhost') ? 'http://localhost:5000' : ''}/api/download/file/${(formData.invoiceFileUrl || '').split('/').pop() || ''}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="px-3 py-1.5 bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900/50 dark:text-blue-300 dark:hover:bg-blue-800/70 rounded-md flex items-center text-sm font-medium"
                                  >
                                    <Eye className="h-4 w-4 mr-1" />
                                    View File
                                  </a>
                                )}
                          <button
                            type="button"
                                className="px-3 py-1.5 bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-300 dark:hover:bg-red-800/50 rounded-md flex items-center text-sm font-medium"
                                onClick={e => {
                                  e.stopPropagation();
                                  setFormData(prev => ({ ...prev, invoiceFileUrl: '' }));
                                  setHasAttachedFile(false);
                                  setUploadError(null);
                                }}
                              >
                                <Trash2 className="h-4 w-4 mr-1" />
                                Remove
                          </button>
                            </div>
                          </>
                        )}
                      </div>
                    </div>

                    {/* Error message and retry button */}
                    {uploadError && (
                      <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <div className="flex items-center">
                          <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                          <p className="text-sm text-red-600 dark:text-red-400 flex-grow">
                            {uploadError}
                          </p>
                          <button
                            type="button"
                            onClick={e => {
                              e.stopPropagation();
                              retryFileUpload();
                            }}
                            className="ml-2 py-1.5 px-3 text-xs bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900/50 dark:text-red-300 dark:hover:bg-red-800/70 rounded-md flex items-center"
                          >
                            <RefreshCw className="h-3 w-3 mr-1" />
                            Retry
                          </button>
                  </div>
                    </div>
                  )}
                  </div>
                </div>
                </div>
              </div>

            {/* We don't need the separate hasAttachedFile info section anymore as it's integrated in the upload component */}

            {/* Notes - Now in a separate section with full width */}
            <div className="mt-6">
              <label
                htmlFor="notes"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2"
              >
                  <FileText className="h-4 w-4" />
                   Notes
                 </label>
                 <textarea
                   id="notes"
                   name="notes"
                   rows={4}
                   value={formData.notes || ''}
                onChange={e => {
                     console.log(`Notes field updated: "${e.target.value}"`);
                     enhancedHandleChange(e);
                   }}
                   onFocus={() => {
                     console.log(`Notes field value when focused: "${formData.notes || ''}"`);
                   }}
                   className="block w-full shadow-sm sm:text-sm focus:ring-blue-500 focus:border-blue-500 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                   placeholder="Add any relevant notes here..."
                   disabled={loading}
                 ></textarea>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 pt-5 border-t border-gray-200 dark:border-gray-700">
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleCancel}
                disabled={loading}
                className="bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {loading ? 'Saving...' : isEditMode ? 'Update Invoice' : 'Add Invoice'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BillingInvoiceForm; 
