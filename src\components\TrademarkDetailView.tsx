import React from 'react';
import { 
  X, 
  Shield, 
  Calendar, 
  Globe, 
  FileText, 
  DollarSign, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Star,
  Edit,
  Download,
  ExternalLink
} from 'lucide-react';

interface Trademark {
  id: string;
  trademarkName: string;
  description?: string;
  trademarkType: 'word_mark' | 'design_mark' | 'combined_mark' | 'service_mark';
  status: 'pending' | 'registered' | 'opposed' | 'abandoned' | 'expired' | 'renewed';
  registrationNumber?: string;
  applicationNumber?: string;
  applicationDate?: string;
  registrationDate?: string;
  renewalDate?: string;
  expiryDate?: string;
  jurisdiction: string;
  registryOffice?: string;
  trademarkClasses: string[];
  goodsAndServices?: string;
  trademarkImageUrl?: string;
  attorney?: string;
  registrationFee?: number;
  renewalFee?: number;
  currency?: string;
  notes?: string;
  isActive: boolean;
  isPrimary: boolean;
  priority: number;
  createdAt: string;
  updatedAt: string;
}

interface TrademarkDetailViewProps {
  trademark: Trademark;
  onClose: () => void;
  onEdit: () => void;
}

const TrademarkDetailView: React.FC<TrademarkDetailViewProps> = ({ 
  trademark, 
  onClose, 
  onEdit 
}) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'registered':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'opposed':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      case 'expired':
      case 'abandoned':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'renewed':
        return <CheckCircle className="h-5 w-5 text-blue-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'registered':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'opposed':
        return 'bg-orange-100 text-orange-800';
      case 'expired':
      case 'abandoned':
        return 'bg-red-100 text-red-800';
      case 'renewed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const isExpiringSoon = (expiryDate?: string) => {
    if (!expiryDate) return false;
    const today = new Date();
    const expiry = new Date(expiryDate);
    const timeDiff = expiry.getTime() - today.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
    return daysDiff <= 90 && daysDiff > 0;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount?: number, currency?: string) => {
    if (!amount || !currency) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const getTrademarkTypeLabel = (type: string) => {
    const types = {
      word_mark: 'Word Mark',
      design_mark: 'Design Mark',
      combined_mark: 'Combined Mark',
      service_mark: 'Service Mark'
    };
    return types[type as keyof typeof types] || type;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            {trademark.trademarkImageUrl ? (
              <img
                src={trademark.trademarkImageUrl}
                alt={trademark.trademarkName}
                className="h-12 w-12 object-contain bg-gray-50 rounded-lg p-1"
              />
            ) : (
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Shield className="h-6 w-6 text-blue-600" />
              </div>
            )}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                {trademark.trademarkName}
                {trademark.isPrimary && (
                  <Star className="h-5 w-5 text-yellow-500 fill-current" />
                )}
              </h2>
              <p className="text-gray-600">{trademark.description}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={onEdit}
              className="flex items-center gap-2 px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            >
              <Edit className="h-4 w-4" />
              Edit
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-8">
          {/* Status and Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Status</h3>
              <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(trademark.status)}`}>
                {getStatusIcon(trademark.status)}
                {trademark.status.replace('_', ' ').toUpperCase()}
              </div>
              {isExpiringSoon(trademark.expiryDate) && (
                <div className="mt-2 flex items-center gap-1 text-orange-600 text-sm">
                  <AlertTriangle className="h-4 w-4" />
                  Expiring Soon
                </div>
              )}
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Type</h3>
              <p className="text-lg font-semibold text-gray-900">
                {getTrademarkTypeLabel(trademark.trademarkType)}
              </p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-500 mb-2 flex items-center gap-1">
                <Globe className="h-4 w-4" />
                Jurisdiction
              </h3>
              <p className="text-lg font-semibold text-gray-900">{trademark.jurisdiction}</p>
              {trademark.registryOffice && (
                <p className="text-sm text-gray-600 mt-1">{trademark.registryOffice}</p>
              )}
            </div>
          </div>

          {/* Registration Details */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Registration Details
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {trademark.applicationNumber && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Application Number</h4>
                  <p className="text-gray-900 font-medium">{trademark.applicationNumber}</p>
                </div>
              )}
              
              {trademark.registrationNumber && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Registration Number</h4>
                  <p className="text-gray-900 font-medium">{trademark.registrationNumber}</p>
                </div>
              )}

              {trademark.applicationDate && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Application Date</h4>
                  <p className="text-gray-900">{formatDate(trademark.applicationDate)}</p>
                </div>
              )}

              {trademark.registrationDate && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Registration Date</h4>
                  <p className="text-gray-900">{formatDate(trademark.registrationDate)}</p>
                </div>
              )}

              {trademark.renewalDate && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Next Renewal</h4>
                  <p className="text-gray-900">{formatDate(trademark.renewalDate)}</p>
                </div>
              )}

              {trademark.expiryDate && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Expiry Date</h4>
                  <p className={`font-medium ${isExpiringSoon(trademark.expiryDate) ? 'text-orange-600' : 'text-gray-900'}`}>
                    {formatDate(trademark.expiryDate)}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Trademark Classes */}
          {trademark.trademarkClasses.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Trademark Classes</h3>
              <div className="flex flex-wrap gap-2">
                {trademark.trademarkClasses.map((className, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                  >
                    {className}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Goods and Services */}
          {trademark.goodsAndServices && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Goods and Services</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-gray-700 leading-relaxed">{trademark.goodsAndServices}</p>
              </div>
            </div>
          )}

          {/* Financial Information */}
          {(trademark.registrationFee || trademark.renewalFee) && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Financial Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {trademark.registrationFee && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Registration Fee</h4>
                    <p className="text-xl font-semibold text-gray-900">
                      {formatCurrency(trademark.registrationFee, trademark.currency)}
                    </p>
                  </div>
                )}
                
                {trademark.renewalFee && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Renewal Fee</h4>
                    <p className="text-xl font-semibold text-gray-900">
                      {formatCurrency(trademark.renewalFee, trademark.currency)}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Legal Information */}
          {trademark.attorney && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Legal Representation</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-500 mb-1">Attorney/Law Firm</h4>
                <p className="text-gray-900 font-medium">{trademark.attorney}</p>
              </div>
            </div>
          )}

          {/* Notes */}
          {trademark.notes && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Notes</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-gray-700 leading-relaxed">{trademark.notes}</p>
              </div>
            </div>
          )}

          {/* Metadata */}
          <div className="border-t border-gray-200 pt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Record Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
              <div>
                <h4 className="font-medium text-gray-500">Created</h4>
                <p className="text-gray-900">{formatDate(trademark.createdAt)}</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-500">Last Updated</h4>
                <p className="text-gray-900">{formatDate(trademark.updatedAt)}</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-500">Priority</h4>
                <p className="text-gray-900">{trademark.priority}</p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-200">
            <div className="flex items-center gap-4">
              {trademark.isPrimary && (
                <span className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                  <Star className="h-4 w-4 fill-current" />
                  Primary Trademark
                </span>
              )}
              {!trademark.isActive && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                  Inactive
                </span>
              )}
            </div>
            
            <div className="flex items-center gap-3">
              <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                <Download className="h-4 w-4" />
                Export
              </button>
              <button className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors">
                <ExternalLink className="h-4 w-4" />
                View in Registry
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrademarkDetailView;
