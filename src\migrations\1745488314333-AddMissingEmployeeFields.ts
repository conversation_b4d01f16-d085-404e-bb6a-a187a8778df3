import { MigrationInterface, QueryRunner } from "typeorm";

export class AddMissingEmployeeFields1745488314333 implements MigrationInterface {
    name = 'AddMissingEmployeeFields1745488314333'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`mileageAtIssuance\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`deviceEntries\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`accommodationProvidedByEmployer\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`accommodationType\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`accommodationAddress\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`workSchedule\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`shiftType\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`previousEmployeeId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`remoteWorkEligible\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`nextReviewDate\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`trainingRequirements\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`status\` varchar(255) NULL DEFAULT 'active'`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`healthInsuranceProvider\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`healthInsurancePolicyNumber\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`healthInsuranceExpiryDate\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`lifeInsuranceProvider\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`lifeInsurancePolicyNumber\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`lifeInsuranceExpiryDate\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`vaccinationRecords\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`medicalHistory\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`bloodGroup\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`allergies\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`chronicConditions\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`regularMedications\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`spouseName\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`spouseDateOfBirth\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`spouseOccupation\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`spouseEmployer\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`spouseContactNumber\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`spouseCNIC\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`children\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`dependents\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`documents\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`requiredDocuments\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`projectEntries\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`notes\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`specialInstructions\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`specialInstructions\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`notes\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`projectEntries\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`requiredDocuments\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`documents\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`dependents\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`children\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`spouseCNIC\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`spouseContactNumber\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`spouseEmployer\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`spouseOccupation\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`spouseDateOfBirth\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`spouseName\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`regularMedications\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`chronicConditions\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`allergies\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`bloodGroup\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`medicalHistory\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`vaccinationRecords\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`lifeInsuranceExpiryDate\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`lifeInsurancePolicyNumber\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`lifeInsuranceProvider\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`healthInsuranceExpiryDate\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`healthInsurancePolicyNumber\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`healthInsuranceProvider\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`status\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`trainingRequirements\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`nextReviewDate\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`remoteWorkEligible\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`previousEmployeeId\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`shiftType\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`workSchedule\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`accommodationAddress\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`accommodationType\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`accommodationProvidedByEmployer\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`deviceEntries\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`mileageAtIssuance\``);
    }

}
