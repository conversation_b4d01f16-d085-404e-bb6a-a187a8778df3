export enum ProjectStatus {
  PLANNING = 'planning',
  ACTIVE = 'active',
  ON_HOLD = 'on_hold',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum ProjectPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  IN_REVIEW = 'in_review',
  DONE = 'done',
  CANCELLED = 'cancelled'
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum TaskType {
  TASK = 'task',
  BUG = 'bug',
  FEATURE = 'feature',
  IMPROVEMENT = 'improvement',
  RESEARCH = 'research'
}

export enum ProjectRole {
  OWNER = 'owner',
  MANAGER = 'manager',
  LEAD = 'lead',
  DEVELOPER = 'developer',
  DESIGNER = 'designer',
  TESTER = 'tester',
  ANALYST = 'analyst',
  MEMBER = 'member',
  VIEWER = 'viewer'
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  project?: string;
  location?: string;
}

export interface Project {
  id: number;
  name: string;
  description?: string;
  status: ProjectStatus;
  priority: ProjectPriority;
  startDate?: string;
  endDate?: string;
  dueDate?: string;
  progress?: number;
  budget?: number;
  clientName?: string;
  department?: string;
  location?: string;
  tags?: string[];
  notes?: string;
  isActive: boolean;
  createdBy: User;
  createdById: string;
  manager?: User;
  managerId?: string;
  tasks?: Task[];
  members?: ProjectMember[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Task {
  id: number;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  type: TaskType;
  startDate?: string;
  dueDate?: string;
  completedDate?: string;
  progress: number;
  estimatedHours?: number;
  actualHours?: number;
  tags?: string[];
  notes?: string;
  isActive: boolean;
  project: Project;
  projectId: number;
  createdBy: User;
  createdById: string;
  assignedTo?: User;
  assignedToId?: string;
  parentTask?: Task;
  parentTaskId?: number;
  subtasks?: Task[];
  comments?: TaskComment[];
  attachments?: TaskAttachment[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectMember {
  id: number;
  role: ProjectRole;
  joinedDate?: string;
  leftDate?: string;
  isActive: boolean;
  notes?: string;
  project: Project;
  projectId: number;
  user: User;
  userId: string;
  addedBy: User;
  addedById: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TaskComment {
  id: number;
  content: string;
  isEdited: boolean;
  editedAt?: Date;
  isActive: boolean;
  task: Task;
  taskId: number;
  author: User;
  authorId: string;
  parentComment?: TaskComment;
  parentCommentId?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface TaskAttachment {
  id: number;
  originalName: string;
  storedName: string;
  filePath: string;
  mimeType: string;
  fileSize: number;
  description?: string;
  isActive: boolean;
  task: Task;
  taskId: number;
  uploadedBy: User;
  uploadedById: string;
  createdAt: Date;
  updatedAt: Date;
}

// Form interfaces for creating/updating
export interface CreateProjectRequest {
  name: string;
  description?: string;
  status?: ProjectStatus;
  priority?: ProjectPriority;
  startDate?: string;
  endDate?: string;
  dueDate?: string;
  budget?: number;
  clientName?: string;
  department?: string;
  location?: string;
  tags?: string[];
  notes?: string;
  managerId?: string;
}

export interface UpdateProjectRequest extends Partial<CreateProjectRequest> {
  progress?: number;
}

export interface CreateTaskRequest {
  title: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  type?: TaskType;
  startDate?: string;
  dueDate?: string;
  estimatedHours?: number;
  tags?: string[];
  notes?: string;
  projectId: number;
  assignedToId?: string;
  parentTaskId?: number;
}

export interface UpdateTaskRequest extends Partial<CreateTaskRequest> {
  progress?: number;
  actualHours?: number;
  completedDate?: string;
}

export interface CreateProjectMemberRequest {
  projectId: number;
  userId: string;
  role?: ProjectRole;
  joinedDate?: string;
  notes?: string;
}

export interface ProjectStats {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  totalTasks: number;
  completedTasks: number;
  overdueTasks: number;
  myTasks: number;
  myProjects: number;
}

export interface TaskStats {
  todo: number;
  inProgress: number;
  inReview: number;
  done: number;
  cancelled: number;
  overdue: number;
}

// Filter and search interfaces
export interface ProjectFilters {
  status?: ProjectStatus[];
  priority?: ProjectPriority[];
  department?: string[];
  managerId?: string[];
  search?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface TaskFilters {
  status?: TaskStatus[];
  priority?: TaskPriority[];
  type?: TaskType[];
  assignedToId?: string[];
  projectId?: number[];
  search?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}
