import React, { useState, useEffect, useMemo, useRef, useCallback, Fragment, useContext } from 'react';
import ReactDOM from 'react-dom';
import { unstable_batchedUpdates } from 'react-dom';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { 
  Search,
  Filter,
  Plus,
  MessageSquare,
  Clock,
  Tag,
  User as UserIcon,
  Calendar,
  ChevronRight,
  ChevronDown,
  BookOpen,
  AlertCircle,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  Timer,
  ArrowUpRight,
  BarChart2,
  TrendingUp,
  Users,
  ThumbsUp,
  Clock8,
  CheckCircle,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  LineChart,
  Calendar as CalendarIcon,
  Inbox,
  FileText,
  Layers,
  Settings,
  HelpCircle,
  ArrowLeft,
  Share2,
  UserPlus,
  X,
  Lock,
  Send,
  Briefcase,
  Trash,
  Upload,
  Paperclip,
  Image as ImageIcon,
  User,
  Unlock,
  Trash2,
  Refresh<PERSON>w,
  User2,
  Edit2,
  MoreHorizontal,
  Check,
  Building,
  Download,
  MoreVertical,
  Loader2,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Off,
  Forward,
  Smile
} from 'lucide-react';
import { TicketForm } from './TicketForm';
import { KnowledgeBase } from './KnowledgeBase';
import KnowledgeBasePage from './KnowledgeBasePage';
import { useAuth } from '../contexts/AuthContext';
import { Table } from './Table';
import { toast } from 'react-hot-toast';
import { isITAdmin } from '../utils/guards';
import { UserSelectDropdown } from './UserSelectDropdown';
import api from '../services/api';
import { User as CommonUser, UserRole, UserPermissions, TicketStatus, TicketPriority, Comment, Ticket, Attachment } from '../types/common';
import RichTextEditor from './RichTextEditor';
import { socketService } from '../services/socket';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import { ChatSection } from './ChatSection';
import { TicketDetailLockStatus } from './ticket/TicketDetailLockStatus';
import { TicketLockStatus } from './ticket/TicketLockStatus';
import { TicketListItem } from './ticket/TicketListItem';
import type { NotificationData } from '../services/socket';
import EmojiPicker, { EmojiClickData, Theme, Categories, EmojiStyle, SuggestionMode, Emoji } from 'emoji-picker-react';
import KnowledgeArticleEditor from './KnowledgeArticleEditor';

interface ServiceDeskProps {
  view: string;
  onViewChange: (view: string) => void;
}

interface FilePreview {
  id: string;
  file: File;
  preview: string;
  type: string;
  fileName: string;
}

interface AttachmentPreview {
  id: string;
  fileName: string;
  fileUrl?: string;
  fileType: string;
  preview?: string;
  type?: string;
}

interface FileUploadResponse {
  id: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
}

const DEFAULT_USER_ROLE = UserRole.EMPLOYEE;

// Define projects from the system
const PROJECTS = [
  'Eurobiz Corporations',
  'Guardian International',
  'Guardian Developers',
  'Grand Developers',
  'ARD Developers'
];

// Define which departments are associated with each project
const PROJECT_DEPARTMENTS: Record<string, string[]> = {
  'Eurobiz Corporations': ['IT', 'FINANCE', 'HR'],
  'Guardian International': ['IT', 'MARKETING', 'SALES'],
  'Guardian Developers': ['IT', 'OPERATIONS', 'PND'],
  'Grand Developers': ['IT', 'CSD', 'LAND', 'LEGAL'],
  'ARD Developers': ['IT', 'MANAGEMENT']
};

// Define which locations are associated with each project
const PROJECT_LOCATIONS: Record<string, string[]> = {
  'Eurobiz Corporations': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Kharian'
  ],
  'Guardian International': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Arifwala'
  ],
  'Guardian Developers': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Vehari'
  ],
  'Grand Developers': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Faisalabad',
    'Grand City Site Office Murree'
  ],
  'ARD Developers': [
    'ARD Head Office Lahore',
    'ARD Site Office RUDA'
  ]
};

const getUserRole = (role?: string | UserRole): UserRole => {
  if (!role) return UserRole.EMPLOYEE;
  return role as UserRole;
};

const getVisibleTickets = (allTickets: Ticket[], currentUser: CommonUser | null) => {
  if (!currentUser?.id) {
    console.log('No current user - returning empty array');
    return [];
  }

  const userRole = getUserRole(currentUser.role);
  console.log('Checking ticket visibility for user:', {
    name: currentUser.name,
    role: userRole,
    department: currentUser.department,
    totalTickets: allTickets.length
  });

  // IT Admin and IT Staff can see all tickets
  if (userRole === UserRole.IT_ADMIN || userRole === UserRole.IT_STAFF) {
    console.log('User is IT Admin/Staff - returning all tickets:', allTickets.length);
    return allTickets;
  }

  // Department Head can see their department's tickets
  if (userRole === UserRole.DEPT_HEAD && currentUser.department) {
    const deptTickets = allTickets.filter(ticket => 
      ticket.department === currentUser.department || 
      ticket.departmentChain?.includes(currentUser.department || '')
    );
    console.log('Department Head tickets:', {
      department: currentUser.department,
      ticketCount: deptTickets.length,
      tickets: deptTickets.map(t => t.ticketNumber)
    });
    return deptTickets;
  }

  // Regular employees can see their own tickets and tickets where they are in visibleTo
  const employeeTickets = allTickets.filter(ticket => {
    // Ensure we have valid objects before checking properties
    if (!ticket || !ticket.createdBy) return false;
    
    const isCreator = ticket.createdBy.id === currentUser.id;
    const isAssignedTo = ticket.assignedTo?.id === currentUser.id;
    
    // Safely check visibleTo array
    const visibleTo = ticket.visibleTo || [];
    const isVisibleToRole = visibleTo.includes(UserRole.EMPLOYEE) && 
                          ticket.createdBy.department === currentUser.department;
    
    return isCreator || isAssignedTo || isVisibleToRole;
  });

  console.log('Employee visible tickets:', {
    userId: currentUser.id,
    ticketCount: employeeTickets.length,
    tickets: employeeTickets.map(t => t.ticketNumber)
  });

  return employeeTickets;
};

interface TicketFormProps {
  userPermissions: {
    canCreateTicket: boolean;
    canEditTicket: boolean;
    canDeleteTicket: boolean;
    canAssignTicket: boolean;
    canViewTicket: boolean;
    department: string;
    isAdmin: boolean;
  };
  onClose: () => void;
  onSubmit: (ticket: Ticket) => void;
}

type StatusFilter = 'all' | TicketStatus;

const saveTickets = (tickets: Ticket[]) => {
  try {
    localStorage.setItem('tickets', JSON.stringify(tickets));
  } catch (error) {
    console.error('Error saving tickets:', error);
  }
};

const fixTicketVisibility = (ticket: Ticket): Ticket => {
  // Always include IT roles and EMPLOYEE role
  const visibleRoles = [
    String(UserRole.IT_ADMIN),
    String(UserRole.IT_STAFF),
    String(UserRole.EMPLOYEE)
  ];

  // Add department and department head if applicable
  if (ticket.department) {
    visibleRoles.push(String(UserRole.DEPT_HEAD));
    visibleRoles.push(ticket.department);
  }

  // Combine roles with existing visibility, ensuring no duplicates
  const visibleTo = Array.from(new Set([
    ...visibleRoles,
    ...(ticket.visibleTo || [])
  ]));

  console.log('Fixed ticket visibility:', {
    ticketNumber: ticket.ticketNumber,
    department: ticket.department,
    visibleTo
  });

  return {
    ...ticket,
    visibleTo
  };
};

const loadTickets = (): Ticket[] => {
  try {
    const savedTickets = localStorage.getItem('tickets');
    console.log('Raw saved tickets:', savedTickets);
    
    if (!savedTickets) {
      console.log('No tickets found in storage, returning empty array');
      return [];
    }

    const parsedTickets = JSON.parse(savedTickets);
    console.log('Parsed tickets:', parsedTickets);
    
    if (!Array.isArray(parsedTickets)) {
      console.error('Invalid ticket data format - not an array');
      return [];
    }

    // Less strict validation to handle missing or malformed fields
    const validTickets = parsedTickets.filter(ticket => {
      const isValid = ticket && 
        ticket.id && 
        ticket.title && 
        ticket.description && 
        ticket.status && 
        ticket.priority && 
        ticket.createdBy;

      if (!isValid) {
        console.error('Invalid ticket data structure:', ticket);
      }

      // Ensure required arrays exist
      if (!ticket.visibleTo) ticket.visibleTo = [];
      if (!ticket.departmentChain) ticket.departmentChain = [];
      if (!ticket.comments) ticket.comments = [];
      if (!ticket.attachments) ticket.attachments = [];

      return isValid;
    }).map(fixTicketVisibility);

    console.log('Loaded tickets:', {
      totalFound: parsedTickets.length,
      validTickets: validTickets.length,
      tickets: validTickets.map(t => ({
        number: t.ticketNumber,
        status: t.status,
        priority: t.priority,
        creator: t.createdBy.name,
        department: t.department,
        visibleTo: t.visibleTo
      }))
    });

    return validTickets;
  } catch (error) {
    console.error('Error loading tickets:', error);
    return [];
  }
};

const convertToCommonUser = (user: any | null): CommonUser | null => {
  if (!user) return null;
  
  // Ensure role is a valid UserRole enum value
  const role = typeof user.role === 'string' ? user.role as UserRole : UserRole.EMPLOYEE;
  
  return {
    id: user.id,
    name: user.name,
    email: user.email,
    role: role,
    department: user.department || 'GENERAL',
    permissions: {
      canCreateTickets: user.permissions?.canCreateTickets || false,
      canCreateTicketsForOthers: user.permissions?.canCreateTicketsForOthers || false,
      canEditTickets: user.permissions?.canEditTickets || false,
      canDeleteTickets: user.permissions?.canDeleteTickets || false,
      canCloseTickets: user.permissions?.canCloseTickets || false,
      canAssignTickets: user.permissions?.canAssignTickets || false,
      canEscalateTickets: user.permissions?.canEscalateTickets || false,
      canViewAllTickets: user.permissions?.canViewAllTickets || false,
      canLockTickets: user.permissions?.canLockTickets || false,
      canCreateEmployee: user.permissions?.canCreateEmployee || false,
      canEditEmployee: user.permissions?.canEditEmployee || false,
      canDeleteEmployee: user.permissions?.canDeleteEmployee || false,
      canViewEmployees: user.permissions?.canViewEmployees || false,
      canManageAttendance: user.permissions?.canManageAttendance || false,
      canManageLeave: user.permissions?.canManageLeave || false,
      canManagePayroll: user.permissions?.canManagePayroll || false,
      canManagePerformance: user.permissions?.canManagePerformance || false,
      canAddUsers: user.permissions?.canAddUsers || false,
      canEditUsers: user.permissions?.canEditUsers || false,
      canDeleteUsers: user.permissions?.canDeleteUsers || false,
      canViewReports: user.permissions?.canViewReports || false,
      canExportData: user.permissions?.canExportData || false,
      canImportData: user.permissions?.canImportData || false,
      canConfigureSystem: user.permissions?.canConfigureSystem || false,
      canManageRoles: user.permissions?.canManageRoles || false,
      canApproveRequests: user.permissions?.canApproveRequests || false,
      canViewAllDepartments: user.permissions?.canViewAllDepartments || false,
      canAccessAllModules: user.permissions?.canAccessAllModules || false,
      canViewDashboards: user.permissions?.canViewDashboards || false,
      canViewAllDashboards: user.permissions?.canViewAllDashboards || false,
      canCreateDashboards: user.permissions?.canCreateDashboards || false,
      canEditDashboards: user.permissions?.canEditDashboards || false,
      canDeleteDashboards: user.permissions?.canDeleteDashboards || false,
      canShareDashboards: user.permissions?.canShareDashboards || false,
      canExportDashboardData: user.permissions?.canExportDashboardData || false,
      canConfigureDashboardAlerts: user.permissions?.canConfigureDashboardAlerts || false,
      canManageDashboardUsers: user.permissions?.canManageDashboardUsers || false,
      department: user.department || 'GENERAL',
      isAdmin: role === UserRole.IT_ADMIN
    }
  };
};

interface TempComment extends Comment {
  id: string;
}

interface ChatSectionProps {
  ticketId: number;
  comments: Comment[];
  currentUser: {
    id: string;
    name: string;
    role: UserRole;
  } | null;
  onAddComment: (content: string, files: File[]) => Promise<void>;
  status: string;
  canAddComment: boolean;
  ticket: Ticket;
}

// Add this component before the ServiceDesk function
const TicketDetailView = ({ 
  ticket, 
  currentUser, 
  onClose, 
  onAddComment, 
  canAddComment,
  getStatusColor,
  getPriorityColor,
  renderActionButtons,
  handleDeleteTicket,
  handleStatusChange,
  handleLockTicket,
  handleUnlockTicket,
  handleResolveTicket,
  handleReferTicket,
  setShowResolveDialog,
  setShowReferDialog,
  showResolveDialog,
  showReferDialog,
  resolutionReason,
  setResolutionReason,
  resolutionNotes,
  setResolutionNotes,
  referToUser,
  setReferToUser,
  referNote,
  setReferNote,
  itStaffUsers
}: { 
  ticket: Ticket, 
  currentUser: any, 
  onClose: () => void, 
  onAddComment: (content: string, files: File[]) => Promise<void>,
  canAddComment: boolean,
  getStatusColor: (status: TicketStatus) => string,
  getPriorityColor: (priority: string) => string,
  renderActionButtons: (ticket: Ticket) => React.ReactNode,
  handleDeleteTicket: (ticketId: number) => Promise<void>,
  handleStatusChange: (ticketId: number, status: TicketStatus) => Promise<void>,
  handleLockTicket: (ticketId: number) => Promise<void>,
  handleUnlockTicket: (ticketId: number) => Promise<void>,
  handleResolveTicket: () => Promise<void>,
  handleReferTicket: () => Promise<void>,
  setShowResolveDialog: (show: boolean) => void,
  setShowReferDialog: (show: boolean) => void,
  showResolveDialog: boolean,
  showReferDialog: boolean,
  resolutionReason: string,
  setResolutionReason: (reason: string) => void,
  resolutionNotes: string,
  setResolutionNotes: (notes: string) => void,
  referToUser: any,
  setReferToUser: (user: any) => void,
  referNote: string,
  setReferNote: (note: string) => void,
  itStaffUsers: any[]
}) => {
  const isITAdmin = currentUser?.role === 'IT_ADMIN';
  
  // Log ticket comments for debugging
  useEffect(() => {
    console.log('Ticket comments in TicketDetailView:', ticket.comments);
  }, [ticket.comments]);

  return (
    <div className="p-6">
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-500">#{ticket.ticketNumber}</span>
              <h1 className="text-2xl font-bold text-gray-900">{ticket.title}</h1>
              <span className={`px-2.5 py-0.5 text-xs font-medium rounded-full ${getStatusColor(ticket.status)}`}>
                {ticket.status}
              </span>
            </div>
            <button
              onClick={onClose}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft className="h-5 w-5" />
              Back to Service Desk
            </button>
          </div>
          
          {/* Remove the department heading section */}
        </div>
        
        <div className="p-4 border-b border-gray-200 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-500 mb-1">Created by</p>
            <div className="flex items-center gap-2">
              <UserIcon className="h-4 w-4 text-gray-400" />
              <div className="flex items-center">
                <span className="font-medium">{ticket.createdBy.name}</span>
                <div className="flex items-center ml-2">
                  <span className="text-xs text-gray-500 mr-1">Dept:</span>
                  <Building className="h-3.5 w-3.5 text-gray-400 mr-1" />
                  <span className="px-2 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-700">{ticket.createdBy.department}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <p className="text-sm text-gray-500 mb-1">Created on</p>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-400" />
              <span>{new Date(ticket.createdAt).toLocaleString()}</span>
            </div>
          </div>
          
          <div>
            <p className="text-sm text-gray-500 mb-1">Priority</p>
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-gray-400" />
              <span className={`px-2.5 py-0.5 text-xs font-medium rounded-full ${getPriorityColor(ticket.priority)}`}>
                {ticket.priority}
              </span>
            </div>
          </div>
          
          <div>
            <p className="text-sm text-gray-500 mb-1">Assigned to</p>
            <div className="flex items-center gap-2">
              <UserIcon className="h-4 w-4 text-gray-400" />
              {ticket.assignedTo ? (
                <div className="flex flex-col">
                  <span className="font-medium">{ticket.assignedTo.name}</span>
                  {ticket.assignedTo.role && (
                    <span className="text-xs text-gray-500">
                      {ticket.assignedTo.role.replace('_', ' ')}
                    </span>
                  )}
                </div>
              ) : ticket.lockedBy && ticket.lockedBy.name ? (
                <div className="flex flex-col">
                  <div className="flex items-center">
                    <span className="font-medium">{ticket.lockedBy.name}</span>
                    <span className="ml-2 px-1.5 py-0.5 text-xs rounded-full bg-yellow-100 text-yellow-700">
                      Working
                    </span>
                  </div>
                  {ticket.lockedBy.role && (
                    <span className="text-xs text-gray-500">
                      {ticket.lockedBy.role.replace('_', ' ')}
                    </span>
                  )}
                </div>
              ) : (
                <span>Unassigned</span>
              )}
            </div>
          </div>
        </div>
        
        <div className="p-4 border-b border-gray-200">
          <h3 className="font-medium text-gray-900 mb-2">Description</h3>
          <p className="text-gray-700 whitespace-pre-wrap">{ticket.description}</p>
        </div>
        
        {/* Ticket Lock Status */}
        <div className="p-4 border-b border-gray-200">
          <TicketDetailLockStatus ticket={ticket} currentUserId={currentUser.id} />
        </div>
        
        {/* Add Attachments Section */}
        {ticket.attachments && ticket.attachments.filter(att => !att.commentId).length > 0 && (
          <div className="p-4 border-b border-gray-200">
            <h3 className="font-medium text-gray-900 mb-2">Attachments ({ticket.attachments.filter(att => !att.commentId).length})</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
              {ticket.attachments.filter(att => !att.commentId).map(attachment => (
                <a
                  key={attachment.id}
                  href={attachment.fileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center p-2 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  {attachment.fileType.startsWith('image/') ? (
                    <div className="w-12 h-12 mr-3 overflow-hidden rounded border border-gray-200">
                      <img 
                        src={attachment.fileUrl} 
                        alt={attachment.fileName}
                        className="w-full h-full object-cover" 
                      />
                    </div>
                  ) : (
                    <div className="w-12 h-12 mr-3 flex items-center justify-center bg-gray-100 rounded">
                      <FileText className="h-6 w-6 text-gray-500" />
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {attachment.fileName}
                    </p>
                    <p className="text-xs text-gray-500">
                      {new Date(attachment.uploadedAt).toLocaleString()}
                    </p>
                  </div>
                </a>
              ))}
            </div>
          </div>
        )}
        
        <div className="flex-1 overflow-y-auto p-4 border-b border-gray-200">
          <h3 className="font-medium text-gray-900 mb-4 flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-gray-500" />
            Chat
          </h3>
          <div className="min-h-[200px]">
            <SimpleChatSection 
              ticketId={ticket.id}
              comments={Array.isArray(ticket.comments) ? ticket.comments : []}
              currentUser={currentUser}
              onAddComment={onAddComment}
              canAddComment={canAddComment}
              status={ticket.status}
            />
          </div>
        </div>

        {/* Action Buttons Section - Only visible to IT Staff and IT Admin */}
        {(currentUser?.role === 'IT_STAFF' || currentUser?.role === 'IT_ADMIN') && (
          <div className="p-4">
            <p className="text-sm font-medium text-gray-700 mb-2">Actions</p>
            <div className="flex flex-wrap gap-2" onClick={(e) => e.stopPropagation()}>
              {renderActionButtons(ticket)}
            </div>
          </div>
        )}

        {/* Resolve Dialog */}
        {showResolveDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full">
              <h3 className="text-lg font-semibold mb-4">Resolve Ticket</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Resolution Type
                  </label>
                  <select
                    value={resolutionReason}
                    onChange={(e) => setResolutionReason(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md mb-2"
                  >
                    <option value="">Select Resolution Type</option>
                    <option value="Issue Fixed">Issue Fixed</option>
                    <option value="Access Granted">Access Granted</option>
                    <option value="Software Installation/Update">Software Installation/Update</option>
                    <option value="Hardware Repair/Replace">Hardware Repair/Replace</option>
                    <option value="Training Completed">Training Completed</option>
                    <option value="Documentation Provided">Documentation Provided</option>
                    <option value="No Response from User">No Response from User</option>
                    <option value="Duplicate Issue">Duplicate Issue</option>
                    <option value="Not Reproducible">Not Reproducible</option>
                    <option value="Other">Other (Specify Below)</option>
                  </select>
                  {resolutionReason === 'Other' && (
                    <input
                      type="text"
                      value={resolutionNotes}
                      onChange={(e) => setResolutionNotes(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="Specify resolution reason"
                    />
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Additional Notes
                  </label>
                  <textarea
                    value={resolutionNotes}
                    onChange={(e) => setResolutionNotes(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    rows={3}
                    placeholder="Enter any additional notes or details about the resolution"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <button
                    onClick={() => {
                      setShowResolveDialog(false);
                      setResolutionReason('');
                      setResolutionNotes('');
                    }}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log('Resolve button clicked:', { resolutionReason, resolutionNotes, showResolveDialog });
                      handleResolveTicket();
                    }}
                    disabled={!resolutionReason || (resolutionReason === 'Other' && !resolutionNotes)}
                    className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50"
                  >
                    Resolve
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Refer Dialog */}
        {showReferDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full">
              <h3 className="text-lg font-semibold mb-4">Reassign Ticket</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reassign To
                  </label>
                  <select
                    value={referToUser?.id || ''}
                    onChange={(e) => {
                      const selectedUser = itStaffUsers.find(user => user.id === e.target.value);
                      setReferToUser(selectedUser || null);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="">Select IT Staff</option>
                    {itStaffUsers.map(user => (
                      <option key={user.id} value={user.id}>
                        {user.name} ({user.role})
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Note
                  </label>
                  <textarea
                    value={referNote}
                    onChange={(e) => setReferNote(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    rows={3}
                    placeholder="Enter reassignment note"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <button
                    onClick={() => setShowReferDialog(false)}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleReferTicket}
                    disabled={!referToUser || !referNote}
                    className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 disabled:opacity-50"
                  >
                    Reassign
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Simple Chat Section without rich text editor
const SimpleChatSection: React.FC<{
  ticketId: number;
  comments: Comment[];
  currentUser: any;
  onAddComment: (content: string, files: File[]) => Promise<void>;
  canAddComment: boolean;
  status: string;
}> = ({ ticketId, comments, currentUser, onAddComment, canAddComment, status }) => {
  const commentContainerRef = useRef<HTMLDivElement>(null);
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const emojiPickerRef = useRef<HTMLDivElement>(null);
  const emojiButtonRef = useRef<HTMLButtonElement>(null);
  const [previewImage, setPreviewImage] = useState<{url: string, fileName: string} | null>(null);
  
  useEffect(() => {
    if (commentContainerRef.current) {
      commentContainerRef.current.scrollTop = commentContainerRef.current.scrollHeight;
    }
  }, [comments]);
  
  // Close emoji picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        showEmojiPicker &&
        emojiPickerRef.current &&
        emojiButtonRef.current &&
        !emojiPickerRef.current.contains(event.target as Node) &&
        !emojiButtonRef.current.contains(event.target as Node)
      ) {
        setShowEmojiPicker(false);
      }
    };

    // Add the event listener with capture to ensure it fires before other handlers
    document.addEventListener('mousedown', handleClickOutside, true);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
    };
  }, [showEmojiPicker]);

  // Log comments for debugging
  useEffect(() => {
    console.log('SimpleChatSection comments:', { 
      count: comments?.length || 0,
      isArray: Array.isArray(comments),
      comments 
    });
    
    // Log each comment to check if isSystemComment is set correctly
    if (Array.isArray(comments)) {
      comments.forEach((comment, index) => {
        console.log(`Comment ${index}:`, {
          id: comment?.id,
          content: comment?.content,
          isSystemComment: comment?.isSystemComment,
          type: comment?.type,
          createdBy: comment?.createdBy
        });
        
        // Check specifically for status change messages
        if (comment?.content && typeof comment.content === 'string' && 
            comment.content.includes('status changed')) {
          console.log('Found status change message:', comment.content);
        }
      });
    }
  }, [comments]);

  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }
    
    if ((!message.trim() && selectedFiles.length === 0) || isSubmitting || !canAddComment) {
      return;
    }

    try {
      setIsSubmitting(true);
      await onAddComment(message, selectedFiles);
      setMessage('');
      setSelectedFiles([]);
      setShowEmojiPicker(false);
    } catch (error) {
      console.error('Error submitting comment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFiles(Array.from(e.target.files));
    }
  };
  
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };
  
  const handleEmojiClick = (emojiData: EmojiClickData) => {
    // Insert the emoji at the current cursor position
    setMessage(prev => {
      // Create a special marker for this emoji to ensure it's rendered as an image
      const emojiMarker = `${emojiData.emoji}`;
      return prev + emojiMarker;
    });
  };
  
  // Helper function to get user initials
  const getUserInitials = (name: string) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };
  
  // Helper function to format timestamp
  const formatTimestamp = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    
    if (isToday) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();
    
    if (isYesterday) {
      return `Yesterday, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    }
    
    return date.toLocaleDateString([], { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Group comments by date
  const groupCommentsByDate = (comments: any[]) => {
    const groups: { [key: string]: any[] } = {};
    
    comments.forEach(comment => {
      const date = new Date(comment.createdAt);
      const dateKey = date.toDateString();
      
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      
      groups[dateKey].push(comment);
    });
    
    return Object.entries(groups).map(([date, comments]) => ({
      date,
      comments
    }));
  };

  // Helper function to replace emoji unicode with Emoji component
  const renderMessageWithEmojis = (content: string) => {
    if (!content) return null;
    
    try {
      // Use a simpler approach - directly replace emoji characters with the Emoji component
      const emojiRegex = /(\p{Emoji_Presentation}|\p{Emoji}\uFE0F)/gu;
      
      // If no emojis, return the content as is
      if (!content.match(emojiRegex)) {
        return content;
      }
      
      // Get all emoji matches
      const matches = Array.from(content.matchAll(emojiRegex));
      
      if (matches.length === 0) {
        return content;
      }
      
      // Create an array to hold the result
      const result: React.ReactNode[] = [];
      let lastIndex = 0;
      
      // Process each match
      matches.forEach((match, index) => {
        const emoji = match[0];
        const matchIndex = match.index as number;
        
        // Add text before the emoji
        if (matchIndex > lastIndex) {
          result.push(
            <span key={`text-${index}`}>
              {content.substring(lastIndex, matchIndex)}
            </span>
          );
        }
        
        // Convert emoji to hex code points
        const unified = Array.from(emoji)
          .map(char => char.codePointAt(0)?.toString(16))
          .filter(Boolean)
          .join('-');
        
        // Add the emoji component
        result.push(
          <span key={`emoji-${index}`} style={{ display: 'inline-block', verticalAlign: 'middle' }}>
            <Emoji 
              unified={unified}
              size={20}
              emojiStyle={EmojiStyle.APPLE}
            />
          </span>
        );
        
        lastIndex = matchIndex + emoji.length;
      });
      
      // Add any remaining text
      if (lastIndex < content.length) {
        result.push(
          <span key="text-end">
            {content.substring(lastIndex)}
          </span>
        );
      }
      
      return result;
    } catch (error) {
      console.error('Error rendering emojis:', error);
      return content;
    }
  };

  const renderComments = () => {
    // Check if comments is undefined, null, or empty array
    if (!Array.isArray(comments) || comments.length === 0) {
      return (
        <div className="text-center py-8">
          <div className="bg-gray-50 rounded-lg p-6 inline-block">
            <MessageSquare className="h-10 w-10 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500 font-medium">No messages yet</p>
            <p className="text-gray-400 text-sm mt-1">
              Messages and updates about this ticket will appear here
            </p>
          </div>
        </div>
      );
    }

    // Filter out invalid comments
    const validComments = comments.filter(comment => comment && typeof comment === 'object');
    
    // Special case: If there's only a system message about status change, make it more visible
    if (validComments.length === 1) {
      const comment = validComments[0];
      if ((comment.isSystemComment || comment.type === 'system') && 
          comment.content && comment.content.includes('status changed')) {
        return (
          <div className="flex justify-center my-6">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-800 rounded-lg px-6 py-4 text-sm shadow-md border-l-4 border-blue-500 max-w-md">
              <div className="flex items-center gap-2 mb-2">
                <div className="bg-blue-100 p-1.5 rounded-full">
                  <Clock className="h-5 w-5 text-blue-600" />
                </div>
                <span className="font-bold text-blue-700">Status Update</span>
              </div>
              <p className="font-medium ml-9">{comment.content}</p>
            </div>
          </div>
        );
      }
    }
    
    if (validComments.length === 0) {
      return (
        <div className="text-center py-8">
          <div className="bg-gray-50 rounded-lg p-6 inline-block">
            <MessageSquare className="h-10 w-10 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500 font-medium">No messages yet</p>
            <p className="text-gray-400 text-sm mt-1">
              Messages and updates about this ticket will appear here
            </p>
          </div>
        </div>
      );
    }
    
    // Group comments by date
    const groupedComments = groupCommentsByDate(validComments);

    return (
      <div className="flex flex-col space-y-6">
        {groupedComments.map((group, groupIndex) => (
          <div key={group.date} className="space-y-4">
            <div className="flex justify-center">
              <div className="bg-gray-100 text-gray-500 text-xs px-3 py-1 rounded-full">
                {new Date(group.date).toLocaleDateString([], { weekday: 'long', month: 'long', day: 'numeric' })}
              </div>
            </div>
            
            {group.comments.map((comment: any, index: number) => {
              if (!comment || !comment.createdBy) {
                console.log('Invalid comment:', comment);
                return null;
              }
              
              const isCurrentUser = currentUser && comment.createdBy.id === currentUser.id;
              const isSystemComment = comment.isSystemComment || comment.type === 'system';
              
              console.log('Rendering comment:', {
                id: comment.id,
                content: comment.content,
                isSystemComment,
                type: comment.type
              });
              
              if (isSystemComment) {
                return (
                  <div key={`${comment.id || index}-${index}`} className="flex justify-center my-3">
                    <div className="bg-blue-50 text-blue-700 rounded-full px-4 py-2 text-sm shadow-sm border border-blue-200 font-medium">
                      {comment.content}
                    </div>
                  </div>
                );
              }
              
              return (
                <div 
                  key={`${comment.id || index}-${index}`}
                  className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
                >
                  {!isCurrentUser && (
                    <div className="flex-shrink-0 mr-2">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-400 to-indigo-500 flex items-center justify-center text-white font-medium shadow-sm">
                        {getUserInitials(comment.createdBy.name)}
                      </div>
                    </div>
                  )}
                  
                  <div 
                    className={`max-w-[75%] ${
                      isCurrentUser 
                        ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white' 
                        : 'bg-white border border-gray-200 text-gray-800'
                    } rounded-2xl p-4 shadow-sm`}
                  >
                    <div className="flex items-center justify-between gap-2 mb-1">
                      <span className="font-medium text-sm">
                        {comment.createdBy.name || 'Unknown User'}
                      </span>
                      <div className={`text-xs ${isCurrentUser ? 'text-blue-100' : 'text-gray-400'}`}>
                        {formatTimestamp(comment.createdAt)}
                      </div>
                    </div>
                    
                    <div className={`prose prose-sm max-w-none ${isCurrentUser ? 'prose-invert' : ''}`}>
                      {(comment.content || '').split('\n').map((line: string, i: number) => (
                        <p key={i} className="mb-1 last:mb-0">
                          {renderMessageWithEmojis(line)}
                        </p>
                      ))}
                    </div>
                    
                    {comment.attachments && comment.attachments.length > 0 && (
                      <div className="mt-3 space-y-2">
                        {comment.attachments.map((attachment: any, attachIndex: number) => {
                          if (!attachment) return null;
                          const isImage = attachment.fileType?.startsWith('image/');
                          const fileUrl = attachment.fileUrl?.startsWith('http') 
                            ? attachment.fileUrl 
                            : `${window.location.origin}${attachment.fileUrl}`;

                          return (
                            <div key={`${attachment.id}-${attachIndex}`} className="relative">
                              {isImage ? (
                                <div className="bg-black rounded-lg overflow-hidden cursor-pointer" onClick={() => handleImageClick(fileUrl, attachment.fileName)}>
                                  <img
                                    src={fileUrl}
                                    alt={attachment.fileName}
                                    className="w-full h-auto max-h-[200px] object-contain hover:opacity-90 transition-opacity"
                                  />
                                  <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-sm p-2 truncate">
                                    {attachment.fileName}
                                  </div>
                                </div>
                              ) : (
                                <a 
                                  href={fileUrl}
                                  download={attachment.fileName}
                                  className={`flex items-center gap-2 p-2 rounded-lg ${
                                    isCurrentUser ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-800'
                                  } hover:opacity-90 transition-opacity`}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  <FileText className="w-5 h-5 opacity-70" />
                                  <span className="text-sm truncate flex-1">{attachment.fileName}</span>
                                  <Download className="w-4 h-4 opacity-70" />
                                </a>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                  
                  {isCurrentUser && (
                    <div className="flex-shrink-0 ml-2">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-medium shadow-sm">
                        {getUserInitials(currentUser.name)}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ))}
      </div>
    );
  };

  // Function to handle image click for preview
  const handleImageClick = (url: string, fileName: string) => {
    setPreviewImage({ url, fileName });
  };
  
  // Function to close image preview
  const closeImagePreview = () => {
    setPreviewImage(null);
  };

  // Custom emoji picker configuration
  const customEmojiPickerConfig = {
    theme: Theme.LIGHT,
    emojiStyle: EmojiStyle.APPLE, // Changed from NATIVE to APPLE for modern emoji style
    categories: [
      {
        name: "Frequently Used",
        category: Categories.SUGGESTED
      },
      {
        name: "Smileys & People",
        category: Categories.SMILEYS_PEOPLE
      },
      {
        name: "Animals & Nature",
        category: Categories.ANIMALS_NATURE
      },
      {
        name: "Food & Drink",
        category: Categories.FOOD_DRINK
      },
      {
        name: "Activities",
        category: Categories.ACTIVITIES
      },
      {
        name: "Travel & Places",
        category: Categories.TRAVEL_PLACES
      },
      {
        name: "Objects",
        category: Categories.OBJECTS
      },
      {
        name: "Symbols",
        category: Categories.SYMBOLS
      },
      {
        name: "Flags",
        category: Categories.FLAGS
      }
    ]
  };
  
  // Custom CSS for emoji picker and emoji display
  useEffect(() => {
    // Add custom CSS to match the emoji picker in the screenshot and ensure Apple emoji style
    const style = document.createElement('style');
    style.textContent = `
      @import url('https://fonts.googleapis.com/css2?family=Noto+Color+Emoji&display=swap');
      
      .EmojiPickerReact {
        --epr-bg-color: #ffffff;
        --epr-category-label-bg-color: #ffffff;
        --epr-text-color: #333333;
        --epr-hover-bg-color: #f1f1f1;
        --epr-search-input-bg-color: #f5f5f5;
        --epr-search-input-border-color: #e0e0e0;
        --epr-category-label-text-color: #666666;
        --epr-emoji-size: 28px;
        border: 1px solid #e0e0e0 !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
        border-radius: 12px !important;
        position: relative !important;
        z-index: 1000 !important;
      }
      
      /* Ensure the emoji picker container is positioned correctly */
      .emoji-picker-container {
        position: fixed !important;
        bottom: 80px !important;
        right: 24px !important;
        z-index: 1000 !important;
      }
      
      .EmojiPickerReact .epr-category-nav {
        padding: 8px !important;
      }
      
      .EmojiPickerReact .epr-emoji-category-label {
        font-weight: 600 !important;
        font-size: 14px !important;
        padding: 8px 12px !important;
      }
      
      .EmojiPickerReact .epr-search {
        margin: 8px !important;
      }
      
      .EmojiPickerReact .epr-emoji-category-content {
        margin: 8px !important;
      }
      
      .EmojiPickerReact .epr-body::-webkit-scrollbar {
        width: 8px !important;
      }
      
      .EmojiPickerReact .epr-body::-webkit-scrollbar-thumb {
        background-color: #d1d1d1 !important;
        border-radius: 4px !important;
      }
      
      .EmojiPickerReact .epr-emoji {
        transition: transform 0.1s ease-in-out;
      }
      
      .EmojiPickerReact .epr-emoji:hover {
        transform: scale(1.2);
      }
      
      .EmojiPickerReact .epr-search-container input {
        border-radius: 20px !important;
        padding: 8px 12px !important;
      }
      
      /* Apply Apple emoji font to all emojis in the chat */
      .emoji-wrapper {
        font-family: "Apple Color Emoji", "Noto Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", sans-serif !important;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        vertical-align: middle;
        line-height: 1;
      }
      
      /* Ensure all emojis in the chat use Apple style */
      .prose p {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Noto Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
      }
      
      /* Apply Apple emoji font to the textarea input */
      textarea, .emoji-input {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Noto Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol" !important;
        font-size: 16px !important;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <div className="flex flex-col h-full bg-gray-50">
      <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDYwIDYwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmOWZhZmIiIGZpbGwtb3BhY2l0eT0iMC40Ij48cGF0aCBkPSJNMzYgMzRjMC0yLjIgMS44LTQgNC00czQgMS44IDQgNC0xLjggNC00IDQtNC0xLjgtNC00bTAgLTEyYzAtMi4yIDEuOC00IDQtNHM0IDEuOCA0IDQtMS44IDQtNCA0LTQtMS44LTQtNG0tMTIgMGMwLTIuMiAxLjgtNCA0LTRzNCAxLjggNCA0LTEuOCA0LTQgNC00LTEuOC00LTRtLTEyIDBjMC0yLjIgMS44LTQgNC00czQgMS44IDQgNC0xLjggNC00IDQtNC0xLjgtNC00bTEyIDBjMC0yLjIgMS44LTQgNC00czQgMS44IDQgNC0xLjggNC00IDQtNC0xLjgtNC00Ii8+PC9nPjwvZz48L3N2Zz4=')]" 
        ref={commentContainerRef}
      >
        {renderComments()}
      </div>
      
      {/* Image Preview Modal */}
      {previewImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4" onClick={closeImagePreview}>
          <div className="relative max-w-4xl max-h-[90vh] w-full">
            <button 
              className="absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-70 transition-colors"
              onClick={closeImagePreview}
            >
              <X className="h-6 w-6" />
            </button>
            <div className="bg-white p-2 rounded-lg">
              <div className="text-center text-gray-700 font-medium py-2">{previewImage.fileName}</div>
              <img 
                src={previewImage.url} 
                alt={previewImage.fileName} 
                className="max-h-[70vh] max-w-full mx-auto object-contain"
              />
            </div>
          </div>
        </div>
      )}
      
      {status === 'RESOLVED' ? (
        <div className="border-t bg-white p-4 text-center text-gray-500">
          This ticket is resolved. Comments are disabled.
        </div>
      ) : (
        <form onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log('Chat form submitted');
          handleSubmit(e);
        }} className="border-t bg-white p-4 relative shadow-md">
          <div className="flex flex-col gap-3">
            <div className="flex items-start gap-3">
              <div className="flex-1 relative">
                <textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder={
                    !canAddComment
                      ? "You cannot add comments at this time."
                      : "Type a message... (Press Enter to send)"
                  }
                  className="w-full border border-gray-300 rounded-lg p-4 pr-16 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none shadow-sm hover:border-gray-400 transition-colors emoji-input"
                  rows={3}
                  disabled={!canAddComment || isSubmitting}
                />
                
                <div className="absolute right-3 bottom-3 flex items-center gap-2 bg-white bg-opacity-80 backdrop-blur-sm rounded-full px-1 py-0.5 shadow-sm">
                  <button
                    type="button"
                    ref={emojiButtonRef}
                    onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                    className={`p-2 rounded-full ${showEmojiPicker ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'} hover:bg-gray-200 transition-colors`}
                    disabled={!canAddComment || isSubmitting}
                    title="Add emoji"
                  >
                    <Smile className="h-5 w-5" />
                  </button>
                  
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    className="p-2 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors"
                    disabled={!canAddComment || isSubmitting}
                    title="Attach file"
                  >
                    <Paperclip className="h-5 w-5" />
                  </button>
                </div>
                
                {showEmojiPicker && ReactDOM.createPortal(
                  <div 
                    className="fixed bottom-24 right-24 z-[9999] shadow-xl rounded-lg overflow-hidden"
                    ref={emojiPickerRef}
                  >
                    <EmojiPicker 
                      onEmojiClick={handleEmojiClick} 
                      width={350}
                      height={450}
                      searchPlaceholder="Search emoji..."
                      previewConfig={{ showPreview: false }}
                      theme={customEmojiPickerConfig.theme}
                      emojiStyle={customEmojiPickerConfig.emojiStyle}
                      categories={customEmojiPickerConfig.categories}
                      lazyLoadEmojis={true}
                      skinTonesDisabled={false}
                      suggestedEmojisMode={SuggestionMode.RECENT}
                      searchDisabled={false}
                      autoFocusSearch={false}
                      className="custom-emoji-picker"
                    />
                  </div>,
                  document.body
                )}
              </div>
              
              <div className="flex items-center h-full">
                <button
                  type="submit"
                  className="p-3 rounded-full bg-blue-500 text-white hover:bg-blue-600 transition-colors shadow-md disabled:opacity-50 disabled:cursor-not-allowed self-center"
                  disabled={(!message.trim() && selectedFiles.length === 0) || isSubmitting || !canAddComment}
                  title="Send message"
                >
                  <Send className="h-6 w-6" />
                </button>
              </div>
            </div>
            
            {selectedFiles.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {selectedFiles.map((file, index) => (
                  <div key={index} className="bg-gray-100 rounded-full px-3 py-1 text-sm flex items-center gap-1 shadow-sm">
                    <FileText className="h-3.5 w-3.5 text-blue-500" />
                    <span className="truncate max-w-[150px]">{file.name}</span>
                    <button
                      type="button"
                      onClick={() => setSelectedFiles(files => files.filter((_, i) => i !== index))}
                      className="text-gray-500 hover:text-gray-700 ml-1"
                    >
                      <X className="h-3.5 w-3.5" />
                    </button>
                  </div>
                ))}
              </div>
            )}
            
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              className="hidden"
              multiple
            />
          </div>
        </form>
      )}
    </div>
  );
};

export function ServiceDesk({ view, onViewChange }: ServiceDeskProps) {
  const { user, hasPermission } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  
  // Helper functions
  const uploadFiles = async (files: File[], ticketId: number): Promise<Attachment[]> => {
    const formData = new FormData();
    files.forEach(file => formData.append('files', file));
    
    const token = localStorage.getItem('authToken');
    const response = await fetch(`/api/tickets/${ticketId}/attachments`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData,
    });
    
    if (!response.ok) {
      throw new Error('Failed to upload files');
    }
    
    return response.json();
  };
  
  // Simplify the click handler to directly change the view without debounce
  const handleCreateTicketClick = () => {
    console.log('Create ticket button clicked, changing view to create-ticket');
    // Update view state first
    onViewChange('create-ticket');
    // Then navigate to make URL match the state
    navigate('/service-desk/create-ticket');
  };
  
  // Simplify the back button handler as well
  const handleBackToServiceDesk = () => {
    console.log('Back button clicked, changing view to service-desk');
    // Update view state first
    onViewChange('tickets');
    // Then navigate to make URL match the state
    navigate('/service-desk/tickets');
  };
  
  // Add effect to sync URL with view state
  useEffect(() => {
    const path = location.pathname;
    console.log('Current path in ServiceDesk:', path);
    
    // Skip routing if we're viewing an article - let App.tsx handle it
    if (path.includes('/service-desk/knowledge/articles/') || 
        path.includes('/service-desk/knowledge/edit/')) {
      console.log('Article or edit path detected, skipping ServiceDesk routing');
      return;
    }
    
    // Check if the URL contains service-desk paths - ORDER MATTERS (most specific first)
    if (path.includes('/service-desk/knowledge/create') && view !== 'knowledge-create') {
      console.log('URL is knowledge-create path, updating view state');
      onViewChange('knowledge-create');
    }
    else if (path === '/service-desk/knowledge' && view !== 'knowledge') {
      console.log('URL is knowledge path, updating view state');
      onViewChange('knowledge');
    }
    else if (path.includes('/service-desk/create-ticket') && view !== 'create-ticket') {
      console.log('URL contains create-ticket, updating view state');
      onViewChange('create-ticket');
    }
    else if (path.includes('/service-desk/tickets') && view !== 'tickets') {
      console.log('URL is tickets path, updating view state');
      onViewChange('tickets');
    }
    else if (path === '/service-desk' && view !== 'service-desk-home') {
      console.log('URL is service-desk root, updating view state');
      onViewChange('service-desk-home');
    }
    // If we're on one of the old /tickets/* routes, redirect to the new /service-desk/* routes
    else if (path.includes('/tickets/create') && view !== 'create-ticket') {
      console.log('URL contains old tickets/create path, redirecting');
      navigate('/service-desk/create-ticket', { replace: true });
      onViewChange('create-ticket');
    }
    else if (path === '/tickets' && view !== 'tickets') {
      console.log('URL is old tickets path, redirecting');
      navigate('/service-desk/tickets', { replace: true });
      onViewChange('tickets');
    }
  }, [location.pathname, view, onViewChange, navigate]);
  
  // Extract ticket ID from URL path
  const ticketIdFromPath = location.pathname.match(/\/tickets\/(\d+)/)?.[1];
  
  // Helper function to get initials from name
  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };
  
  const defaultUser = {
    id: 'USR001',
    name: 'Default Admin',
    email: '<EMAIL>',
    role: UserRole.IT_ADMIN,
    department: 'IT',
    permissions: {
      canCreateTickets: true,
      canCreateTicketsForOthers: true,
      canEditTickets: true,
      canDeleteTickets: true,
      canCloseTickets: true,
      canAssignTickets: true,
      canEscalateTickets: true,
      canViewAllTickets: true,
      canLockTickets: true,
      canCreateEmployee: true,
      canEditEmployee: true,
      canDeleteEmployee: true,
      canViewEmployees: true,
      canManageAttendance: true,
      canManageLeave: true,
      canManagePayroll: true,
      canManagePerformance: true,
      department: user?.department || 'General',
      isAdmin: user?.role === UserRole.IT_ADMIN
    }
  };

  // Only use defaultUser if we're in development mode
  const currentUser = process.env.NODE_ENV === 'development' ? (user || defaultUser) : user;
  
  // If no user is authenticated in production, redirect to login
  useEffect(() => {
    if (process.env.NODE_ENV === 'production' && !user) {
      window.location.href = '/login';
      return;
    }
  }, [user]);

  // Early return if no user in production
  if (process.env.NODE_ENV === 'production' && !currentUser) {
    return null;
  }

  // Ensure currentUser is available for the rest of the component
  if (!currentUser) {
    return null;
  }

  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<'all' | TicketStatus>('all');
  const [selectedPriority, setSelectedPriority] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all');
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [selectedLocation, setSelectedLocation] = useState<string>('all');
  const [newComment, setNewComment] = useState('');
  const [showReferDialog, setShowReferDialog] = useState(false);
  const [referToUser, setReferToUser] = useState<any | null>(null);
  const [referNote, setReferNote] = useState('');
  const [showResolveDialog, setShowResolveDialog] = useState(false);
  const [resolutionReason, setResolutionReason] = useState('');
  const [resolutionNotes, setResolutionNotes] = useState('');
  
  // Debug effect to track resolve dialog state changes
  useEffect(() => {
    console.log('Resolve dialog state changed:', {
      showResolveDialog,
      resolutionReason,
      selectedTicketId: selectedTicket?.id,
      selectedTicketStatus: selectedTicket?.status
    });
  }, [showResolveDialog, resolutionReason, selectedTicket?.id]);
  
  // Debug effect to track resolution state changes
  useEffect(() => {
    if (resolutionReason) {
      console.log('Resolution reason changed:', {
        resolutionReason,
        resolutionNotes,
        showResolveDialog,
        selectedTicketId: selectedTicket?.id
      });
    }
  }, [resolutionReason, resolutionNotes]);
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [filteredTickets, setFilteredTickets] = useState<Ticket[]>([]);
  const [deleteConfirmMap, setDeleteConfirmMap] = useState<Record<number, boolean>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRequester, setSelectedRequester] = useState<any | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [itStaffUsers, setITStaffUsers] = useState<any[]>([]);
  const [allUsers, setAllUsers] = useState<any[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [attachmentPreviews, setAttachmentPreviews] = useState<Array<{
    id: string;
    file: File;
    preview: string;
    type: string;
    fileName: string;
  }>>([]);
  const commentContainerRef = useRef<HTMLDivElement>(null);

  const [ticketCounts, setTicketCounts] = useState({
    total: 0,
    open: 0,
    inProgress: 0,
    resolved: 0,
    critical: 0
  });

  // Reset location filter when project changes
  useEffect(() => {
    setSelectedLocation('all');
  }, [selectedProject]);

  // Add effect to fetch counts
  useEffect(() => {
    const counts = getTicketCounts();
      setTicketCounts(counts);
  }, [tickets]);

  // Clear resolution state when selected ticket changes or component unmounts
  useEffect(() => {
    // Clear resolution state immediately when ticket changes
    setResolutionReason('');
    setResolutionNotes('');
    setShowResolveDialog(false);
    setShowReferDialog(false);
    setReferToUser(null);
    setReferNote('');
    
    return () => {
      // Also clear on unmount
      setResolutionReason('');
      setResolutionNotes('');
      setShowResolveDialog(false);
      setShowReferDialog(false);
      setReferToUser(null);
      setReferNote('');
    };
  }, [selectedTicket?.id]);
  
  // Additional safety check - clear resolution state if ticket status changes to RESOLVED
  useEffect(() => {
    if (selectedTicket?.status === TicketStatus.RESOLVED) {
      console.log('Ticket is resolved, clearing resolution state');
      setResolutionReason('');
      setResolutionNotes('');
      setShowResolveDialog(false);
    }
  }, [selectedTicket?.status]);

  const getTicketCounts = () => {
    // Get visible tickets based on user role
    const visibleTickets = tickets;
    
    const counts = {
      total: visibleTickets.length,
      open: visibleTickets.filter((ticket: Ticket) => ticket.status === TicketStatus.OPEN).length,
      inProgress: visibleTickets.filter((ticket: Ticket) => ticket.status === TicketStatus.IN_PROGRESS).length,
      resolved: visibleTickets.filter((ticket: Ticket) => ticket.status === TicketStatus.RESOLVED).length,
      critical: visibleTickets.filter((ticket: Ticket) => ticket.priority === TicketPriority.CRITICAL).length
    };

    // Log the counts for debugging
    console.log('Calculated ticket counts:', {
      user: user?.name,
      role: user?.role,
      department: user?.department,
      counts,
      visibleTickets: visibleTickets.map((ticket: Ticket) => ticket.ticketNumber)
    });

    return counts;
  };

  // Add effect to load specific ticket from URL
  useEffect(() => {
    if (ticketIdFromPath && tickets.length > 0) {
      const ticketId = parseInt(ticketIdFromPath, 10);
      const foundTicket = tickets.find(t => t.id === ticketId);
      if (foundTicket) {
        setSelectedTicket(foundTicket);
        // Update view if needed
        if (view !== 'tickets') {
          onViewChange('tickets');
        }
      } else {
        console.warn(`Ticket with ID ${ticketId} not found`);
      }
    }
  }, [ticketIdFromPath, tickets, view, onViewChange]);

    const fetchTickets = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const token = localStorage.getItem('authToken');
        const userData = localStorage.getItem('userData');
        const user = userData ? JSON.parse(userData) : null;
      
        if (!token) {
          console.error('No auth token found');
          setError('Please log in to view tickets');
          setIsLoading(false);
          return;
        }

        console.log('Fetching tickets with token');
      
        const response = await fetch('/api/tickets', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
          }
        });

        if (response.status === 401) {
          console.error('Authentication failed');
          setError('Session expired. Please log in again.');
          setIsLoading(false);
          // Redirect to login if needed
          window.location.href = '/login';
          return;
        }

        // For employees, silently handle 403 errors - they'll just see their own tickets
        if (response.status === 403 && user?.role === 'EMPLOYEE') {
          console.log('Employee has limited ticket access - showing only their tickets');
          // Set empty tickets array - the user's tickets will be loaded separately
          setTickets([]);
          setFilteredTickets([]);
          setIsLoading(false);
          return;
        } else if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const fetchedTickets = await response.json() as Ticket[];
        console.log('Fetched tickets:', fetchedTickets);

        if (!Array.isArray(fetchedTickets)) {
          console.error('Invalid response format:', fetchedTickets);
          setError('Invalid data received from server');
          setIsLoading(false);
          return;
        }

        const validTickets = fetchedTickets.map((ticket: Ticket) => ({
          ...ticket,
          visibleTo: ticket.visibleTo || [],
          departmentChain: ticket.departmentChain || [],
          comments: ticket.comments || [],
          attachments: ticket.attachments || []
        }));

        setTickets(validTickets);
        setFilteredTickets(validTickets);
        setIsLoading(false);
      } catch (err: any) {
        console.error('Error loading tickets:', err);
        setError(err.message || 'Failed to load tickets');
        setIsLoading(false);
      }
    };

  useEffect(() => {
    fetchTickets();
  }, []);

  useEffect(() => {
    if (commentContainerRef.current) {
      commentContainerRef.current.scrollTop = commentContainerRef.current.scrollHeight;
    }
  }, [selectedTicket?.comments]);

  const handleNewTicket = async (formData: FormData): Promise<void> => {
    try {
      if (!user) {
        throw new Error('User not authenticated');
      }

      console.log('Submitting ticket with data:', {
        title: formData.get('title'),
        category: formData.get('category'),
        priority: formData.get('priority'),
        department: formData.get('department'),
        filesCount: formData.getAll('files').length
      });

      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/tickets', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const newTicket = await response.json() as Ticket;
      console.log('Created new ticket:', {
        id: newTicket.id,
        ticketNumber: newTicket.ticketNumber,
        creator: newTicket.createdBy.name,
        department: newTicket.department,
        visibleTo: newTicket.visibleTo,
        userRole: user.role
      });

      // Format the new ticket with all required properties
      const formattedTicket = {
        ...newTicket,
        visibleTo: newTicket.visibleTo || [],
        departmentChain: newTicket.departmentChain || [],
        comments: newTicket.comments || [],
        attachments: newTicket.attachments || []
      };

      // Update tickets state with the new formatted ticket
      setTickets(prevTickets => {
        // Check if the ticket already exists in the list
        const existingIndex = prevTickets.findIndex(t => t.id === newTicket.id);
        
        let updatedTickets;
        if (existingIndex >= 0) {
          // Replace existing ticket
          updatedTickets = [...prevTickets];
          updatedTickets[existingIndex] = formattedTicket;
        } else {
          // Add new ticket to the beginning of the list
          updatedTickets = [formattedTicket, ...prevTickets];
        }
        
        // Save to localStorage for persistence
        saveTickets(updatedTickets);
        return updatedTickets;
      });
      
      // Also update filtered tickets to ensure the new ticket appears in the current view
      setFilteredTickets(prevFiltered => {
        // Check if the ticket already exists in the filtered list
        const existingIndex = prevFiltered.findIndex(t => t.id === newTicket.id);
        
        // Only add to filtered list if it passes current filters
        const shouldInclude = 
          (selectedStatus === 'all' || newTicket.status === selectedStatus) &&
          (selectedPriority === 'all' || newTicket.priority === selectedPriority) &&
          (selectedDepartment === 'all' || newTicket.department === selectedDepartment);
        
        if (!shouldInclude) {
          return prevFiltered;
        }
        
        let updatedFiltered;
        if (existingIndex >= 0) {
          // Replace existing ticket
          updatedFiltered = [...prevFiltered];
          updatedFiltered[existingIndex] = formattedTicket;
        } else {
          // Add new ticket to the beginning of the list
          updatedFiltered = [formattedTicket, ...prevFiltered];
        }
        
        return updatedFiltered;
      });
      
      // Remove the duplicate toast notification
      // toast.success('Ticket created successfully');
      onViewChange('service-desk');

      // Dispatch a custom event to trigger a refresh
      const refreshEvent = new CustomEvent('notification:data-refresh', {
        detail: {
          type: 'ticket',
          data: {
            ticketId: newTicket.id.toString()
          }
        }
      });
      window.dispatchEvent(refreshEvent);
      
      // Force a refresh of the tickets list after a short delay
      setTimeout(() => {
        forceRefreshTickets();
      }, 500);
    } catch (error: any) {
      console.error('Error creating ticket:', error);
      toast.error(error.message || 'Failed to create ticket');
    }
  };

  // Add state variables for comment handling
  const [isAddingComment, setIsAddingComment] = useState(false);
  const [commentText, setCommentText] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isResolvingTicket, setIsResolvingTicket] = useState(false);

  const handleAddComment = async (content: string, files: File[]) => {
    if (!selectedTicket || !currentUser) return;
    
    console.log('Adding comment:', {
      ticketId: selectedTicket.id,
      content,
      filesCount: files.length,
      currentStatus: selectedTicket.status,
      resolutionReason,
      showResolveDialog
    });
    
    // Generate a unique temporary ID that we can reference later
    const tempId = `temp-${Date.now()}`;
    
    try {
      setIsAddingComment(true);
      
      // Create a temporary comment to show immediately in the UI
      const tempComment: TempComment = {
        id: tempId,
        content,
        type: 'user',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: {
          id: currentUser.id,
          name: currentUser.name,
          role: currentUser.role
        },
        ticketId: selectedTicket.id,
        isSystemComment: false,
        attachments: []
      };
      
      // Step 1: Upload files first if any
      let uploadedAttachments: Attachment[] = [];
      if (files.length > 0) {
        // Create temporary attachment previews for immediate display
        const tempAttachments = files.map((file, index) => {
          const fileType = file.type;
          const isImage = fileType.startsWith('image/');
          let preview = '';
          
          // Create object URL for images to show preview
          if (isImage) {
            preview = URL.createObjectURL(file);
          }
          
          return {
            id: `temp-attachment-${index}`,
            fileName: file.name,
            fileType: fileType,
            fileUrl: isImage ? preview : '',
            uploadedBy: {
              id: currentUser.id,
              name: currentUser.name,
              role: currentUser.role
            },
            uploadedAt: new Date().toISOString()
          };
        });
        
        // Add these temporary attachments to the temporary comment
        tempComment.attachments = tempAttachments;
        
        // Upload the files to the server
        uploadedAttachments = await uploadFiles(files, selectedTicket.id);
        console.log('Uploaded attachments:', uploadedAttachments);
      }
      
      // Step 2: Update the UI immediately with the temporary comment
      setSelectedTicket(prevTicket => {
        if (!prevTicket) return prevTicket;
        
        return {
          ...prevTicket,
          comments: [...prevTicket.comments, tempComment as Comment]
        };
      });
      
      // Step 3: Send the comment to the server with attachment IDs
      const token = localStorage.getItem('authToken');
      const commentData = {
        content,
        attachments: uploadedAttachments.map(a => a.id)
      };
      
      const response = await fetch(`/api/tickets/${selectedTicket.id}/comments`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(commentData)
      });
      
      if (!response.ok) {
        throw new Error(`Failed to add comment: ${response.status}`);
      }
      
      // Step 4: Get the real comment data from the server
      const responseData = await response.json();
      const comment = responseData.comment;
      
      console.log('Server response for comment:', comment);
      console.log('Server response attachments:', comment.attachments);
      
      // Step 5: Format the server response to match our comment structure
      const formattedComment = {
        ...comment,
        type: 'user',
        attachments: comment.attachments && comment.attachments.length > 0 
          ? comment.attachments 
          : uploadedAttachments,
        createdBy: {
          id: comment.createdBy.id,
          name: comment.createdBy.name,
          role: comment.createdBy.role
        }
      };
      
      console.log('Formatted comment with attachments:', formattedComment);
      
      // Step 6: Replace the temporary comment with the real one
      setSelectedTicket(prevTicket => {
        if (!prevTicket) return prevTicket;
        
        const updatedComments = prevTicket.comments.filter(c => c.id !== tempId);
        return {
          ...prevTicket,
          comments: [...updatedComments, formattedComment]
        };
      });
      
      // Step 7: Also update the comment in the main tickets list
      setTickets(prevTickets => {
        return prevTickets.map(ticket => {
          if (ticket.id === selectedTicket.id) {
            const updatedComments = ticket.comments.filter(c => c.id !== tempId);
            return {
              ...ticket,
              comments: [...updatedComments, formattedComment]
            };
          }
          return ticket;
        });
      });
      
      toast.success('Comment added successfully');
      setCommentText('');
      setAttachments([]);
      
      // Step 8: Refresh the ticket details to ensure we have the latest data including attachments
      if (selectedTicket.id) {
        setTimeout(async () => {
          const refreshedTicket = await fetchTicketDetails(selectedTicket.id);
          if (refreshedTicket) {
            setSelectedTicket(refreshedTicket);
          }
        }, 500);
      }
    } catch (error) {
      console.error('Error adding comment:', error);
      toast.error('Failed to add comment');
      
      // Remove the temporary comment if there was an error
      setSelectedTicket(prevTicket => {
        if (!prevTicket) return prevTicket;
        
        return {
          ...prevTicket,
          comments: prevTicket.comments.filter(c => c.id !== tempId)
        };
      });
    } finally {
      setIsAddingComment(false);
    }
  };

  const handleStatusChange = async (ticketId: number, newStatus: TicketStatus): Promise<void> => {
    try {
      if (!user) {
        toast.error('You must be logged in to update ticket status');
        return;
      }

      // First get the current ticket to check its status
      const currentTicket = tickets.find(t => t.id === ticketId);
      if (!currentTicket) {
        toast.error('Ticket not found');
        return;
      }

      // Validate the status transition
      if (currentTicket.status === newStatus) {
        return; // Already in desired status
      }

      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/tickets/${ticketId}/status`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: newStatus,
          comment: `Status changed to ${newStatus}`
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Get the updated ticket from the server
      const updatedTicket = await response.json();
      
      // Fetch the complete ticket details to ensure we have all data including attachments
      const completeTicket = await fetchTicketDetails(ticketId);
      
      if (completeTicket) {
        // Update tickets state with the complete ticket data
        setTickets(prevTickets =>
          prevTickets.map(ticket =>
            ticket.id === ticketId ? completeTicket : ticket
          )
        );

        // Update selected ticket if it's the one being modified
        if (selectedTicket?.id === ticketId) {
          setSelectedTicket(completeTicket);
        }
      } else {
        // Fallback to using the basic updated ticket if fetch failed
        setTickets(prevTickets =>
          prevTickets.map(ticket =>
            ticket.id === ticketId ? updatedTicket : ticket
          )
        );

        // Update selected ticket if it's the one being modified
        if (selectedTicket?.id === ticketId) {
          setSelectedTicket(updatedTicket);
        }
      }

      toast.success(`Ticket status updated to ${newStatus}`);
    } catch (error: any) {
      console.error('Error updating ticket status:', error);
      const errorMessage = error.response?.data?.error || 'Failed to update ticket status';
      toast.error(errorMessage);
      throw error; // Re-throw to handle in calling function
    }
  };

  const handleResolveTicket = async () => {
    if (!selectedTicket) {
      console.warn('handleResolveTicket called but no selectedTicket');
      return;
    }
    
    // Add validation to prevent accidental resolution
    if (!resolutionReason || resolutionReason.trim() === '') {
      console.error('handleResolveTicket called without resolution reason');
      toast.error('Resolution reason is required');
      return;
    }
    
    // Add confirmation check
    if (!showResolveDialog) {
      console.error('handleResolveTicket called but resolve dialog is not open');
      toast.error('Invalid resolution attempt');
      return;
    }
    
    console.log('Resolving ticket:', {
      ticketId: selectedTicket.id,
      ticketNumber: selectedTicket.ticketNumber,
      resolutionReason,
      resolutionNotes,
      showResolveDialog
    });
    
    try {
      setIsResolvingTicket(true);
      
      // First add a system comment with the resolution details
      const token = localStorage.getItem('authToken');
      const commentResponse = await fetch(`/api/tickets/${selectedTicket.id}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          content: `Ticket resolved: ${resolutionReason}\n${resolutionNotes}`,
          isSystemComment: true
        })
      });
      
      if (!commentResponse.ok) {
        throw new Error(`Failed to add resolution comment: ${commentResponse.status}`);
      }
      
      // Then change the status to RESOLVED using the handleStatusChange function
      await handleStatusChange(selectedTicket.id, TicketStatus.RESOLVED);
      
      toast.success('Ticket resolved successfully');
      setShowResolveDialog(false);
      setResolutionReason('');
      setResolutionNotes('');
      
    } catch (error) {
      console.error('Error resolving ticket:', error);
      toast.error('Failed to resolve ticket');
    } finally {
      setIsResolvingTicket(false);
    }
  };

  // Add a function to force refresh the tickets list
  const forceRefreshTickets = async () => {
    console.log('Force refreshing tickets list');
    try {
      setIsLoading(true);
      
      const token = localStorage.getItem('authToken');
      if (!token) {
        console.error('No auth token found for refresh');
        setIsLoading(false);
        return;
      }
      
      const response = await fetch('/api/tickets?includeAttachments=true', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const fetchedTickets = await response.json() as Ticket[];
      console.log('Refreshed tickets list, received:', fetchedTickets.length, 'tickets');
      
      // Log the ticket numbers for debugging
      console.log('Ticket numbers:', fetchedTickets.map(t => `#T${t.ticketNumber}`).join(', '));

      // Format tickets with required properties and ensure comments have attachments
      const formattedTickets = fetchedTickets.map(ticket => ({
        ...ticket,
        visibleTo: ticket.visibleTo || [],
        departmentChain: ticket.departmentChain || [],
        comments: (ticket.comments || []).map(comment => ({
          ...comment,
          attachments: comment.attachments || []
        })),
        attachments: ticket.attachments || []
      }));
      
      // Compare with current tickets to see if there are any new ones
      const currentTicketIds = new Set(tickets.map(t => t.id));
      const newTickets = formattedTickets.filter(t => !currentTicketIds.has(t.id));
      
      if (newTickets.length > 0) {
        console.log('Found new tickets:', newTickets.map(t => `#T${t.ticketNumber}`).join(', '));
      }
      
      // Check for tickets that have been updated
      const updatedTickets = formattedTickets.filter(newTicket => {
        const existingTicket = tickets.find(t => t.id === newTicket.id);
        if (!existingTicket) return false;
        
        return newTicket.updatedAt !== existingTicket.updatedAt;
      });
      
      if (updatedTickets.length > 0) {
        console.log('Found updated tickets:', updatedTickets.map(t => `#T${t.ticketNumber}`).join(', '));
      }

      // Update both tickets and filtered tickets
      setTickets(formattedTickets);
      
      // If we have a selected ticket, refresh its details too
      if (selectedTicket) {
        const refreshedTicket = formattedTickets.find(t => t.id === selectedTicket.id);
        if (refreshedTicket) {
          console.log('Updating selected ticket with refreshed data');
          console.log('Refreshed ticket attachments:', refreshedTicket.attachments);
          console.log('Refreshed ticket comments:', refreshedTicket.comments);
          setSelectedTicket(refreshedTicket);
        }
      }
      
      // Apply current filters to the new tickets
      setTimeout(() => {
        // Apply filters to the updated tickets list
        const filtered = formattedTickets.filter(ticket => {
          // Apply current filters
          let matches = true;
          
          // Status filter
          if (selectedStatus !== 'all' && ticket.status !== selectedStatus) {
            matches = false;
          }
          
          // Priority filter
          if (matches && selectedPriority !== 'all' && 
              ticket.priority.toLowerCase() !== selectedPriority.toLowerCase()) {
            matches = false;
          }
          
          // Department filter
          if (matches && selectedDepartment !== 'all' && 
              ticket.department !== selectedDepartment) {
            matches = false;
          }
          
          // Project filter
          if (matches && selectedProject !== 'all' && 
              (ticket as any).project !== selectedProject) {
            matches = false;
          }
          
          // Location filter
          if (matches && selectedLocation !== 'all' && 
              (ticket as any).location !== selectedLocation) {
            matches = false;
          }
          
          return matches;
        });
        
        setFilteredTickets(filtered);
        console.log('Filtered tickets updated, showing', filtered.length, 'of', formattedTickets.length, 'tickets');
      }, 0);
      
      setIsLoading(false);
      console.log('Tickets list refreshed successfully');
    } catch (error) {
      console.error('Error refreshing tickets:', error);
      toast.error('Failed to refresh tickets');
      setIsLoading(false);
    }
  };

  // Add listener for custom notification:data-refresh events
  useEffect(() => {
    const handleDataRefresh = (event: Event) => {
      const customEvent = event as CustomEvent<{type: string, data: any}>;
      const { type, data } = customEvent.detail;
      
      console.log('ServiceDesk: Received data refresh event:', { type, data });
      
      if (type === 'ticket') {
        const ticketId = parseInt(data.ticketId || '0');
        
        // If we're viewing this specific ticket, refresh it
        if (selectedTicket && selectedTicket.id === ticketId) {
          console.log('ServiceDesk: Refreshing currently viewed ticket');
          fetchTicketDetails(ticketId).then(refreshedTicket => {
            if (refreshedTicket) {
              setSelectedTicket(refreshedTicket);
            }
          });
        }
        
        // Always refresh the ticket list to ensure it's up to date
        // This ensures the list reflects the latest status even if we're in detail view
        console.log('ServiceDesk: Refreshing ticket list');
        fetchTickets();
      }
    };
    
    // Add event listener for custom refresh events
    window.addEventListener('notification:data-refresh', handleDataRefresh);
    
    // Clean up
    return () => {
      window.removeEventListener('notification:data-refresh', handleDataRefresh);
    };
  }, [selectedTicket, fetchTickets]);

  // Update handleReferTicket to use forceRefreshTickets
  const handleReferTicket = async () => {
    try {
        if (!selectedTicket || !referToUser || !referNote) {
            toast.error('Please provide all required information');
            return;
        }

        // Check if user has permission to reassign tickets
        if (!currentUser || !currentUser.role || 
            (currentUser.role !== UserRole.IT_ADMIN && currentUser.role !== UserRole.IT_STAFF)) {
            toast.error('You do not have permission to reassign tickets');
            return;
        }

        // Log the request details for debugging
        console.log('Reassigning ticket:', {
            ticketId: selectedTicket.id,
            assignedToId: referToUser.id,
            currentStatus: selectedTicket.status,
            comment: referNote,
            currentUser: {
                id: currentUser.id,
                role: currentUser.role
            }
        });

        // Ensure the ticket is in a valid state for reassignment
        if (selectedTicket.status !== TicketStatus.IN_PROGRESS) {
            toast.error('Only tickets in progress can be reassigned');
            return;
        }

        // Ensure the ticket is locked by the current user
        if (selectedTicket.lockedById !== currentUser.id) {
            toast.error('You must lock the ticket before reassigning it');
            return;
        }

        // Update the ticket with assignment and comment
        console.log('Sending reassignment request with data:', {
            assignedToId: referToUser.id,
            comment: referNote,
            status: selectedTicket.status,
            ticketId: selectedTicket.id
        });

        const updateResponse = await api.patch(`/tickets/${selectedTicket.id}`, {
            assignedToId: referToUser.id,
            comment: referNote,
            status: selectedTicket.status, // Maintain current status
            ticketId: selectedTicket.id // Explicitly include the ticketId to prevent null value
        });

        if (updateResponse.data) {
            console.log('Reassignment successful, response:', updateResponse.data);
            
            // Force refresh tickets to ensure we have the latest data
            await forceRefreshTickets();
            await getTicketCounts();

            setReferToUser(null);
            setReferNote('');
            setShowReferDialog(false);
            toast.success(`Ticket reassigned to ${referToUser.name}`);
        }
    } catch (error: any) {
        console.error('Error reassigning ticket:', error);
        const errorMessage = error.response?.data?.error || 'Failed to reassign ticket';
        toast.error(errorMessage);
    }
};

  const handleLockTicket = async (ticketId: number) => {
    try {
      const ticketToLock = tickets.find(t => t.id === ticketId);
      if (!ticketToLock) {
        console.error('Ticket not found in local state:', ticketId);
        toast.error('Ticket not found');
        return;
      }

      // Check if the user has permission to lock this ticket
      const isAssignedToCurrentUser = ticketToLock.assignedToId === currentUser?.id;
      const isITStaff = currentUser?.role === UserRole.IT_STAFF || currentUser?.role === UserRole.IT_ADMIN;
      
      if (!isITStaff) {
        toast.error('Only IT staff can lock tickets');
        return;
      }
      
      // For IN_PROGRESS tickets, check if the user is assigned to the ticket
      if (ticketToLock.status === TicketStatus.IN_PROGRESS && !isAssignedToCurrentUser && currentUser?.role !== UserRole.IT_ADMIN) {
        toast.error('You can only lock tickets assigned to you');
        return;
      }

      // If ticket is OPEN, we'll lock and change status in one request
      if (ticketToLock.status === TicketStatus.OPEN) {
        const token = localStorage.getItem('authToken');
        if (!token) {
          console.error('Authentication token not found');
          toast.error('Authentication token not found');
          return;
        }

        console.log('Starting work on ticket:', {
          ticketId,
          currentStatus: ticketToLock.status,
          userId: user?.id
        });

        const response = await fetch(`/api/tickets/${ticketId}/start-working`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        const responseData = await response.json();

        if (!response.ok) {
          console.error('Error starting work on ticket:', responseData);
          toast.error(responseData.error || `Failed to start working on ticket`);
          
          // If we got back a ticket object, update our local state
          if (responseData.ticket) {
            setTickets(prevTickets =>
              prevTickets.map(ticket =>
                ticket.id === ticketId ? responseData.ticket : ticket
              )
            );
            if (selectedTicket?.id === ticketId) {
              setSelectedTicket(responseData.ticket);
            }
          }
          return;
        }

        // Update tickets state with the response from the server
        setTickets(prevTickets =>
          prevTickets.map(ticket =>
            ticket.id === ticketId ? { ...ticket, ...responseData } : ticket
          )
        );

        // Update selected ticket if it's the one being modified
        if (selectedTicket?.id === ticketId) {
          setSelectedTicket({ ...selectedTicket, ...responseData });
        }

        // Update ticket counts
        await getTicketCounts();

        toast.success('You are now working on this ticket');
        return;
      }

      // For non-OPEN tickets, just lock the ticket
      const lockToken = localStorage.getItem('authToken');
      if (!lockToken) {
        console.error('Authentication token not found');
        toast.error('Authentication token not found');
        return;
      }

      console.log('Locking ticket:', {
        ticketId,
        currentStatus: ticketToLock.status,
        userId: user?.id
      });

      const lockResponse = await fetch(`/api/tickets/${ticketId}/lock`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${lockToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!lockResponse.ok) {
        const errorData = await lockResponse.json();
        console.error('Error locking ticket:', errorData);
        toast.error(errorData.error || 'Failed to lock ticket');
        return;
      }

      const lockData = await lockResponse.json();

      // Update tickets state with the response from the server
      setTickets(prevTickets =>
        prevTickets.map(ticket =>
          ticket.id === ticketId ? { ...ticket, ...lockData } : ticket
        )
      );

      // Update selected ticket if it's the one being modified
      if (selectedTicket?.id === ticketId) {
        setSelectedTicket({ ...selectedTicket, ...lockData });
      }

      toast.success('Ticket locked successfully');
    } catch (error) {
      console.error('Error locking ticket:', error);
      toast.error('Failed to lock ticket');
    }
  };

  const handleUnlockTicket = async (ticketId: number): Promise<void> => {
    try {
      if (!user) {
        toast.error('You must be logged in to unlock a ticket');
        return;
      }

      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/tickets/${ticketId}/unlock`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const updatedTicket = await response.json();

      // Update tickets state with the response from the server
      setTickets(prevTickets =>
        prevTickets.map(ticket =>
          ticket.id === ticketId ? updatedTicket : ticket
        )
      );

      // Update selected ticket if it's the one being modified
      if (selectedTicket?.id === ticketId) {
        setSelectedTicket(updatedTicket);
      }

      toast.success('Ticket unlocked successfully');
    } catch (error: any) {
      console.error('Error unlocking ticket:', error);
      const errorMessage = error.response?.data?.error || 'Failed to unlock ticket';
      toast.error(errorMessage);
    }
  };

  const renderFilePreview = (preview: AttachmentPreview) => {
    if ((preview.fileType || preview.type || '').startsWith('image/')) {
      return (
        <div className="relative w-20 h-20 group">
          <img 
            src={preview.preview || preview.fileUrl || ''} 
            alt={preview.fileName}
            className="w-full h-full object-cover rounded"
          />
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity">
            <button
              onClick={() => setAttachmentPreviews(prev => prev.filter(p => p.id !== preview.id))}
              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      );
    }

    return (
      <div className="relative flex items-center gap-2 bg-gray-100 p-2 rounded group">
        <FileText className="h-5 w-5 text-gray-500" />
        <span className="text-sm truncate max-w-[150px]">{preview.fileName}</span>
        <button
          onClick={() => setAttachmentPreviews(prev => prev.filter(p => p.id !== preview.id))}
          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    );
  };

  // Add effect to fetch IT staff users
  useEffect(() => {
    const fetchITStaffUsers = async () => {
      try {
        const response = await api.get('/tickets/ids');
        if (response.data && response.data.users) {
          // Filter users to only include IT staff and IT admin
          const staffUsers = response.data.users.filter((user: any) => 
            [UserRole.IT_ADMIN, UserRole.IT_STAFF].includes(user.role)
          );
          setITStaffUsers(staffUsers);
        }
      } catch (error) {
        console.error('Error fetching IT staff users:', error);
        // Fallback to empty array if API fails
        setITStaffUsers([]);
      }
    };

    fetchITStaffUsers();
  }, []);

  // Add effect to fetch all users
  useEffect(() => {
    const fetchAllUsers = async () => {
      try {
        const response = await api.get('/users');
        if (response.data && response.data.users) {
          console.log('Fetched all users:', response.data.users);
          
          // Check if users have project information
          const usersWithProjects = response.data.users.filter((user: any) => user.project);
          console.log('Users with project information:', usersWithProjects.length);
          
          if (usersWithProjects.length > 0) {
            console.log('Sample user with project:', usersWithProjects[0]);
          }
          
          setAllUsers(response.data.users);
        }
      } catch (error) {
        console.error('Error fetching all users:', error);
        setAllUsers([]);
      }
    };

    fetchAllUsers();
  }, []);

  // Add a new view for ticket details
  const [ticketDetailView, setTicketDetailView] = useState(false);

  // Add a function to fetch a single ticket with all its details
  const fetchTicketDetails = async (ticketId: number) => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/tickets/${ticketId}?includeAttachments=true&includeCommentAttachments=true`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
          'Cache-Control': 'no-cache'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const ticket = await response.json() as Ticket;
      console.log('Fetched ticket details:', ticket);
      console.log('Ticket attachments:', ticket.attachments);
      console.log('Ticket comments:', ticket.comments);
      
      if (ticket.comments) {
        ticket.comments.forEach((comment, index) => {
          console.log(`Comment ${index} attachments:`, comment.attachments);
        });
      }
      
      // Ensure all arrays exist and comments have attachments
      const completeTicket = {
        ...ticket,
        visibleTo: ticket.visibleTo || [],
        departmentChain: ticket.departmentChain || [],
        comments: (ticket.comments || []).map(comment => ({
          ...comment,
          attachments: comment.attachments || []
        })),
        attachments: ticket.attachments || []
      };
      
      return completeTicket;
    } catch (error) {
      console.error('Error fetching ticket details:', error);
      return null;
    }
  };

  // Modify the handleTicketSelect function to handle both ticket objects and IDs
  const handleTicketSelect = (ticket: Ticket) => {
    console.log('🎯 Selecting ticket:', ticket.ticketNumber, ticket.title);
    setSelectedTicket(ticket);
    setTicketDetailView(true);
  };

  // Add this effect after the other useEffect hooks
  useEffect(() => {
    // Clear any cached tickets with REFERRED status
    const savedTickets = localStorage.getItem('tickets');
    if (savedTickets) {
      try {
        const parsedTickets = JSON.parse(savedTickets);
        if (Array.isArray(parsedTickets)) {
          const updatedTickets = parsedTickets.map(ticket => {
            if (ticket.status === 'REFERRED') {
              return {
                ...ticket,
                status: 'IN_PROGRESS'
              };
            }
            return ticket;
          });
          localStorage.setItem('tickets', JSON.stringify(updatedTickets));
        }
      } catch (error) {
        console.error('Error updating cached tickets:', error);
      }
    }
  }, []); // Run once on component mount

  // Add this effect after the other useEffect hooks
  useEffect(() => {
    // Force refresh tickets from server to ensure we have the latest data
    forceRefreshTickets();
  }, []); // Run once on component mount

  const canAddComment = (ticket: Ticket | null): boolean => {
    if (!ticket || !currentUser) return false;
    
    // If ticket is resolved, no comments allowed
    if (ticket.status === TicketStatus.RESOLVED) {
      return false;
    }
    
    // If ticket is locked by someone else
    if (ticket.lockedById && ticket.lockedById !== currentUser.id) {
      // Allow comments from the ticket creator even when locked
      if (ticket.createdBy.id === currentUser.id) {
        return true;
      }
      // IT staff or admin cannot comment when another agent has locked the ticket
      if (currentUser.role === UserRole.IT_STAFF || currentUser.role === UserRole.IT_ADMIN) {
        return false;
      }
    }
    
    // Allow comments if:
    // 1. Ticket is not locked
    // 2. Ticket is locked by current user
    // 3. Ticket is in OPEN or IN_PROGRESS status
    return ticket.status === TicketStatus.OPEN || ticket.status === TicketStatus.IN_PROGRESS;
  };

  const getStatusColor = (status: TicketStatus): string => {
    switch (status) {
      case TicketStatus.OPEN:
        return 'bg-yellow-100 text-yellow-800';
      case TicketStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      case TicketStatus.RESOLVED:
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string): string => {
    switch (priority.toUpperCase()) {
      case 'CRITICAL':
        return 'bg-red-100 text-red-800';
      case 'HIGH':
        return 'bg-orange-100 text-orange-800';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800';
      case 'LOW':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleDeleteTicket = async (ticketId: number): Promise<void> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/tickets/${ticketId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete ticket');
      }

      setTickets(prev => prev.filter(t => t.id !== ticketId));
      setFilteredTickets(prev => prev.filter(t => t.id !== ticketId));
      toast.success('Ticket deleted successfully');
    } catch (error) {
      console.error('Error deleting ticket:', error);
      toast.error('Failed to delete ticket');
    }
  };

  const renderActionButtons = (ticket: Ticket, isDetailView: boolean = false): React.ReactNode => {
    const isLockedByCurrentUser = ticket.lockedById === currentUser?.id;
    const isITStaff = currentUser?.role === UserRole.IT_STAFF || currentUser?.role === UserRole.IT_ADMIN;
    const isAssignedToCurrentUser = ticket.assignedToId === currentUser?.id;
    
    // Log permissions for debugging
    if (isDetailView) {
      console.log('Action button permissions:', {
        ticketId: ticket.id,
        status: ticket.status,
        isLockedByCurrentUser,
        isITStaff,
        isAssignedToCurrentUser,
        currentUserId: currentUser?.id,
        lockedById: ticket.lockedById,
        assignedToId: ticket.assignedToId
      });
    }

    return (
      <div className="flex gap-2">
        {/* Start Working Button - Show for OPEN tickets or for tickets assigned to current user but not locked */}
        {((ticket.status === TicketStatus.OPEN && isITStaff) || 
           (ticket.status === TicketStatus.IN_PROGRESS && isAssignedToCurrentUser && !ticket.lockedById)) && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleLockTicket(ticket.id);
            }}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            Start Working
          </button>
        )}

        {/* Resolve Button - Show for IN_PROGRESS tickets locked by current user or assigned to current admin */}
        {ticket.status === TicketStatus.IN_PROGRESS && 
          (isLockedByCurrentUser || (currentUser?.role === UserRole.IT_ADMIN && isAssignedToCurrentUser)) && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowResolveDialog(true);
            }}
            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
          >
            Resolve
          </button>
        )}

        {/* Reassign Button - Show for IN_PROGRESS tickets locked by current user or assigned to current admin */}
        {ticket.status === TicketStatus.IN_PROGRESS && 
          (isLockedByCurrentUser || (currentUser?.role === UserRole.IT_ADMIN && isAssignedToCurrentUser)) && 
          isITStaff && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowReferDialog(true);
            }}
            className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600"
          >
            Reassign
          </button>
        )}

        {/* Delete Button - Show only for IT Admins */}
        {currentUser?.role === UserRole.IT_ADMIN && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              if (!deleteConfirmMap[ticket.id]) {
                setDeleteConfirmMap(prev => ({ ...prev, [ticket.id]: true }));
                setTimeout(() => {
                  setDeleteConfirmMap(prev => ({ ...prev, [ticket.id]: false }));
                }, 5000);
              } else {
                handleDeleteTicket(ticket.id);
                setDeleteConfirmMap(prev => ({ ...prev, [ticket.id]: false }));
              }
            }}
            className={`p-2 rounded-full transition-colors ${
              deleteConfirmMap[ticket.id]
                ? 'bg-red-100 text-red-700 hover:bg-red-200'
                : 'text-red-500 hover:text-red-700 hover:bg-red-50'
            }`}
            title={deleteConfirmMap[ticket.id] ? "Click again to confirm delete" : "Delete Ticket"}
          >
            <Trash2 className="h-5 w-5" />
          </button>
        )}
      </div>
    );
  };

  // DISABLE SOCKET EVENT LISTENERS TO PREVENT CONTINUOUS API CALLS
  useEffect(() => {
    console.log('ServiceDesk: Socket event listeners temporarily disabled to prevent continuous API calls');
    
    // Return empty cleanup
    return () => {};
  }, []); // Empty dependency array

  // Define applyFilters function outside of useEffect to make it accessible
  const applyFilters = () => {
    let result = [...tickets];
    
    // Apply search filter
    if (searchQuery.trim()) {
      const search = searchQuery.toLowerCase().trim();
      result = result.filter(ticket => 
        ticket.title.toLowerCase().includes(search) ||
        ticket.description.toLowerCase().includes(search) ||
        ticket.ticketNumber.toString().includes(search) ||
        (ticket.category && ticket.category.toLowerCase().includes(search))
      );
    }
    
    // Apply status filter
    if (selectedStatus !== 'all') {
      result = result.filter(ticket => ticket.status === selectedStatus);
    }
    
    // Priority filter
    if (selectedPriority !== 'all') {
      result = result.filter(ticket => 
        ticket.priority.toLowerCase() === selectedPriority.toLowerCase()
      );
    }
    
    // Department filter
    if (selectedDepartment !== 'all') {
      result = result.filter(ticket => ticket.department === selectedDepartment);
    }
    
    // Project filter
    if (selectedProject !== 'all') {
      result = result.filter(ticket => 
        // Check if ticket has project property or use other criteria
        (ticket as any).project === selectedProject || 
        ticket.category === selectedProject
      );
    }
    
    // Location filter
    if (selectedLocation !== 'all') {
      result = result.filter(ticket => 
        // Check if ticket has location property
        (ticket as any).location === selectedLocation
      );
    }
    
    return result;
  };

  // Add effect to sync filtered tickets with main tickets
  useEffect(() => {
    // Update filtered tickets whenever the main tickets list changes
    setFilteredTickets(applyFilters());
    
    // Log for debugging
    console.log('Filtered tickets updated. Total tickets:', tickets.length, 'Filtered tickets:', filteredTickets.length);
    
  }, [tickets, searchQuery, selectedStatus, selectedPriority, selectedDepartment, selectedProject, allUsers]);

  // Add state for tracking if we're expecting new tickets
  const [expectingNewTickets, setExpectingNewTickets] = useState(false);

  // DISABLE POLLING TO PREVENT CONTINUOUS API CALLS
  useEffect(() => {
    console.log('ServiceDesk: Polling temporarily disabled to prevent continuous API calls');
    
    // Only do initial fetch, no polling
    fetchTickets();
    
    // Return empty cleanup
    return () => {};
  }, []); // Empty dependency array - only run once

  if (view === 'create-ticket') {
    return (
      <div className="p-6">
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Create New Ticket</h1>
                <p className="text-gray-600">Submit a new support ticket</p>
              </div>
              <button
                onClick={(e) => {
                  e.preventDefault(); // Prevent default behavior
                  e.stopPropagation(); // Stop event propagation
                  handleBackToServiceDesk();
                }}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-800"
              >
                <ArrowLeft className="h-5 w-5" />
                Back to Service Desk
              </button>
            </div>
          </div>
          <TicketForm
            onClose={handleBackToServiceDesk}
            onSubmit={handleNewTicket}
            userPermissions={currentUser?.permissions}
          />
        </div>
      </div>
    );
  }

  if (view === 'knowledge') {
    return <KnowledgeBasePage />;
  }
  
  if (view === 'knowledge-create') {
    console.log('Rendering KnowledgeArticleEditor from ServiceDesk with view="knowledge-create"');
    console.log('Current auth status:', {
      userPresent: !!user,
      userRole: user?.role,
      hasAuthToken: !!localStorage.getItem('authToken'),
      hasUserData: !!localStorage.getItem('userData')
    });
    return (
      <div className="min-h-screen p-6">
        <KnowledgeArticleEditor />
      </div>
    );
  }

  // Add a condition to check for ticket detail view
  if (ticketDetailView && selectedTicket) {
    return (
      <TicketDetailView 
        ticket={selectedTicket}
        currentUser={currentUser}
        onClose={() => {
          setTicketDetailView(false);
          setSelectedTicket(null);
        }}
        onAddComment={handleAddComment}
        canAddComment={canAddComment(selectedTicket)}
        getStatusColor={getStatusColor}
        getPriorityColor={getPriorityColor}
        renderActionButtons={(ticket) => renderActionButtons(ticket, true)}
        handleDeleteTicket={handleDeleteTicket}
        handleStatusChange={handleStatusChange}
        handleLockTicket={handleLockTicket}
        handleUnlockTicket={handleUnlockTicket}
        handleResolveTicket={handleResolveTicket}
        handleReferTicket={handleReferTicket}
        setShowResolveDialog={setShowResolveDialog}
        setShowReferDialog={setShowReferDialog}
        showResolveDialog={showResolveDialog}
        showReferDialog={showReferDialog}
        resolutionReason={resolutionReason}
        setResolutionReason={setResolutionReason}
        resolutionNotes={resolutionNotes}
        setResolutionNotes={setResolutionNotes}
        referToUser={referToUser}
        setReferToUser={setReferToUser}
        referNote={referNote}
        setReferNote={setReferNote}
        itStaffUsers={itStaffUsers}
      />
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200 transform transition-all duration-300 hover:scale-105">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-sm text-gray-600">Total Tickets</h3>
                <p className="text-2xl font-bold text-gray-900">
                  {ticketCounts.total}
                </p>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 transform transition-all duration-300 hover:scale-105">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-sm text-gray-600">Open Tickets</h3>
                <p className="text-2xl font-bold text-gray-900">
                  {ticketCounts.open}
                </p>
              </div>
              <div className="bg-yellow-100 p-3 rounded-full">
                <AlertCircle className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </div>
          
          {/* In Progress Tickets Card */}
          <div className="bg-white p-4 rounded-lg border border-gray-200 transform transition-all duration-300 hover:scale-105">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-sm text-gray-600">In Progress</h3>
                <p className="text-2xl font-bold text-gray-900">
                  {ticketCounts.inProgress}
                </p>
              </div>
              <div className="bg-orange-100 p-3 rounded-full">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 transform transition-all duration-300 hover:scale-105">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-sm text-gray-600">Resolved Tickets</h3>
                <p className="text-2xl font-bold text-gray-900">
                  {ticketCounts.resolved}
                </p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <CheckCircle2 className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 transform transition-all duration-300 hover:scale-105">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-sm text-gray-600">Critical Issues</h3>
                <p className="text-2xl font-bold text-gray-900">
                  {ticketCounts.critical}
                </p>
              </div>
              <div className="bg-red-100 p-3 rounded-full">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {view === 'service-desk-home' ? 'Service Desk' : 
               view === 'tickets' ? 'Support Tickets' : 
               view === 'knowledge-base' ? 'Knowledge Base' : 'Service Desk'}
            </h1>
            <p className="text-gray-600">
              {view === 'tickets' ? 'Manage and track all support tickets' : 
               'Get help and support for your IT needs'}
            </p>
          </div>
          {(view === 'tickets' || view === 'service-desk-home') && (
              <button
              onClick={(e) => {
                e.preventDefault(); // Prevent default behavior
                e.stopPropagation(); // Stop event propagation
                handleCreateTicketClick();
              }}
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white font-semibold rounded-lg hover:bg-blue-600 transition-colors"
            >
              <Plus className="h-5 w-5" />
              Create Ticket
              </button>
          )}
            </div>
            
            <div className="space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search by ticket title, description, or keywords (e.g., 'printer not working', 'password reset')"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value as TicketStatus)}
                className="w-full pl-3 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
              >
                <option value="all">All Status</option>
                <option value="OPEN">Open</option>
                <option value="IN_PROGRESS">In Progress</option>
                <option value="RESOLVED">Resolved</option>
              </select>
              <ChevronDown className="absolute right-3 top-[60%] transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none" />
              </div>

            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
              <select
                value={selectedPriority}
                onChange={(e) => setSelectedPriority(e.target.value)}
                className="w-full pl-3 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
              >
                <option value="all">All Priorities</option>
                <option value="critical">Critical</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
              <ChevronDown className="absolute right-3 top-[60%] transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none" />
            </div>
            
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
                <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="w-full pl-3 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
              >
                <option value="all">All Departments</option>
                <option value="CSD">CSD</option>
                <option value="FINANCE">Finance</option>
                <option value="HR">HR</option>
                <option value="IT">IT</option>
                <option value="LAND">Land</option>
                <option value="LEGAL">Legal</option>
                <option value="MANAGEMENT">Management</option>
                <option value="MARKETING">Marketing</option>
                <option value="OPERATIONS">Operations</option>
                <option value="PND">PND</option>
                <option value="SALES">Sales</option>
                </select>
              <ChevronDown className="absolute right-3 top-[60%] transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none" />
              </div>
              
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-1">Project</label>
                <select
                  value={selectedProject}
                  onChange={(e) => setSelectedProject(e.target.value)}
                  className="w-full pl-3 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
                >
                  <option value="all">All Projects</option>
                  {PROJECTS.map((project) => (
                    <option key={project} value={project}>{project}</option>
                  ))}
                </select>
                <ChevronDown className="absolute right-3 top-[60%] transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none" />
              </div>
              
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                <select
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  className="w-full pl-3 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
                >
                  <option value="all">All Locations</option>
                  {selectedProject !== 'all' 
                    ? PROJECT_LOCATIONS[selectedProject]?.map((location) => (
                        <option key={location} value={location}>{location}</option>
                      ))
                    : // If no project is selected, show all unique locations
                      [...new Set(
                        Object.values(PROJECT_LOCATIONS).flat()
                      )].sort().map((location) => (
                        <option key={location} value={location}>{location}</option>
                      ))
                  }
                </select>
                <ChevronDown className="absolute right-3 top-[60%] transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none" />
              </div>
                    </div>
                    </div>
                  </div>

      {/* Add Ticket List Section */}
      <div className="mt-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-4 text-gray-600">Loading tickets...</p>
              </div>
            ) : error ? (
              <div className="text-center py-8 text-red-600">
                <AlertCircle className="h-12 w-12 mx-auto mb-4" />
                <p>{error}</p>
              </div>
            ) : filteredTickets.length === 0 ? (
              <div className="text-center py-8 text-gray-600">
                <Inbox className="h-12 w-12 mx-auto mb-4" />
                <p>No tickets found</p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                      ID
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                      Priority
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                      Ticket Details
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                      Created By
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                      Assigned To
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider whitespace-nowrap">
                      LAST UPDATED
                    </th>
                    {user?.role === 'IT_ADMIN' && (
                      <th className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                        Actions
                      </th>
                    )}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
        {filteredTickets.map((ticket: Ticket) => (
                    <tr 
            key={ticket.id}
                      onClick={() => handleTicketSelect(ticket)}
                      className="hover:bg-gray-50 cursor-pointer transition-all duration-200 group"
                    >
                      {/* ID Column */}
                      <td className="px-4 py-4 whitespace-nowrap">
                        <span className="text-sm font-medium text-gray-900">#{ticket.ticketNumber}</span>
                      </td>

                      {/* Priority Column */}
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-1">
                          {ticket.priority === 'CRITICAL' && (
                            <AlertCircle className="h-3 w-3 text-red-500" />
                          )}
                          {ticket.priority === 'HIGH' && (
                            <AlertTriangle className="h-3 w-3 text-orange-500" />
                          )}
                          {ticket.priority === 'MEDIUM' && (
                            <AlertCircle className="h-3 w-3 text-yellow-500" />
                          )}
                          {ticket.priority === 'LOW' && (
                            <AlertCircle className="h-3 w-3 text-green-500" />
                          )}
                          <span className={`inline-flex items-center gap-1 px-1 py-0.5 text-xs font-normal rounded-full ${getPriorityColor(ticket.priority)}`}>
                            {ticket.priority}
                          </span>
                        </div>
                      </td>

                      {/* Ticket Details Column */}
                      <td className="px-4 py-4">
                        <div className="max-w-md">
                          <div className="flex items-center gap-2">
                            <h3 className="text-base font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">{ticket.title}</h3>
                          </div>
                          <p className="text-sm text-gray-500 line-clamp-1 mt-1">{ticket.description}</p>
                          <div className="flex items-center gap-2 mt-2">
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-medium rounded-full border border-gray-200 bg-gray-50 text-gray-700">
                              <Tag className="h-3 w-3" />
                              {ticket.category}
                            </span>
                          </div>
                        </div>
                      </td>

                      {/* Date Column */}
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex flex-col items-start">
                          <div className="text-xs text-gray-900">
                            {new Date(ticket.createdAt).toLocaleTimeString('en-US', {
                              hour: 'numeric',
                              minute: '2-digit',
                              hour12: true
                            })}
                          </div>
                          <div className="text-xs text-gray-500">
                            {new Date(ticket.createdAt).toLocaleDateString('en-US', {
                              month: 'long',
                              day: 'numeric',
                              year: 'numeric'
                            })}
                          </div>
                        </div>
                      </td>

                      {/* Status Column */}
                      <td className="px-4 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center gap-1 px-1 py-0.5 text-xs font-normal rounded-full ${getStatusColor(ticket.status)}`}>
                          {ticket.status === 'RESOLVED' && (
                            <CheckCircle className="h-3 w-3" />
                          )}
                          {ticket.status === 'IN_PROGRESS' && (
                            <RefreshCw className="h-3 w-3" />
                          )}
                          {ticket.status === 'OPEN' && (
                            <Clock className="h-3 w-3" />
                          )}
                          {ticket.status}
                        </span>
                      </td>

                      {/* Created By Column */}
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-sm font-medium text-blue-700">
                            {getInitials(ticket.createdBy?.name || '')}
                          </div>
                          <div>
                            <div className="font-medium text-sm text-gray-900">{ticket.createdBy?.name}</div>
                            <div className="text-xs text-gray-500">{ticket.createdBy?.department}</div>
                          </div>
                        </div>
                      </td>

                      {/* Assigned To Column */}
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          {ticket.status === TicketStatus.OPEN ? (
                            <div className="flex items-center gap-2">
                              <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-sm font-medium text-gray-500">
                                <UserIcon className="h-4 w-4" />
                              </div>
                              <div className="text-sm text-gray-500">Unassigned</div>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-sm font-medium text-blue-700">
                                {getInitials((ticket.status === TicketStatus.IN_PROGRESS ? ticket.lockedBy?.name : ticket.lockedBy?.name) || '')}
                              </div>
                              <div>
                                <div className="font-medium text-sm text-gray-900">
                                  {ticket.status === TicketStatus.IN_PROGRESS ? ticket.lockedBy?.name : ticket.lockedBy?.name}
                                </div>
                                <div className="text-xs text-gray-500">IT</div>
                              </div>
                            </div>
                          )}
                        </div>
                      </td>

                      {/* Last Updated Column - Replacing Actions */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col items-start">
                          <div className="text-xs text-gray-900">
                            {new Date(ticket.updatedAt).toLocaleTimeString('en-US', {
                              hour: 'numeric',
                              minute: '2-digit',
                              hour12: true
                            })}
                          </div>
                          <div className="text-xs text-gray-500">
                            {new Date(ticket.updatedAt).toLocaleDateString('en-US', {
                              month: 'long',
                              day: 'numeric',
                              year: 'numeric'
                            })}
                          </div>
                        </div>
                      </td>
                      {user?.role === 'IT_ADMIN' && (
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              if (!deleteConfirmMap[ticket.id]) {
                                setDeleteConfirmMap(prev => ({ ...prev, [ticket.id]: true }));
                                setTimeout(() => {
                                  setDeleteConfirmMap(prev => ({ ...prev, [ticket.id]: false }));
                                }, 5000);
                              } else {
                                handleDeleteTicket(ticket.id);
                                setDeleteConfirmMap(prev => ({ ...prev, [ticket.id]: false }));
                              }
                            }}
                            className={`p-2 rounded-full transition-colors ${
                              deleteConfirmMap[ticket.id]
                                ? 'bg-red-100 text-red-700 hover:bg-red-200'
                                : 'text-red-500 hover:text-red-700 hover:bg-red-50'
                            }`}
                            title={deleteConfirmMap[ticket.id] ? "Click again to confirm delete" : "Delete Ticket"}
                          >
                            <Trash2 className="h-5 w-5" />
                          </button>
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>
              </div>
    </div>
  );
}

export default ServiceDesk;