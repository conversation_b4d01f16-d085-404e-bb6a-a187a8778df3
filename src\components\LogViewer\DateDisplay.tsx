import React from 'react';
import { Calendar, Clock } from 'lucide-react';
import { formatStackedDate } from './utils/dateFormat';

interface DateDisplayProps {
  timestamp: string;
  className?: string;
  showIcons?: boolean;
  variant?: 'default' | 'success' | 'error' | 'warning' | 'info';
}

/**
 * Component for displaying dates in a stacked format with time on top and date below
 */
export const DateDisplay: React.FC<DateDisplayProps> = ({ 
  timestamp, 
  className = '',
  showIcons = true,
  variant = 'default'
}) => {
  // Handle ISO format directly (like 2025-05-15T14:09:17.991Z)
  const isISOFormat = timestamp.includes('T') && timestamp.includes('Z');
  
  let datePart = '';
  let timePart = '';
  
  if (isISOFormat) {
    // Split the ISO string into date and time parts
    const parts = timestamp.split('T');
    datePart = parts[0];
    
    // Format time as HH:MM:SS without milliseconds
    const fullTimePart = parts[1].replace('Z', '');
    // Either split at the decimal point or take the first 8 chars (HH:MM:SS)
    timePart = fullTimePart.includes('.') 
      ? fullTimePart.split('.')[0] 
      : fullTimePart.substring(0, 8);
  } else {
    // Use our utility for other formats
    const formatted = formatStackedDate(timestamp);
    datePart = formatted.top;
    
    // If time has milliseconds or AM/PM, format it properly
    if (formatted.bottom.includes('.') || formatted.bottom.includes('M')) {
      // For milliseconds format (HH:MM:SS.sss)
      if (formatted.bottom.includes('.')) {
        timePart = formatted.bottom.split('.')[0];
      }
      // For AM/PM format (HH:MM:SS AM/PM)
      else if (formatted.bottom.includes(' ')) {
        const timeAndPeriod = formatted.bottom.split(' ');
        timePart = timeAndPeriod[0]; // Just the HH:MM:SS part
      }
      else {
        timePart = formatted.bottom;
      }
    } else {
      timePart = formatted.bottom;
    }
  }

  // Get time color based on variant
  const getTimeColor = () => {
    switch(variant) {
      case 'success':
        return 'text-emerald-600 dark:text-emerald-400';
      case 'error':
        return 'text-red-600 dark:text-red-400';
      case 'warning':
        return 'text-amber-600 dark:text-amber-400';
      case 'info':
        return 'text-blue-600 dark:text-blue-400';
      default:
        return 'text-indigo-600 dark:text-indigo-400';
    }
  };
  
  return (
    <div className={`flex flex-col ${className}`}>
      {/* Time on top */}
      <div className="flex items-center">
        {showIcons && <Clock className="h-3.5 w-3.5 mr-1.5 text-gray-500 dark:text-gray-400" />}
        <span className={`text-sm font-mono font-medium ${getTimeColor()}`}>{timePart}</span>
      </div>
      {/* Date below */}
      <div className="flex items-center mt-0.5">
        {showIcons && <Calendar className="h-3.5 w-3.5 mr-1.5 text-gray-500 dark:text-gray-400" />}
        <span className="text-sm text-gray-600 dark:text-gray-400">{datePart}</span>
      </div>
    </div>
  );
}; 