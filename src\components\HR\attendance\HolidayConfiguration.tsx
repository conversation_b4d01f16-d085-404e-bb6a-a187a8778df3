import React, { useState, useEffect } from 'react';
import { Calendar, Clock, CalendarDays, Trash2, Plus, Save, X, Calendar as CalendarIcon, SunMoon, Edit2 } from 'lucide-react';
import HolidayService from '../../../services/HolidayService';
import { toast } from 'react-hot-toast';

interface Holiday {
  id: number;
  name: string;
  date: string;
  type: 'public' | 'company' | 'optional';
  description?: string;
  metadata?: {
    religion?: string;
    culturalSignificance?: string;
    observanceLevel?: 'major' | 'minor' | 'regional';
    alternativeNames?: string[];
    duration?: number;
    fastingRequired?: boolean;
    lunarCalendar?: boolean;
  };
}

interface HolidayConfigurationProps {
  onSave?: (holidayData: { holidays: Holiday[], weekendDays: number[] }) => void;
  initialHolidays?: Holiday[];
  initialWeekendDays?: number[];
}

const DAYS_OF_WEEK = [
  { id: 0, name: 'Sunday' },
  { id: 1, name: 'Monday' },
  { id: 2, name: 'Tuesday' },
  { id: 3, name: 'Wednesday' },
  { id: 4, name: 'Thursday' },
  { id: 5, name: 'Friday' },
  { id: 6, name: 'Saturday' }
];

const HOLIDAY_TYPES = [
  { id: 'public', name: 'Public Holiday', color: 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400 border-red-200 dark:border-red-700' },
  { id: 'company', name: 'Company Holiday', color: 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 border-blue-200 dark:border-blue-700' },
  { id: 'optional', name: 'Religious/Optional Holiday', color: 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-400 border-purple-200 dark:border-purple-700' }
];

const RELIGIONS = [
  { id: 'Islam', name: 'Islam', emoji: '☪️', color: 'text-green-600' },
  { id: 'Christianity', name: 'Christianity', emoji: '✝️', color: 'text-blue-600' },
  { id: 'Hinduism', name: 'Hinduism', emoji: '🕉️', color: 'text-orange-600' },
  { id: 'Buddhism', name: 'Buddhism', emoji: '☸️', color: 'text-yellow-600' },
  { id: 'Judaism', name: 'Judaism', emoji: '✡️', color: 'text-indigo-600' },
  { id: 'Sikhism', name: 'Sikhism', emoji: '☬', color: 'text-purple-600' },
  { id: 'Cultural', name: 'Cultural/Secular', emoji: '🌏', color: 'text-gray-600' }
];

const OBSERVANCE_LEVELS = [
  { id: 'major', name: 'Major Holiday', color: 'bg-red-50 text-red-700 border-red-200' },
  { id: 'minor', name: 'Minor Observance', color: 'bg-yellow-50 text-yellow-700 border-yellow-200' },
  { id: 'regional', name: 'Regional Holiday', color: 'bg-gray-50 text-gray-700 border-gray-200' }
];

const HolidayConfiguration: React.FC<HolidayConfigurationProps> = ({ 
  onSave,
  initialHolidays = [],
  initialWeekendDays = [0, 6] // Default: Saturday and Sunday
}) => {
  const [holidays, setHolidays] = useState<Holiday[]>(initialHolidays);
  const [weekendDays, setWeekendDays] = useState<number[]>(initialWeekendDays);
  const [newHoliday, setNewHoliday] = useState<Omit<Holiday, 'id'>>({
    name: '',
    date: new Date().toISOString().split('T')[0],
    type: 'public',
    description: ''
  });
  const [isAddingHoliday, setIsAddingHoliday] = useState(false);
  const [editingHoliday, setEditingHoliday] = useState<Holiday | null>(null);
  const [showUpcomingOnly, setShowUpcomingOnly] = useState(true);
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [years, setYears] = useState<number[]>([]);

  // Generate a list of years (current year - 1 to current year + 2)
  useEffect(() => {
    const now = new Date().getFullYear();
    setYears([now - 1, now, now + 1, now + 2]);
  }, []);

  const handleWeekendToggle = (dayId: number) => {
    if (weekendDays.includes(dayId)) {
      setWeekendDays(weekendDays.filter(day => day !== dayId));
    } else {
      setWeekendDays([...weekendDays, dayId].sort());
    }
  };

  const handleEditHoliday = (holiday: Holiday) => {
    setEditingHoliday(holiday);
    setNewHoliday({
      name: holiday.name,
      date: holiday.date,
      type: holiday.type,
      description: holiday.description || '',
      metadata: holiday.metadata
    });
    setIsAddingHoliday(true);
  };

  const handleCancelEdit = () => {
    setEditingHoliday(null);
    setIsAddingHoliday(false);
    setNewHoliday({
      name: '',
      date: new Date().toISOString().split('T')[0],
      type: 'public',
      description: ''
    });
  };

  const handleUpdateHoliday = async () => {
    if (!editingHoliday || !newHoliday.name.trim() || !newHoliday.date) {
      toast.error('Please provide holiday name and date');
      return;
    }

    try {
      console.log('Updating holiday:', editingHoliday.id, newHoliday);
      
      const updatedHoliday = { ...newHoliday, id: editingHoliday.id };
      
      // Try to update via API first
      const apiResult = await HolidayService.updateHoliday(editingHoliday.id, newHoliday);
      
      if (apiResult.success && apiResult.data) {
        console.log('Successfully updated holiday via API:', apiResult.data);
        setHolidays(holidays.map(h => h.id === editingHoliday.id ? apiResult.data! : h));
        toast.success(`Holiday "${newHoliday.name}" updated successfully`);
      } else {
        console.log('API update failed, updating locally');
        // Fallback to local update
        setHolidays(holidays.map(h => h.id === editingHoliday.id ? updatedHoliday : h));
        toast.success(`Holiday "${newHoliday.name}" updated locally`);
        toast.error('Could not sync with server - holiday updated locally only');
      }
      
      // Reset form and state
      handleCancelEdit();
      
    } catch (error) {
      console.error('Error updating holiday:', error);
      
      // Emergency fallback - update locally
      const updatedHoliday = { ...newHoliday, id: editingHoliday.id };
      setHolidays(holidays.map(h => h.id === editingHoliday.id ? updatedHoliday : h));
      toast.success(`Holiday "${newHoliday.name}" updated locally`);
      toast.error('Server error - holiday updated locally only');
      
      handleCancelEdit();
    }
  };

  const handleAddHoliday = async () => {
    if (!newHoliday.name.trim() || !newHoliday.date) {
      toast.error('Please provide holiday name and date');
      return;
    }

    // If we're editing, call update instead
    if (editingHoliday) {
      return handleUpdateHoliday();
    }

    try {
      console.log('Creating new holiday:', newHoliday);
      
      // Try to create via API first
      const apiResult = await HolidayService.createHoliday(newHoliday);
      
      if (apiResult.success && apiResult.data) {
        console.log('Successfully created holiday via API:', apiResult.data);
        setHolidays([...holidays, apiResult.data!]);
        toast.success(`Holiday "${newHoliday.name}" added successfully`);
      } else {
        console.log('API creation failed, adding locally');
        // Fallback to local creation
        const id = holidays.length > 0 
          ? Math.max(...holidays.map(h => h.id)) + 1 
          : 1;
        
        const localHoliday = { ...newHoliday, id };
        setHolidays([...holidays, localHoliday]);
        toast.success(`Holiday "${newHoliday.name}" added locally`);
        toast.error('Could not sync with server - holiday saved locally only');
      }
      
      // Reset form
      handleCancelEdit();
      
    } catch (error) {
      console.error('Error creating holiday:', error);
      
      // Emergency fallback - create locally
      const id = holidays.length > 0 
        ? Math.max(...holidays.map(h => h.id)) + 1 
        : 1;
      
      const localHoliday = { ...newHoliday, id };
      setHolidays([...holidays, localHoliday]);
      toast.success(`Holiday "${newHoliday.name}" added locally`);
      toast.error('Server error - holiday saved locally only');
      
      handleCancelEdit();
    }
  };

  const handleDeleteHoliday = async (id: number) => {
    const holidayToDelete = holidays.find(h => h.id === id);
    const holidayName = holidayToDelete?.name || 'Unknown Holiday';
    
    if (!confirm(`Are you sure you want to delete "${holidayName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      console.log('Deleting holiday:', id);
      
      // Try to delete via API first
      const apiResult = await HolidayService.deleteHoliday(id);
      
      if (apiResult.success) {
        console.log('Successfully deleted holiday via API');
        setHolidays(holidays.filter(holiday => holiday.id !== id));
        toast.success(`Holiday "${holidayName}" deleted successfully`);
      } else {
        console.log('API deletion failed, removing locally');
        // Fallback to local deletion
        setHolidays(holidays.filter(holiday => holiday.id !== id));
        toast.success(`Holiday "${holidayName}" removed locally`);
        toast.error('Could not sync with server - holiday removed locally only');
      }
      
    } catch (error) {
      console.error('Error deleting holiday:', error);
      
      // Emergency fallback - delete locally
    setHolidays(holidays.filter(holiday => holiday.id !== id));
      toast.success(`Holiday "${holidayName}" removed locally`);
      toast.error('Server error - holiday removed locally only');
    }
  };

  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setCurrentYear(parseInt(e.target.value));
  };

  const handleSaveConfiguration = async () => {
    try {
      console.log('Saving holiday configuration...');
      
      const config = { holidays, weekendDays };
      
      // Try to save via API first
      const apiResult = await HolidayService.saveHolidayConfiguration(config);
      
      if (apiResult.success) {
        console.log('Successfully saved holiday configuration via API');
        // Remove duplicate toast - parent component will handle success notification
        
        // Call the parent's onSave callback if provided
        if (onSave) {
          onSave(config);
        }
      } else {
        console.log('API save failed, saving locally');
        // Fallback to local save via parent callback
        if (onSave) {
          onSave(config);
        }
        toast.success('Holiday configuration saved locally');
        toast.error('Could not sync with server - configuration saved locally only');
      }
      
    } catch (error) {
      console.error('Error saving holiday configuration:', error);
      
      // Emergency fallback - use parent callback
    if (onSave) {
      onSave({ holidays, weekendDays });
      }
      toast.success('Holiday configuration saved locally');
      toast.error('Server error - configuration saved locally only');
    }
  };

  const filteredHolidays = holidays.filter(holiday => {
    if (!showUpcomingOnly) return true;
    
    const holidayDate = new Date(holiday.date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    return holidayDate >= today;
  }).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  const getHolidayTypeColor = (type: 'public' | 'company' | 'optional') => {
    const typeConfig = HOLIDAY_TYPES.find(t => t.id === type);
    return typeConfig?.color || '';
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm transition-colors duration-200">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <div className="flex items-center">
          <div className="h-8 w-8 rounded-lg bg-blue-600 dark:bg-blue-500 flex items-center justify-center mr-3">
            <CalendarDays className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-800 dark:text-white">Holiday & Weekend Configuration</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">Configure holidays and weekend days for attendance tracking</p>
          </div>
        </div>
        <button 
          onClick={handleSaveConfiguration}
          className="px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-lg text-sm flex items-center hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors duration-200"
        >
          <Save className="h-4 w-4 mr-2" />
          Save Configuration
        </button>
      </div>
      
      <div className="p-6">
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
            <SunMoon className="h-4 w-4 mr-2 text-orange-500 dark:text-orange-400" />
            Weekend Configuration
          </h4>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Select which days of the week should be considered weekends.
            Employees are typically not expected to work on weekend days.
          </p>
          
          <div className="flex flex-wrap gap-2">
            {DAYS_OF_WEEK.map(day => (
              <button
                key={day.id}
                onClick={() => handleWeekendToggle(day.id)}
                className={`py-2 px-4 border text-sm font-medium rounded-lg flex items-center transition-colors duration-200 ${
                  weekendDays.includes(day.id)
                    ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-400 border-orange-200 dark:border-orange-700'
                    : 'bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'
                }`}
              >
                <span>{day.name}</span>
              </button>
            ))}
          </div>
        </div>
        
        <div>
          <div className="flex justify-between items-center mb-3">
            <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-red-500 dark:text-red-400" />
              Holidays Configuration
            </h4>
            <div className="flex items-center space-x-4">
              <select
                value={currentYear}
                onChange={handleYearChange}
                className="text-sm border border-gray-300 dark:border-gray-600 rounded-md py-1 px-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors duration-200"
              >
                {years.map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="upcoming-only"
                  checked={showUpcomingOnly}
                  onChange={() => setShowUpcomingOnly(!showUpcomingOnly)}
                  className="h-4 w-4 border-gray-300 dark:border-gray-600 rounded text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 mr-2"
                />
                <label htmlFor="upcoming-only" className="text-sm text-gray-600 dark:text-gray-400">
                  Show upcoming only
                </label>
              </div>
              
              <button
                onClick={() => setIsAddingHoliday(true)}
                className="py-1 px-3 bg-green-600 dark:bg-green-500 text-white rounded-lg text-sm flex items-center hover:bg-green-700 dark:hover:bg-green-600 transition-colors duration-200"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Holiday
              </button>
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4 transition-colors duration-200">
            <div className="flex space-x-3 mb-2">
              {HOLIDAY_TYPES.map(type => (
                <div key={type.id} className="flex items-center">
                  <span className={`inline-block h-3 w-3 rounded-full ${type.color.split(' ')[0]} ${type.color.includes('dark:') ? type.color.split(' ').find(c => c.startsWith('dark:bg-'))?.replace('dark:bg-', 'dark:bg-') || '' : ''}`}></span>
                  <span className="text-xs text-gray-600 dark:text-gray-400 ml-1">{type.name}</span>
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Configure all holidays for attendance calculations and reporting.
              Public holidays are typically mandatory, while optional holidays can be taken based on employee preference.
            </p>
          </div>
          
          {isAddingHoliday && (
            <div className="mb-4 border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-blue-50 dark:bg-blue-900/30 transition-colors duration-200">
              <div className="flex justify-between items-center mb-3">
                <h5 className="text-sm font-medium text-blue-800 dark:text-blue-300">
                  {editingHoliday ? 'Edit Holiday' : 'Add New Holiday'}
                </h5>
                <button onClick={handleCancelEdit} className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors duration-200">
                  <X className="h-4 w-4" />
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Holiday Name*
                  </label>
                  <input
                    type="text"
                    value={newHoliday.name}
                    onChange={(e) => setNewHoliday({...newHoliday, name: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-md py-2 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200"
                    placeholder="E.g., Eid al-Fitr, Diwali, Christmas"
                  />
                </div>
                
                <div>
                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Date*
                  </label>
                  <input
                    type="date"
                    value={newHoliday.date}
                    onChange={(e) => setNewHoliday({...newHoliday, date: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-md py-2 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors duration-200"
                  />
                </div>
                
                <div>
                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Holiday Type
                  </label>
                  <select
                    value={newHoliday.type}
                    onChange={(e) => setNewHoliday({...newHoliday, type: e.target.value as 'public' | 'company' | 'optional'})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-md py-2 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors duration-200"
                  >
                    {HOLIDAY_TYPES.map(type => (
                      <option key={type.id} value={type.id}>{type.name}</option>
                    ))}
                  </select>
                </div>
                
                {newHoliday.type === 'optional' && (
                <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Religion/Culture
                    </label>
                    <select
                      value={newHoliday.metadata?.religion || ''}
                      onChange={(e) => setNewHoliday({
                        ...newHoliday, 
                        metadata: { ...newHoliday.metadata, religion: e.target.value }
                      })}
                      className="w-full border border-gray-300 dark:border-gray-600 rounded-md py-2 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors duration-200"
                    >
                      <option value="">Select Religion/Culture</option>
                      {RELIGIONS.map(religion => (
                        <option key={religion.id} value={religion.id}>
                          {religion.emoji} {religion.name}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
                
                <div className="md:col-span-2">
                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Description
                  </label>
                  <input
                    type="text"
                    value={newHoliday.description || ''}
                    onChange={(e) => setNewHoliday({...newHoliday, description: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-md py-2 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200"
                    placeholder="Brief description of the holiday"
                  />
                </div>

                {newHoliday.type === 'optional' && newHoliday.metadata?.religion && (
                  <>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Observance Level
                      </label>
                      <select
                        value={newHoliday.metadata?.observanceLevel || ''}
                        onChange={(e) => setNewHoliday({
                          ...newHoliday,
                          metadata: { ...newHoliday.metadata, observanceLevel: e.target.value as 'major' | 'minor' | 'regional' }
                        })}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-md py-2 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors duration-200"
                      >
                        <option value="">Select Level</option>
                        {OBSERVANCE_LEVELS.map(level => (
                          <option key={level.id} value={level.id}>{level.name}</option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Duration (Days)
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="10"
                        value={newHoliday.metadata?.duration || 1}
                        onChange={(e) => setNewHoliday({
                          ...newHoliday,
                          metadata: { ...newHoliday.metadata, duration: parseInt(e.target.value) }
                        })}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-md py-2 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors duration-200"
                      />
                    </div>
                  </>
                )}
              </div>
              
              <div className="flex justify-end">
                <button
                  onClick={handleCancelEdit}
                  className="mr-2 px-3 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddHoliday}
                  className="px-3 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-md text-sm hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors duration-200"
                >
                  {editingHoliday ? 'Update Holiday' : 'Add Holiday'}
                </button>
              </div>
            </div>
          )}
          
          <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Holiday Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Type & Religion
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Description & Details
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredHolidays.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="px-6 py-8 text-center text-sm text-gray-500 dark:text-gray-400">
                      <CalendarIcon className="h-10 w-10 mx-auto text-gray-300 dark:text-gray-600 mb-2" />
                      <p className="font-medium text-gray-600 dark:text-gray-300">No holidays configured</p>
                      <p className="text-gray-500 dark:text-gray-400 text-xs mt-1">Click "Add Holiday" to create your first holiday</p>
                    </td>
                  </tr>
                ) : (
                  filteredHolidays.map(holiday => {
                    const religion = RELIGIONS.find(r => r.id === holiday.metadata?.religion);
                    const observanceLevel = OBSERVANCE_LEVELS.find(l => l.id === holiday.metadata?.observanceLevel);
                    
                    return (
                    <tr key={holiday.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {religion && (
                              <span className="text-lg mr-2" title={religion.name}>
                                {religion.emoji}
                              </span>
                            )}
                            <div>
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {holiday.name}
                              </div>
                              {holiday.metadata?.duration && holiday.metadata.duration > 1 && (
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  {holiday.metadata.duration} days
                                </div>
                              )}
                            </div>
                          </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          <div>
                        {new Date(holiday.date).toLocaleDateString('en-US', {
                              weekday: 'short',
                              month: 'short',
                          day: 'numeric'
                        })}
                          </div>
                          <div className="text-xs text-gray-400 dark:text-gray-500">
                            {new Date(holiday.date).getFullYear()}
                          </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                          <div className="space-y-1">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getHolidayTypeColor(holiday.type)}`}>
                          {HOLIDAY_TYPES.find(t => t.id === holiday.type)?.name}
                        </span>
                            {religion && (
                              <div className="flex items-center">
                                <span className={`text-xs font-medium ${religion.color}`}>
                                  {religion.name}
                                </span>
                              </div>
                            )}
                            {observanceLevel && (
                              <span className={`inline-block px-2 py-0.5 text-xs font-medium rounded-full border ${observanceLevel.color}`}>
                                {observanceLevel.name}
                              </span>
                            )}
                          </div>
                      </td>
                        <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                          <div>
                        {holiday.description || '-'}
                          </div>
                          {holiday.metadata?.culturalSignificance && (
                            <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                              {holiday.metadata.culturalSignificance}
                            </div>
                          )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                          <div className="flex items-center justify-end space-x-2">
                            <button
                              onClick={() => handleEditHoliday(holiday)}
                              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200"
                              title="Edit holiday"
                            >
                              <Edit2 className="h-4 w-4" />
                            </button>
                        <button
                          onClick={() => handleDeleteHoliday(holiday.id)}
                          className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 transition-colors duration-200"
                              title="Delete holiday"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                          </div>
                      </td>
                    </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HolidayConfiguration; 