import { Request, Response } from 'express';
import { getRepository } from 'typeorm';
import { Role, RoleCategory } from '../../entities/Role';
import { UserRoleAssignment } from '../../entities/UserRoleAssignment';
import { User } from '../../entities/User';
import { AppDataSource } from '../../config/database';

// Get all roles with pagination and filtering
export const getRoles = async (req: Request, res: Response) => {
  try {
    // Check if database is connected
    if (!AppDataSource.isInitialized) {
      console.error('Database connection not initialized');
      return res.status(500).json({ 
        error: 'Database connection error', 
        message: 'Database connection not initialized'
      });
    }
    
    const roleRepository = AppDataSource.getRepository(Role);
    const category = req.query.category as RoleCategory | undefined;
    
    try {
      let query = roleRepository.createQueryBuilder('role')
        .leftJoinAndSelect('role.parentRole', 'parentRole');
      
      // Apply category filter if provided
      if (category) {
        query = query.where('role.category = :category', { category });
      }
      
      // Apply search filter if provided
      if (req.query.search) {
        const search = `%${req.query.search}%`;
        query = query.andWhere('(role.name LIKE :search OR role.description LIKE :search)', { search });
      }
      
      const roles = await query.getMany();
      
      return res.json({ roles });
    } catch (dbError: any) {
      console.error('Database query error:', dbError);
      return res.status(500).json({ 
        error: 'Database query error', 
        message: dbError.message 
      });
    }
  } catch (error: any) {
    console.error('Error fetching roles:', error);
    return res.status(500).json({ 
      message: 'Server error while fetching roles',
      error: error.message 
    });
  }
}; 