import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import { getRepository } from 'typeorm';
import { User } from '../entities/User';
import { UserRole, UserPermissions } from '../types/common';
import bcrypt from 'bcryptjs';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const COOKIE_NAME = 'auth_token';

export const register = async (req: Request, res: Response) => {
  try {
    const { name, email, password } = req.body;

    if (!name || !email || !password) {
      return res.status(400).json({ message: 'Name, email and password are required' });
    }

    const userRepository = getRepository(User);
    
    // Check if user already exists
    const existingUser = await userRepository.findOne({ where: { email } });
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // Create admin user permissions
    const adminPermissions: UserPermissions = {
      canCreateTickets: true,
      canCreateTicketsForOthers: true,
      canEditTickets: true,
      canDeleteTickets: true,
      canAssignTickets: true,
      canEscalateTickets: true,
      canViewAllTickets: true,
      department: 'IT Department',
      isAdmin: true
    };

    // Create new user with IT_ADMIN role
    const user = userRepository.create({
      name,
      email,
      password,
      role: UserRole.IT_ADMIN,
      department: 'IT Department',
      isActive: true,
      permissions: adminPermissions,
    });

    await userRepository.save(user);

    // Create JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Set HTTP-only cookie
    res.cookie(COOKIE_NAME, token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    });

    // Return user data (excluding password)
    const { password: _, ...userData } = user;
    return res.status(201).json(userData);
  } catch (error) {
    console.error('Registration error:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

export const login = async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ message: 'Email and password are required' });
    }

    const userRepository = getRepository(User);
    const user = await userRepository.findOne({ where: { email } });

    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Create JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Set HTTP-only cookie
    res.cookie(COOKIE_NAME, token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    });

    // Return user data (excluding password)
    const { password: _, ...userData } = user;
    return res.json(userData);
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

export const logout = (_req: Request, res: Response) => {
  res.clearCookie(COOKIE_NAME);
  return res.json({ message: 'Logged out successfully' });
};

export const getCurrentUser = async (req: Request, res: Response) => {
  try {
    const token = req.cookies[COOKIE_NAME];
    
    if (!token) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string };
    const userRepository = getRepository(User);
    const user = await userRepository.findOne({ where: { id: decoded.userId } });

    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    const { password: _, ...userData } = user;
    return res.json(userData);
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json({ message: 'Invalid token' });
    }
    console.error('Get current user error:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

// Add token refresh function
export const refreshToken = async (req: Request, res: Response) => {
  try {
    // Check for token in cookies first
    let token = req.cookies[COOKIE_NAME];
    
    // Then check Authorization header
    if (!token && req.headers.authorization) {
      const authHeader = req.headers.authorization;
      if (authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7);
      }
    }
    
    if (!token) {
      return res.status(401).json({ 
        message: 'No token provided for refresh' 
      });
    }
    
    try {
      // Verify the token - allowing expired tokens for refresh
      const decoded = jwt.verify(token, JWT_SECRET, { ignoreExpiration: true }) as { 
        userId: string;
        email: string;
        exp: number;
      };
      
      // Check if token is within a reasonable refresh window (e.g., expired less than 7 days ago)
      const currentTime = Math.floor(Date.now() / 1000);
      const tokenExpiry = decoded.exp;
      const refreshWindow = 7 * 24 * 60 * 60; // 7 days
      
      if (currentTime - tokenExpiry > refreshWindow) {
        return res.status(401).json({ 
          message: 'Token expired beyond refresh window' 
        });
      }
      
      // Find the user
      const userRepository = getRepository(User);
      const user = await userRepository.findOne({ where: { id: decoded.userId } });
      
      if (!user) {
        return res.status(401).json({ message: 'User not found' });
      }
      
      // Generate new token
      const newToken = jwt.sign(
        { userId: user.id, email: user.email },
        JWT_SECRET,
        { expiresIn: '24h' } // Token valid for 24 hours
      );
      
      // Set HTTP-only cookie
      res.cookie(COOKIE_NAME, newToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      });
      
      // Remove password from user data
      const { password: _, ...userData } = user;
      
      // Return the user data and new token
      return res.json({
        message: 'Token refreshed successfully',
        token: newToken,
        user: userData
      });
    } catch (error) {
      console.error('Token refresh error:', error);
      return res.status(401).json({ 
        message: 'Invalid token for refresh',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  } catch (error) {
    console.error('Auth refresh error:', error);
    return res.status(500).json({ 
      message: 'Server error during token refresh',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}; 