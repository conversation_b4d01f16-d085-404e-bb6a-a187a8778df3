import React from 'react';
import { Lock, User } from 'lucide-react';
import { Ticket } from '../../types/common';

interface TicketLockStatusProps {
  ticket: Ticket;
  currentUserId: string;
}

export const TicketLockStatus: React.FC<TicketLockStatusProps> = ({
  ticket,
  currentUserId
}) => {
  if (!ticket.lockedById) {
    return null;
  }

  const isLockedByCurrentUser = ticket.lockedById === currentUserId;
  
  return (
    <div className={`flex items-center gap-1 text-xs ${isLockedByCurrentUser ? 'text-amber-600' : 'text-gray-500'}`}>
      {isLockedByCurrentUser ? (
        <>
          <Lock className="h-3 w-3" />
          <span>You are working on this</span>
        </>
      ) : (
        <>
          <User className="h-3 w-3" />
          <span>
            {ticket.lockedBy?.name} is working on this
          </span>
        </>
      )}
    </div>
  );
}; 