import api, { safelyHandleResponse } from './api';
import { EmployeeForm } from '../pages/HR/AddEmployeePage';

class EmployeeService {
  private baseUrl = '/api/employees';

  /**
   * Create a new employee
   * @param employeeData The employee data to create
   * @returns Promise with the created employee data or error
   */
  async createEmployee(employeeData: EmployeeForm) {
    console.log('Sending API request to create employee:', this.baseUrl);
    try {
      return await safelyHandleResponse(api.post(this.baseUrl, employeeData));
    } catch (error) {
      console.error('Error creating employee:', error);
      return { data: null, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Update an existing employee
   * @param id The ID of the employee to update
   * @param employeeData The updated employee data
   * @returns Promise with the updated employee data or error
   */
  async updateEmployee(id: string, employeeData: Partial<EmployeeForm>) {
    return safelyHandleResponse(api.put(`${this.baseUrl}/${id}`, employeeData));
  }

  /**
   * Get an employee by ID
   * @param id The ID of the employee to retrieve
   * @param params Optional query parameters
   * @returns Promise with the employee data or error
   */
  async getEmployee(id: string, params = {}) {
    console.log(`Fetching employee with ID: ${id}`, params);
    try {
      // Make sure we're requesting the full employee data with relations
      const response = await safelyHandleResponse(api.get(`${this.baseUrl}/${id}`, {
        params: { includeRelations: true, ...params }
      }));
      
      // Log the response structure for debugging
      if (response.data) {
        console.log(`Received employee data for ID ${id} with structure:`, 
                   Object.keys(response.data.employee || {}));
        
        // Check if we have the expected nested objects
        const employee = response.data.employee || {};
        console.log('Contact data present:', !!employee.contact);
        console.log('Job data present:', !!employee.job);
      }
      
      return response;
    } catch (error) {
      console.error(`Error fetching employee with ID ${id}:`, error);
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Get a list of employees with optional filters
   * @param filters Optional query parameters for filtering employees
   * @returns Promise with the list of employees or error
   */
  async getEmployees(filters = {}) {
    console.log('Getting employees from API with filters:', filters);
    const url = '/api/employees/listing';
    
    // Add cache-busting timestamp to ensure we get fresh data
    const enhancedFilters = {
      ...filters,
      timestamp: new Date().getTime() // Add timestamp for cache busting
    };
    
    console.log('Full API URL for employee listing:', url, 'with params:', enhancedFilters);
    
    try {
      const response = await safelyHandleResponse(api.get(url, { params: enhancedFilters }));
      console.log('Employee API response received:', response);
      return response;
    } catch (error) {
      console.error('Error in getEmployees method:', error);
      return { data: null, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Delete an employee
   * @param id The ID of the employee to delete
   * @returns Promise with the deletion result or error
   */
  async deleteEmployee(id: string) {
    return safelyHandleResponse(api.delete(`${this.baseUrl}/${id}`));
  }

  /**
   * Upload employee documents
   * @param employeeId The ID of the employee
   * @param formData FormData containing the files to upload
   * @returns Promise with the upload result or error
   */
  async uploadEmployeeDocuments(employeeId: string, formData: FormData) {
    console.log('Uploading documents for employee ID:', employeeId);
    try {
      return await safelyHandleResponse(
        api.post(`${this.baseUrl}/${employeeId}/documents`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
      );
    } catch (error) {
      console.error('Error uploading employee documents:', error);
      return { data: null, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Import employees in bulk
   * @param employeeData Array of employee data to import
   * @returns Promise with the import result or error
   */
  async importEmployees(employeeData: any[]) {
    console.log('Importing employees, count:', employeeData.length);
    const dataSize = JSON.stringify({ employees: employeeData }).length / 1024; // Size in KB
    console.log(`Import data size: ${dataSize.toFixed(2)} KB`);
    
    // Warn if the data size is approaching the limit
    if (dataSize > 40000) { // 40MB
      console.warn('Warning: Import data size is very large and may exceed server limits');
    }
    
    try {
      return await safelyHandleResponse(
        api.post(`${this.baseUrl}/import`, { employees: employeeData }, {
          timeout: 120000, // Increase timeout for large imports (2 minutes)
          maxContentLength: 52428800, // 50MB
          maxBodyLength: 52428800 // 50MB
        })
      );
    } catch (error) {
      console.error('Error importing employees:', error);
      
      // Check if it's a specific type of error
      const axiosError = error as any;
      if (axiosError.code === 'ECONNABORTED') {
        return { data: null, error: 'Request timed out. Your import file may be too large.' };
      }
      
      if (axiosError.response?.status === 413) {
        return { data: null, error: 'The import file is too large. Please reduce the file size or split into multiple files.' };
      }
      
      return { data: null, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
}

export default new EmployeeService(); 