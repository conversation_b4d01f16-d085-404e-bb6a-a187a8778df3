import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Grid,
  Tooltip,
  Avatar,
  Rating
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Edit as EditIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Work as WorkIcon,
  Schedule as ScheduleIcon,
  MoreVert as MoreVertIcon,
  Search as SearchIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { recruitmentAPI } from '../../../../services/recruitmentAPI';

interface JobApplication {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  status: string;
  source: string;
  rating: number;
  isStarred: boolean;
  createdAt: string;
  expectedSalary?: number;
  salaryCurrency?: string;
  totalExperienceYears?: number;
  jobPosting: {
    id: number;
    title: string;
    department: string;
  };
  assignedTo?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  interviewCount?: number;
  evaluationCount?: number;
}

const JobApplicationsList: React.FC = () => {
  const navigate = useNavigate();
  const [applications, setApplications] = useState<JobApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [sourceFilter, setSourceFilter] = useState('');
  const [starredFilter, setStarredFilter] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedApplication, setSelectedApplication] = useState<JobApplication | null>(null);

  useEffect(() => {
    fetchApplications();
  }, [page, rowsPerPage, searchTerm, statusFilter, sourceFilter, starredFilter]);

  const fetchApplications = async () => {
    try {
      setLoading(true);
      const params = {
        page: page + 1,
        limit: rowsPerPage,
        search: searchTerm || undefined,
        status: statusFilter || undefined,
        source: sourceFilter || undefined,
        isStarred: starredFilter === 'true' ? true : starredFilter === 'false' ? false : undefined,
        sortBy: 'createdAt',
        sortOrder: 'DESC'
      };

      const response = await recruitmentAPI.getJobApplications(params);
      setApplications(response.data.applications);
      setTotal(response.data.pagination.total);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch job applications');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, application: JobApplication) => {
    setAnchorEl(event.currentTarget);
    setSelectedApplication(application);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedApplication(null);
  };

  const handleToggleStar = async (application: JobApplication) => {
    try {
      await recruitmentAPI.toggleApplicationStar(application.id);
      await fetchApplications();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to update application');
    }
  };

  const handleStatusChange = async (applicationId: number, newStatus: string) => {
    try {
      await recruitmentAPI.updateApplicationStatus(applicationId, newStatus);
      await fetchApplications();
      handleMenuClose();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to update application status');
    }
  };

  const getStatusColor = (status: string) => {
    const statusColors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      submitted: 'info',
      under_review: 'warning',
      screening: 'primary',
      interview_scheduled: 'primary',
      interviewing: 'primary',
      technical_assessment: 'secondary',
      reference_check: 'secondary',
      offer_pending: 'warning',
      offer_extended: 'success',
      offer_accepted: 'success',
      hired: 'success',
      rejected: 'error',
      withdrawn: 'default',
      on_hold: 'warning'
    };
    return statusColors[status] || 'default';
  };

  const getSourceColor = (source: string) => {
    const sourceColors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      company_website: 'primary',
      job_board: 'secondary',
      linkedin: 'info',
      referral: 'success',
      recruiter: 'warning',
      social_media: 'secondary',
      career_fair: 'info',
      direct_application: 'primary',
      other: 'default'
    };
    return sourceColors[source] || 'default';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  if (loading && applications.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Job Applications
        </Typography>
        <Button
          variant="outlined"
          startIcon={<FilterIcon />}
          onClick={() => navigate('/hr/recruitment/applications/pipeline')}
        >
          View Pipeline
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Name, email, or position..."
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  label="Status"
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  <MenuItem value="submitted">Submitted</MenuItem>
                  <MenuItem value="under_review">Under Review</MenuItem>
                  <MenuItem value="interviewing">Interviewing</MenuItem>
                  <MenuItem value="offer_pending">Offer Pending</MenuItem>
                  <MenuItem value="hired">Hired</MenuItem>
                  <MenuItem value="rejected">Rejected</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Source</InputLabel>
                <Select
                  value={sourceFilter}
                  label="Source"
                  onChange={(e) => setSourceFilter(e.target.value)}
                >
                  <MenuItem value="">All Sources</MenuItem>
                  <MenuItem value="company_website">Company Website</MenuItem>
                  <MenuItem value="job_board">Job Board</MenuItem>
                  <MenuItem value="linkedin">LinkedIn</MenuItem>
                  <MenuItem value="referral">Referral</MenuItem>
                  <MenuItem value="recruiter">Recruiter</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Starred</InputLabel>
                <Select
                  value={starredFilter}
                  label="Starred"
                  onChange={(e) => setStarredFilter(e.target.value)}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="true">Starred Only</MenuItem>
                  <MenuItem value="false">Not Starred</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FilterIcon />}
                onClick={fetchApplications}
              >
                Apply Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Applications Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Candidate</TableCell>
                <TableCell>Position</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Source</TableCell>
                <TableCell>Rating</TableCell>
                <TableCell>Applied</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {applications.map((application) => (
                <TableRow key={application.id} hover>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                        {getInitials(application.firstName, application.lastName)}
                      </Avatar>
                      <Box>
                        <Box display="flex" alignItems="center">
                          <Typography variant="subtitle2">
                            {application.firstName} {application.lastName}
                          </Typography>
                          <IconButton
                            size="small"
                            onClick={() => handleToggleStar(application)}
                            sx={{ ml: 1 }}
                          >
                            {application.isStarred ? (
                              <StarIcon color="warning" fontSize="small" />
                            ) : (
                              <StarBorderIcon fontSize="small" />
                            )}
                          </IconButton>
                        </Box>
                        <Typography variant="body2" color="textSecondary">
                          <EmailIcon fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                          {application.email}
                        </Typography>
                        {application.phone && (
                          <Typography variant="body2" color="textSecondary">
                            <PhoneIcon fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                            {application.phone}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {application.jobPosting.title}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      {application.jobPosting.department}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={application.status.replace(/_/g, ' ').toUpperCase()}
                      color={getStatusColor(application.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={application.source.replace(/_/g, ' ').toUpperCase()}
                      color={getSourceColor(application.source)}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Rating
                      value={application.rating}
                      readOnly
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {formatDate(application.createdAt)}
                    </Typography>
                    {application.expectedSalary && (
                      <Typography variant="caption" color="textSecondary">
                        Expected: {application.salaryCurrency || '$'}{application.expectedSalary.toLocaleString()}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell align="right">
                    <Tooltip title="View Details">
                      <IconButton
                        size="small"
                        onClick={() => navigate(`/hr/recruitment/applications/${application.id}`)}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Schedule Interview">
                      <IconButton
                        size="small"
                        onClick={() => navigate(`/hr/recruitment/applications/${application.id}/interview`)}
                      >
                        <ScheduleIcon />
                      </IconButton>
                    </Tooltip>
                    <IconButton
                      size="small"
                      onClick={(e) => handleMenuClick(e, application)}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={total}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleStatusChange(selectedApplication?.id || 0, 'under_review')}>
          Move to Review
        </MenuItem>
        <MenuItem onClick={() => handleStatusChange(selectedApplication?.id || 0, 'interview_scheduled')}>
          Schedule Interview
        </MenuItem>
        <MenuItem onClick={() => handleStatusChange(selectedApplication?.id || 0, 'rejected')}>
          Reject Application
        </MenuItem>
        <MenuItem onClick={() => navigate(`/hr/recruitment/applications/${selectedApplication?.id}/edit`)}>
          <EditIcon sx={{ mr: 1 }} />
          Edit Application
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default JobApplicationsList;
