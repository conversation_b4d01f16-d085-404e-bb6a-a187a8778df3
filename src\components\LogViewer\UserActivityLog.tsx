import React, { useState, useMemo } from 'react';
import { 
  Activity, Clock, XCircle, AlertTriangle, CheckCircle, 
  Filter, ChevronLeft, ChevronRight, Search, X
} from 'lucide-react';
import { SystemLog } from './types';
import { formatSystemDate } from './utils/dateFormat';

interface UserActivityLogProps {
  logs: SystemLog[];
  username: string;
  onViewLogDetails: (log: SystemLog) => void;
}

export const UserActivityLog: React.FC<UserActivityLogProps> = ({ 
  logs, 
  username, 
  onViewLogDetails 
}) => {
  // State for pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [logsPerPage, setLogsPerPage] = useState(15);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'info' | 'success' | 'warning' | 'error'>('all');

  // Filter logs by user and search/type filters
  const filteredLogs = useMemo(() => {
    return logs
      .filter(log => log.user === username)
      .filter(log => {
        if (!searchTerm) return true;
        
        const searchLower = searchTerm.toLowerCase();
        return (
          log.action.toLowerCase().includes(searchLower) ||
          log.details.toLowerCase().includes(searchLower)
        );
      })
      .filter(log => {
        if (filterType === 'all') return true;
        return log.type === filterType;
      });
  }, [logs, username, searchTerm, filterType]);

  // Sort logs by timestamp (newest first)
  const sortedLogs = useMemo(() => {
    return [...filteredLogs].sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }, [filteredLogs]);

  // Paginate logs
  const paginatedLogs = useMemo(() => {
    const startIdx = (currentPage - 1) * logsPerPage;
    return sortedLogs.slice(startIdx, startIdx + logsPerPage);
  }, [sortedLogs, currentPage, logsPerPage]);

  // Calculate total pages
  const totalPages = Math.max(1, Math.ceil(sortedLogs.length / logsPerPage));

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  // Clear filters
  const clearFilters = () => {
    setSearchTerm('');
    setFilterType('all');
  };

  // Get the range of logs being displayed
  const getDisplayRange = () => {
    const start = (currentPage - 1) * logsPerPage + 1;
    const end = Math.min(currentPage * logsPerPage, sortedLogs.length);
    return `${start}-${end} of ${sortedLogs.length}`;
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-3 flex items-center gap-2">
        <Activity className="h-5 w-5 text-blue-500" />
        All User Activity
      </h3>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-3 mb-4">
        <div className="relative flex-grow">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search in activity logs..."
            className="block w-full pl-10 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:ring-blue-500 focus:border-blue-500 py-2"
          />
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>

        <div className="flex space-x-2">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as any)}
            className="block px-3 py-2 text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:ring-blue-500 focus:border-blue-500 shadow-sm"
          >
            <option value="all">All Types</option>
            <option value="info">Info</option>
            <option value="success">Success</option>
            <option value="warning">Warning</option>
            <option value="error">Error</option>
          </select>

          <select
            value={logsPerPage}
            onChange={(e) => setLogsPerPage(Number(e.target.value))}
            className="block px-3 py-2 text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:ring-blue-500 focus:border-blue-500 shadow-sm"
          >
            <option value={10}>10 per page</option>
            <option value={15}>15 per page</option>
            <option value={25}>25 per page</option>
            <option value={50}>50 per page</option>
            <option value={100}>100 per page</option>
          </select>

          {(searchTerm || filterType !== 'all') && (
            <button
              onClick={clearFilters}
              className="flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </button>
          )}
        </div>
      </div>

      {/* Activity Log Table */}
      {paginatedLogs.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Details
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {paginatedLogs.map(log => (
                <tr 
                  key={log.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors cursor-pointer"
                  onClick={() => onViewLogDetails(log)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {log.type === 'info' && <Activity className="h-5 w-5 text-blue-500 mr-2" />}
                      {log.type === 'success' && <CheckCircle className="h-5 w-5 text-green-500 mr-2" />}
                      {log.type === 'warning' && <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2" />}
                      {log.type === 'error' && <XCircle className="h-5 w-5 text-red-500 mr-2" />}
                      <span className="capitalize text-sm text-gray-900 dark:text-gray-100">
                        {log.type}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 font-medium">
                    {log.action}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1 text-gray-400" />
                      {formatSystemDate(log.timestamp)}
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400 max-w-md truncate">
                    {log.details}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-8 bg-gray-50 dark:bg-gray-700/30 rounded-lg border border-gray-200 dark:border-gray-700">
          <Activity className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <h3 className="text-lg font-medium text-gray-600 dark:text-gray-300">No activity logs found</h3>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            {searchTerm || filterType !== 'all' 
              ? 'Try changing your search or filter criteria' 
              : 'No activity has been recorded for this user yet'}
          </p>
        </div>
      )}

      {/* Pagination */}
      {sortedLogs.length > logsPerPage && (
        <div className="flex items-center justify-between mt-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3">
          <div className="text-sm text-gray-700 dark:text-gray-300">
            Showing <span className="font-medium">{getDisplayRange()}</span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="inline-flex items-center px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <span className="text-sm text-gray-700 dark:text-gray-300">
              Page {currentPage} of {totalPages}
            </span>
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="inline-flex items-center px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}; 