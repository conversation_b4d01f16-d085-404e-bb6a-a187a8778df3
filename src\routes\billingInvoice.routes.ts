import express from 'express';
import { BillingInvoiceController } from '../controllers/BillingInvoiceController';
import { requireAuth } from '../middleware/auth';

const router = express.Router();

// Apply auth middleware to all routes
router.use(requireAuth);

// GET all invoices with pagination, search, and filters
router.get('/', BillingInvoiceController.getAllInvoices);

// GET invoice by ID
router.get('/:id', BillingInvoiceController.getInvoiceById);

// CREATE a new invoice
router.post('/', BillingInvoiceController.createInvoice);

// UPDATE an existing invoice
router.put('/:id', BillingInvoiceController.updateInvoice);

// DELETE an invoice
router.delete('/:id', BillingInvoiceController.deleteInvoice);

// UPDATE approval status
router.patch('/:id/approval', BillingInvoiceController.updateApprovalStatus);

export default router; 