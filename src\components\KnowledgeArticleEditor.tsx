import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import api from '../services/api';
import { KnowledgeStatus } from '../entities/KnowledgeBase';
import { Save, X, Image, HelpCircle, Eye, ArrowLeft, FileText, Tag, CheckCircle, AlertTriangle, Bug, RefreshCw, ZoomIn } from 'lucide-react';
import DOMPurify from 'dompurify';
import { toast } from 'react-hot-toast';
import KnowledgeRichTextEditor from './KnowledgeRichTextEditor';
import ImageViewer from './ImageViewer';

interface KnowledgeArticle {
  id?: string;
  title: string;
  content: string;
  summary?: string;
  status: KnowledgeStatus;
  isFeatured: boolean;
  categoryId: string;
  tags: string[];
}

interface Category {
  id: string;
  name: string;
}

interface UploadedImage {
  fileName: string;
  fileUrl: string;
  fileType: string;
  size: number;
}

// Add CSS styles for article images in preview mode
const articleStyles = `
  .article-content {
    color: #374151;
    line-height: 1.8;
    font-size: 1.0625rem;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-width: 800px;
    margin: 0 auto;
  }
  
  .article-content p {
    margin-bottom: 1.5rem;
    color: #4b5563;
    min-height: 1.5em;
  }
  
  .article-content p + p {
    margin-top: 1em;
  }
  
  .article-content br {
    display: block;
    content: "";
    margin-top: 0.5em;
  }
  
  .article-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-top: 3rem;
    margin-bottom: 1.5rem;
    color: #111827;
    line-height: 1.2;
    letter-spacing: -0.025em;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.5rem;
  }
  
  .article-content h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-top: 2.5rem;
    margin-bottom: 1.25rem;
    color: #1f2937;
    line-height: 1.3;
    letter-spacing: -0.025em;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.5rem;
  }
  
  .article-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #1f2937;
    line-height: 1.4;
    letter-spacing: -0.025em;
    background-color: #f9fafb;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid #3b82f6;
  }
  
  .article-content strong {
    color: #111827;
    font-weight: 700;
  }
  
  .article-content a {
    color: #2563eb;
    text-decoration: none;
    border-bottom: 1px solid #2563eb;
  }
  
  .article-content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.375rem;
    margin: 1.5rem 0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    display: block;
  }
  
  .article-content ul, .article-content ol {
    margin: 1.5rem 0;
    padding-left: 1.25rem;
    color: #4b5563;
  }
  
  .article-content li {
    margin: 0.75rem 0;
    padding-left: 0.5rem;
  }
  
  .article-content blockquote {
    border-left: 4px solid #3b82f6;
    padding: 1.25rem 1.5rem;
    margin: 2rem 0;
    background: #f3f4f6;
    border-radius: 0.75rem;
    font-style: italic;
    color: #374151;
  }
  
  .article-content code {
    background: #f3f4f6;
    padding: 0.2rem 0.4rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #1f2937;
    font-family: monospace;
    border: 1px solid #e5e7eb;
  }
  
  .article-content pre {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 0.75rem;
    overflow-x: auto;
    margin: 2rem 0;
    border: 1px solid #e5e7eb;
  }
`;

const KnowledgeArticleEditor: React.FC = () => {
  const { id: paramId } = useParams<{ id: string }>();
  
  // Extract ID from URL if useParams doesn't work (similar to KnowledgeArticleView)
  const getArticleId = () => {
    if (paramId) {
      return paramId;
    }
    
    // Fallback: extract from URL path, but exclude "create" and "edit" keywords
    const pathParts = window.location.pathname.split('/');
    const lastPart = pathParts[pathParts.length - 1];
    
    // Don't treat "create" or "edit" as article IDs
    if (lastPart === 'create' || lastPart === 'edit') {
      return null;
    }
    
    // Check if it's an edit URL pattern like /edit/[id]
    if (pathParts.length >= 2 && pathParts[pathParts.length - 2] === 'edit') {
      return lastPart;
    }
    
    return null;
  };
  
  const id = getArticleId();
  const location = useLocation();
  
  const navigate = useNavigate();
  
  // Show the full editor (disable the simple version)
  const showFullEditor = true;
  
  const [article, setArticle] = useState<KnowledgeArticle>({
    title: '',
    content: '',
    summary: '',
    status: KnowledgeStatus.DRAFT,
    isFeatured: false,
    categoryId: '',
    tags: []
  });
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [previewMode, setPreviewMode] = useState<boolean>(false);
  const [renderedContent, setRenderedContent] = useState<string>('');
  const [tagInput, setTagInput] = useState<string>('');
  const [uploadingImage, setUploadingImage] = useState<boolean>(false);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);
  const [selectedImage, setSelectedImage] = useState<UploadedImage | null>(null);

  // Add hardcoded categories since database was reset
  const predefinedCategories = [
    { id: "cat1", name: "Getting Started" },
    { id: "cat2", name: "My Account & Settings" },
    { id: "cat3", name: "Company Profile" },
    { id: "cat4", name: "Standard Operating Procedures (SOPs)" },
    { id: "cat6", name: "Internet & Network Access" },
    { id: "cat7", name: "Software Usage" },
    { id: "cat11", name: "Email & Communication Tools" },
    { id: "cat10", name: "IT Support & Ticketing" },
    { id: "cat13", name: "HR Policies & Employee Handbook" },
    { id: "cat14", name: "FAQs & Troubleshooting" }
  ];

  // Helper function to get the correct knowledge base path
  const getKnowledgeBasePath = () => {
    const currentPath = location.pathname;
    return currentPath.includes('/service-desk') ? '/service-desk/knowledge' : '/knowledge';
  };

  // Check user role directly
  useEffect(() => {
    setUserRole('IT_ADMIN'); // Set role for testing
  }, [navigate]);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await api.get('/knowledge-base/categories');
        // Combine fetched categories with predefined ones
        const fetchedCategories = response.data || [];
        
        // Map of category names to their IDs for lookup
        const existingCategoryMap = new Map();
        fetchedCategories.forEach((cat: any) => {
          existingCategoryMap.set(cat.name.toLowerCase(), cat.id);
        });
        
        // Check if we need to create any predefined categories
        const categoriesToCreate = [];
        for (const predefinedCat of predefinedCategories) {
          const normalizedName = predefinedCat.name.toLowerCase();
          if (!existingCategoryMap.has(normalizedName)) {
            categoriesToCreate.push(predefinedCat);
          }
        }
        
        // Create missing categories if needed
        if (categoriesToCreate.length > 0) {
          for (const catToCreate of categoriesToCreate) {
            try {
              const createResponse = await api.post('/knowledge-base/categories', {
                name: catToCreate.name,
                slug: catToCreate.name.toLowerCase().replace(/[^\w\s]/gi, '').replace(/\s+/g, '-'),
                description: `Category for ${catToCreate.name} articles`,
                displayOrder: predefinedCategories.indexOf(catToCreate) + 1
              });
              
              if (createResponse.data && createResponse.data.id) {
                // Update the map with the newly created category
                existingCategoryMap.set(catToCreate.name.toLowerCase(), createResponse.data.id);
              }
            } catch (error) {
              console.error(`Error creating category "${catToCreate.name}":`, error);
            }
          }
          
          // Fetch the updated categories
          const updatedResponse = await api.get('/knowledge-base/categories');
          setCategories(updatedResponse.data || []);
        } else {
          // Use predefined categories if the fetched list is empty or has less than 3 items
          if (fetchedCategories.length < 3) {
            setCategories(predefinedCategories);
          } else {
            setCategories([...fetchedCategories]);
          }
        }
      } catch (err: any) {
        console.error('Error fetching categories:', err);
        
        // Handle specific error cases
        if (err.response?.status === 401) {
          setError('Unauthorized: You need to be logged in to access categories.');
          toast.error('Authentication error. Please log in again.');
        } else if (err.response?.status === 403) {
          setError('Forbidden: You do not have permission to access categories.');
          toast.error('Permission denied for accessing categories.');
        } else {
          const errorMessage = err.response?.data?.message || err.message || 'Failed to load categories';
          setError(errorMessage);
          toast.error(errorMessage);
        }
      }
    };

    fetchCategories();

    if (id && id !== 'create' && id !== 'edit') {
      const fetchArticle = async () => {
        setIsLoading(true);
        try {
          const response = await api.get(`/knowledge-base/articles/${id}`);
          
          // Check if the response contains the expected data
          if (!response.data || !response.data.title) {
            throw new Error('Invalid article data received from server');
          }
          
          const articleData = {
            id: response.data.id,
            title: response.data.title,
            content: response.data.content || '',
            summary: response.data.summary || '',
            status: response.data.status || KnowledgeStatus.DRAFT,
            isFeatured: response.data.isFeatured || false,
            categoryId: response.data.category?.id || '',
            tags: response.data.tags?.map((tag: any) => tag.name) || []
          };
          setArticle(articleData);
          setError(null);
        } catch (err: any) {
          console.error('Error fetching article:', err);
          
          // Handle specific error cases
          if (err.response?.status === 401) {
            toast.error('Please log in to edit articles');
            navigate(getKnowledgeBasePath());
          } else if (err.response?.status === 403) {
            toast.error('You do not have permission to edit this article');
            navigate(getKnowledgeBasePath());
          } else if (err.response?.status === 404) {
            toast.error('Article not found');
            navigate(getKnowledgeBasePath());
          } else {
            const errorMessage = err.response?.data?.message || err.message || 'Failed to load the article';
            setError(errorMessage);
            toast.error(errorMessage);
          }
        } finally {
          setIsLoading(false);
        }
      };

      fetchArticle();
    }
  }, [id]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setArticle(prev => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setArticle(prev => ({ ...prev, [name]: checked }));
  };

  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTagInput(e.target.value);
  };

  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && tagInput.trim()) {
      e.preventDefault();
      if (!article.tags.includes(tagInput.trim())) {
        setArticle(prev => ({
          ...prev,
          tags: [...prev.tags, tagInput.trim()]
        }));
      }
      setTagInput('');
    }
  };

  const addTag = () => {
    if (tagInput.trim() && !article.tags.includes(tagInput.trim())) {
      setArticle(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
      
      // Focus back on the tag input for better UX
      setTimeout(() => {
        const tagInputElement = document.getElementById('tagInput');
        if (tagInputElement) {
          tagInputElement.focus();
        }
      }, 0);
    } else if (article.tags.includes(tagInput.trim())) {
      toast.error('This tag already exists');
    } else if (!tagInput.trim()) {
      toast.error('Please enter a tag name');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setArticle(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // Override the handleSubmit function to prevent the form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    
    try {
      // Validate required fields
      if (!article.title.trim()) {
        throw new Error('Title is required');
      }
      
      if (!article.content.trim()) {
        throw new Error('Content is required');
      }
      
      // Ensure we have a valid category
      if (!article.categoryId) {
        throw new Error('Please select a category');
      }
      
      // Get the auth token from localStorage if available
      const token = localStorage.getItem('authToken');
      
      // Create the selected category on the server if needed
      const selectedCategory = predefinedCategories.find(cat => cat.id === article.categoryId);
      let effectiveCategoryId = article.categoryId;
      
              if (selectedCategory) {
        try {
          // Attempt to create the category in the backend
          const createResponse = await api.post('/knowledge-base/categories', {
            name: selectedCategory.name,
            slug: selectedCategory.name.toLowerCase().replace(/[^\w\s]/gi, '').replace(/\s+/g, '-'),
            description: `Category for ${selectedCategory.name}`,
            displayOrder: predefinedCategories.indexOf(selectedCategory) + 1
          }, {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': token ? `Bearer ${token}` : ''
            },
            withCredentials: true
          });
          
          if (createResponse.data && createResponse.data.id) {
            effectiveCategoryId = createResponse.data.id;
          }
        } catch (error: any) {
          console.error('Error creating category:', error);
          // We'll continue and try to use the original ID
        }
      }
      
      // Create config with auth token
      const config = {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        withCredentials: true // Include cookies in the request
      };
      
      // Prepare payload - format tags as expected by the server
      const payload = {
        title: article.title,
        content: DOMPurify.sanitize(article.content), // Sanitize content before saving
        summary: article.summary,
        categoryId: effectiveCategoryId, // Use our effective category ID
        status: article.status,
        isFeatured: article.isFeatured,
        tags: article.tags
      };
      
      let response;
      
      if (id) {
        // Update existing article
        response = await api.put(`/knowledge-base/articles/${id}`, payload, config);
        toast.success('Article updated successfully!');
        setSuccessMessage('Article updated successfully!');
        
        // Redirect back to knowledge base
        setTimeout(() => {
          navigate(getKnowledgeBasePath());
        }, 1000);
      } else {
        // Create new article
        response = await api.post('/knowledge-base/articles', payload, config);
        toast.success('Article created successfully!');
        setSuccessMessage('Article created successfully!');
        
        // Redirect back to knowledge base
        setTimeout(() => {
          navigate(getKnowledgeBasePath());
        }, 1000);
      }
    } catch (err: any) {
      console.error('Error saving article:', err);
      
      // Handle different error types
      if (err.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        const errorMessage = err.response.data?.message || 
                            (typeof err.response.data === 'string' ? err.response.data : 'Failed to save article');
        setError(`Server error: ${errorMessage}`);
        toast.error(typeof errorMessage === 'string' ? errorMessage : 'Server error occurred');
      } else if (err.request) {
        // The request was made but no response was received
        setError('Network error: No response received from server');
        toast.error('Network error. Please check your connection and try again.');
      } else {
        // Something happened in setting up the request that triggered an Error
        setError(`Error: ${err.message}`);
        toast.error(err.message);
      }

      // Scroll to the top to show the error message
      window.scrollTo({ top: 0, behavior: 'smooth' });
      
      // Add visual cue for required fields if they're missing
      if (!article.title.trim()) {
        document.getElementById('title')?.classList.add('border-red-500');
      }
      
      if (!article.categoryId) {
        document.getElementById('categoryId')?.classList.add('border-red-500');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    // Use direct navigation with window.location.origin for more reliable redirection
    const currentPath = window.location.pathname;
    const knowledgeBasePath = currentPath.includes('/service-desk') 
      ? '/service-desk/knowledge'
      : '/knowledge';
    
    window.location.href = `${window.location.origin}${knowledgeBasePath}`;
    
    // Dispatch custom event to close the editor (for backward compatibility)
    const closeEvent = new Event('close-knowledge-editor');
    window.dispatchEvent(closeEvent);
  };

  const togglePreview = () => {
    setPreviewMode(!previewMode);
    
    if (!previewMode) {
      // Sanitize the content before rendering in preview mode
      const sanitizedContent = DOMPurify.sanitize(article.content);
      setRenderedContent(sanitizedContent);
    }
  };

  const handleImageUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    setUploadingImage(true);
    setError(null);
    
    try {
      const formData = new FormData();
      
      // Add each file to the form data
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
          throw new Error(`File ${file.name} exceeds the 5MB size limit`);
        }
        
        // Validate file type
        if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)) {
          throw new Error(`File ${file.name} is not a supported image type`);
        }
        
        formData.append('images', file);
      }
      

      
      // Get the auth token from localStorage if available
      const token = localStorage.getItem('authToken');
      
      const response = await api.post('/knowledge-base/upload-images', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        withCredentials: true // Include cookies in the request
      });
      

      
      // Handle different response formats
      let newUploadedImages: UploadedImage[] = [];
      
      if (response.data.images && Array.isArray(response.data.images)) {
        // Standard format
        newUploadedImages = response.data.images;
      } else if (response.data.urls && Array.isArray(response.data.urls)) {
        // Alternative format some APIs might use
        newUploadedImages = response.data.urls.map((url: string, index: number) => ({
          fileName: files[index]?.name || `Image ${index + 1}`,
          fileUrl: url,
          fileType: files[index]?.type || 'image/jpeg',
          size: files[index]?.size || 0
        }));
      } else if (typeof response.data === 'string') {
        // Simple string URL response
        newUploadedImages = [{
          fileName: files[0]?.name || 'Uploaded Image',
          fileUrl: response.data,
          fileType: files[0]?.type || 'image/jpeg',
          size: files[0]?.size || 0
        }];
      } else if (response.data.url) {
        // Single image response with url property
        newUploadedImages = [{
          fileName: files[0]?.name || 'Uploaded Image',
          fileUrl: response.data.url,
          fileType: files[0]?.type || 'image/jpeg',
          size: files[0]?.size || 0
        }];
      } else if (response.data.fileUrl) {
        // Single image response with fileUrl property
        newUploadedImages = [{
          fileName: files[0]?.name || 'Uploaded Image',
          fileUrl: response.data.fileUrl,
          fileType: files[0]?.type || 'image/jpeg',
          size: files[0]?.size || 0
        }];
      }
      
      // Add the new images to the state
      setUploadedImages(prev => [...prev, ...newUploadedImages]);
      
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      
      toast.success(`${newUploadedImages.length} image(s) uploaded successfully`);
    } catch (err: any) {
      console.error('Error uploading images:', err);
      
      // Set a user-friendly error message
      const errorMessage = err.response?.data?.message || err.message || 'Failed to upload images';
      setError(typeof errorMessage === 'string' ? errorMessage : 'Error uploading images');
      toast.error(typeof errorMessage === 'string' ? errorMessage : 'Error uploading images');
    } finally {
      setUploadingImage(false);
    }
  };

  const renderMarkdownHelp = () => (
    <div className="bg-gray-50 p-4 rounded-lg text-sm">
      <h3 className="font-bold mb-2">Formatting Guide</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <p className="font-medium text-gray-700 mb-2">Basic Formatting</p>
          <ul className="space-y-1 text-gray-600">
            <li><code className="bg-gray-200 px-1 rounded"># Heading 1</code></li>
            <li><code className="bg-gray-200 px-1 rounded">## Heading 2</code></li>
            <li><code className="bg-gray-200 px-1 rounded">### Heading 3</code></li>
            <li><code className="bg-gray-200 px-1 rounded">**Bold text**</code></li>
            <li><code className="bg-gray-200 px-1 rounded">*Italic text*</code></li>
          </ul>
        </div>
        <div>
          <p className="font-medium text-gray-700 mb-2">Links & Media</p>
          <ul className="space-y-1 text-gray-600">
            <li><code className="bg-gray-200 px-1 rounded">[Link text](https://example.com)</code></li>
            <li><code className="bg-gray-200 px-1 rounded">![Alt text](image-url.jpg)</code></li>
            <li><code className="bg-gray-200 px-1 rounded">- List item</code></li>
            <li><code className="bg-gray-200 px-1 rounded">1. Numbered item</code></li>
            <li><code className="bg-gray-200 px-1 rounded">`code`</code></li>
          </ul>
        </div>
      </div>
      
      <div className="mt-4">
        <p className="font-medium text-gray-700 mb-2">Line Breaks & Paragraphs</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div>
            <p className="text-sm text-gray-600 mb-1">Single Line Break</p>
            <pre className="bg-gray-200 p-2 rounded text-xs">
              Line 1
              Line 2 (appears on next line)
            </pre>
          </div>
          <div>
            <p className="text-sm text-gray-600 mb-1">Paragraph Break (double line break)</p>
            <pre className="bg-gray-200 p-2 rounded text-xs">
              Paragraph 1

              Paragraph 2 (new paragraph)
            </pre>
          </div>
        </div>
      </div>
      
      <div className="mt-4">
        <p className="font-medium text-gray-700 mb-2">Numbered Steps with Emojis</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div>
            <p className="text-sm text-gray-600 mb-1">Option 1: Regular Numbers</p>
            <pre className="bg-gray-200 p-2 rounded text-xs">
              1. First step
              2. Second step
              3. Third step
            </pre>
          </div>
          <div>
            <p className="text-sm text-gray-600 mb-1">Option 2: Emoji Numbers</p>
            <pre className="bg-gray-200 p-2 rounded text-xs">
              1️⃣ First step
              2️⃣ Second step
              3️⃣ Third step
            </pre>
          </div>
        </div>
      </div>
      
      <div className="mt-4">
        <p className="font-medium text-gray-700 mb-2">Special Characters</p>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          <div className="bg-gray-200 p-2 rounded text-center">
            <span className="text-xl">✅</span>
            <p className="text-xs mt-1">Checkmark</p>
          </div>
          <div className="bg-gray-200 p-2 rounded text-center">
            <span className="text-xl">❌</span>
            <p className="text-xs mt-1">Cross</p>
          </div>
          <div className="bg-gray-200 p-2 rounded text-center">
            <span className="text-xl">⚠️</span>
            <p className="text-xs mt-1">Warning</p>
          </div>
          <div className="bg-gray-200 p-2 rounded text-center">
            <span className="text-xl">ℹ️</span>
            <p className="text-xs mt-1">Info</p>
          </div>
        </div>
      </div>
    </div>
  );

  // Add a component to display uploaded images
  const UploadedImagesPreview = () => {
    if (uploadedImages.length === 0) return null;
    
    return (
      <div className="mt-4">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Uploaded Images</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {uploadedImages.map((image, index) => (
            <div key={`${image.fileUrl}-${index}`} className="relative group">
              <img 
                src={image.fileUrl} 
                alt={image.fileName} 
                className="w-full h-24 object-cover rounded-md border border-gray-200 cursor-pointer"
                onClick={() => setSelectedImage(image)}
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                <div className="flex space-x-2">
                <button
                  type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Get the rich text editor element
                      const editor = document.querySelector('[contenteditable="true"]');
                      if (editor) {
                        // Create image element
                        const imgHtml = `<img src="${image.fileUrl}" alt="${image.fileName}" style="max-width: 100%; height: auto; margin: 10px 0;" />`;
                        
                        // Insert at cursor position or at the end
                        const selection = window.getSelection();
                        if (selection && selection.rangeCount > 0) {
                          const range = selection.getRangeAt(0);
                          range.deleteContents();
                          const fragment = document.createRange().createContextualFragment(imgHtml);
                          range.insertNode(fragment);
                          range.collapse(false);
                        } else {
                          // If no selection, append to the end
                          editor.innerHTML += imgHtml;
                        }
                        
                        // Trigger change event to update the content
                        const event = new Event('input', { bubbles: true });
                        editor.dispatchEvent(event);
                        
                        // Notify about the successful insertion
                        toast.success('Image inserted into content');
                      } else {
                        toast.error('Editor not found');
                      }
                  }}
                  className="p-1 bg-white rounded-full"
                    title="Insert into content"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                  </button>
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedImage(image);
                    }}
                    className="p-1 bg-white rounded-full"
                    title="View image"
                  >
                    <ZoomIn className="h-4 w-4" />
                  </button>
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      navigator.clipboard.writeText(image.fileUrl);
                      toast.success('Image URL copied to clipboard');
                    }}
                    className="p-1 bg-white rounded-full"
                    title="Copy URL"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>
                </div>
              </div>
              <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 truncate">
                {image.fileName}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const handleContentChange = (newContent: string) => {
    // Make sure content is properly sanitized and formatted
    const sanitizedContent = DOMPurify.sanitize(newContent);
    
    // Update the article state with the new content
    setArticle(prev => ({ 
      ...prev, 
      content: sanitizedContent 
    }));
    
    // Clear any previous errors related to content
    if (error && error.includes('Content is required')) {
      setError(null);
    }
  };

  // Custom image upload handler for the rich text editor
  const handleRichTextImageUpload = async (file: File): Promise<string> => {
    try {
      const formData = new FormData();
      formData.append('image', file);
      
      const response = await api.post('/knowledge-base/upload-images', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      // Add the uploaded image to the uploadedImages state
      const uploadedImage: UploadedImage = {
        fileName: file.name,
        fileUrl: response.data.fileUrl || response.data.url,
        fileType: file.type,
        size: file.size
      };
      
      setUploadedImages(prev => [...prev, uploadedImage]);
      
      // Return the URL for the rich text editor to insert
      return response.data.fileUrl || response.data.url;
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image');
      throw error;
    }
  };

  if (isLoading && !article.title) {
    return (
      <div className="bg-gray-50 min-h-screen py-8">
        <div className="container mx-auto px-4">
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <style>{articleStyles}</style>
      {selectedImage && (
        <ImageViewer
          image={selectedImage}
          onClose={() => setSelectedImage(null)}
        />
      )}
      {/* Blue header section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-8">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button 
                onClick={() => {
                  // Dispatch custom event to close the editor
                  const closeEvent = new Event('close-knowledge-editor');
                  window.dispatchEvent(closeEvent);
                }}
                className="mr-4 text-white hover:text-blue-100 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <div className="flex items-center">
                <FileText className="h-6 w-6 text-white mr-2" />
                <h1 className="text-2xl font-bold text-white">
                  {id ? 'Edit Article' : 'Create New Article'}
                </h1>
              </div>
            </div>
            
            <div className="flex space-x-3">
              {!previewMode ? (
                <button
                  type="button"
                  onClick={togglePreview}
                  className="flex items-center px-4 py-2 bg-blue-800 text-white rounded-md hover:bg-blue-900 transition-colors"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </button>
              ) : (
                <button
                  type="button"
                  onClick={togglePreview}
                  className="flex items-center px-4 py-2 bg-blue-800 text-white rounded-md hover:bg-blue-900 transition-colors"
                >
                  <X className="h-4 w-4 mr-2" />
                  Exit Preview
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-8 flex-grow">
        {error && (
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
              <div>
                <p className="font-medium">{typeof error === 'string' ? error : 'An error occurred'}</p>
              </div>
            </div>
          </div>
        )}

        {successMessage && (
          <div className="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
              <p className="font-medium">{successMessage}</p>
            </div>
          </div>
        )}

        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          {previewMode ? (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">{article.title || 'Untitled Article'}</h2>
                <button
                  type="button"
                  onClick={togglePreview}
                  className="flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                >
                  <X className="h-4 w-4 mr-2" />
                  Exit Preview
                </button>
              </div>
              
              {article.summary && (
                <div className="mb-6 text-gray-700 italic border-l-4 border-blue-200 pl-4 py-2 bg-blue-50 rounded-r">
                  {article.summary}
                </div>
              )}
              
              <div className="mb-6">
                <div className="flex flex-wrap gap-2 mb-4">
                  {article.tags.map((tag, index) => (
                    <span
                      key={`preview-${tag}-${index}`}
                      className="bg-gray-100 text-gray-800 text-sm px-3 py-1 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
              
              <div 
                className="prose prose-blue max-w-none article-content"
                dangerouslySetInnerHTML={{ __html: renderedContent }}
              />
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-2">
                  <div className="mb-6">
                    <label htmlFor="title" className="block text-gray-700 font-medium mb-2">
                      Title <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="title"
                      name="title"
                      value={article.title}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                      placeholder="Enter article title"
                    />
                  </div>

                  <div className="mb-6">
                    <label htmlFor="summary" className="block text-gray-700 font-medium mb-2">
                      Summary
                    </label>
                    <input
                      type="text"
                      id="summary"
                      name="summary"
                      value={article.summary}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Brief description of the article (optional)"
                    />
                  </div>

                  <div className="mb-6">
                    <div className="flex justify-between items-center mb-2">
                      <label htmlFor="content" className="block text-gray-700 font-medium">
                        Content <span className="text-red-500">*</span>
                      </label>
                      <div className="flex items-center space-x-2">
                        <button
                          type="button"
                          className="flex items-center px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                          onClick={() => document.getElementById('markdown-help')?.classList.toggle('hidden')}
                        >
                          <HelpCircle className="h-4 w-4 mr-1" />
                          Formatting Help
                        </button>
                      </div>
                    </div>
                    <div id="markdown-help" className="hidden mb-4">
                      {renderMarkdownHelp()}
                    </div>
                    
                    <KnowledgeRichTextEditor
                      initialValue={article.content}
                      onChange={handleContentChange}
                      onImageUpload={handleRichTextImageUpload}
                    />
                    
                    <UploadedImagesPreview />
                  </div>
                </div>

                <div className="md:col-span-1">
                  <div className="bg-gray-50 p-4 rounded-lg mb-6">
                    <h3 className="font-medium text-gray-700 mb-4">Article Settings</h3>
                    
                    <div className="mb-4">
                      <label htmlFor="categoryId" className="block text-gray-700 font-medium mb-2">
                        Category <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="categoryId"
                        name="categoryId"
                        value={article.categoryId}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        <option value="">Select a category</option>
                        {categories.map(category => (
                          <option key={category.id} value={category.id}>
                            {category.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="mb-4">
                      <label htmlFor="status" className="block text-gray-700 font-medium mb-2">
                        Status
                      </label>
                      <select
                        id="status"
                        name="status"
                        value={article.status}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value={KnowledgeStatus.DRAFT}>Draft</option>
                        <option value={KnowledgeStatus.PUBLISHED}>Published</option>
                        <option value={KnowledgeStatus.ARCHIVED}>Archived</option>
                      </select>
                      <p className="mt-1 text-sm text-gray-500">
                        {article.status === KnowledgeStatus.DRAFT && 'Save as draft to continue editing later.'}
                        {article.status === KnowledgeStatus.PUBLISHED && 'Publish to make this article visible to all users.'}
                        {article.status === KnowledgeStatus.ARCHIVED && 'Archive to hide this article from users.'}
                      </p>
                    </div>

                    <div className="mb-6">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="isFeatured"
                          checked={article.isFeatured}
                          onChange={handleCheckboxChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-gray-700">Featured Article</span>
                      </label>
                    </div>

                    <div className="mb-4">
                      <label className="block text-gray-700 font-medium mb-2">
                        Tags
                      </label>
                      <div className="flex flex-wrap gap-2 mb-2">
                        {article.tags.map((tag, index) => (
                          <span
                            key={`edit-${tag}-${index}`}
                            className="bg-blue-100 text-blue-800 text-sm px-2 py-1 rounded-full flex items-center"
                          >
                            {tag}
                            <button
                              type="button"
                              onClick={() => removeTag(tag)}
                              className="ml-1 text-blue-600 hover:text-blue-800"
                            >
                              &times;
                            </button>
                          </span>
                        ))}
                      </div>
                      <div className="flex">
                        <input
                          type="text"
                          id="tagInput"
                          value={tagInput}
                          onChange={handleTagInputChange}
                          onKeyDown={handleTagInputKeyDown}
                          placeholder="Add a tag"
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                        <button
                          type="button"
                          onClick={addTag}
                          className="px-3 py-2 bg-gray-100 text-gray-700 rounded-r-md border border-l-0 border-gray-300 hover:bg-gray-200"
                        >
                          Add
                        </button>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Press Enter to add a tag</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="border-t border-gray-200 pt-6 flex justify-between items-center">
                <button
                  type="button"
                  onClick={togglePreview}
                  className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </button>
                
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className={`flex items-center px-6 py-2 ${
                      article.status === KnowledgeStatus.PUBLISHED 
                        ? 'bg-green-600 hover:bg-green-700' 
                        : 'bg-blue-600 hover:bg-blue-700'
                    } text-white rounded-md transition-colors`}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {isLoading 
                      ? 'Saving...' 
                      : article.status === KnowledgeStatus.PUBLISHED 
                        ? (id ? 'Update & Publish' : 'Save & Publish') 
                        : (id ? 'Update Article' : 'Save Article')
                    }
                  </button>
                </div>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default KnowledgeArticleEditor; 