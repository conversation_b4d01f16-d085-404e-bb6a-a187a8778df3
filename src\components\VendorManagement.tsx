import React, { useState } from 'react';
import {
  Search,
  Filter,
  Plus,
  Building2,
  Mail,
  Phone,
  Globe,
  DollarSign,
  Shield,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  BarChart2,
  TrendingUp,
  ArrowRight,
  FileText,
  Users,
  Star
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { VendorContracts } from './VendorContracts';
import { VendorAgreements } from './VendorAgreements';
import { VendorPerformance } from './VendorPerformance';
import VendorContacts from './VendorContacts';
import { VendorServices } from './VendorServices';

interface VendorManagementProps {
  currentPage: string;
}

// Import the VendorService type from VendorServices
type Currency = 'PKR' | 'USD' | 'EUR';

interface PricingTier {
  name: string;
  price: number;
  billingCycle: 'monthly' | 'quarterly' | 'annually';
  features: string[];
}

interface PricingType {
  type: 'fixed' | 'hourly' | 'per_unit' | 'tiered';
  amount: number;
  currency: Currency;
  tiers?: PricingTier[];
  minimumCommitment?: number;
  discounts?: {
    type: 'volume' | 'early-payment' | 'long-term';
    percentage: number;
    conditions: string;
  }[];
}

interface SLAMetrics {
  responseTime: number;
  uptime: number;
  penalties: string;
  resolutionTime: number;
  availabilityHours: string;
  escalationMatrix: {
    level: number;
    contact: string;
    responseTime: number;
  }[];
  maintenanceWindows: {
    day: string;
    time: string;
    duration: number;
  }[];
}

interface ServiceMetrics {
  availability: number;
  performance: number;
  satisfaction: number;
  reliability: number;
  incidentCount: number;
  avgResolutionTime: number;
  costEfficiency: number;
  userAdoption: number;
  complianceScore: number;
}

interface Compliance {
  certifications: string[];
  standards: string[];
  lastAuditDate: string;
  nextAuditDue: string;
  findings: {
    type: 'critical' | 'major' | 'minor';
    description: string;
    status: 'open' | 'closed' | 'in-progress';
    dueDate: string;
  }[];
}

interface Integration {
  system: string;
  status: 'active' | 'pending' | 'failed';
  lastSync: string;
  apiVersion: string;
  endpoints: string[];
}

interface VendorService {
  id: string;
  name: string;
  description: string;
  hasSLA: boolean;
  category: string;
  subcategory?: string;
  tags: string[];
  status: 'active' | 'pending' | 'suspended' | 'terminated';
  criticality: 'high' | 'medium' | 'low';
  sla?: SLAMetrics;
  pricing: PricingType;
  metrics: ServiceMetrics;
  compliance: Compliance;
  integrations: Integration[];
  documentation: {
    type: string;
    url: string;
    lastUpdated: string;
  }[];
  supportChannels: {
    type: 'email' | 'phone' | 'portal' | 'chat';
    contact: string;
    availability: string;
  }[];
  dependencies: string[];
  backupProvider?: string;
  lastUpdated: string;
  nextReview: string;
  changeHistory: {
    date: string;
    type: 'configuration' | 'pricing' | 'sla' | 'status';
    description: string;
    user: string;
  }[];
  notifications: {
    id: string;
    type: 'warning' | 'info' | 'critical';
    message: string;
    date: string;
    isRead: boolean;
  }[];
}

interface Address {
  street: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
}

interface ContactPerson {
  name: string;
  title: string;
  email: string;
  phone: string;
  alternativePhone?: string;
  department?: string;
}

type VendorType = 
  | 'ISP' 
  | 'CRM_PROVIDER' 
  | 'ERP_VENDOR' 
  | 'SOFTWARE_DEVELOPER' 
  | 'HARDWARE_SUPPLIER' 
  | 'CLOUD_PROVIDER' 
  | 'SECURITY_VENDOR' 
  | 'CONSULTING_FIRM'
  | 'MAINTENANCE_PROVIDER';

type VendorStatus = 'ACTIVE' | 'INACTIVE' | 'BLACKLISTED' | 'PENDING_REVIEW';

type PaymentTerms = 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY' | 'PER_SERVICE';

interface VendorPerformanceMetrics {
  rating: number;
  responseTime: number;
  deliveryScore: number;
  qualityScore: number;
  uptime?: number;
  incidentResolutionTime?: number;
  supportQualityScore?: number;
  documentationQuality?: number;
  complianceScore?: number;
  lastReviewDate: string;
  nextReviewDate: string;
}

interface FinancialInfo {
  spending: {
    current: {
      value: number;
      currency: Currency;
    };
    previous: {
      value: number;
      currency: Currency;
    };
    trend: number;
  };
  paymentHistory: {
    lastPaymentDate: string;
    paymentStatus: 'PAID' | 'PENDING' | 'OVERDUE';
    outstandingAmount?: number;
  };
  budgetAllocation: number;
  costSavings?: number;
}

interface Vendor {
  id: string;
  vendorName: string;
  companyName: string;
  contactPerson: ContactPerson;
  alternativeContacts?: ContactPerson[];
  address: Address;
  vendorType: VendorType;
  status: VendorStatus;
  contract: {
    startDate: string;
    expiryDate: string;
    renewalReminder?: boolean;
    autoRenewal?: boolean;
    terminationNotice: number; // days
  };
  paymentTerms: PaymentTerms;
  remarks?: string[];
  performance: VendorPerformanceMetrics;
  financial: FinancialInfo;
  documents: {
    type: string;
    name: string;
    url: string;
    uploadDate: string;
    expiryDate?: string;
  }[];
  services: string[]; // References to VendorService IDs
  certifications: {
    name: string;
    issueDate: string;
    expiryDate: string;
    status: 'VALID' | 'EXPIRED' | 'PENDING';
  }[];
  complianceStatus: {
    framework: string;
    status: 'COMPLIANT' | 'NON_COMPLIANT' | 'PENDING_REVIEW';
    lastAuditDate: string;
    nextAuditDate: string;
  }[];
  riskAssessment: {
    level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    lastAssessmentDate: string;
    nextAssessmentDate: string;
    findings: string[];
  };
  createdAt: string;
  updatedAt: string;
}

export function VendorManagement({ currentPage }: VendorManagementProps) {
  const { user } = useAuth();
  const [showForm, setShowForm] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [vendorServices, setVendorServices] = useState<VendorService[]>([
    {
      id: 'S001',
      name: 'Cloud Hosting',
      description: 'Enterprise-grade cloud hosting solution with high availability and scalability.',
      hasSLA: true,
      category: 'infrastructure',
      subcategory: 'hosting',
      tags: ['cloud', 'hosting', 'infrastructure'],
      status: 'active',
      criticality: 'high',
      sla: {
        responseTime: 1,
        uptime: 99.99,
        penalties: 'Service credit for downtime',
        resolutionTime: 4,
        availabilityHours: '24/7',
        escalationMatrix: [
          { level: 1, contact: '<EMAIL>', responseTime: 1 },
          { level: 2, contact: '<EMAIL>', responseTime: 2 }
        ],
        maintenanceWindows: [
          { day: 'Sunday', time: '02:00', duration: 2 }
        ]
      },
      pricing: {
        type: 'tiered',
        amount: 50000,
        currency: 'PKR',
        tiers: [
          {
            name: 'Basic',
            price: 50000,
            billingCycle: 'monthly',
            features: ['99.9% uptime', '24/7 support', 'Basic monitoring']
          }
        ]
      },
      metrics: {
        availability: 99.99,
        performance: 95,
        satisfaction: 98,
        reliability: 99.5,
        incidentCount: 2,
        avgResolutionTime: 1.5,
        costEfficiency: 92,
        userAdoption: 88,
        complianceScore: 100
      },
      compliance: {
        certifications: ['ISO 27001', 'SOC 2'],
        standards: ['GDPR', 'HIPAA'],
        lastAuditDate: '2024-01-15',
        nextAuditDue: '2024-07-15',
        findings: []
      },
      integrations: [
        {
          system: 'Monitoring',
          status: 'active',
          lastSync: '2024-03-15T10:00:00Z',
          apiVersion: 'v2.1',
          endpoints: ['/metrics', '/alerts']
        }
      ],
      documentation: [
        {
          type: 'API',
          url: 'https://docs.vendor.com/api',
          lastUpdated: '2024-02-28'
        }
      ],
      supportChannels: [
        {
          type: 'email',
          contact: '<EMAIL>',
          availability: '24/7'
        }
      ],
      dependencies: ['Network', 'Power'],
      backupProvider: 'Backup Cloud Inc',
      lastUpdated: '2024-03-15',
      nextReview: '2024-06-15',
      changeHistory: [
        {
          date: '2024-03-15',
          type: 'configuration',
          description: 'Updated SLA parameters',
          user: 'admin'
        }
      ],
      notifications: [
        {
          id: 'N001',
          type: 'info',
          message: 'Scheduled maintenance upcoming',
          date: '2024-03-20',
          isRead: false
        }
      ]
    },
    {
      id: 'S002',
      name: 'IT Support',
      description: 'Comprehensive IT support services with dedicated helpdesk.',
      hasSLA: true,
      category: 'support',
      subcategory: 'helpdesk',
      tags: ['support', 'helpdesk', 'IT'],
      status: 'active',
      criticality: 'medium',
      sla: {
        responseTime: 2,
        uptime: 99.5,
        penalties: 'Service credit for missed SLA',
        resolutionTime: 8,
        availabilityHours: '9/5',
        escalationMatrix: [
          { level: 1, contact: '<EMAIL>', responseTime: 2 },
          { level: 2, contact: '<EMAIL>', responseTime: 4 }
        ],
        maintenanceWindows: [
          { day: 'Saturday', time: '10:00', duration: 4 }
        ]
      },
      pricing: {
        type: 'fixed',
        amount: 25000,
        currency: 'PKR',
        minimumCommitment: 12,
        discounts: [
          {
            type: 'long-term',
            percentage: 10,
            conditions: '12-month commitment'
          }
        ]
      },
      metrics: {
        availability: 99.5,
        performance: 92,
        satisfaction: 95,
        reliability: 98,
        incidentCount: 15,
        avgResolutionTime: 4.2,
        costEfficiency: 88,
        userAdoption: 85,
        complianceScore: 95
      },
      compliance: {
        certifications: ['ISO 9001'],
        standards: ['ITIL v4'],
        lastAuditDate: '2024-02-15',
        nextAuditDue: '2024-08-15',
        findings: [
          {
            type: 'minor',
            description: 'Documentation update needed',
            status: 'in-progress',
            dueDate: '2024-04-15'
          }
        ]
      },
      integrations: [
        {
          system: 'Ticketing',
          status: 'active',
          lastSync: '2024-03-15T12:00:00Z',
          apiVersion: 'v3.0',
          endpoints: ['/tickets', '/users']
        }
      ],
      documentation: [
        {
          type: 'User Guide',
          url: 'https://docs.vendor.com/support',
          lastUpdated: '2024-03-01'
        }
      ],
      supportChannels: [
        {
          type: 'email',
          contact: '<EMAIL>',
          availability: '9am-5pm'
        },
        {
          type: 'phone',
          contact: '+**********',
          availability: '9am-5pm'
        }
      ],
      dependencies: ['Internet', 'Ticketing System'],
      backupProvider: 'Backup Support Ltd',
      lastUpdated: '2024-03-10',
      nextReview: '2024-06-10',
      changeHistory: [
        {
          date: '2024-03-10',
          type: 'sla',
          description: 'Updated response time requirements',
          user: 'admin'
        }
      ],
      notifications: [
        {
          id: 'N002',
          type: 'warning',
          message: 'High ticket volume expected',
          date: '2024-03-18',
          isRead: true
        }
      ]
    }
  ]);

  // Update the vendors array with the new interface
  const [vendors, setVendors] = useState<Vendor[]>([
    {
      id: 'V001',
      vendorName: 'Tech Solutions Inc',
      companyName: 'Tech Solutions International Private Limited',
      contactPerson: {
        name: 'John Smith',
        title: 'Account Manager',
        email: '<EMAIL>',
        phone: '+92 300 1234567',
        department: 'Enterprise Sales'
      },
      address: {
        street: '123 Tech Park Avenue',
        city: 'Islamabad',
        state: 'Federal Territory',
        country: 'Pakistan',
        postalCode: '44000'
      },
      vendorType: 'SOFTWARE_DEVELOPER',
      status: 'ACTIVE',
      contract: {
        startDate: '2024-01-01',
        expiryDate: '2024-12-31',
        renewalReminder: true,
        autoRenewal: false,
        terminationNotice: 60
      },
      paymentTerms: 'QUARTERLY',
      remarks: [
        'Excellent support response time',
        'Proactive in suggesting improvements'
      ],
      performance: {
        rating: 4.5,
        responseTime: 24,
        deliveryScore: 98,
        qualityScore: 95,
        supportQualityScore: 92,
        documentationQuality: 90,
        complianceScore: 95,
        lastReviewDate: '2024-01-15',
        nextReviewDate: '2024-04-15'
      },
      financial: {
      spending: {
        current: {
          value: 450000,
          currency: 'PKR'
        },
        previous: {
          value: 380000,
          currency: 'PKR'
        },
        trend: 18.4
      },
        paymentHistory: {
          lastPaymentDate: '2024-02-28',
          paymentStatus: 'PAID',
          outstandingAmount: 0
        },
        budgetAllocation: 2000000,
        costSavings: 150000
      },
      documents: [
        {
          type: 'CONTRACT',
          name: 'Master Service Agreement',
          url: '/documents/V001/msa.pdf',
          uploadDate: '2024-01-01'
        },
        {
          type: 'SLA',
          name: 'Service Level Agreement',
          url: '/documents/V001/sla.pdf',
          uploadDate: '2024-01-01'
        }
      ],
      services: ['S001', 'S002'],
      certifications: [
        {
          name: 'ISO 27001',
          issueDate: '2023-06-15',
          expiryDate: '2024-06-14',
          status: 'VALID'
        }
      ],
      complianceStatus: [
        {
          framework: 'GDPR',
          status: 'COMPLIANT',
          lastAuditDate: '2023-12-15',
          nextAuditDate: '2024-12-15'
        }
      ],
      riskAssessment: {
        level: 'LOW',
        lastAssessmentDate: '2024-01-15',
        nextAssessmentDate: '2024-07-15',
        findings: []
      },
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-03-15T10:30:00Z'
    }
  ]);

  const [pageTitle, setPageTitle] = useState('Vendor Management');

  const getPageTitle = (page: string) => {
    switch (page) {
      case 'services':
        return 'Vendor Services';
      case 'contracts':
        return 'Vendor Contracts';
      case 'agreements':
        return 'Vendor Agreements';
      case 'performance':
        return 'Vendor Performance';
      case 'contacts':
        return 'Vendor Contacts';
      default:
        return 'Vendor Management';
    }
  };

  React.useEffect(() => {
    setPageTitle(getPageTitle(currentPage));
  }, [currentPage]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderContent = () => {
    console.log('Current page:', currentPage); // Debug log

    switch (currentPage) {
      case 'services':
        return <VendorServices services={vendorServices} onUpdate={setVendorServices} />;
      case 'contracts':
        return <VendorContracts />;
      case 'agreements':
        return <VendorAgreements />;
      case 'performance':
        return <VendorPerformance />;
      case 'contacts':
        return <VendorContacts />;
      case 'vendor-management':
      default:
        return (
          <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm text-gray-600">Total Vendors</p>
              <h3 className="text-2xl font-bold text-gray-900">48</h3>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Building2 className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <span className="text-green-600">+5 new</span>
            <span className="text-gray-300">|</span>
            <span className="text-gray-600">this month</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm text-gray-600">Active Contracts</p>
              <h3 className="text-2xl font-bold text-gray-900">32</h3>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <FileText className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <span className="text-yellow-600">8 renewals</span>
            <span className="text-gray-300">|</span>
            <span className="text-gray-600">due soon</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm text-gray-600">Total Spending</p>
              <h3 className="text-2xl font-bold text-gray-900">₨12.5M</h3>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <DollarSign className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <span className="text-green-600">+18.4%</span>
            <span className="text-gray-300">|</span>
            <span className="text-gray-600">vs last month</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm text-gray-600">Avg Performance</p>
              <h3 className="text-2xl font-bold text-gray-900">4.6/5</h3>
            </div>
            <div className="p-3 bg-yellow-100 rounded-lg">
              <BarChart2 className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <span className="text-green-600">+0.3</span>
            <span className="text-gray-300">|</span>
            <span className="text-gray-600">improvement</span>
          </div>
        </div>
      </div>

            {/* Search and Filter Bar */}
            <div className="bg-white rounded-lg shadow-sm p-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <input
                      type="text"
                      placeholder="Search vendors..."
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex gap-4">
                  <select
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={selectedStatus}
                    onChange={(e) => setSelectedStatus(e.target.value)}
                  >
                    <option value="all">All Status</option>
                    <option value="ACTIVE">Active</option>
                    <option value="INACTIVE">Inactive</option>
                    <option value="BLACKLISTED">Blacklisted</option>
                    <option value="PENDING_REVIEW">Pending Review</option>
                  </select>
                  <select
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                  >
                    <option value="all">All Categories</option>
                    <option value="ISP">ISP</option>
                    <option value="CRM_PROVIDER">CRM Provider</option>
                    <option value="ERP_VENDOR">ERP Vendor</option>
                    <option value="SOFTWARE_DEVELOPER">Software Developer</option>
                    <option value="HARDWARE_SUPPLIER">Hardware Supplier</option>
                  </select>
                  <button
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center gap-2"
                    onClick={() => setShowForm(true)}
                  >
                    <Plus className="h-5 w-5" />
                    Add Vendor
                  </button>
          </div>
        </div>
      </div>

      {/* Vendors List */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Active Vendors</h2>
          </div>
        </div>

        <div className="p-6">
          <div className="space-y-6">
                  {vendors
                    .filter(vendor => {
                      const matchesSearch = vendor.vendorName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                        vendor.companyName.toLowerCase().includes(searchQuery.toLowerCase());
                      const matchesStatus = selectedStatus === 'all' || vendor.status === selectedStatus;
                      const matchesCategory = selectedCategory === 'all' || vendor.vendorType === selectedCategory;
                      return matchesSearch && matchesStatus && matchesCategory;
                    })
                    .map((vendor) => (
              <div
                key={vendor.id}
                      className="bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors cursor-pointer"
              >
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Building2 className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                              <h3 className="font-semibold text-gray-900">{vendor.vendorName}</h3>
                        <p className="text-sm text-gray-500">ID: {vendor.id}</p>
                      </div>
                    </div>

                    <div className="flex flex-wrap items-center gap-4 mb-4">
                            <span className={`px-2.5 py-0.5 text-xs font-medium rounded-full ${getStatusColor(vendor.status.toLowerCase())}`}>
                        {vendor.status}
                      </span>
                      <div className="flex items-center gap-1 text-gray-500 text-sm">
                        <Mail className="h-4 w-4" />
                              <span>{vendor.contactPerson.email}</span>
                      </div>
                      <div className="flex items-center gap-1 text-gray-500 text-sm">
                        <Phone className="h-4 w-4" />
                              <span>{vendor.contactPerson.phone}</span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div className="bg-white p-3 rounded-lg">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-gray-500">Spending</span>
                          <TrendingUp className="h-4 w-4 text-green-500" />
                        </div>
                        <div className="flex items-center justify-between">
                                <span className="text-lg font-semibold">
                                  {vendor.financial.spending.current.currency} {vendor.financial.spending.current.value.toLocaleString()}
                                </span>
                                <span className="text-sm text-green-600">+{vendor.financial.spending.trend}%</span>
                        </div>
                      </div>

                      <div className="bg-white p-3 rounded-lg">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-gray-500">Rating</span>
                          <Star className="h-4 w-4 text-yellow-500" />
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-lg font-semibold">{vendor.performance.rating}/5</span>
                                <span className="text-sm text-gray-500">Performance</span>
                        </div>
                      </div>

                      <div className="bg-white p-3 rounded-lg">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-gray-500">Response Time</span>
                          <Clock className="h-4 w-4 text-blue-500" />
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-lg font-semibold">{vendor.performance.responseTime}h</span>
                          <span className="text-sm text-gray-500">average</span>
                        </div>
                      </div>

                      <div className="bg-white p-3 rounded-lg">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-gray-500">Quality Score</span>
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-lg font-semibold">{vendor.performance.qualityScore}%</span>
                          <span className="text-sm text-gray-500">satisfaction</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
          </div>
        );
    }
  };

  return (
    <div className="flex-1 flex flex-col min-h-0 bg-gray-100">
      <div className="flex-shrink-0 border-b border-gray-200 bg-white">
        <div className="px-6 py-4">
          <h2 className="text-lg font-medium text-gray-900">{pageTitle}</h2>
        </div>
      </div>
      <div className="flex-1 overflow-auto p-6">
        {renderContent()}
      </div>
    </div>
  );
}