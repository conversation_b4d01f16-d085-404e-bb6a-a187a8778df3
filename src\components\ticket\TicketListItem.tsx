import React from 'react';
import { 
  Calendar, 
  MessageSquare, 
  User as UserIcon,
  CheckCircle,
  RefreshCw,
  Clock
} from 'lucide-react';
import { Ticket, TicketStatus } from '../../types/common';

interface TicketListItemProps {
  ticket: Ticket;
  currentUserId: string;
  onSelect: (ticket: Ticket) => void;
  getStatusColor: (status: TicketStatus) => string;
  getPriorityColor: (priority: string) => string;
  renderActionButtons: (ticket: Ticket) => React.ReactNode;
}

export const TicketListItem: React.FC<TicketListItemProps> = ({
  ticket,
  currentUserId,
  onSelect,
  getStatusColor,
  getPriorityColor,
  renderActionButtons
}) => {
  const getStatusIcon = (status: TicketStatus) => {
    switch (status) {
      case 'RESOLVED':
        return <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 text-green-600" />;
      case 'IN_PROGRESS':
        return <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-amber-600" />;
      default:
        return null;
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  return (
    <tr 
      onClick={() => onSelect(ticket)}
      className="hover:bg-gray-50 cursor-pointer border-b border-gray-100"
    >
      <td className="py-2 sm:py-4 px-2 sm:px-4 whitespace-nowrap">
        <span className="text-xs sm:text-sm text-gray-500">#{ticket?.ticketNumber}</span>
      </td>
      
      <td className="py-2 sm:py-4 px-2 sm:px-4">
        <div className="max-w-[150px] sm:max-w-[200px] lg:max-w-md">
          <div className="font-medium text-sm sm:text-base text-gray-900 truncate">{ticket?.title}</div>
          <div className="text-xs sm:text-sm text-gray-500 truncate hidden sm:block">{ticket?.description}</div>
        </div>
      </td>

      <td className="py-2 sm:py-4 px-2 sm:px-4 whitespace-nowrap">
        <span className={`inline-flex items-center gap-1 sm:gap-1.5 px-1.5 sm:px-2.5 py-0.5 sm:py-1 text-xs font-medium rounded-full ${getStatusColor(ticket?.status)}`}>
          {getStatusIcon(ticket?.status)}
          <span className="hidden sm:inline">{ticket?.status}</span>
          <span className="sm:hidden">{ticket?.status.slice(0, 3)}</span>
        </span>
      </td>

      <td className="py-2 sm:py-4 px-2 sm:px-4 whitespace-nowrap">
        <span className={`px-1.5 sm:px-2.5 py-0.5 sm:py-1 text-xs font-medium rounded-full ${getPriorityColor(ticket?.priority)}`}>
          <span className="hidden sm:inline">{ticket?.priority}</span>
          <span className="sm:hidden">{ticket?.priority.slice(0, 3)}</span>
        </span>
      </td>

      <td className="py-2 sm:py-4 px-2 sm:px-4 whitespace-nowrap hidden md:table-cell">
        <span className="px-2 py-0.5 text-xs font-medium rounded-full bg-gray-100 text-gray-700">
          {ticket?.category}
        </span>
      </td>

      <td className="py-2 sm:py-4 px-2 sm:px-4 whitespace-nowrap">
        <div className="flex items-center gap-1 sm:gap-2">
          <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-blue-100 flex items-center justify-center text-xs sm:text-sm font-medium text-blue-700">
            {getInitials(ticket?.createdBy?.name)}
          </div>
          <div className="hidden sm:block">
            <div className="font-medium text-sm text-gray-900">{ticket?.createdBy?.name}</div>
            <div className="text-xs text-gray-500">{ticket?.createdBy?.department}</div>
          </div>
        </div>
      </td>

      <td className="py-2 sm:py-4 px-2 sm:px-4 whitespace-nowrap hidden lg:table-cell">
        {ticket?.status === 'OPEN' ? (
          <span className="text-sm text-gray-900">{ticket?.createdBy?.name}</span>
        ) : (
          <span className="text-sm text-gray-900">{ticket?.assignedTo?.name || 'Unassigned'}</span>
        )}
      </td>

      <td className="py-2 sm:py-4 px-2 sm:px-4 whitespace-nowrap text-xs sm:text-sm text-gray-500 hidden sm:table-cell">
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400" />
          {new Date(ticket?.createdAt || '').toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          })}
        </div>
      </td>

      <td className="py-2 sm:py-4 px-2 sm:px-4 whitespace-nowrap">
        <div className="flex items-center gap-1">
          <MessageSquare className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400" />
          <span className="text-xs sm:text-sm text-gray-600">{ticket?.comments?.length || 0}</span>
        </div>
      </td>
    </tr>
  );
}; 