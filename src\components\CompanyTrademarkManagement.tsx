import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Shield, 
  Calendar, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  FileText,
  Upload,
  Eye,
  Star,
  Globe
} from 'lucide-react';
import { toast } from 'react-hot-toast';

// Types
interface Trademark {
  id: string;
  trademarkName: string;
  description?: string;
  trademarkType: 'word_mark' | 'design_mark' | 'combined_mark' | 'service_mark';
  status: 'pending' | 'registered' | 'opposed' | 'abandoned' | 'expired' | 'renewed';
  registrationNumber?: string;
  applicationNumber?: string;
  applicationDate?: string;
  registrationDate?: string;
  renewalDate?: string;
  expiryDate?: string;
  jurisdiction: string;
  registryOffice?: string;
  trademarkClasses: string[];
  goodsAndServices?: string;
  trademarkImageUrl?: string;
  attorney?: string;
  registrationFee?: number;
  renewalFee?: number;
  currency?: string;
  notes?: string;
  isActive: boolean;
  isPrimary: boolean;
  priority: number;
  createdAt: string;
  updatedAt: string;
}

const CompanyTrademarkManagement: React.FC = () => {
  const [trademarks, setTrademarks] = useState<Trademark[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingTrademark, setEditingTrademark] = useState<Trademark | null>(null);
  const [selectedTrademark, setSelectedTrademark] = useState<Trademark | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Mock data for demonstration
  useEffect(() => {
    const mockTrademarks: Trademark[] = [
      {
        id: '1',
        trademarkName: 'InfraSpine',
        description: 'IT Management System Platform',
        trademarkType: 'combined_mark',
        status: 'registered',
        registrationNumber: 'TM-2024-001',
        applicationNumber: 'APP-2023-456',
        applicationDate: '2023-06-15',
        registrationDate: '2024-01-20',
        renewalDate: '2034-01-20',
        expiryDate: '2034-01-20',
        jurisdiction: 'Pakistan',
        registryOffice: 'IPO Pakistan',
        trademarkClasses: ['Class 09', 'Class 42'],
        goodsAndServices: 'Computer software, IT services, cloud computing',
        trademarkImageUrl: '/images/infraspine-logo.png',
        attorney: 'Legal Associates Ltd.',
        registrationFee: 25000,
        renewalFee: 15000,
        currency: 'PKR',
        notes: 'Primary company trademark for IT services',
        isActive: true,
        isPrimary: true,
        priority: 1,
        createdAt: '2023-06-15T10:00:00Z',
        updatedAt: '2024-01-20T15:30:00Z'
      },
      {
        id: '2',
        trademarkName: 'InfraSpine Pro',
        description: 'Premium IT Management Suite',
        trademarkType: 'word_mark',
        status: 'pending',
        applicationNumber: 'APP-2024-123',
        applicationDate: '2024-03-10',
        jurisdiction: 'United States',
        registryOffice: 'USPTO',
        trademarkClasses: ['Class 09', 'Class 35', 'Class 42'],
        goodsAndServices: 'Software as a Service, Business management software',
        attorney: 'IP Law Firm LLC',
        registrationFee: 1200,
        currency: 'USD',
        notes: 'International expansion trademark',
        isActive: true,
        isPrimary: false,
        priority: 2,
        createdAt: '2024-03-10T09:00:00Z',
        updatedAt: '2024-03-10T09:00:00Z'
      }
    ];

    setTimeout(() => {
      setTrademarks(mockTrademarks);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'registered':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'opposed':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      case 'expired':
      case 'abandoned':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'renewed':
        return <CheckCircle className="h-5 w-5 text-blue-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'registered':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'opposed':
        return 'bg-orange-100 text-orange-800';
      case 'expired':
      case 'abandoned':
        return 'bg-red-100 text-red-800';
      case 'renewed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const isExpiringSoon = (expiryDate?: string) => {
    if (!expiryDate) return false;
    const today = new Date();
    const expiry = new Date(expiryDate);
    const timeDiff = expiry.getTime() - today.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
    return daysDiff <= 90 && daysDiff > 0;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleDeleteTrademark = (id: string) => {
    if (window.confirm('Are you sure you want to delete this trademark?')) {
      setTrademarks(trademarks.filter(tm => tm.id !== id));
      toast.success('Trademark deleted successfully');
    }
  };

  const handleSetPrimary = (id: string) => {
    setTrademarks(trademarks.map(tm => ({
      ...tm,
      isPrimary: tm.id === id
    })));
    toast.success('Primary trademark updated');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Shield className="h-6 w-6 text-blue-600" />
            Company Trademarks
          </h2>
          <p className="text-gray-600 mt-1">
            Manage your intellectual property and trademark portfolio
          </p>
        </div>
        <div className="flex items-center gap-3">
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'grid'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Grid
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'list'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              List
            </button>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            Add Trademark
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Trademarks</p>
              <p className="text-2xl font-bold text-gray-900">{trademarks.length}</p>
            </div>
            <Shield className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Registered</p>
              <p className="text-2xl font-bold text-green-600">
                {trademarks.filter(tm => tm.status === 'registered').length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">
                {trademarks.filter(tm => tm.status === 'pending').length}
              </p>
            </div>
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Expiring Soon</p>
              <p className="text-2xl font-bold text-orange-600">
                {trademarks.filter(tm => isExpiringSoon(tm.expiryDate)).length}
              </p>
            </div>
            <AlertTriangle className="h-8 w-8 text-orange-600" />
          </div>
        </div>
      </div>

      {/* Trademarks Grid/List */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {trademarks.map((trademark) => (
            <div key={trademark.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
              {/* Card Header */}
              <div className="p-4 border-b border-gray-100">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    {trademark.trademarkImageUrl ? (
                      <img
                        src={trademark.trademarkImageUrl}
                        alt={trademark.trademarkName}
                        className="h-12 w-12 object-contain bg-gray-50 rounded-lg p-1"
                      />
                    ) : (
                      <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Shield className="h-6 w-6 text-blue-600" />
                      </div>
                    )}
                    <div>
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        {trademark.trademarkName}
                        {trademark.isPrimary && (
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        )}
                      </h3>
                      <p className="text-sm text-gray-600">{trademark.description}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Card Body */}
              <div className="p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(trademark.status)}`}>
                    {getStatusIcon(trademark.status)}
                    {trademark.status.replace('_', ' ').toUpperCase()}
                  </span>
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <Globe className="h-4 w-4" />
                    {trademark.jurisdiction}
                  </div>
                </div>

                {trademark.registrationNumber && (
                  <div className="text-sm">
                    <span className="text-gray-600">Reg. No:</span>
                    <span className="ml-1 font-medium">{trademark.registrationNumber}</span>
                  </div>
                )}

                {trademark.expiryDate && (
                  <div className="text-sm">
                    <span className="text-gray-600">Expires:</span>
                    <span className={`ml-1 font-medium ${isExpiringSoon(trademark.expiryDate) ? 'text-orange-600' : 'text-gray-900'}`}>
                      {formatDate(trademark.expiryDate)}
                    </span>
                    {isExpiringSoon(trademark.expiryDate) && (
                      <AlertTriangle className="inline h-4 w-4 text-orange-500 ml-1" />
                    )}
                  </div>
                )}

                <div className="text-sm">
                  <span className="text-gray-600">Classes:</span>
                  <span className="ml-1">{trademark.trademarkClasses.join(', ')}</span>
                </div>
              </div>

              {/* Card Actions */}
              <div className="px-4 py-3 bg-gray-50 border-t border-gray-100">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => setSelectedTrademark(trademark)}
                      className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center gap-1"
                    >
                      <Eye className="h-4 w-4" />
                      View
                    </button>
                    <button
                      onClick={() => setEditingTrademark(trademark)}
                      className="text-gray-600 hover:text-gray-700 text-sm font-medium flex items-center gap-1"
                    >
                      <Edit className="h-4 w-4" />
                      Edit
                    </button>
                  </div>
                  <div className="flex items-center gap-2">
                    {!trademark.isPrimary && (
                      <button
                        onClick={() => handleSetPrimary(trademark.id)}
                        className="text-yellow-600 hover:text-yellow-700 text-sm"
                        title="Set as primary"
                      >
                        <Star className="h-4 w-4" />
                      </button>
                    )}
                    <button
                      onClick={() => handleDeleteTrademark(trademark.id)}
                      className="text-red-600 hover:text-red-700 text-sm"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        // List View
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trademark
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Registration
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Jurisdiction
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Expiry
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {trademarks.map((trademark) => (
                  <tr key={trademark.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {trademark.trademarkImageUrl ? (
                          <img
                            src={trademark.trademarkImageUrl}
                            alt={trademark.trademarkName}
                            className="h-10 w-10 object-contain bg-gray-50 rounded-lg p-1 mr-3"
                          />
                        ) : (
                          <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <Shield className="h-5 w-5 text-blue-600" />
                          </div>
                        )}
                        <div>
                          <div className="text-sm font-medium text-gray-900 flex items-center gap-2">
                            {trademark.trademarkName}
                            {trademark.isPrimary && (
                              <Star className="h-4 w-4 text-yellow-500 fill-current" />
                            )}
                          </div>
                          <div className="text-sm text-gray-500">{trademark.description}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(trademark.status)}`}>
                        {getStatusIcon(trademark.status)}
                        {trademark.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {trademark.registrationNumber || 'Pending'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {trademark.jurisdiction}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {trademark.expiryDate ? (
                        <span className={isExpiringSoon(trademark.expiryDate) ? 'text-orange-600 font-medium' : 'text-gray-900'}>
                          {formatDate(trademark.expiryDate)}
                          {isExpiringSoon(trademark.expiryDate) && (
                            <AlertTriangle className="inline h-4 w-4 text-orange-500 ml-1" />
                          )}
                        </span>
                      ) : (
                        'N/A'
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end gap-2">
                        <button
                          onClick={() => setSelectedTrademark(trademark)}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => setEditingTrademark(trademark)}
                          className="text-gray-600 hover:text-gray-700"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        {!trademark.isPrimary && (
                          <button
                            onClick={() => handleSetPrimary(trademark.id)}
                            className="text-yellow-600 hover:text-yellow-700"
                            title="Set as primary"
                          >
                            <Star className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          onClick={() => handleDeleteTrademark(trademark.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Empty State */}
      {trademarks.length === 0 && (
        <div className="text-center py-12">
          <Shield className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No trademarks</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by adding your first trademark.</p>
          <div className="mt-6">
            <button
              onClick={() => setShowAddForm(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Trademark
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompanyTrademarkManagement;
