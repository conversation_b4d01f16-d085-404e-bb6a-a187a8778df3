import React, { useState, useEffect } from 'react';
import { 
  AlertCircle, Upload, User, Monitor, Clock, 
  AlertTriangle, CheckCircle, X, Send,
  FileText, Tag, Shield, Users, Settings,
  HardDrive, MessageSquare, Network, Printer,
  Mail, Globe, Fingerprint, HelpCircle,
  Lock, AtSign, UserPlus, Search, MapPin, Briefcase,
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useAuth } from '../contexts/AuthContext';
import { User as UserType } from '../types/user';
import { UserSelectDropdown } from './UserSelectDropdown';
import { ErrorBoundary } from 'react-error-boundary';
import { UserRole, UserPermissions } from '../types/common';

interface TicketFormProps {
  onSubmit: (ticketData: any) => Promise<void>;
  onClose?: () => void;
  userPermissions?: UserPermissions;
}

interface Employee {
  id: string;
  name: string;
  department: string;
  email: string;
  phone: string;
}

interface Asset {
  id: string;
  name: string;
  serialNumber: string;
  imeiNumber?: string;
  type: string;
}

interface Department {
  id: string;
  name: string;
  head?: string;
  parentDepartment?: string;
}

const PRIORITY_BADGES = {
  low: { color: 'bg-green-100 text-green-800', label: '🟢 Low' },
  medium: { color: 'bg-yellow-100 text-yellow-800', label: '🟡 Medium' },
  high: { color: 'bg-red-100 text-red-800', label: '🔴 High' },
  critical: { color: 'bg-red-600 text-white', label: '🚨 Critical' }
};

const TICKET_CATEGORIES = [
  { value: 'hardware_issue', label: 'Hardware Issue', icon: HardDrive },
  { value: 'software_issue', label: 'Software Issue', icon: Settings },
  { value: 'network_issue', label: 'Network Issue', icon: Network },
  { value: 'printer_issue', label: 'Printer Issue', icon: Printer },
  { value: 'user_account', label: 'User Account Issues', icon: User },
  { value: 'email_communication', label: 'Email & Communication Issues', icon: Mail },
  { value: 'internet_access', label: 'Internet Access Required', icon: Globe },
  { value: 'biometric_enrollment', label: 'Bio Matric Attendance Enrollment', icon: Fingerprint },
  { value: 'erp_rights', label: 'ERP/SHE Accounts Rights Creation', icon: Lock },
  { value: 'email_creation', label: 'Email Creation', icon: AtSign },
  { value: 'other', label: 'Other', icon: HelpCircle }
];

// Temporary manual users
const MANUAL_USERS = [{
  id: "FRONTEND001",
  name: "Frontend User",
  department: "Testing",
  role: "IT_ADMIN",
  email: "<EMAIL>"
}];

// Import the PROJECT_LOCATIONS constant
const PROJECTS = [
  'Eurobiz Corporations',
  'Guardian International',
  'Guardian Developers',
  'Grand Developers',
  'ARD Developers'
];

// Define which locations are associated with each project
const PROJECT_LOCATIONS: Record<string, string[]> = {
  'Eurobiz Corporations': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Kharian'
  ],
  'Guardian International': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Arifwala'
  ],
  'Guardian Developers': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Vehari'
  ],
  'Grand Developers': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Faisalabad',
    'Grand City Site Office Murree'
  ],
  'ARD Developers': [
    'ARD Head Office Lahore',
    'ARD Site Office RUDA'
  ]
};

function ErrorFallback({ error }: { error: Error }) {
  return (
    <div role="alert" className="p-4 bg-red-100 text-red-700 rounded">
      <p>Something went wrong:</p>
      <pre className="mt-2">{error.message}</pre>
    </div>
  );
}

export const TicketForm: React.FC<TicketFormProps> = ({ 
  onSubmit, 
  onClose, 
  userPermissions = {
    role: 'EMPLOYEE',
    canCreateTickets: true,
    canCreateTicketsForOthers: false,
    canApproveRequests: false,
    canManageTickets: false,
    canViewAllTickets: false,
    canEscalateTickets: false,
    canCloseTickets: false,
    canReferTickets: false,
    department: 'General',
    isAdmin: false
  }
}) => {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [attachments, setAttachments] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    priority: 'low',
    status: 'open'
  });

  // Character count for description
  const MAX_DESCRIPTION_LENGTH = 500;
  const remainingChars = MAX_DESCRIPTION_LENGTH - formData.description.length;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp'
    ];

    const invalidFiles = files.filter(file => !allowedTypes.includes(file.type));
    if (invalidFiles.length > 0) {
      toast.error(`Only images (JPG, PNG, GIF, WebP) are allowed`);
      return;
    }

    const maxSize = 5 * 1024 * 1024; // 5MB
    const oversizedFiles = files.filter(file => file.size > maxSize);
    if (oversizedFiles.length > 0) {
      toast.error(`Images must be under 5MB: ${oversizedFiles.map(f => f.name).join(', ')}`);
      return;
    }

    setAttachments(prev => [...prev, ...files]);

    // Create preview URLs for all images
    files.forEach(file => {
      const url = URL.createObjectURL(file);
      setPreviewUrls(prev => [...prev, url]);
    });
  };

  // Cleanup preview URLs when component unmounts
  useEffect(() => {
    return () => {
      previewUrls.forEach(url => URL.revokeObjectURL(url));
    };
  }, [previewUrls]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!formData.title.trim()) {
        throw new Error('Title is required');
      }
      if (!formData.description.trim()) {
        throw new Error('Description is required');
      }
      if (!formData.category) {
        throw new Error('Category is required');
      }

      // Create FormData for multipart/form-data submission
      const formDataToSubmit = new FormData();
      
      // Append each field individually
      formDataToSubmit.append('title', formData.title);
      formDataToSubmit.append('description', formData.description);
      formDataToSubmit.append('category', formData.category);
      formDataToSubmit.append('priority', formData.priority);
      formDataToSubmit.append('status', formData.status);
      formDataToSubmit.append('department', user?.department || 'General');

      // Append each file to FormData
      attachments.forEach(file => {
        formDataToSubmit.append('files', file);
      });

      await onSubmit(formDataToSubmit);
      if (onClose) onClose();
    } catch (error: any) {
      console.error('Error submitting ticket:', error);
      toast.error(error.message || 'Failed to submit ticket');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center mb-6">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-blue-500" />
            Create New Ticket
          </h3>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Ticket title input */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Ticket Title *
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter ticket title"
              required
            />
          </div>

          <div className="space-y-4">
            {/* Category Selection with Icons */}
            <div>
              <label className="block text-sm font-medium text-gray-700 flex items-center gap-1 mb-1">
                <Settings className="h-4 w-4 text-gray-400" />
                Category *
              </label>
              <select
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                required
              >
                <option value="">Select category</option>
                {TICKET_CATEGORIES.map(({ value, label, icon: Icon }) => (
                  <option key={value} value={value} className="flex items-center gap-2">
                    {label}
                  </option>
                ))}
              </select>
            </div>

            {/* Priority Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 flex items-center gap-1 mb-1">
                <AlertCircle className="h-4 w-4 text-gray-400" />
                Priority
              </label>
              <select
                name="priority"
                value={formData.priority}
                onChange={handleInputChange}
                className="block w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              >
                {Object.entries(PRIORITY_BADGES).map(([value, { label }]) => (
                  <option key={value} value={value}>{label}</option>
                ))}
              </select>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 flex items-center gap-1 mb-1">
                <FileText className="h-4 w-4 text-gray-400" />
                Description *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                maxLength={MAX_DESCRIPTION_LENGTH}
                rows={6}
                className="block w-full rounded-md border border-gray-300 py-3 px-4 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm min-h-[150px] resize-y"
                required
                placeholder="Please provide detailed information about your issue..."
              />
              <p className="mt-1 text-sm text-gray-500 text-right">
                {remainingChars} characters remaining
              </p>
            </div>

            {/* File Attachments */}
            <div>
              <label className="block text-sm font-medium text-gray-700 flex items-center gap-1 mb-1">
                <Upload className="h-4 w-4 text-gray-400" />
                Attachments
              </label>
              {attachments.length > 0 && (
                <div className="mt-2 space-y-2">
                  <div className="text-xs font-medium text-gray-700">
                    Attached Images ({attachments.length})
                  </div>
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                    {previewUrls.map((url, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={url}
                          alt={`Preview ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg border border-gray-200"
                        />
                        <button
                          type="button"
                          onClick={() => {
                            setAttachments(prev => prev.filter((_, i) => i !== index));
                            setPreviewUrls(prev => {
                              URL.revokeObjectURL(prev[index]);
                              return prev.filter((_, i) => i !== index);
                            });
                          }}
                          className="absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* File Upload Input */}
              <div className="mt-1 flex justify-center px-4 py-4 border-2 border-gray-300 border-dashed rounded-lg hover:border-blue-400 transition-colors duration-200 bg-gray-50">
                <div className="space-y-1 text-center">
                  <Upload className="mx-auto h-8 w-8 text-gray-400" />
                  <div className="flex flex-col items-center text-sm text-gray-600">
                    <label
                      htmlFor="file-upload"
                      className="relative cursor-pointer rounded-md bg-white font-semibold text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 hover:underline px-3 py-1.5 shadow-sm"
                    >
                      <span>Upload images</span>
                      <input
                        id="file-upload"
                        type="file"
                        multiple
                        onChange={handleFileChange}
                        className="sr-only"
                        accept="image/jpeg,image/png,image/gif,image/webp"
                      />
                    </label>
                    <p className="text-xs text-gray-500 mt-1">
                      JPG, PNG, GIF, WebP up to 5MB
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isSubmitting}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isSubmitting ? (
                <>
                  <Clock className="animate-spin -ml-1 mr-2 h-4 w-4" />
                  Submitting...
                </>
              ) : (
                <>
                  <Send className="-ml-1 mr-2 h-4 w-4" />
                  Submit Ticket
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </ErrorBoundary>
  );
};