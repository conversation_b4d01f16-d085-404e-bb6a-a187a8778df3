import React, { useState, useEffect } from 'react';
import { 
  Users, 
  UserPlus, 
  Calendar, 
  BarChart3, 
  Filter, 
  Search, 
  Layers,
  FileCheck,
  Clock,
  Mail,
  AlertCircle,
  X,
  Check,
  Download,
  Plus,
  DollarSign
} from 'lucide-react';
import { Tab } from '@headlessui/react';
import { PayrollPeriod, PayrollStatus, PayrollEmployee, EmployeePayrollEntry, PayrollCurrency, PayrollFrequency } from '../../../types/payroll';
import IndividualPayrollProcessor from './IndividualPayrollProcessor';
import BulkPayrollProcessor from './BulkPayrollProcessor';

interface EmployeePayrollProps {
  isAdmin?: boolean;
}

const EmployeePayroll: React.FC<EmployeePayrollProps> = ({ isAdmin = false }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [payrollPeriods, setPayrollPeriods] = useState<PayrollPeriod[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState<PayrollPeriod | null>(null);
  const [loading, setLoading] = useState(false);
  const [processingCount, setProcessingCount] = useState(0);
  const [employees, setEmployees] = useState<PayrollEmployee[]>([]);
  const [payrollEntries, setPayrollEntries] = useState<EmployeePayrollEntry[]>([]);
  const [selectedCurrency, setSelectedCurrency] = useState<PayrollCurrency>(PayrollCurrency.USD);

  // Currency symbols mapping
  const currencySymbols: Record<PayrollCurrency, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    PKR: '₨',
    JPY: '¥',
    CAD: 'C$',
    AUD: 'A$',
    INR: '₹'
  };

  // Format currency amount
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: selectedCurrency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Fetch sample data
  useEffect(() => {
    // Simulate API call
    setLoading(true);
    setTimeout(() => {
      // Sample payroll periods
      const samplePeriods: PayrollPeriod[] = [
        {
          id: 1,
          name: 'May 2025',
          startDate: '2025-05-01',
          endDate: '2025-05-31',
          paymentDate: '2025-06-05',
          frequency: PayrollFrequency.MONTHLY,
          status: PayrollStatus.PROCESSING,
          totalEmployees: 32,
          totalAmount: 1250000,
          currency: PayrollCurrency.USD
        },
        {
          id: 2,
          name: 'April 2025',
          startDate: '2025-04-01',
          endDate: '2025-04-30',
          paymentDate: '2025-05-05',
          frequency: PayrollFrequency.MONTHLY,
          status: PayrollStatus.COMPLETED,
          totalEmployees: 32,
          totalAmount: 1245300,
          currency: PayrollCurrency.USD
        },
        {
          id: 3,
          name: 'March 2025',
          startDate: '2025-03-01',
          endDate: '2025-03-31',
          paymentDate: '2025-04-05',
          frequency: PayrollFrequency.MONTHLY,
          status: PayrollStatus.COMPLETED,
          totalEmployees: 31,
          totalAmount: 1220500,
          currency: PayrollCurrency.USD
        }
      ];

      // Sample employees
      const sampleEmployees: PayrollEmployee[] = [
        {
          id: 1,
          employeeId: 'EMP001',
          firstName: 'John',
          lastName: 'Smith',
          department: 'IT',
          designation: 'Senior Developer',
          joinDate: '2022-01-15',
          baseSalary: 85000,
          currency: PayrollCurrency.USD,
          status: 'active',
          paymentMethod: 'bank_transfer',
          bankName: 'City Bank',
          accountNumber: '**********',
          taxIdentificationNumber: 'TX123456'
        },
        {
          id: 2,
          employeeId: 'EMP002',
          firstName: 'Sarah',
          lastName: 'Johnson',
          department: 'HR',
          designation: 'HR Manager',
          joinDate: '2022-03-10',
          baseSalary: 75000,
          currency: PayrollCurrency.USD,
          status: 'active',
          paymentMethod: 'bank_transfer',
          bankName: 'Global Bank',
          accountNumber: '**********',
          taxIdentificationNumber: 'TX789012'
        },
        {
          id: 3,
          employeeId: 'EMP003',
          firstName: 'Michael',
          lastName: 'Wong',
          department: 'Finance',
          designation: 'Financial Analyst',
          joinDate: '2022-02-01',
          baseSalary: 70000,
          currency: PayrollCurrency.USD,
          status: 'active',
          paymentMethod: 'bank_transfer',
          bankName: 'City Bank',
          accountNumber: '**********',
          taxIdentificationNumber: 'TX345678'
        },
        {
          id: 4,
          employeeId: 'EMP004',
          firstName: 'Emma',
          lastName: 'Garcia',
          department: 'Marketing',
          designation: 'Marketing Director',
          joinDate: '2021-11-15',
          baseSalary: 95000,
          currency: PayrollCurrency.USD,
          status: 'active',
          paymentMethod: 'bank_transfer',
          bankName: 'Universal Bank',
          accountNumber: '**********',
          taxIdentificationNumber: 'TX901234'
        },
        {
          id: 5,
          employeeId: 'EMP005',
          firstName: 'David',
          lastName: 'Lee',
          department: 'IT',
          designation: 'DevOps Engineer',
          joinDate: '2022-04-20',
          baseSalary: 80000,
          currency: PayrollCurrency.USD,
          status: 'active',
          paymentMethod: 'bank_transfer',
          bankName: 'Global Bank',
          accountNumber: '**********',
          taxIdentificationNumber: 'TX567890'
        }
      ];

      // Sample payroll entries
      const samplePayrollEntries: EmployeePayrollEntry[] = [
        {
          id: 1,
          payrollPeriodId: 1,
          employeeId: 1,
          employeeName: 'John Smith',
          department: 'IT',
          designation: 'Senior Developer',
          baseSalary: 85000,
          grossSalary: 7500,
          totalEarnings: 7500,
          totalDeductions: 750,
          totalTax: 1125,
          netSalary: 5625,
          paymentMethod: 'bank_transfer',
          paymentStatus: 'pending',
          bankName: 'City Bank',
          accountNumber: '**********',
          currency: PayrollCurrency.USD,
          remarks: '',
          earnings: [],
          deductions: [],
          taxes: [],
          attendance: {
            totalDays: 31,
            presentDays: 21,
            absentDays: 0,
            leaveDays: 1,
            holidayDays: 9,
            workingDays: 22
          }
        },
        {
          id: 2,
          payrollPeriodId: 1,
          employeeId: 2,
          employeeName: 'Sarah Johnson',
          department: 'HR',
          designation: 'HR Manager',
          baseSalary: 75000,
          grossSalary: 6250,
          totalEarnings: 6250,
          totalDeductions: 625,
          totalTax: 938,
          netSalary: 4687,
          paymentMethod: 'bank_transfer',
          paymentStatus: 'pending',
          bankName: 'Global Bank',
          accountNumber: '**********',
          currency: PayrollCurrency.USD,
          remarks: '',
          earnings: [],
          deductions: [],
          taxes: [],
          attendance: {
            totalDays: 31,
            presentDays: 22,
            absentDays: 0,
            leaveDays: 0,
            holidayDays: 9,
            workingDays: 22
          }
        }
      ];

      setPayrollPeriods(samplePeriods);
      setSelectedPeriod(samplePeriods[0]); // Set first period as selected
      setEmployees(sampleEmployees);
      setPayrollEntries(samplePayrollEntries);
      setProcessingCount(samplePayrollEntries.length);
      setSelectedCurrency(samplePeriods[0].currency || PayrollCurrency.USD);
      setLoading(false);
    }, 1000);
  }, []);

  // Status badge component with appropriate colors
  const StatusBadge = ({ status }: { status: PayrollStatus }) => {
    const statusConfig = {
      draft: { bg: 'bg-gray-100', text: 'text-gray-800' },
      pending: { bg: 'bg-yellow-100', text: 'text-yellow-800' },
      processing: { bg: 'bg-blue-100', text: 'text-blue-800' },
      approved: { bg: 'bg-green-100', text: 'text-green-800' },
      completed: { bg: 'bg-green-100', text: 'text-green-800' },
      rejected: { bg: 'bg-red-100', text: 'text-red-800' },
      on_hold: { bg: 'bg-purple-100', text: 'text-purple-800' }
    };

    const config = statusConfig[status] || statusConfig.draft;

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {status.replace('_', ' ').charAt(0).toUpperCase() + status.replace('_', ' ').slice(1)}
      </span>
    );
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Employee Payroll</h3>
            <p className="text-sm text-gray-500">Manage and process employee payroll</p>
          </div>

          <div className="mt-4 md:mt-0 flex flex-col md:flex-row gap-2 md:items-center">
            {selectedPeriod && (
              <div className="flex space-x-2 items-center bg-blue-50 px-4 py-2 rounded-lg">
                <Calendar className="h-4 w-4 text-blue-600" />
                <div>
                  <span className="text-sm font-medium text-gray-700">Current Period:</span>
                  <span className="ml-1 text-sm font-medium text-blue-600">{selectedPeriod.name}</span>
                </div>
                <StatusBadge status={selectedPeriod.status} />
              </div>
            )}
            
            <div className="flex space-x-2 items-center bg-gray-50 px-4 py-2 rounded-lg">
              <DollarSign className="h-4 w-4 text-gray-600" />
              <select
                value={selectedCurrency}
                onChange={(e) => setSelectedCurrency(e.target.value as PayrollCurrency)}
                className="bg-transparent border-none text-sm font-medium text-gray-700 focus:ring-0 focus:outline-none"
              >
                <option value="USD">USD ($)</option>
                <option value="EUR">EUR (€)</option>
                <option value="GBP">GBP (£)</option>
                <option value="PKR">PKR (₨)</option>
                <option value="JPY">JPY (¥)</option>
                <option value="CAD">CAD (C$)</option>
                <option value="AUD">AUD (A$)</option>
                <option value="INR">INR (₹)</option>
              </select>
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg shadow flex-1 min-w-[200px] border-l-4 border-blue-500">
            <p className="text-sm text-gray-500">Total Employees</p>
            <div className="flex items-center mt-1">
              <Users className="h-5 w-5 text-blue-500 mr-2" />
              <span className="text-2xl font-bold text-gray-800">{selectedPeriod?.totalEmployees || 0}</span>
            </div>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow flex-1 min-w-[200px] border-l-4 border-green-500">
            <p className="text-sm text-gray-500">Processed</p>
            <div className="flex items-center mt-1">
              <Check className="h-5 w-5 text-green-500 mr-2" />
              <span className="text-2xl font-bold text-gray-800">{processingCount}</span>
            </div>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow flex-1 min-w-[200px] border-l-4 border-yellow-500">
            <p className="text-sm text-gray-500">Pending Approval</p>
            <div className="flex items-center mt-1">
              <Clock className="h-5 w-5 text-yellow-500 mr-2" />
              <span className="text-2xl font-bold text-gray-800">0</span>
            </div>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow flex-1 min-w-[200px] border-l-4 border-purple-500">
            <p className="text-sm text-gray-500">Total Amount</p>
            <div className="flex items-center mt-1">
              <BarChart3 className="h-5 w-5 text-purple-500 mr-2" />
              <span className="text-2xl font-bold text-gray-800">
                {selectedPeriod ? formatCurrency(selectedPeriod.totalAmount) : formatCurrency(0)}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <Tab.Group selectedIndex={activeTab} onChange={setActiveTab}>
          <Tab.List className="flex bg-gray-50 border-b border-gray-200">
            <Tab className={({ selected }) =>
              `py-3 px-4 text-sm font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
              ${selected 
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`
            }>
              <UserPlus className="h-4 w-4 mr-2" />
              Individual Processing
            </Tab>
            
            <Tab className={({ selected }) =>
              `py-3 px-4 text-sm font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
              ${selected 
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`
            }>
              <Layers className="h-4 w-4 mr-2" />
              Bulk Processing
            </Tab>
            
            <Tab className={({ selected }) =>
              `py-3 px-4 text-sm font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
              ${selected 
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`
            }>
              <FileCheck className="h-4 w-4 mr-2" />
              Approval Workflow
            </Tab>
            
            <Tab className={({ selected }) =>
              `py-3 px-4 text-sm font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
              ${selected 
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`
            }>
              <Mail className="h-4 w-4 mr-2" />
              Payslips
            </Tab>
          </Tab.List>
          
          <Tab.Panels>
            <Tab.Panel>
              <IndividualPayrollProcessor isAdmin={isAdmin} selectedCurrency={selectedCurrency} />
            </Tab.Panel>
            
            <Tab.Panel>
              <BulkPayrollProcessor isAdmin={isAdmin} selectedCurrency={selectedCurrency} />
            </Tab.Panel>
            
            <Tab.Panel>
              <div className="p-6">
                <div className="pb-4 border-b border-gray-200 mb-6">
                  <h4 className="text-base font-medium text-gray-900">Approval Workflow</h4>
                  <p className="text-sm text-gray-500">Review and approve payroll before final processing</p>
                </div>
                
                {/* Simple approval workflow table */}
                <div className="bg-white rounded-lg shadow overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Request ID
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Period
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Employees
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Requested By
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Currency
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total Amount
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">PYR-2025050001</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">May 2025</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Bulk Processing</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">32</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Sarah Johnson</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">USD</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatCurrency(1250000)}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            Pending Approval
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button className="text-blue-600 hover:text-blue-900 mr-3">View</button>
                          <button className="text-green-600 hover:text-green-900 mr-3">Approve</button>
                          <button className="text-red-600 hover:text-red-900">Reject</button>
                        </td>
                      </tr>
                      {/* You can add more rows here */}
                    </tbody>
                  </table>
                </div>
              </div>
            </Tab.Panel>
            
            <Tab.Panel>
              <div className="p-6">
                <div className="pb-4 border-b border-gray-200 mb-6">
                  <h4 className="text-base font-medium text-gray-900">Payslips</h4>
                  <p className="text-sm text-gray-500">Generate and distribute employee payslips</p>
                </div>
                
                <div className="flex justify-between items-center mb-4">
                  <div className="flex space-x-2">
                    <select className="border border-gray-300 rounded-md px-3 py-2 text-sm">
                      <option>May 2025</option>
                      <option>April 2025</option>
                      <option>March 2025</option>
                    </select>
                    <select className="border border-gray-300 rounded-md px-3 py-2 text-sm">
                      <option>All Departments</option>
                      <option>IT</option>
                      <option>HR</option>
                      <option>Finance</option>
                      <option>Marketing</option>
                    </select>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                      <Mail className="h-4 w-4 mr-2" />
                      Email Selected
                    </button>
                    <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                      <Download className="h-4 w-4 mr-2" />
                      Download All
                    </button>
                  </div>
                </div>
                
                <div className="bg-white rounded-lg shadow overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="p-4">
                          <input type="checkbox" className="h-4 w-4 text-blue-600 border-gray-300 rounded" />
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Employee
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Department
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Period
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Currency
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Net Salary
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {payrollEntries.map(entry => (
                        <tr key={entry.id}>
                          <td className="p-4 whitespace-nowrap">
                            <input type="checkbox" className="h-4 w-4 text-blue-600 border-gray-300 rounded" />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div>
                                <div className="text-sm font-medium text-gray-900">{entry.employeeName}</div>
                                <div className="text-sm text-gray-500">{entry.designation}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{entry.department}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {payrollPeriods.find(p => p.id === entry.payrollPeriodId)?.name || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {entry.currency || 'USD'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {entry.currency ? formatCurrency(entry.netSalary) : formatCurrency(entry.netSalary)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                              {entry.paymentStatus.charAt(0).toUpperCase() + entry.paymentStatus.slice(1)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button className="text-indigo-600 hover:text-indigo-900 mr-3">View</button>
                            <button className="text-blue-600 hover:text-blue-900 mr-3">
                              <Download className="h-4 w-4" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </Tab.Panel>
          </Tab.Panels>
        </Tab.Group>
      </div>
    </div>
  );
};

export default EmployeePayroll; 