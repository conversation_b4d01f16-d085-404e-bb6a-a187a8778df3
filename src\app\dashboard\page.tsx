import React, { useEffect, useState, useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { socketService, NotificationData } from '../../services/socket';

// Define the types needed for the Dashboard
interface DashboardStats {
  ticketsCreated: number;
  ticketsResolved: number;
  ticketsOpen: number;
  ticketsByPriority: Record<string, number>;
  ticketsByDepartment: Record<string, number>;
  ticketsByStatus: Record<string, number>;
  // Add other stats as needed
}

interface Notification {
  id: string;
  type: string;
  message: string;
  timestamp: Date;
  read: boolean;
}

export default function Dashboard() {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [connectionStatus, setConnectionStatus] = useState(false);

  // Fetch dashboard stats
  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/dashboard/stats');
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard stats');
      }
      const data = await response.json();
      setStats(data);
    } catch (err) {
      console.error('Error fetching dashboard stats:', err);
      setError('Failed to load dashboard statistics');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (user) {
      fetchStats();
    }
  }, [user, fetchStats]);

  // Connect to socket service when user is authenticated
  useEffect(() => {
    let unsubscribe: (() => void) | null = null;

    const connectToSocket = async () => {
      if (!user) return;
      
      try {
        console.log('Connecting to socket service...');
        await socketService.connect();
        setConnectionStatus(true);
        
        // Subscribe to notifications
        if (user.id) {
          socketService.subscribeToNotifications(user.id);
          
          // Set up notification handler
          unsubscribe = socketService.onNotification((data: NotificationData) => {
            console.log('Notification received:', data);
            setNotifications(prev => [
              {
                id: data.id,
                message: data.message,
                type: data.type,
                timestamp: new Date(data.timestamp),
                read: data.read
              },
              ...prev
            ]);
          });
        }
      } catch (error) {
        console.error('Socket connection error:', error);
        setConnectionStatus(false);
      }
    };

    connectToSocket();

    // Cleanup function - unsubscribe but don't disconnect
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
      if (user?.id) {
        socketService.unsubscribeFromNotifications(user.id);
      }
    };
  }, [user]);

  if (loading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500 p-4">{error}</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">Dashboard</h1>
        <p className="text-gray-600">
          Welcome back, {user?.name}! 
          {connectionStatus ? 
            <span className="text-green-500 ml-2">● Connected to notification service</span> : 
            <span className="text-red-500 ml-2">● Disconnected from notification service</span>
          }
        </p>
      </div>

      {/* Dashboard content */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4">Tickets Overview</h2>
            <div className="flex justify-between">
              <div>
                <p className="text-gray-600">Created</p>
                <p className="text-2xl font-bold">{stats.ticketsCreated}</p>
              </div>
              <div>
                <p className="text-gray-600">Resolved</p>
                <p className="text-2xl font-bold">{stats.ticketsResolved}</p>
              </div>
              <div>
                <p className="text-gray-600">Open</p>
                <p className="text-2xl font-bold">{stats.ticketsOpen}</p>
              </div>
            </div>
          </div>
          
          {/* Add more dashboard widgets as needed */}
        </div>
      )}
    </div>
  );
} 