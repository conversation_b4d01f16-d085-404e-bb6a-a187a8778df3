import React, { useState, useEffect } from 'react';
import { Shield, Save, AlertCircle, Eye, Download, Search, Filter, Calendar, User, Activity } from 'lucide-react';

interface Setting {
  id: string;
  category: string;
  key: string;
  value: string;
  description: string;
  lastUpdated?: string;
  updatedBy?: string;
}

interface AuditLog {
  id: string;
  timestamp: string;
  user: string;
  action: string;
  resource: string;
  details: string;
  ipAddress: string;
  userAgent: string;
  status: 'success' | 'warning' | 'error';
}

interface AuditSettingsProps {
  settings: Setting[];
  onSettingChange: (id: string, value: string) => void;
  onSave: () => void;
  isSaving?: boolean;
  saveSuccess?: boolean;
}

const AuditSettings: React.FC<AuditSettingsProps> = ({
  settings,
  onSettingChange,
  onSave,
  isSaving = false,
  saveSuccess = false
}) => {
  const [localSettings, setLocalSettings] = useState<Setting[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [exportingLogs, setExportingLogs] = useState(false);
  const [realTimeMonitoring, setRealTimeMonitoring] = useState(false);

  useEffect(() => {
    const auditSettings = settings.filter(setting => setting.category === 'audit');
    setLocalSettings(auditSettings);
    
    // Check if real-time monitoring is enabled
    const realtimeSetting = auditSettings.find(s => s.key === 'enableRealTimeMonitoring');
    setRealTimeMonitoring(realtimeSetting?.value === 'true');
  }, [settings]);

  useEffect(() => {
    // Generate sample audit logs
    const sampleLogs: AuditLog[] = [
      {
        id: '1',
        timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
        user: '<EMAIL>',
        action: 'User Login',
        resource: 'Authentication System',
        details: 'Successful login from dashboard',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        status: 'success'
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
        user: '<EMAIL>',
        action: 'Settings Modified',
        resource: 'System Settings',
        details: 'Updated email configuration',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        status: 'success'
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        user: '<EMAIL>',
        action: 'Failed Login Attempt',
        resource: 'Authentication System',
        details: 'Invalid password provided',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
        status: 'error'
      },
      {
        id: '4',
        timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
        user: 'system',
        action: 'Backup Created',
        resource: 'Backup System',
        details: 'Automated daily backup completed',
        ipAddress: 'localhost',
        userAgent: 'System Process',
        status: 'success'
      },
      {
        id: '5',
        timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
        user: '<EMAIL>',
        action: 'User Created',
        resource: 'User Management',
        details: 'Created new user <NAME_EMAIL>',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        status: 'success'
      }
    ];
    setAuditLogs(sampleLogs);
  }, []);

  useEffect(() => {
    // Simulate real-time log updates when monitoring is enabled
    if (realTimeMonitoring) {
      const interval = setInterval(() => {
        const newLog: AuditLog = {
          id: Date.now().toString(),
          timestamp: new Date().toISOString(),
          user: ['<EMAIL>', '<EMAIL>', '<EMAIL>'][Math.floor(Math.random() * 3)],
          action: ['Page View', 'Data Access', 'Settings View', 'Report Generated'][Math.floor(Math.random() * 4)],
          resource: ['Dashboard', 'User Management', 'Reports', 'Settings'][Math.floor(Math.random() * 4)],
          details: 'Real-time activity detected',
          ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
          status: Math.random() > 0.9 ? 'warning' : 'success'
        };
        
        setAuditLogs(prev => [newLog, ...prev.slice(0, 49)]); // Keep only latest 50 logs
      }, 10000); // Add new log every 10 seconds

      return () => clearInterval(interval);
    }
  }, [realTimeMonitoring]);

  const handleChange = (id: string, value: string) => {
    setLocalSettings(prev => prev.map(setting => 
      setting.id === id ? { ...setting, value } : setting
    ));
    setHasChanges(true);
    onSettingChange(id, value);
    
    // Update real-time monitoring state
    const setting = localSettings.find(s => s.id === id);
    if (setting?.key === 'enableRealTimeMonitoring') {
      setRealTimeMonitoring(value === 'true');
    }
  };

  const handleSave = () => {
    onSave();
    setHasChanges(false);
  };

  const handleExportLogs = async () => {
    setExportingLogs(true);
    
    // Simulate export process
    setTimeout(() => {
      const filteredLogs = getFilteredLogs();
      const csvContent = [
        'Timestamp,User,Action,Resource,Details,IP Address,Status',
        ...filteredLogs.map(log => 
          `"${log.timestamp}","${log.user}","${log.action}","${log.resource}","${log.details}","${log.ipAddress}","${log.status}"`
        )
      ].join('\n');
      
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
      setExportingLogs(false);
    }, 2000);
  };

  const getFilteredLogs = () => {
    return auditLogs.filter(log => {
      const matchesSearch = searchTerm === '' || 
        log.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.resource.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.details.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = filterStatus === 'all' || log.status === filterStatus;
      
      const logDate = new Date(log.timestamp).toISOString().split('T')[0];
      const matchesDateRange = (!dateRange.start || logDate >= dateRange.start) &&
                              (!dateRange.end || logDate <= dateRange.end);
      
      return matchesSearch && matchesStatus && matchesDateRange;
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-50 border-green-200';
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'error': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const isNumberField = (key: string) => {
    return key.toLowerCase().includes('days') || 
           key.toLowerCase().includes('retention') || 
           key.toLowerCase().includes('limit');
  };

  const filteredLogs = getFilteredLogs();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Shield className="h-6 w-6 text-purple-600" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Audit & Logs Settings</h2>
            <p className="text-sm text-gray-600">Configure audit logging, monitoring, and compliance settings</p>
          </div>
        </div>
        {hasChanges && (
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSaving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        )}
      </div>

      {saveSuccess && (
        <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700">
          <AlertCircle className="h-4 w-4" />
          Audit settings saved successfully!
        </div>
      )}

      {realTimeMonitoring && (
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Activity className="h-4 w-4 text-purple-600 animate-pulse" />
            <span className="text-sm font-medium text-purple-900">Real-time Monitoring Active</span>
          </div>
          <p className="text-sm text-purple-700">
            System is actively monitoring and logging user activities in real-time.
          </p>
        </div>
      )}

      {/* Settings Configuration */}
      <div className="bg-white rounded-lg border border-gray-200 divide-y divide-gray-200">
        <div className="p-4 bg-gray-50">
          <h3 className="text-lg font-medium text-gray-900">Audit Configuration</h3>
        </div>
        {localSettings.map((setting) => (
          <div key={setting.id} className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-900 mb-1">
                  {setting.key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </label>
                <p className="text-sm text-gray-600 mb-3">{setting.description}</p>
                
                {setting.key.includes('enable') || setting.key.includes('Enable') ? (
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={setting.id}
                      checked={setting.value === 'true'}
                      onChange={(e) => handleChange(setting.id, e.target.checked ? 'true' : 'false')}
                      className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                    />
                    <label htmlFor={setting.id} className="ml-2 text-sm text-gray-700">
                      {setting.value === 'true' ? 'Enabled' : 'Disabled'}
                    </label>
                  </div>
                ) : isNumberField(setting.key) ? (
                  <div className="flex items-center gap-3">
                    <input
                      type="number"
                      value={setting.value}
                      onChange={(e) => handleChange(setting.id, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder={`Enter ${setting.key}`}
                      min="0"
                    />
                    {setting.key.includes('days') && (
                      <span className="text-sm text-gray-500">days</span>
                    )}
                  </div>
                ) : (
                  <input
                    type="text"
                    value={setting.value}
                    onChange={(e) => handleChange(setting.id, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder={`Enter ${setting.key}`}
                  />
                )}
              </div>
            </div>
            
            {setting.lastUpdated && (
              <div className="mt-3 text-xs text-gray-500">
                Last updated: {new Date(setting.lastUpdated).toLocaleString()} by {setting.updatedBy}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Audit Logs Viewer */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Recent Audit Logs</h3>
            <button
              onClick={handleExportLogs}
              disabled={exportingLogs}
              className="flex items-center gap-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {exportingLogs ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Download className="h-4 w-4" />
              )}
              {exportingLogs ? 'Exporting...' : 'Export Logs'}
            </button>
          </div>
          
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search logs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
            
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="success">Success</option>
              <option value="warning">Warning</option>
              <option value="error">Error</option>
            </select>
            
            <input
              type="date"
              placeholder="Start Date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
            
            <input
              type="date"
              placeholder="End Date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
        </div>
        
        <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
          {filteredLogs.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <Eye className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p>No audit logs found matching your criteria.</p>
            </div>
          ) : (
            filteredLogs.map((log) => (
              <div key={log.id} className="p-4 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(log.status)}`}>
                        {log.status.charAt(0).toUpperCase() + log.status.slice(1)}
                      </span>
                      <span className="text-sm font-medium text-gray-900">{log.action}</span>
                      <span className="text-sm text-gray-500">•</span>
                      <span className="text-sm text-gray-600">{log.resource}</span>
                    </div>
                    <p className="text-sm text-gray-700 mb-2">{log.details}</p>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {log.user}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {new Date(log.timestamp).toLocaleString()}
                      </div>
                      <span>IP: {log.ipAddress}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default AuditSettings;