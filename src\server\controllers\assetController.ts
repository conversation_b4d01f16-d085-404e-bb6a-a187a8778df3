import { Request, Response } from 'express';
import { assetRepository } from '../../repositories/assetRepository';
import { Asset, AssetType, AssetStatus, AssetCondition } from '../../entities/Asset';
import { validate } from 'class-validator';
import { AppDataSource } from '../../config/database';
import { v4 as uuidv4 } from 'uuid';
import logger from '../../utils/logger';
import { User } from '../../entities/User';
import { ValidationError } from 'class-validator';

export const assetController = {
  async getAssets(req: Request, res: Response) {
    try {
      const { 
        search, 
        assetType, 
        status, 
        department, 
        assignedToId, 
        location,
        warrantyExpiring,
        needsMaintenance,
        page, 
        limit 
      } = req.query;
      
      console.log('Asset query params:', { search, assetType, status, department, assignedToId, location, warrantyExpiring, needsMaintenance, page, limit });
      
      const result = await assetRepository.findAll({
        search: search as string,
        assetType: assetType as string,
        status: status as string,
        department: department as string,
        assignedToId: assignedToId as string,
        location: location as string,
        warrantyExpiring: warrantyExpiring === 'true',
        needsMaintenance: needsMaintenance === 'true',
        page: page ? parseInt(page as string) : undefined,
        limit: limit ? parseInt(limit as string) : undefined
      });
      
      // If result has assets property and it's an array, return that
      if (result && typeof result === 'object' && 'assets' in result && Array.isArray(result.assets)) {
        console.log(`Returning ${result.assets.length} assets`);
        return res.json(result.assets);
      }
      
      // If result is already an array, return it
      if (Array.isArray(result)) {
        console.log(`Returning ${result.length} assets`);
        return res.json(result);
      }
      
      // If we got here, return an empty array
      console.log('No assets found or invalid result format, returning empty array');
      return res.json([]);
    } catch (error) {
      console.error('Error fetching assets:', error);
      res.status(500).json({ error: 'Failed to fetch assets' });
    }
  },
  
  async getAssetById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      console.log('Getting asset by ID:', id);
      
      const asset = await assetRepository.findById(id);
      
      if (!asset) {
        console.error('Asset not found with ID:', id);
        return res.status(404).json({ 
          error: 'Asset not found. It may have been deleted or the ID is invalid.',
          id: id
        });
      }
      
      console.log('Found asset:', asset.id, asset.assetTag);
      res.json(asset);
    } catch (error) {
      console.error('Error fetching asset:', error);
      res.status(500).json({ error: 'Failed to fetch asset' });
    }
  },
  
  async createAsset(req: Request, res: Response) {
    try {
      logger.info('=== CREATE ASSET REQUEST ===');
      logger.info('Request body:', req.body);
      logger.info('File upload:', req.file ? `${req.file.originalname} (${req.file.size} bytes)` : 'None');
      
      // Parse asset data
      let assetData: Partial<Asset>;
      try {
        assetData = req.body.assetData ? JSON.parse(req.body.assetData) : req.body;
        logger.info('Parsed asset data:', assetData);
      } catch (error) {
        logger.error('Error parsing asset data:', error);
        return res.status(400).json({ error: 'Invalid asset data format' });
      }

      // Validate required fields
      const requiredFields = ['assetType', 'manufacturer', 'model', 'serialNumber'] as const;
      type RequiredField = typeof requiredFields[number];
      
      const missingFields = requiredFields.filter((field: RequiredField) => {
        const value = assetData[field];
        return value === undefined || value === null || value === '';
      });
      
      if (missingFields.length > 0) {
        logger.error('Missing required fields:', missingFields);
        return res.status(400).json({
          error: 'Missing required fields',
          missingFields
        });
      }

      // Validate assetType enum
      const assetType = assetData.assetType as string;
      if (!Object.values(AssetType).includes(assetType as AssetType)) {
        logger.error('Invalid asset type:', assetType);
        logger.error('Valid types:', Object.values(AssetType));
        return res.status(400).json({
          error: 'Invalid asset type',
          validTypes: Object.values(AssetType),
          providedType: assetType
        });
      }

      // Set default values
      assetData.status = assetData.status || AssetStatus.Active;
      assetData.condition = assetData.condition || AssetCondition.New;
      assetData.location = assetData.location || 'Main Office';
      assetData.department = assetData.department || 'IT';
      assetData.createdAt = new Date();
      assetData.updatedAt = new Date();

      // Create the asset
      try {
        logger.info('Creating asset with data:', assetData);
        const asset = await assetRepository.create(assetData);
        logger.info('Asset created successfully:', asset);
        
        res.status(201).json(asset);
      } catch (error: any) {
        logger.error('Error creating asset:', error);
        
        if (error.code === 'ER_DUP_ENTRY') {
          return res.status(400).json({
            error: 'Duplicate entry',
            message: 'An asset with this serial number already exists'
          });
        }

        return res.status(500).json({
          error: 'Failed to create asset',
          message: error.message
        });
      }
    } catch (error: any) {
      logger.error('Unexpected error in createAsset:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      });
    }
  },
  
  async updateAsset(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // Special handling for assignedToId
      if ('assignedToId' in updateData) {
        if (updateData.assignedToId === null) {
          // Clear the assignment but keep the department if it's explicitly set
          updateData.assignedTo = undefined;
          updateData.assignedToId = undefined;
          updateData.assignedAt = undefined;
          // Only clear department if it's not explicitly set in the update data
          if (!('department' in updateData) || !updateData.department) {
            updateData.department = undefined;
          }
        } else {
          // Set the assignment
          const user = await AppDataSource.getRepository(User).findOneBy({ id: updateData.assignedToId });
          if (user) {
            updateData.assignedTo = user;
            updateData.assignedAt = new Date();
            // Only update department if it's not explicitly set in the update data
            if (!('department' in updateData) || !updateData.department) {
              updateData.department = user.department;
            }
          } else {
            // If user not found, clear the assignment but keep department if set
            updateData.assignedTo = undefined;
            updateData.assignedToId = undefined;
            updateData.assignedAt = undefined;
            if (!('department' in updateData) || !updateData.department) {
              updateData.department = undefined;
            }
          }
        }
      }

      // Remove assignedTo from updateData if it exists as an object
      if (updateData.assignedTo && typeof updateData.assignedTo === 'object') {
        delete updateData.assignedTo;
      }

      const asset = await assetRepository.update(id, updateData);
      res.json(asset);
    } catch (error) {
      console.error('Error updating asset:', error);
      if (error instanceof ValidationError) {
        // Log validation errors with more detail
        console.error('Validation error details:', {
          field: error.property,
          value: error.value,
          valueType: typeof error.value,
          constraints: error.constraints
        });
        return res.status(400).json({
          error: 'Validation failed',
          details: {
            field: error.property,
            value: error.value,
            constraints: error.constraints
          },
          message: 'Please check the required fields and their values'
        });
      }
      res.status(500).json({ error: 'Failed to update asset' });
    }
  },
  
  async deleteAsset(req: Request, res: Response) {
    try {
      const { id } = req.params;
      console.log('=== DELETE ASSET REQUEST ===');
      console.log('Deleting asset with ID/Tag:', id);
      
      // Try to find the asset directly by asset tag first if it looks like a tag
      let existingAsset = null;
      
      if (id.includes('-')) {
        console.log('ID looks like an asset tag, trying direct lookup by assetTag first');
        const assetRepo = AppDataSource.getRepository(Asset);
        existingAsset = await assetRepo.findOne({ 
          where: { assetTag: id }
        });
        
        if (existingAsset) {
          console.log('Asset found directly by assetTag:', existingAsset.id, existingAsset.assetTag);
        } else {
          console.log('Asset not found directly by assetTag, trying repository method');
          existingAsset = await assetRepository.findById(id);
        }
      } else {
        console.log('ID does not look like an asset tag, using repository method');
        existingAsset = await assetRepository.findById(id);
      }
      
      if (!existingAsset) {
        console.error('Asset not found with ID/Tag:', id);
        
        // List all asset tags to help diagnose
        const assetRepo = AppDataSource.getRepository(Asset);
        const allAssets = await assetRepo.find({ select: ['id', 'assetTag'] });
        const assetTags = allAssets.map(a => a.assetTag);
        
        return res.status(404).json({ 
          error: 'Asset not found. It may have been deleted or the ID is invalid.',
          id: id,
          availableTags: assetTags.slice(0, 10) // Show first 10 tags to avoid overwhelming response
        });
      }
      
      // Store the actual UUID for deletion
      const assetUuid = existingAsset.id;
      console.log('Found asset with UUID:', assetUuid);
      console.log('Asset tag:', existingAsset.assetTag);
      
      // Delete the asset using the UUID
      await assetRepository.delete(String(assetUuid));
      
      res.json({ success: true, message: 'Asset deleted successfully' });
    } catch (error) {
      console.error('Error deleting asset:', error);
      res.status(500).json({ error: 'Failed to delete asset' });
    }
  },
  
  async assignAsset(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { userId } = req.body;
      
      if (!userId) {
        return res.status(400).json({ error: 'User ID is required' });
      }
      
      // Check if asset exists - now handles both UUID and formatted IDs
      const existingAsset = await assetRepository.findById(id);
      if (!existingAsset) {
        console.error('Asset not found with ID:', id);
        return res.status(404).json({ 
          error: 'Asset not found. It may have been deleted or the ID is invalid.',
          id: id
        });
      }
      
      // Use the actual UUID for assignment
      const assetUuid = existingAsset.id;
      console.log('Assigning asset with UUID:', assetUuid, 'to user:', userId);
      
      const asset = await assetRepository.assignToUser(String(assetUuid), userId);
      
      res.json(asset);
    } catch (error) {
      console.error('Error assigning asset:', error);
      res.status(500).json({ error: 'Failed to assign asset' });
    }
  },
  
  async unassignAsset(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      // Check if asset exists - now handles both UUID and formatted IDs
      const existingAsset = await assetRepository.findById(id);
      if (!existingAsset) {
        console.error('Asset not found with ID:', id);
        return res.status(404).json({ 
          error: 'Asset not found. It may have been deleted or the ID is invalid.',
          id: id
        });
      }
      
      // Use the actual UUID for unassignment
      const assetUuid = existingAsset.id;
      console.log('Unassigning asset with UUID:', assetUuid);
      
      const asset = await assetRepository.unassignFromUser(String(assetUuid));
      
      res.json(asset);
    } catch (error) {
      console.error('Error unassigning asset:', error);
      res.status(500).json({ error: 'Failed to unassign asset' });
    }
  },
  
  async getAssetDepreciation(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      const depreciationData = await assetRepository.getAssetDepreciation(id);
      
      res.json(depreciationData);
    } catch (error) {
      console.error('Error calculating asset depreciation:', error);
      res.status(500).json({ error: 'Failed to calculate asset depreciation' });
    }
  },
  
  async getAssetStats(req: Request, res: Response) {
    try {
      const { timeRange, department, status } = req.query;
      
      // Get real stats from the database
      const stats = await assetRepository.getAssetStats();
      
      res.json(stats);
    } catch (error) {
      console.error('Error fetching asset stats:', error);
      res.status(500).json({ error: 'Failed to fetch asset stats' });
    }
  }
}; 