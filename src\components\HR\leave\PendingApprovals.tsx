import React, { useState, useEffect } from 'react';
import { CheckCircle, X, Check, AlertTriangle } from 'lucide-react';
import { LeaveRequest, LeaveStatus } from '../../../types/attendance';

interface PendingApprovalsProps {
  pendingRequests: LeaveRequest[];
  onApprove: (request: LeaveRequest) => void;
  onReject: (request: LeaveRequest) => void;
  validateLeaveBalance: (employeeId: number, leaveType: string, requestedDays: number) => any;
  detectLeaveConflicts: (employeeId: number, startDate: string, endDate: string, excludeRequestId?: number) => any;
}

const PendingApprovals: React.FC<PendingApprovalsProps> = ({
  pendingRequests,
  onApprove,
  onReject,
  validateLeaveBalance,
  detectLeaveConflicts
}) => {
  const calculateDays = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  };

  const getLeaveTypeLabel = (leaveType: string) => {
    const labels: { [key: string]: string } = {
      'Annual': 'Annual Leave',
      'Sick': 'Sick Leave',
      'Personal': 'Personal Leave',
      'Unpaid': 'Unpaid Leave',
      'Maternity': 'Maternity Leave',
      'Paternity': 'Paternity Leave',
      'Bereavement': 'Bereavement Leave',
      'Compensatory Off': 'Compensatory Off',
      'Other': 'Other'
    };
    return labels[leaveType] || leaveType;
  };

  if (pendingRequests.length === 0) {
    return (
      <div className="text-center py-12">
        <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">All caught up!</h3>
        <p className="text-gray-500">No pending leave requests to review.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Pending Approvals</h3>
        <div className="text-sm text-gray-500">
          {pendingRequests.length} requests awaiting approval
        </div>
      </div>

      <div className="space-y-4">
        {pendingRequests.map((request) => {
          const requestedDays = calculateDays(request.startDate, request.endDate);
          const balanceValidation = validateLeaveBalance(request.employeeId, request.leaveType, requestedDays);
          const conflictCheck = detectLeaveConflicts(request.employeeId, request.startDate, request.endDate, request.id);

          return (
            <div key={request.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-gray-600">
                      {request.employeeName.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">{request.employeeName}</h4>
                    <p className="text-xs text-gray-500">Employee ID: {request.employeeId}</p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button 
                    onClick={() => onApprove(request)}
                    className="px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700 flex items-center"
                  >
                    <Check className="h-3 w-3 mr-1" />
                    Approve
                  </button>
                  <button 
                    onClick={() => onReject(request)}
                    className="px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 flex items-center"
                  >
                    <X className="h-3 w-3 mr-1" />
                    Reject
                  </button>
                </div>
              </div>
              
              <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Leave Type</p>
                  <p className="font-medium">{getLeaveTypeLabel(request.leaveType)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Duration</p>
                  <p className="font-medium">{requestedDays} days</p>
                </div>
                <div>
                  <p className="text-gray-500">Start Date</p>
                  <p className="font-medium">{new Date(request.startDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <p className="text-gray-500">End Date</p>
                  <p className="font-medium">{new Date(request.endDate).toLocaleDateString()}</p>
                </div>
              </div>
              
              <div className="mt-4">
                <p className="text-gray-500 text-sm">Reason</p>
                <p className="text-sm text-gray-900 mt-1">{request.reason}</p>
              </div>
              
              {/* Validation Status */}
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <h5 className="text-sm font-medium text-gray-900 mb-2">Request Validation</h5>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-600">Leave Balance:</span>
                    <span className={`font-medium ${balanceValidation.isValid ? 'text-green-600' : 'text-red-600'}`}>
                      {balanceValidation.isValid ? '✓ Sufficient' : '✗ Insufficient'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-600">Schedule Conflicts:</span>
                    <span className={`font-medium ${!conflictCheck.hasConflicts ? 'text-green-600' : 'text-yellow-600'}`}>
                      {!conflictCheck.hasConflicts ? '✓ No conflicts' : `⚠ ${conflictCheck.conflicts.length} conflict(s)`}
                    </span>
                  </div>
                  {!balanceValidation.isValid && (
                    <p className="text-xs text-red-600 mt-1">{balanceValidation.message}</p>
                  )}
                  {conflictCheck.hasConflicts && (
                    <p className="text-xs text-yellow-600 mt-1">{conflictCheck.message}</p>
                  )}
                </div>
              </div>

              {/* Warning for issues */}
              {(!balanceValidation.isValid || conflictCheck.hasConflicts) && (
                <div className="mt-3 bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <div className="flex">
                    <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2 flex-shrink-0 mt-0.5" />
                    <div>
                      <h4 className="text-sm font-medium text-yellow-800">Review Required</h4>
                      <p className="text-sm text-yellow-700 mt-1">
                        This request has validation issues that need attention before approval.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default PendingApprovals; 