import { Request, Response, NextFunction } from 'express';
import { User as UserInterface } from '../types/user';
import { User } from '../entities/User';
import { AuthRequest } from '../server/middleware/authMiddleware';
import { Like } from 'typeorm';
import { AppError } from '../middleware/errorHandler';
import { userRepository } from '../repositories/userRepository';
import logger from '../utils/logger';
import { cacheManager } from '../utils/cache';
import { AppDataSource } from '../config/database';
import { Role } from '../entities/Role';
import { UserRoleAssignment } from '../entities/UserRoleAssignment';
import * as bcrypt from 'bcryptjs';

type UserRole = 'IT_ADMIN' | 'IT_STAFF' | 'EMPLOYEE' | 'CEO' | 'FINANCE_MANAGER' | 'DEPT_HEAD' | 'VIEW' | 'HR_ADMIN' | 'HR_STAFF' | 'ADMIN' | 'SYSTEM_ADMIN' | 'IT_SUPPORT' | 'SENIOR_EMPLOYEE';

// Define default permissions for each role
const getRolePermissions = (role: UserRole) => {
  const basePermissions = {
    canCreateTickets: true,
    canCreateTicketsForOthers: false,
    canEditTickets: false,
    canDeleteTickets: false,
    canCloseTickets: false,
    canLockTickets: false,
    canAssignTickets: false,
    canEscalateTickets: false,
    canViewAllTickets: false,
    
    // HR Permissions
    canCreateEmployee: false,
    canEditEmployee: false,
    canDeleteEmployee: false,
    canViewEmployees: false,
    canManageAttendance: false,
    canManageLeave: false,
    canManagePayroll: false,
    canManagePerformance: false,
  };

  switch (role) {
    case 'SYSTEM_ADMIN':
    case 'ADMIN':
      return {
        ...basePermissions,
        // Full system permissions
        canViewAllTickets: true,
        canCreateTicketsForOthers: true,
        canEditTickets: true,
        canDeleteTickets: true,
        canCloseTickets: true,
        canLockTickets: true,
        canAssignTickets: true,
        canEscalateTickets: true,
        
        // Full HR permissions
        canCreateEmployee: true,
        canEditEmployee: true,
        canDeleteEmployee: true,
        canViewEmployees: true,
        canManageAttendance: true,
        canManageLeave: true,
        canManagePayroll: true,
        canManagePerformance: true,
      };
    
    case 'CEO':
      return {
        ...basePermissions,
        // CEO has full visibility and some management capabilities
        canViewAllTickets: true,
        canCreateTicketsForOthers: true,
        canEditTickets: true,
        canCloseTickets: true,
        canAssignTickets: true,
        
        // CEO has visibility into HR functions
        canViewEmployees: true,
        canManageLeave: true,
        canManagePayroll: true,
        canManagePerformance: true,
      };
    
    case 'IT_ADMIN':
      return {
        ...basePermissions,
        // IT Administrator permissions - full IT access
        canViewAllTickets: true,
        canCreateTicketsForOthers: true,
        canEditTickets: true,
        canDeleteTickets: true,
        canCloseTickets: true,
        canLockTickets: true,
        canAssignTickets: true,
        canEscalateTickets: true,
        
        // Basic HR view permissions for IT admin
        canCreateEmployee: true,
        canEditEmployee: true,
        canViewEmployees: true,
      };

    case 'IT_STAFF':
    case 'IT_SUPPORT':
      return {
        ...basePermissions,
        // IT Staff permissions
        canViewAllTickets: true,
        canCreateTicketsForOthers: true,
        canEditTickets: true,
        canCloseTickets: true,
        canLockTickets: true,
        canAssignTickets: true,
        canEscalateTickets: true,
        canViewEmployees: true,
      };
      
    case 'HR_ADMIN':
      return {
        ...basePermissions,
        // HR Administrator permissions
        canViewAllTickets: true,
        canCreateTicketsForOthers: true,
        canEditTickets: true,
        canCloseTickets: true,
        
        // Full HR permissions
        canCreateEmployee: true,
        canEditEmployee: true,
        canDeleteEmployee: true,
        canViewEmployees: true,
        canManageAttendance: true,
        canManageLeave: true,
        canManagePayroll: true,
        canManagePerformance: true,
      };
      
    case 'HR_STAFF':
      return {
        ...basePermissions,
        // HR Staff permissions
        canCreateEmployee: true,
        canEditEmployee: true, 
        canViewEmployees: true,
        canManageAttendance: true,
        canManageLeave: true,
      };

    case 'DEPT_HEAD':
      return {
        ...basePermissions,
        // Department Head permissions
        canEditTickets: true,
        canCloseTickets: true,
        canViewEmployees: true,
        canManageLeave: true,
      };

    case 'FINANCE_MANAGER':
      return {
        ...basePermissions,
        canCreateTicketsForOthers: true,
        canEditTickets: true,
        canCloseTickets: true,
        canLockTickets: true,
        
        // Finance managers can manage payroll
        canViewEmployees: true,
        canManagePayroll: true,
      };

    case 'EMPLOYEE':
    case 'SENIOR_EMPLOYEE':
      return {
        ...basePermissions,
        // Basic employee permissions
        canCreateTickets: true,
      };

    default:
      return basePermissions;
  }
};

export const userController = {
  async login(req: Request, res: Response) {
    try {
      const { email, password } = req.body;
      
      // In a real app, you'd verify against a database
      const user = SYSTEM_USERS.find(u => u.email === email && u.password === password);
      
      if (!user) {
        return res.status(401).json({ error: 'Invalid credentials' });
      }

      // Don't send password in response
      const { password: _, ...userWithoutPassword } = user;
      
      res.json({
        user: userWithoutPassword,
        token: 'dummy-jwt-token' // In real app, generate JWT
      });
    } catch (error) {
      res.status(500).json({ error: 'Login failed' });
    }
  },

  async getCurrentUser(req: AuthRequest, res: Response) {
    try {
      const user = req.user;
      if (!user) {
        return res.status(401).json({ error: 'Not authenticated' });
      }
      res.json(user);
    } catch (error) {
      res.status(500).json({ error: 'Failed to get user' });
    }
  }
};

// System users with proper roles and permissions
const SYSTEM_USERS = [
  {
    id: 'IT001',
    name: 'John Admin',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'IT_ADMIN' as UserRole,
    department: 'IT',
    permissions: getRolePermissions('IT_ADMIN')
  },
  {
    id: 'ADMIN001',
    name: 'Super Admin',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'ADMIN' as UserRole,
    department: 'Administration',
    permissions: getRolePermissions('ADMIN')
  },
  {
    id: 'SYS001',
    name: 'System Administrator',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'SYSTEM_ADMIN' as UserRole,
    department: 'IT',
    permissions: getRolePermissions('SYSTEM_ADMIN')
  },
  {
    id: 'CEO001',
    name: 'CEO',
    email: '<EMAIL>',
    password: 'ceo123',
    role: 'CEO' as UserRole,
    department: 'Executive',
    permissions: getRolePermissions('CEO')
  },
  {
    id: 'IT002',
    name: 'Sarah Support',
    email: '<EMAIL>',
    password: 'support123',
    role: 'IT_STAFF' as UserRole,
    department: 'IT',
    permissions: getRolePermissions('IT_STAFF')
  },
  {
    id: 'HEAD001',
    name: 'Mike Head',
    email: '<EMAIL>',
    password: 'head123',
    role: 'DEPT_HEAD' as UserRole,
    department: 'Management',
    permissions: getRolePermissions('DEPT_HEAD')
  },
  {
    id: 'MGR001',
    name: 'Lisa Manager',
    email: '<EMAIL>',
    password: 'manager123',
    role: 'FINANCE_MANAGER' as UserRole,
    department: 'Finance',
    permissions: getRolePermissions('FINANCE_MANAGER')
  },
  {
    id: 'EMP001',
    name: 'Emma Employee',
    email: '<EMAIL>',
    password: 'emp123',
    role: 'EMPLOYEE' as UserRole,
    department: 'Marketing',
    permissions: getRolePermissions('EMPLOYEE')
  }
];

const roleRepository = AppDataSource.getRepository(Role);
const userRoleRepository = AppDataSource.getRepository(UserRoleAssignment);

export const getUsers = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const users = await userRepository.find({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        department: true,
        isActive: true,
        project: true,
        location: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    // For each user, get their role assignments
    const usersWithRoles = await Promise.all(users.map(async (user) => {
      const roleAssignments = await userRoleRepository.find({
        where: { userId: user.id },
        relations: ['role']
      });
      
      return {
        ...user,
        roles: roleAssignments.map(ra => String(ra.roleId)) // Convert to string for consistency with frontend
      };
    }));

    res.json(usersWithRoles);
  } catch (error) {
    next(error);
  }
};

export const getUserById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    // Check cache first
    const cachedUser = await cacheManager.get(`user_${id}`);
    if (cachedUser) {
      return res.json(cachedUser);
    }

    const user = await userRepository.findOneBy({ id });
    
    if (!user) {
      throw new AppError(404, 'USER_NOT_FOUND', 'User not found');
    }

    // Get user's role assignments
    const roleAssignments = await userRoleRepository.find({
      where: { userId: id },
      relations: ['role']
    });
    
    const userWithRoles = {
      ...user,
      roles: roleAssignments.map(ra => String(ra.roleId)), // Convert to string for consistency with frontend
      // Don't send password to client
      password: undefined
    };
    
    // Cache result
    await cacheManager.set(`user_${id}`, userWithRoles, 60 * 5); // 5 minutes
    
    res.json(userWithRoles);
  } catch (error) {
    next(error);
  }
};

export const createUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { name, email, password, role, department, isActive = true, project, location } = req.body;
    
    // Check if email already exists
    const existingUser = await userRepository.findOneBy({ email });
    
    if (existingUser) {
      throw new AppError(400, 'EMAIL_ALREADY_EXISTS', 'Email already in use');
    }
    
    const hashedPassword = await bcrypt.hash(password, 10);
    
    const newUser = userRepository.create({
      name,
      email,
      password: hashedPassword,
      role,
      department,
      isActive,
      project,
      location
    });
    
    await userRepository.save(newUser);
    
    logger.info(`New user created: ${email}`);
    
    // Return user without password
    const { password: _, ...userWithoutPassword } = newUser;
    res.status(201).json(userWithoutPassword);
  } catch (error) {
    next(error);
  }
};

export const updateUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { role } = req.body;
    const user = await userRepository.findOneBy({ id });
    
    if (!user) {
      throw new AppError(404, 'USER_NOT_FOUND', 'User not found');
    }

    // Check if role is a comma-separated string (multiple roles)
    if (typeof role === 'string' && role.includes(',')) {
      // This is a new role assignment request
      const roleIds = role.split(',').filter(Boolean);
      
      if (roleIds.length > 0) {
        // Default project ID if not specified
        const projectId = req.body.projectId || 'default';
        
        try {
          // First, remove existing role assignments
          await userRoleRepository.delete({ userId: id });
          
          // Create new role assignments
          const assignments = [];
          
          for (const roleIdStr of roleIds) {
            // Convert roleId to number if necessary
            const roleId = parseInt(roleIdStr, 10);
            if (isNaN(roleId)) {
              console.warn(`Invalid role ID format: ${roleIdStr}`);
              continue;
            }
            
            const roleExists = await roleRepository.findOneBy({ id: roleId });
            if (roleExists) {
              const assignment = new UserRoleAssignment();
              assignment.userId = id;
              assignment.roleId = roleId; // Now using the number
              assignment.assignedBy = req.user?.id || user.id;
              assignment.projectId = projectId;
              
              if (req.body.locationId) {
                assignment.locationId = req.body.locationId;
              }
              
              if (req.body.departmentId) {
                assignment.departmentId = req.body.departmentId;
              }
              
              assignments.push(assignment);
              
              // Update role user count
              roleExists.userCount = roleExists.userCount + 1;
              await roleRepository.save(roleExists);
            }
          }
          
          if (assignments.length > 0) {
            await userRoleRepository.save(assignments);
          }
        } catch (error) {
          console.error('Failed to save role assignments:', error);
          // Continue with regular user update even if role assignment fails
        }
      }
    }

    // Process regular user fields
    userRepository.merge(user, req.body);
    const updatedUser = await userRepository.save(user);

    // Invalidate cache
    cacheManager.del(`user_${id}`);
    logger.info(`User updated: ${user.email}`);

    // Get updated user's role assignments
    const roleAssignments = await userRoleRepository.find({
      where: { userId: id },
      relations: ['role']
    });

    const responseUser = {
      ...updatedUser,
      roles: roleAssignments.map(ra => String(ra.roleId)), // Convert to string for consistency with frontend
      // Don't send password to client
      password: undefined
    };

    res.json(responseUser);
  } catch (error) {
    next(error);
  }
};

export const deleteUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const user = await userRepository.findOneBy({ id });
    
    if (!user) {
      throw new AppError(404, 'USER_NOT_FOUND', 'User not found');
    }

    await userRepository.remove(user);
    
    // Invalidate cache
    cacheManager.del(`user_${id}`);
    logger.info(`User deleted: ${user.email}`);

    res.status(204).send();
  } catch (error) {
    next(error);
  }
}; 