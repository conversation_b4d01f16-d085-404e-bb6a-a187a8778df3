import { Request, Response } from 'express';
import { KnowledgeBase, KnowledgeStatus } from '../../entities/KnowledgeBase';
import { KnowledgeCategory } from '../../entities/KnowledgeCategory';
import { KnowledgeTag } from '../../entities/KnowledgeTag';
import { User } from '../../entities/User';
import { AppDataSource } from '../../config/database';
import logger from '../utils/logger';
import { UserRole } from '../../types/common';
import { UploadedFile } from 'express-fileupload';
import fs from 'fs';
import path from 'path';

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
      };
    }
  }
}

// Define upload directory path for knowledge base images
const kbUploadDir = path.join(__dirname, '../../../public/uploads/knowledge');

// Get all knowledge base articles
export const getAllArticles = async (req: Request, res: Response) => {
  try {
    // Ensure database is initialized
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }
    
    const { status, category, featured, search } = req.query;
    
    const knowledgeBaseRepository = AppDataSource.getRepository(KnowledgeBase);
    
    let query = knowledgeBaseRepository.createQueryBuilder('article')
      .leftJoinAndSelect('article.category', 'category')
      .leftJoinAndSelect('article.createdBy', 'createdBy')
      .leftJoinAndSelect('article.tags', 'tags');
    
    // Apply filters
    if (status) {
      query = query.andWhere('article.status = :status', { status });
    } else {
      // By default, only show published articles
      query = query.andWhere('article.status = :status', { status: KnowledgeStatus.PUBLISHED });
    }
    
    if (category) {
      query = query.andWhere('category.id = :categoryId', { categoryId: category });
    }
    
    if (featured === 'true') {
      query = query.andWhere('article.isFeatured = :isFeatured', { isFeatured: true });
    }
    
    if (search) {
      query = query.andWhere(
        '(article.title LIKE :search OR article.content LIKE :search OR article.summary LIKE :search)',
        { search: `%${search}%` }
      );
    }
    
    const articles = await query.orderBy('article.createdAt', 'ASC').getMany();
    
    return res.status(200).json(articles);
  } catch (error) {
    logger.error('Error fetching knowledge base articles:', error);
    return res.status(500).json({ message: 'Error fetching knowledge base articles' });
  }
};

// Get a single knowledge base article by ID
export const getArticleById = async (req: Request, res: Response) => {
  try {
    // Ensure database is initialized
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }
    
    const { id } = req.params;
    
    const knowledgeBaseRepository = AppDataSource.getRepository(KnowledgeBase);
    
    const article = await knowledgeBaseRepository.findOne({
      where: { id },
      relations: ['category', 'createdBy', 'tags']
    });
    
    if (!article) {
      return res.status(404).json({ message: 'Knowledge base article not found' });
    }
    
    // Don't increment view count here - it's now handled by a separate endpoint
    
    return res.status(200).json(article);
  } catch (error) {
    logger.error('Error fetching knowledge base article:', error);
    return res.status(500).json({ message: 'Error fetching knowledge base article' });
  }
};

// Increment view count for an article
export const incrementArticleViewCount = async (req: Request, res: Response) => {
  try {
    // Ensure database is initialized
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }
    
    const { id } = req.params;
    
    const knowledgeBaseRepository = AppDataSource.getRepository(KnowledgeBase);
    
    // Find the article
    const article = await knowledgeBaseRepository.findOne({
      where: { id }
    });
    
    if (!article) {
      return res.status(404).json({ message: 'Knowledge base article not found' });
    }
    
    // Increment view count using a partial update to avoid changing other fields
    await knowledgeBaseRepository.increment({ id }, 'viewCount', 1);
    
    return res.status(200).json({ success: true, viewCount: article.viewCount + 1 });
  } catch (error) {
    logger.error('Error incrementing article view count:', error);
    return res.status(500).json({ message: 'Error incrementing article view count' });
  }
};

// Create a new knowledge base article
export const createArticle = async (req: Request, res: Response) => {
  try {
    // Ensure database is initialized
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }
    
    const { title, content, summary, categoryId, status, isFeatured, tags } = req.body;
    
    // Validate required fields
    if (!title || !content) {
      return res.status(400).json({ message: 'Title and content are required' });
    }
    
    // Make categoryId optional during testing phase
    // Check if category is provided but doesn't exist
    let category = null;
    if (categoryId) {
      const categoryRepository = AppDataSource.getRepository(KnowledgeCategory);
      category = await categoryRepository.findOne({ where: { id: categoryId } });
      if (!category && categoryId) {
        // Create a default category if needed
        try {
          const newCategory = new KnowledgeCategory();
          newCategory.name = 'General Knowledge';
          newCategory.description = 'Default category created during testing';
          newCategory.slug = 'general-knowledge';
          newCategory.displayOrder = 1;
          
          category = await categoryRepository.save(newCategory);
          console.log('Created default category:', category);
        } catch (error) {
          console.error('Error creating default category:', error);
        }
      }
    }
    
    const knowledgeBaseRepository = AppDataSource.getRepository(KnowledgeBase);
    const userRepository = AppDataSource.getRepository(User);
    
    // Get current user or use a default admin user for testing
    let userId = req.user?.id;
    if (!userId) {
      // For testing purposes, find an admin user
      const adminUser = await userRepository.findOne({ 
        where: { role: UserRole.IT_ADMIN }
      });
      
      if (adminUser) {
        userId = adminUser.id;
        console.log('Using existing admin user for article creation:', adminUser.id);
      } else {
        // If no admin user found, create a test user
        try {
          const newUser = new User();
          newUser.name = 'Test Admin';
          newUser.email = '<EMAIL>';
          newUser.password = 'hashed_password_placeholder';
          newUser.role = UserRole.IT_ADMIN;
          
          const createdUser = await userRepository.save(newUser);
          userId = createdUser.id;
          console.log('Created test admin user:', createdUser.id);
        } catch (error) {
          console.error('Error creating test user:', error);
          return res.status(500).json({ message: 'Error creating test user' });
        }
      }
    }
    
    // Create new article
    const newArticle = new KnowledgeBase();
    newArticle.title = title;
    newArticle.content = content;
    newArticle.summary = summary || '';
    newArticle.categoryId = category?.id || null; // Make it nullable for testing
    newArticle.createdById = userId;
    newArticle.status = status || KnowledgeStatus.DRAFT;
    newArticle.isFeatured = isFeatured || false;
    
    const savedArticle = await knowledgeBaseRepository.save(newArticle);
    
    // Add tags if provided
    if (tags && Array.isArray(tags) && tags.length > 0) {
      const tagRepository = AppDataSource.getRepository(KnowledgeTag);
      
      const tagEntities = tags.map(tagName => {
        const tag = new KnowledgeTag();
        tag.name = tagName;
        tag.articleId = savedArticle.id;
        return tag;
      });
      
      await tagRepository.save(tagEntities);
      
      // Reload the article with tags
      const articleWithTags = await knowledgeBaseRepository.findOne({
        where: { id: savedArticle.id },
        relations: ['category', 'createdBy', 'tags']
      });
      
      return res.status(201).json(articleWithTags);
    }
    
    return res.status(201).json(savedArticle);
  } catch (error) {
    logger.error('Error creating knowledge base article:', error);
    return res.status(500).json({ message: 'Error creating knowledge base article', error: error.message });
  }
};

// Update a knowledge base article
export const updateArticle = async (req: Request, res: Response) => {
  try {
    // Ensure database is initialized
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }
    
    const { id } = req.params;
    const { title, content, summary, categoryId, status, isFeatured, tags } = req.body;
    
    const knowledgeBaseRepository = AppDataSource.getRepository(KnowledgeBase);
    const categoryRepository = AppDataSource.getRepository(KnowledgeCategory);
    const tagRepository = AppDataSource.getRepository(KnowledgeTag);
    
    // Check if article exists
    const article = await knowledgeBaseRepository.findOne({
      where: { id },
      relations: ['tags']
    });
    
    if (!article) {
      return res.status(404).json({ message: 'Knowledge base article not found' });
    }
    
    // Check if category exists if provided
    if (categoryId) {
      const category = await categoryRepository.findOne({ where: { id: categoryId } });
      if (!category) {
        return res.status(404).json({ message: 'Category not found' });
      }
      article.categoryId = categoryId;
    }
    
    // Update article fields
    if (title) article.title = title;
    if (content) article.content = content;
    if (summary !== undefined) article.summary = summary;
    if (status) article.status = status;
    if (isFeatured !== undefined) article.isFeatured = isFeatured;
    
    // Update last updated by
    if (req.user?.id) {
      article.lastUpdatedById = req.user.id;
    }
    
    // Save article
    const updatedArticle = await knowledgeBaseRepository.save(article);
    
    // Update tags if provided
    if (tags && Array.isArray(tags)) {
      // Delete existing tags
      await tagRepository.delete({ articleId: article.id });
      
      // Create new tags
      if (tags.length > 0) {
        const tagEntities = tags.map(tagName => {
          const tag = new KnowledgeTag();
          tag.name = tagName;
          tag.articleId = article.id;
          return tag;
        });
        
        await tagRepository.save(tagEntities);
      }
    }
    
    // Fetch the complete updated article with relations
    const completeArticle = await knowledgeBaseRepository.findOne({
      where: { id: updatedArticle.id },
      relations: ['category', 'createdBy', 'lastUpdatedBy', 'tags']
    });
    
    return res.status(200).json(completeArticle);
  } catch (error) {
    logger.error(`Error updating knowledge base article with ID ${req.params.id}:`, error);
    return res.status(500).json({ message: 'Error updating knowledge base article' });
  }
};

// Delete a knowledge base article
export const deleteArticle = async (req: Request, res: Response) => {
  try {
    // Ensure database is initialized
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }
    
    const { id } = req.params;
    
    const knowledgeBaseRepository = AppDataSource.getRepository(KnowledgeBase);
    
    const article = await knowledgeBaseRepository.findOne({ where: { id } });
    
    if (!article) {
      return res.status(404).json({ message: 'Knowledge base article not found' });
    }
    
    await knowledgeBaseRepository.remove(article);
    
    return res.status(200).json({ message: 'Knowledge base article deleted successfully' });
  } catch (error) {
    logger.error(`Error deleting knowledge base article with ID ${req.params.id}:`, error);
    return res.status(500).json({ message: 'Error deleting knowledge base article' });
  }
};

// Upload images for knowledge base articles
export const uploadImages = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user has permission to upload images
    const userRepository = AppDataSource.getRepository(User);
    const user = await userRepository.findOne({ where: { id: req.user.id } });
    
    if (!user) {
      return res.status(401).json({ error: 'User not found' });
    }
    
    const allowedRoles = [UserRole.IT_ADMIN, UserRole.IT_STAFF];
    if (!allowedRoles.includes(user.role as UserRole)) {
      return res.status(403).json({ error: 'You do not have permission to upload images' });
    }

    if (!req.files || !('images' in req.files)) {
      return res.status(400).json({ error: 'No images uploaded' });
    }

    const files = req.files.images;
    const uploadedFiles = Array.isArray(files) 
      ? (files as unknown as UploadedFile[])
      : [(files as unknown as UploadedFile)];

    // Create uploads directory if it doesn't exist
    if (!fs.existsSync(kbUploadDir)) {
      fs.mkdirSync(kbUploadDir, { recursive: true });
    }

    // Validate file types
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB
    
    for (const file of uploadedFiles) {
      if (!allowedTypes.includes(file.mimetype)) {
        return res.status(400).json({ 
          error: `Invalid file type: ${file.name}. Only JPEG, PNG, GIF, and WebP images are allowed.` 
        });
      }
      
      if (file.size > maxSize) {
        return res.status(400).json({ 
          error: `File too large: ${file.name}. Maximum size is 5MB.` 
        });
      }
    }

    // Upload files and create response
    const uploadedImages = await Promise.all(uploadedFiles.map(async (file: UploadedFile) => {
      // Generate unique filename to prevent collisions
      const uniqueFilename = `kb-${Date.now()}-${Math.random().toString(36).substring(2)}-${file.name}`;
      const filePath = path.join(kbUploadDir, uniqueFilename);
      
      // Move file to uploads directory
      await file.mv(filePath);
      
      // Return file information
      return {
        fileName: file.name,
        fileUrl: `/uploads/knowledge/${uniqueFilename}`,
        fileType: file.mimetype,
        size: file.size
      };
    }));

    res.status(200).json({
      message: 'Images uploaded successfully',
      images: uploadedImages
    });
  } catch (error) {
    console.error('Error uploading images:', error);
    res.status(500).json({ error: 'Failed to upload images' });
  }
};

// Get related articles for a specific article
export const getRelatedArticles = async (req: Request, res: Response) => {
  try {
    // Ensure database is initialized
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }
    
    const { id } = req.params;
    
    const knowledgeBaseRepository = AppDataSource.getRepository(KnowledgeBase);
    
    // First, get the current article to find its category and tags
    const currentArticle = await knowledgeBaseRepository.findOne({
      where: { id },
      relations: ['category', 'tags']
    });
    
    if (!currentArticle) {
      return res.status(404).json({ message: 'Knowledge base article not found' });
    }
    
    // Find articles in the same category, excluding the current article
    const relatedArticles = await knowledgeBaseRepository
      .createQueryBuilder('article')
      .leftJoinAndSelect('article.category', 'category')
      .where('article.id != :id', { id })
      .andWhere('article.categoryId = :categoryId', { categoryId: currentArticle.categoryId })
      .andWhere('article.status = :status', { status: 'PUBLISHED' })
      .orderBy('article.viewCount', 'DESC')
      .take(4)
      .getMany();
    
    // Format the response to include only necessary fields
    const formattedRelatedArticles = relatedArticles.map(article => ({
      id: article.id,
      title: article.title,
      category: {
        name: article.category.name
      }
    }));
    
    return res.status(200).json(formattedRelatedArticles);
  } catch (error) {
    logger.error('Error fetching related articles:', error);
    return res.status(500).json({ message: 'Error fetching related articles' });
  }
}; 