import { AppDataSource } from '../config/database';
import { LeaveAllocation } from '../entities/LeaveAllocation';
import { LeaveRequest } from '../entities/LeaveRequest';
import { LeaveTypePolicy } from '../entities/LeaveTypePolicy';
import { ProratedLeaveCalculationService } from './ProratedLeaveCalculationService';

export interface CalculatedLeaveBalance {
  employeeId: number;
  leaveType: string;
  year: number;
  totalAllocated: number;
  used: number;
  pending: number;
  remaining: number;
  carriedForward: number;
  isProrated?: boolean;
  proratedInfo?: {
    originalEntitlement: number;
    prorationFactor: number;
    joiningDate: string;
    calculationMethod: string;
  };
}

export interface EmployeeLeaveData {
  employeeId: number;
  employeeName: string;
  employeeCode: string;
  department: string;
  position: string;
  balances: CalculatedLeaveBalance[];
}

export class LeaveBalanceCalculationService {
  private proratedLeaveService = new ProratedLeaveCalculationService();
  
  /**
   * Calculate leave balance for a specific employee and leave type
   * Balance = Allocation - Used - Pending
   * Now includes prorated leave calculations when enabled
   */
  async calculateEmployeeLeaveBalance(
    employeeId: number, 
    leaveType: string, 
    year: number
  ): Promise<CalculatedLeaveBalance | null> {
    const leaveAllocationRepository = AppDataSource.getRepository(LeaveAllocation);
    const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
    const leaveTypePolicyRepository = AppDataSource.getRepository(LeaveTypePolicy);

    // Get leave type policy to check if prorated leave is enabled
    const leaveTypePolicy = await leaveTypePolicyRepository.findOne({
      where: { leaveType, isActive: true }
    });

    // Get allocation (source of truth for entitlement)
    let allocation = await leaveAllocationRepository.findOne({
      where: { employeeId, leaveType, year, isActive: true }
    });

    let totalAllocated = 0;
    let isProrated = false;
    let proratedInfo: {
      originalEntitlement: number;
      prorationFactor: number;
      joiningDate: string;
      calculationMethod: string;
    } | undefined = undefined;

    // Check if prorated leave is enabled first, regardless of existing allocation
    if (leaveTypePolicy?.settings?.enableProratedLeave) {
      try {
        // Calculate prorated allocation for this employee
        const proratedCalculations = await this.proratedLeaveService.calculateProratedLeaveForEmployee(employeeId);
        const proratedCalc = proratedCalculations.find(calc => calc.leaveType === leaveType);
        
        if (proratedCalc) {
          totalAllocated = proratedCalc.proratedEntitlement;
          isProrated = true;
          proratedInfo = {
            originalEntitlement: proratedCalc.originalEntitlement,
            prorationFactor: proratedCalc.prorationFactor,
            joiningDate: proratedCalc.joiningDate.toISOString().split('T')[0],
            calculationMethod: proratedCalc.calculationMethod
          };

          // Update existing allocation or create new one
          if (allocation) {
            // PRESERVE manual adjustments - don't reset them when updating prorated calculations
            const existingManualAdjustment = allocation.manualAdjustment || 0;
            const existingCarriedForward = allocation.carriedForward || 0;
            
            // Only update policy allocation if this is not a manual allocation
            if (allocation.source !== 'MANUAL') {
              allocation.policyAllocation = totalAllocated;
              allocation.notes = `Prorated allocation: ${proratedCalc.calculationMethod}`;
            }
            // Keep existing manual adjustments - DON'T reset to 0
            // allocation.manualAdjustment stays as is
            
            // Recalculate total allocated including manual adjustments
            totalAllocated = allocation.totalAllocated; // This includes policy + manual + carried forward
            
            allocation.updatedAt = new Date();
            allocation = await leaveAllocationRepository.save(allocation);
            console.log(`✅ Updated prorated allocation for employee ${employeeId}, leave type ${leaveType}: Policy=${allocation.policyAllocation}, Manual=${allocation.manualAdjustment}, Total=${totalAllocated} days`);
          } else {
            // Create new allocation record
            const newAllocation = leaveAllocationRepository.create({
              employeeId,
              leaveType,
              year,
              policyAllocation: totalAllocated,
              manualAdjustment: 0,
              carriedForward: 0,
              source: 'POLICY',
              notes: `Prorated allocation: ${proratedCalc.calculationMethod}`,
              isActive: true
            });
            allocation = await leaveAllocationRepository.save(newAllocation);
            console.log(`✅ Created prorated allocation for employee ${employeeId}, leave type ${leaveType}: ${totalAllocated} days`);
          }
        } else {
          // No prorated calculation available, use existing allocation or default
          totalAllocated = allocation?.totalAllocated || (leaveTypePolicy?.maxDaysPerYear || 0);
          isProrated = false;
        }
      } catch (error) {
        console.warn(`⚠️ Failed to calculate prorated leave for employee ${employeeId}, leave type ${leaveType}:`, error);
        // Fall back to existing allocation or standard allocation
        totalAllocated = allocation?.totalAllocated || (leaveTypePolicy?.maxDaysPerYear || 0);
        isProrated = false;
      }
    } else {
      // Prorated leave not enabled, use existing allocation or standard allocation
      totalAllocated = allocation?.totalAllocated || (leaveTypePolicy?.maxDaysPerYear || 0);
      isProrated = false;
    }

    if (totalAllocated === 0) {
      return null; // No allocation = no entitlement
    }

    // Calculate used leave (approved requests)
    const usedResult = await leaveRequestRepository
      .createQueryBuilder('lr')
      .select('COALESCE(SUM(lr.daysRequested), 0)', 'totalUsed')
      .where('lr.employeeId = :employeeId', { employeeId })
      .andWhere('lr.leaveType = :leaveType', { leaveType })
      .andWhere('YEAR(lr.startDate) = :year', { year })
      .andWhere('lr.status = :status', { status: 'APPROVED' })
      .getRawOne();

    const used = parseFloat(usedResult.totalUsed) || 0;

    // Calculate pending leave (pending approval)
    const pendingResult = await leaveRequestRepository
      .createQueryBuilder('lr')
      .select('COALESCE(SUM(lr.daysRequested), 0)', 'totalPending')
      .where('lr.employeeId = :employeeId', { employeeId })
      .andWhere('lr.leaveType = :leaveType', { leaveType })
      .andWhere('YEAR(lr.startDate) = :year', { year })
      .andWhere('lr.status = :status', { status: 'PENDING' })
      .getRawOne();

    const pending = parseFloat(pendingResult.totalPending) || 0;

    // Calculate remaining balance
    const remaining = totalAllocated - used - pending;

    return {
      employeeId,
      leaveType,
      year,
      totalAllocated,
      used,
      pending,
      remaining: Math.max(0, remaining), // Ensure non-negative
      carriedForward: allocation?.carriedForward || 0,
      isProrated,
      proratedInfo
    };
  }

  /**
   * Calculate all leave balances for an employee
   * Now includes prorated leave calculations when enabled
   */
  async calculateEmployeeAllBalances(
    employeeId: number, 
    year: number
  ): Promise<CalculatedLeaveBalance[]> {
    const leaveAllocationRepository = AppDataSource.getRepository(LeaveAllocation);
    const leaveTypePolicyRepository = AppDataSource.getRepository(LeaveTypePolicy);

    // Get all allocations for the employee
    const allocations = await leaveAllocationRepository.find({
      where: { employeeId, year, isActive: true }
    });

    // Get all active leave types (including those without allocations yet)
    const activeLeaveTypes = await leaveTypePolicyRepository.find({
      where: { isActive: true }
    });

    // Calculate balance for each leave type
    const balances: CalculatedLeaveBalance[] = [];
    
    // Process existing allocations
    for (const allocation of allocations) {
      const balance = await this.calculateEmployeeLeaveBalance(
        employeeId, 
        allocation.leaveType, 
        year
      );
      if (balance) {
        balances.push(balance);
      }
    }

    // Process leave types without allocations (for prorated calculations)
    const allocatedLeaveTypes = allocations.map(a => a.leaveType);
    const unallocatedLeaveTypes = activeLeaveTypes.filter(lt => 
      !allocatedLeaveTypes.includes(lt.leaveType) && 
      lt.settings?.enableProratedLeave === true
    );

    for (const leaveType of unallocatedLeaveTypes) {
      const balance = await this.calculateEmployeeLeaveBalance(
        employeeId, 
        leaveType.leaveType, 
        year
      );
      if (balance) {
        balances.push(balance);
      }
    }

    return balances;
  }

  /**
   * Get all active employees with their calculated leave balances
   */
  async getAllEmployeesWithCalculatedBalances(
    year: number,
    department?: string,
    page: number = 1,
    limit: number = 10,
    search?: string
  ): Promise<{
    employees: EmployeeLeaveData[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    const employeeRepository = AppDataSource.getRepository('Employee');

    // Build employee query
    let employeeQuery = employeeRepository.createQueryBuilder('employee')
      .leftJoinAndSelect('employee.job', 'job')
      .where('employee.status = :status', { status: 'active' });

    if (department) {
      employeeQuery = employeeQuery.andWhere('job.department = :department', { department });
    }

    if (search) {
      employeeQuery = employeeQuery.andWhere(
        '(employee.firstName LIKE :search OR employee.lastName LIKE :search OR employee.employeeId LIKE :search OR job.department LIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Get total count for pagination
    const totalCount = await employeeQuery.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    employeeQuery = employeeQuery.skip(offset).take(limit);

    const employees = await employeeQuery.getMany();

    // Calculate balances for each employee
    const employeeData: EmployeeLeaveData[] = await Promise.all(
      employees.map(async (employee: any) => {
        const balances = await this.calculateEmployeeAllBalances(employee.id, year);
        
        return {
          employeeId: employee.id,
          employeeName: `${employee.firstName || ''} ${employee.lastName || ''}`.trim(),
          employeeCode: employee.employeeId || `EMP${String(employee.id).padStart(3, '0')}`,
          department: employee.job?.department || 'N/A',
          position: employee.job?.designation || 'N/A',
          balances
        };
      })
    );

    return {
      employees: employeeData,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    };
  }

  /**
   * Get leave allocation summary (for creating allocations from policies)
   */
  async getLeaveAllocationSummary(year: number): Promise<{
    totalEmployees: number;
    totalAllocations: number;
    allocationsByLeaveType: { [leaveType: string]: number };
    employeesWithoutAllocations: number;
  }> {
    const employeeRepository = AppDataSource.getRepository('Employee');
    const leaveAllocationRepository = AppDataSource.getRepository(LeaveAllocation);

    const totalEmployees = await employeeRepository.count({
      where: { status: 'active' }
    });

    const allocations = await leaveAllocationRepository.find({
      where: { year, isActive: true }
    });

    const allocationsByLeaveType: { [leaveType: string]: number } = {};
    allocations.forEach(allocation => {
      allocationsByLeaveType[allocation.leaveType] = 
        (allocationsByLeaveType[allocation.leaveType] || 0) + 1;
    });

    const employeesWithAllocations = new Set(allocations.map(a => a.employeeId)).size;
    const employeesWithoutAllocations = totalEmployees - employeesWithAllocations;

    return {
      totalEmployees,
      totalAllocations: allocations.length,
      allocationsByLeaveType,
      employeesWithoutAllocations
    };
  }
}

export const leaveBalanceCalculationService = new LeaveBalanceCalculationService(); 