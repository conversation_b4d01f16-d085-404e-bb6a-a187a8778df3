import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, BeforeInsert, BeforeUpdate } from 'typeorm';
import { Ticket } from '../../entities/Ticket';
import { Comment } from './Comment';
import { KnowledgeBase } from '../../entities/KnowledgeBase';
import { Attachment } from '../../entities/Attachment';
import bcrypt from 'bcryptjs';
import { IsEmail, IsNotEmpty, MinLength, IsEnum, Length } from 'class-validator';

export enum UserRole {
  IT_ADMIN = 'IT_ADMIN',
  IT_STAFF = 'IT_STAFF',
  EMPLOYEE = 'EMPLOYEE',
  CEO = 'CEO',
  FINANCE_MANAGER = 'FINANCE_MANAGER',
  DEPT_HEAD = 'DEPT_HEAD',
  VIEW = 'VIEW'
}

export interface UserPermissions {
  canCreateTickets: boolean;
  canCreateTicketsForOthers: boolean;
  canEditTickets: boolean;
  canDeleteTickets: boolean;
  canCloseTickets: boolean;
  canLockTickets: boolean;
  canAssignTickets: boolean;
  canEscalateTickets: boolean;
  canViewAllTickets: boolean;
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'Name is required' })
  @Length(2, 100, { message: 'Name must be between 2 and 100 characters' })
  name: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @Column({ type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Password is required' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  password: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.EMPLOYEE
  })
  @IsEnum(UserRole, { message: 'Invalid role' })
  role: UserRole;

  @Column({ type: 'varchar', length: 50 })
  @IsNotEmpty({ message: 'Department is required' })
  @Length(2, 50, { message: 'Department must be between 2 and 50 characters' })
  department: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Length(2, 100, { message: 'Project name must be between 2 and 100 characters' })
  project?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Length(2, 100, { message: 'Location must be between 2 and 100 characters' })
  location?: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'json' })
  permissions: UserPermissions;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @OneToMany(() => Ticket, ticket => ticket.createdBy)
  createdTickets: Ticket[];

  @OneToMany(() => Ticket, ticket => ticket.assignedTo)
  assignedTickets: Ticket[];

  @OneToMany(() => Comment, comment => comment.createdBy)
  comments: Comment[];

  @OneToMany(() => KnowledgeBase, article => article.createdBy)
  knowledgeArticles: KnowledgeBase[];

  @OneToMany(() => Attachment, attachment => attachment.uploadedBy)
  uploads: Attachment[];

  private tempPassword: string | null = null;

  setPassword(password: string) {
    this.tempPassword = password;
  }

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.tempPassword) {
      const salt = await bcrypt.genSalt(10);
      this.password = await bcrypt.hash(this.tempPassword, salt);
      this.tempPassword = null;
    }
  }

  async validatePassword(password: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, this.password);
    } catch (error) {
      console.error('Password validation error:', error);
      return false;
    }
  }

  // Exclude password when converting to JSON
  toJSON() {
    const { password, tempPassword, ...rest } = this;
    return rest;
  }
} 