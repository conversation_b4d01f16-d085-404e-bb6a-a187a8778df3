import { Request, Response } from 'express';
import { AppDataSource } from '../../config/database';
import { Ticket } from '../../entities/Ticket';
import { User } from '../../entities/User';
import { Comment } from '../../entities/Comment';
import { DashboardStats, DepartmentInfo, CategoryStatusInfo } from '../types/dashboard';
import { Between, In, Like, FindOptionsRelations } from 'typeorm';
import { AuthRequest } from '../middleware/authMiddleware';
import { UserRole } from '../../types/common';
import { TicketStatus, TicketPriority } from '../../types/common';

const DEPARTMENT_CONFIGS: Record<string, { icon: string; color: string }> = {
  'IT': { icon: 'Laptop', color: '#4F46E5' },
  'HR': { icon: 'Users', color: '#EC4899' },
  'FINANCE': { icon: 'DollarSign', color: '#10B981' },
  'MARKETING': { icon: 'TrendingUp', color: '#F59E0B' },
  'SALES': { icon: 'ShoppingCart', color: '#3B82F6' },
  'OPERATIONS': { icon: 'Settings', color: '#6366F1' },
  'CSD': { icon: 'HeadphonesIcon', color: '#8B5CF6' },
  'LAND': { icon: 'Map', color: '#14B8A6' },
  'LEGAL': { icon: 'Scale', color: '#EF4444' },
  'MANAGEMENT': { icon: 'Briefcase', color: '#8B5CF6' },
  'PND': { icon: 'Tool', color: '#06B6D4' }
};

export const dashboardController = {
  async getStats(req: AuthRequest, res: Response) {
    try {
      const { timeRange = 'week', department, priority } = req.query;
      const ticketRepository = AppDataSource.getRepository(Ticket);
      const user = req.user as User & { role?: UserRole };

      if (!user) {
        return res.status(401).json({ error: 'Unauthorized' });
      }
      
      // Type guard to ensure user has the role property
      const userWithRole = user as User & { role: UserRole };

      // Build the where clause based on filters
      const whereClause: any = {};

      // Time range filter
      const now = new Date();
      switch (timeRange) {
        case 'today':
          const startOfDay = new Date(now.setHours(0, 0, 0, 0));
          whereClause.createdAt = Between(startOfDay, now);
          break;
        case 'week':
          const startOfWeek = new Date(now);
          startOfWeek.setDate(now.getDate() - 7);
          whereClause.createdAt = Between(startOfWeek, now);
          break;
        case 'month':
          const startOfMonth = new Date(now);
          startOfMonth.setMonth(now.getMonth() - 1);
          whereClause.createdAt = Between(startOfMonth, now);
          break;
      }

      // Department filter
      if (department && department !== 'all') {
        whereClause.department = department;
      }

      // Priority filter
      if (priority && priority !== 'all') {
        whereClause.priority = priority;
      }

      // Apply visibility rules based on user role
      console.log(`🔍 DASHBOARD CONTROLLER: User role check - Role: ${user.role}, Type: ${typeof user.role}`);
      console.log(`🔍 DASHBOARD CONTROLLER: User object:`, {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        department: user.department,
        hasRole: !!user.role
      });
      
      if (!user.role || (user.role !== UserRole.IT_ADMIN && user.role !== UserRole.IT_STAFF)) {
        if (user.role === UserRole.DEPT_HEAD && user.department) {
          console.log(`🔍 DASHBOARD CONTROLLER: Applying department filter for DEPT_HEAD: ${user.department}`);
          whereClause.department = user.department;
        } else {
          console.log(`🔍 DASHBOARD CONTROLLER: Applying user filter for role: ${user.role}, restricting to user's own tickets`);
          whereClause.createdById = user.id;
        }
      } else {
        console.log(`🔍 DASHBOARD CONTROLLER: Admin/IT staff access granted for role: ${user.role}`);
      }

      const relations: FindOptionsRelations<Ticket> = {
        createdBy: true,
        assignedTo: true,
        comments: {
          createdBy: true
        }
      };

      console.log('Fetching dashboard tickets with criteria:', {
        userRole: user.role,
        userId: user.id,
        department: user.department,
        whereClause
      });

      const tickets = await ticketRepository.find({
        where: whereClause,
        relations,
        order: {
          createdAt: 'DESC',
          comments: {
            createdAt: 'ASC'
          }
        }
      });

      console.log('Found tickets for dashboard:', {
        userRole: user.role,
        userId: user.id,
        ticketCount: tickets.length,
        ticketNumbers: tickets.map(t => t.ticketNumber)
      });

      if (!tickets || tickets.length === 0) {
        const emptyStats: DashboardStats = {
          tickets: {
            total: 0,
            open: 0,
            inProgress: 0,
            resolved: 0,
            closed: 0,
            byPriority: { low: 0, medium: 0, high: 0, critical: 0 },
            byDepartment: {},
            responseTime: {
              lessThanHour: 0,
              oneToFourHours: 0,
              fourToDay: 0,
              moreThanDay: 0
            },
            categoryStatus: {},
            trends: {
              labels: [],
              newTickets: [],
              resolvedTickets: []
            },
            recentActivity: []
          },
          performance: {
            averageResponseTime: '0h',
            averageResolutionTime: '0h',
            satisfactionRate: 0
          }
        };
        return res.json(emptyStats);
      }

      // Calculate response times based on first comment
      const responseTimeStats = tickets.reduce((acc, ticket) => {
        const firstResponse = ticket.comments[0];
        if (!firstResponse) return acc;

        const responseTime = new Date(firstResponse.createdAt).getTime() - new Date(ticket.createdAt).getTime();
        const hours = responseTime / (1000 * 60 * 60);

        if (hours < 1) acc.lessThanHour++;
        else if (hours < 4) acc.oneToFourHours++;
        else if (hours < 24) acc.fourToDay++;
        else acc.moreThanDay++;

        return acc;
      }, {
        lessThanHour: 0,
        oneToFourHours: 0,
        fourToDay: 0,
        moreThanDay: 0
      });

      // Calculate category status distribution
      const categoryStatus = tickets.reduce((acc, ticket) => {
        if (!acc[ticket.category]) {
          acc[ticket.category] = {
            open: 0,
            inProgress: 0,
            resolved: 0
          };
        }
        
        switch (ticket.status) {
          case TicketStatus.OPEN:
            acc[ticket.category].open++;
            break;
          case TicketStatus.IN_PROGRESS:
            acc[ticket.category].inProgress++;
            break;
          case TicketStatus.RESOLVED:
            acc[ticket.category].resolved++;
            break;
        }

        return acc;
      }, {} as Record<string, CategoryStatusInfo>);

      // Calculate trends for the last 7 days
      const last7Days = Array.from({ length: 7 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - i);
        return date.toISOString().split('T')[0];
      }).reverse();

      const trends = {
        labels: last7Days.map(date => 
          new Date(date).toLocaleDateString('en-US', { weekday: 'short' })
        ),
        newTickets: await Promise.all(last7Days.map(async date => {
          const startDate = new Date(date);
          const endDate = new Date(date);
          endDate.setDate(endDate.getDate() + 1);
          
          return await ticketRepository.count({
            where: {
              ...whereClause,
              createdAt: Between(startDate, endDate)
            }
          });
        })),
        resolvedTickets: await Promise.all(last7Days.map(async date => {
          const startDate = new Date(date);
          const endDate = new Date(date);
          endDate.setDate(endDate.getDate() + 1);
          
          return await ticketRepository.count({
            where: {
              ...whereClause,
              status: TicketStatus.RESOLVED,
              updatedAt: Between(startDate, endDate)
            }
          });
        }))
      };

      // Calculate department stats
      const departmentStats = tickets.reduce<Record<string, DepartmentInfo>>((acc, ticket) => {
        const dept = ticket.department;
        if (!acc[dept]) {
          const config = DEPARTMENT_CONFIGS[dept] || {
            icon: 'Building',
            color: '#6B7280'
          };
          acc[dept] = {
            name: dept,
            ticketCount: 0,
            color: config.color
          };
        }
        acc[dept].ticketCount++;
        return acc;
      }, {});

      // Calculate performance metrics
      const totalResponseTimes = tickets.reduce((acc, ticket) => {
        const firstResponse = ticket.comments[0];
        if (firstResponse) {
          acc.push(new Date(firstResponse.createdAt).getTime() - new Date(ticket.createdAt).getTime());
        }
        return acc;
      }, [] as number[]);

      const averageResponseTime = totalResponseTimes.length > 0
        ? Math.round(totalResponseTimes.reduce((a, b) => a + b, 0) / totalResponseTimes.length / (1000 * 60 * 60))
        : 0;

      // Compile all stats
      const stats: DashboardStats = {
        tickets: {
          total: tickets.length,
          open: tickets.filter(t => t.status === TicketStatus.OPEN).length,
          inProgress: tickets.filter(t => t.status === TicketStatus.IN_PROGRESS).length,
          resolved: tickets.filter(t => t.status === TicketStatus.RESOLVED).length,
          closed: tickets.filter(t => t.status === TicketStatus.RESOLVED).length,
          byPriority: {
            low: tickets.filter(t => t.priority === TicketPriority.LOW).length,
            medium: tickets.filter(t => t.priority === TicketPriority.MEDIUM).length,
            high: tickets.filter(t => t.priority === TicketPriority.HIGH).length,
            critical: tickets.filter(t => t.priority === TicketPriority.CRITICAL).length,
          },
          byDepartment: departmentStats,
          responseTime: responseTimeStats,
          categoryStatus,
          trends,
          recentActivity: tickets.slice(0, 10).map(ticket => ({
            id: ticket.id,
            type: 'TICKET_CREATED',
            description: `New ticket created: ${ticket.title}`,
            userId: ticket.createdBy.id,
            userName: ticket.createdBy.name,
            userRole: ticket.createdBy.role,
            timestamp: ticket.createdAt
          }))
        },
        performance: {
          averageResponseTime: `${averageResponseTime}h`,
          averageResolutionTime: '0h', // TODO: Implement resolution time calculation
          satisfactionRate: 0 // TODO: Implement satisfaction rate calculation
        }
      };

      res.json(stats);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      res.status(500).json({ error: 'Failed to fetch dashboard statistics' });
    }
  }
}; 