import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { User } from './User';
import { KnowledgeCategory } from './KnowledgeCategory';
import { KnowledgeTag } from './KnowledgeTag';

export enum KnowledgeStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED'
}

@Entity('knowledge_base')
export class KnowledgeBase {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'varchar', length: 255 })
  title!: string;

  @Column({ type: 'text' })
  content!: string;

  @Column({ type: 'text', nullable: true })
  summary?: string;

  @Column({
    type: 'enum',
    enum: KnowledgeStatus,
    default: KnowledgeStatus.DRAFT
  })
  status!: KnowledgeStatus;

  @Column({ type: 'int', default: 0 })
  viewCount!: number;

  @Column({ type: 'boolean', default: false })
  isFeatured!: boolean;

  @ManyToOne(() => KnowledgeCategory, category => category.articles, { nullable: false })
  @JoinColumn({ name: 'categoryId' })
  category!: KnowledgeCategory;

  @Column({ type: 'varchar', length: 36 })
  categoryId!: string;

  @ManyToOne(() => User, user => user.knowledgeArticles, { nullable: false })
  @JoinColumn({ name: 'createdById' })
  createdBy!: User;

  @Column({ type: 'varchar', length: 36 })
  createdById!: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'lastUpdatedById' })
  lastUpdatedBy?: User;

  @Column({ type: 'varchar', length: 36, nullable: true })
  lastUpdatedById?: string;

  @OneToMany(() => KnowledgeTag, tag => tag.article, {
    cascade: true,
    onDelete: 'CASCADE'
  })
  tags!: KnowledgeTag[];

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
} 