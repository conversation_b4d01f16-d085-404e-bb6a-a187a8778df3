import { MigrationInterface, QueryRunner, Table, TableIndex, <PERSON>F<PERSON>ign<PERSON><PERSON> } from 'typeorm';

export class EnhancedTaskManagement1750000000000 implements MigrationInterface {
  name = 'EnhancedTaskManagement1750000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new columns to tasks table
    await queryRunner.query(`
      ALTER TABLE tasks 
      ADD COLUMN isTemplate BOOLEAN DEFAULT FALSE,
      ADD COLUMN templateName VARCHAR(255),
      ADD COLUMN recurrenceType ENUM('none', 'daily', 'weekly', 'monthly', 'quarterly', 'yearly') DEFAULT 'none',
      ADD COLUMN recurrenceConfig JSON,
      ADD COLUMN originalTaskId INT,
      ADD COLUMN approvalStatus ENUM('not_required', 'pending', 'approved', 'rejected') DEFAULT 'not_required',
      ADD COLUMN approvalWorkflow JSON,
      ADD COLUMN requiresApproval BOOLEAN DEFAULT FALSE,
      ADD COLUMN checklist JSON,
      ADD COLUMN timeSpentMinutes INT DEFAULT 0,
      ADD COLUMN isBlocked BOOLEAN DEFAULT FALSE,
      ADD COLUMN blockReason TEXT
    `);

    // Create task_time_entries table
    await queryRunner.createTable(
      new Table({
        name: 'task_time_entries',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment'
          },
          {
            name: 'taskId',
            type: 'int',
            isNullable: false
          },
          {
            name: 'userId',
            type: 'varchar',
            length: '36',
            isNullable: false
          },
          {
            name: 'durationMinutes',
            type: 'int',
            isNullable: false
          },
          {
            name: 'startTime',
            type: 'timestamp',
            isNullable: false
          },
          {
            name: 'endTime',
            type: 'timestamp',
            isNullable: true
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true
          },
          {
            name: 'type',
            type: 'enum',
            enum: ['manual', 'timer', 'imported'],
            default: "'manual'"
          },
          {
            name: 'isBillable',
            type: 'boolean',
            default: false
          },
          {
            name: 'hourlyRate',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: true
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP'
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP'
          }
        ]
      }),
      true
    );

    // Create task_templates table
    await queryRunner.createTable(
      new Table({
        name: 'task_templates',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment'
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isNullable: false
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true
          },
          {
            name: 'titleTemplate',
            type: 'varchar',
            length: '255',
            isNullable: false
          },
          {
            name: 'descriptionTemplate',
            type: 'text',
            isNullable: true
          },
          {
            name: 'defaultPriority',
            type: 'enum',
            enum: ['low', 'medium', 'high', 'critical'],
            default: "'medium'"
          },
          {
            name: 'defaultType',
            type: 'enum',
            enum: ['task', 'bug', 'feature', 'improvement', 'research', 'subtask', 'checklist_item'],
            default: "'task'"
          },
          {
            name: 'category',
            type: 'enum',
            enum: ['it_infrastructure', 'software_development', 'security', 'maintenance', 'deployment', 'testing', 'documentation', 'general'],
            default: "'general'"
          },
          {
            name: 'defaultEstimatedHours',
            type: 'int',
            isNullable: true
          },
          {
            name: 'defaultTags',
            type: 'json',
            isNullable: true
          },
          {
            name: 'defaultChecklist',
            type: 'json',
            isNullable: true
          },
          {
            name: 'subtaskTemplates',
            type: 'json',
            isNullable: true
          },
          {
            name: 'requiresApproval',
            type: 'boolean',
            default: false
          },
          {
            name: 'defaultApprovers',
            type: 'json',
            isNullable: true
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true
          },
          {
            name: 'isPublic',
            type: 'boolean',
            default: false
          },
          {
            name: 'usageCount',
            type: 'int',
            default: 0
          },
          {
            name: 'createdById',
            type: 'varchar',
            length: '36',
            isNullable: false
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP'
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP'
          }
        ]
      }),
      true
    );

    // Create task_automation_rules table
    await queryRunner.createTable(
      new Table({
        name: 'task_automation_rules',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment'
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isNullable: false
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true
          },
          {
            name: 'trigger',
            type: 'enum',
            enum: ['task_created', 'task_status_changed', 'task_assigned', 'task_due_soon', 'task_overdue', 'project_created', 'user_workload_low', 'skill_match'],
            isNullable: false
          },
          {
            name: 'triggerConditions',
            type: 'json',
            isNullable: false
          },
          {
            name: 'action',
            type: 'enum',
            enum: ['assign_to_user', 'assign_by_skill', 'assign_by_workload', 'send_notification', 'create_subtask', 'update_status', 'set_priority', 'add_tag', 'send_email'],
            isNullable: false
          },
          {
            name: 'actionConfig',
            type: 'json',
            isNullable: false
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true
          },
          {
            name: 'executionCount',
            type: 'int',
            default: 0
          },
          {
            name: 'lastExecutedAt',
            type: 'timestamp',
            isNullable: true
          },
          {
            name: 'priority',
            type: 'int',
            default: 0
          },
          {
            name: 'projectId',
            type: 'int',
            isNullable: true
          },
          {
            name: 'createdById',
            type: 'varchar',
            length: '36',
            isNullable: false
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP'
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP'
          }
        ]
      }),
      true
    );

    // Create task_dependencies table for many-to-many relationship
    await queryRunner.createTable(
      new Table({
        name: 'task_dependencies',
        columns: [
          {
            name: 'taskId',
            type: 'int',
            isPrimary: true
          },
          {
            name: 'dependsOnTaskId',
            type: 'int',
            isPrimary: true
          }
        ]
      }),
      true
    );

    // Add foreign keys
    await queryRunner.createForeignKey(
      'task_time_entries',
      new TableForeignKey({
        columnNames: ['taskId'],
        referencedTableName: 'tasks',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'task_time_entries',
      new TableForeignKey({
        columnNames: ['userId'],
        referencedTableName: 'users',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'task_templates',
      new TableForeignKey({
        columnNames: ['createdById'],
        referencedTableName: 'users',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'task_automation_rules',
      new TableForeignKey({
        columnNames: ['projectId'],
        referencedTableName: 'projects',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'task_automation_rules',
      new TableForeignKey({
        columnNames: ['createdById'],
        referencedTableName: 'users',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'task_dependencies',
      new TableForeignKey({
        columnNames: ['taskId'],
        referencedTableName: 'tasks',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'task_dependencies',
      new TableForeignKey({
        columnNames: ['dependsOnTaskId'],
        referencedTableName: 'tasks',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    // Add indexes for better performance
    await queryRunner.createIndex('task_time_entries', new TableIndex({ name: 'IDX_task_time_entries_task', columnNames: ['taskId'] }));
    await queryRunner.createIndex('task_time_entries', new TableIndex({ name: 'IDX_task_time_entries_user', columnNames: ['userId'] }));
    await queryRunner.createIndex('task_templates', new TableIndex({ name: 'IDX_task_templates_category', columnNames: ['category'] }));
    await queryRunner.createIndex('task_templates', new TableIndex({ name: 'IDX_task_templates_public', columnNames: ['isPublic'] }));
    await queryRunner.createIndex('task_automation_rules', new TableIndex({ name: 'IDX_automation_rules_trigger', columnNames: ['trigger'] }));
    await queryRunner.createIndex('task_automation_rules', new TableIndex({ name: 'IDX_automation_rules_active', columnNames: ['isActive'] }));
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign keys first
    const taskTimeEntriesTable = await queryRunner.getTable('task_time_entries');
    const taskTemplatesTable = await queryRunner.getTable('task_templates');
    const taskAutomationRulesTable = await queryRunner.getTable('task_automation_rules');
    const taskDependenciesTable = await queryRunner.getTable('task_dependencies');

    if (taskTimeEntriesTable) {
      const foreignKeys = taskTimeEntriesTable.foreignKeys;
      for (const foreignKey of foreignKeys) {
        await queryRunner.dropForeignKey('task_time_entries', foreignKey);
      }
    }

    if (taskTemplatesTable) {
      const foreignKeys = taskTemplatesTable.foreignKeys;
      for (const foreignKey of foreignKeys) {
        await queryRunner.dropForeignKey('task_templates', foreignKey);
      }
    }

    if (taskAutomationRulesTable) {
      const foreignKeys = taskAutomationRulesTable.foreignKeys;
      for (const foreignKey of foreignKeys) {
        await queryRunner.dropForeignKey('task_automation_rules', foreignKey);
      }
    }

    if (taskDependenciesTable) {
      const foreignKeys = taskDependenciesTable.foreignKeys;
      for (const foreignKey of foreignKeys) {
        await queryRunner.dropForeignKey('task_dependencies', foreignKey);
      }
    }

    // Drop tables
    await queryRunner.dropTable('task_dependencies');
    await queryRunner.dropTable('task_automation_rules');
    await queryRunner.dropTable('task_templates');
    await queryRunner.dropTable('task_time_entries');

    // Remove columns from tasks table
    await queryRunner.query(`
      ALTER TABLE tasks 
      DROP COLUMN isTemplate,
      DROP COLUMN templateName,
      DROP COLUMN recurrenceType,
      DROP COLUMN recurrenceConfig,
      DROP COLUMN originalTaskId,
      DROP COLUMN approvalStatus,
      DROP COLUMN approvalWorkflow,
      DROP COLUMN requiresApproval,
      DROP COLUMN checklist,
      DROP COLUMN timeSpentMinutes,
      DROP COLUMN isBlocked,
      DROP COLUMN blockReason
    `);
  }
}
