import React from 'react';

/**
 * Debug component to show routing information
 * Uses native browser APIs instead of React Router
 */
const DebugInfo: React.FC = () => {
  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      backgroundColor: '#f0f0f0',
      padding: '10px',
      border: '1px solid #ccc',
      borderRadius: '5px',
      fontSize: '12px',
      zIndex: 9999,
      maxWidth: '300px'
    }}>
      <h4 style={{ margin: '0 0 10px 0', color: 'green' }}>🔧 Debug Info</h4>
      <div><strong>Current URL:</strong> {window.location.pathname}</div>
      <div><strong>Search:</strong> {window.location.search}</div>
      <div><strong>Hash:</strong> {window.location.hash}</div>
      <div><strong>Time:</strong> {new Date().toLocaleTimeString()}</div>
      <div><strong>Component:</strong> SimpleTest</div>
    </div>
  );
};

export default DebugInfo;
