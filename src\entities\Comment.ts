import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinC<PERSON>umn, BeforeInsert, BeforeUpdate } from 'typeorm';
import { Ticket } from './Ticket';
import { User } from './User';
import { UserRole } from '../types/common';
import { Attachment } from './Attachment';

@Entity('comments')
export class Comment {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'text' })
  content!: string;

  @ManyToOne(() => Ticket, ticket => ticket.comments, { 
    onDelete: 'CASCADE',
    nullable: false 
  })
  @JoinColumn({ name: 'ticketId' })
  ticket!: Ticket;

  @Column({ type: 'int' })
  ticketId!: number;

  @ManyToOne(() => User, user => user.comments, { 
    onDelete: 'CASCADE',
    nullable: false 
  })
  @JoinColumn({ name: 'createdById' })
  createdBy!: User;

  @Column({ type: 'uuid' })
  createdById!: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.EMPLOYEE
  })
  role!: UserRole;

  @Column({ type: 'boolean', default: false })
  isSystemComment!: boolean;

  @OneToMany(() => Attachment, attachment => attachment.comment, {
    cascade: true
  })
  attachments!: Attachment[];

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  @BeforeInsert()
  @BeforeUpdate()
  ensureTicketId() {
    // Ensure ticketId is set from the ticket relationship if available
    if (this.ticket && this.ticket.id && !this.ticketId) {
      this.ticketId = this.ticket.id;
    }
  }
} 