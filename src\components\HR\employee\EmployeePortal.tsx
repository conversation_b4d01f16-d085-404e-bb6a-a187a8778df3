import React, { useState, useEffect } from 'react';
import { 
  User, 
  Calendar, 
  Clock, 
  DollarSign, 
  FileText, 
  LogOut, 
  Mail, 
  Phone, 
  Building, 
  CalendarIcon, 
  CreditCard, 
  BadgeCheck,
  ChevronRight,
  GraduationCap,
  Users,
  BarChart2,
  Heart,
  Settings,
  Award,
  AlertCircle,
  Briefcase,
  Shield,
  SkipForward,
  Monitor,
  Star,
  FolderOpen,
  Car,
  CheckCircle,
  XCircle,
  MapPin
} from 'lucide-react';
import { useAuth } from '../../../contexts/AuthContext';
import UserAttendanceView from '../attendance/UserAttendanceView';
import EmployeeLeaveView from '../leave/EmployeeLeaveView';
import EmployeePayrollView from '../payroll/EmployeePayrollView';
import EmployeeDocumentsView from './EmployeeDocumentsView';
import EmployeeDetailsView from './EmployeeDetailsView';

import EmployeeBenefitsView from './EmployeeBenefitsView';
import ExpenseReimbursementView from '../payroll/ExpenseReimbursementView';
import employeePortalService, { 
  EmployeePortalProfile, 
  EmployeeLeaveData, 
  EmployeePayrollData, 
  EmployeePerformanceData,
  EmployeeDocumentData,
  EmployeeAttendanceData,
  EmployeeBenefitsData,
  EmployeeTrainingData,
  EmployeeTeamData
} from '../../../services/EmployeePortalService';

// Define new required component types for components that don't exist yet
interface EmployeePerformanceViewProps {
  performanceData: EmployeePerformanceData | null;
}
interface EmployeeTeamViewProps {
  teamData: EmployeeTeamData | null;
}
interface EmployeeTrainingViewProps {
  trainingData: EmployeeTrainingData | null;
}

// Real employee data interfaces and state
interface EmployeePortalData {
  profile: EmployeePortalProfile | null;
  leaveData: EmployeeLeaveData | null;
  payrollData: EmployeePayrollData | null;
  performanceData: EmployeePerformanceData | null;
  documentData: EmployeeDocumentData | null;
  attendanceData: EmployeeAttendanceData | null;
  benefitsData: EmployeeBenefitsData | null;
  trainingData: EmployeeTrainingData | null;
  teamData: EmployeeTeamData | null;
}

// Real data components for tabs
const EmployeePerformanceView: React.FC<EmployeePerformanceViewProps> = ({ performanceData }) => (
  <div className="p-4">
    <h2 className="text-xl font-semibold mb-4">Performance Metrics</h2>
    <div className="bg-white rounded-lg shadow p-6">
      {performanceData ? (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900">Current Rating</h3>
              <p className="text-2xl font-bold text-blue-700">
                {performanceData.currentRating || 'N/A'}
              </p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-semibold text-green-900">Goals Completed</h3>
              <p className="text-2xl font-bold text-green-700">
                {performanceData.goals.filter((g: any) => g.status === 'completed').length} / {performanceData.goals.length}
              </p>
            </div>
          </div>
          
          {performanceData.goals.length > 0 && (
            <div>
              <h4 className="font-semibold mb-2">Current Goals</h4>
              <div className="space-y-2">
                {performanceData.goals.map((goal: any) => (
                  <div key={goal.id} className="flex items-center justify-between bg-gray-50 p-3 rounded">
                    <span>{goal.title}</span>
                    <span className={`px-2 py-1 rounded text-xs ${
                      goal.status === 'completed' ? 'bg-green-100 text-green-800' :
                      goal.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {goal.status.replace('_', ' ')}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {performanceData.achievements.length > 0 && (
            <div>
              <h4 className="font-semibold mb-2">Recent Achievements</h4>
              <div className="space-y-2">
                {performanceData.achievements.map((achievement: any) => (
                  <div key={achievement.id} className="bg-yellow-50 p-3 rounded">
                    <h5 className="font-medium text-yellow-900">{achievement.title}</h5>
                    <p className="text-sm text-yellow-700">{achievement.description}</p>
                    <p className="text-xs text-yellow-600 mt-1">{achievement.date}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ) : (
        <p className="text-gray-500">Loading performance data...</p>
      )}
    </div>
  </div>
);

const EmployeeTeamView: React.FC<EmployeeTeamViewProps> = ({ teamData }) => (
  <div className="p-4">
    <h2 className="text-xl font-semibold mb-4">My Team</h2>
    <div className="bg-white rounded-lg shadow p-6">
      {teamData ? (
        <div className="space-y-6">
          {teamData.manager && (
            <div>
              <h3 className="font-semibold mb-2">Reporting Manager</h3>
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900">{teamData.manager.name}</h4>
                <p className="text-sm text-blue-700">{teamData.manager.designation}</p>
                <p className="text-xs text-blue-600">{teamData.manager.department}</p>
                <p className="text-xs text-blue-600">{teamData.manager.email}</p>
              </div>
            </div>
          )}
          
          {teamData.reportees.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">Direct Reports ({teamData.reportees.length})</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {teamData.reportees.map((reportee: any) => (
                  <div key={reportee.id} className="bg-green-50 p-4 rounded-lg">
                    <h4 className="font-medium text-green-900">{reportee.name}</h4>
                    <p className="text-sm text-green-700">{reportee.designation}</p>
                    <p className="text-xs text-green-600">{reportee.department}</p>
                    <p className="text-xs text-green-600">{reportee.email}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {teamData.colleagues.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">Team Colleagues ({teamData.colleagues.length})</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {teamData.colleagues.map((colleague: any) => (
                  <div key={colleague.id} className="bg-gray-50 p-3 rounded-lg">
                    <h4 className="font-medium text-gray-900">{colleague.name}</h4>
                    <p className="text-sm text-gray-700">{colleague.designation}</p>
                    <p className="text-xs text-gray-600">{colleague.email}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ) : (
        <p className="text-gray-500">Loading team data...</p>
      )}
    </div>
  </div>
);

const EmployeeTrainingView: React.FC<EmployeeTrainingViewProps> = ({ trainingData }) => (
  <div className="p-4">
    <h2 className="text-xl font-semibold mb-4">Training & Development</h2>
    <div className="bg-white rounded-lg shadow p-6">
      {trainingData ? (
        <div className="space-y-6">
          {trainingData.trainingRequirements.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">Training Requirements</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {trainingData.trainingRequirements.map((requirement: string, index: number) => (
                  <div key={index} className="bg-orange-50 p-3 rounded-lg">
                    <p className="text-orange-900 font-medium">{requirement}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {trainingData.completedTrainings.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">Completed Trainings ({trainingData.completedTrainings.length})</h3>
              <div className="space-y-2">
                {trainingData.completedTrainings.map((training: any) => (
                  <div key={training.id} className="bg-green-50 p-4 rounded-lg">
                    <h4 className="font-medium text-green-900">{training.title}</h4>
                    <p className="text-sm text-green-700">Completed: {training.completionDate}</p>
                    {training.score && (
                      <p className="text-sm text-green-700">Score: {training.score}%</p>
                    )}
                    {training.certificateUrl && (
                      <a 
                        href={training.certificateUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-xs text-green-600 hover:underline"
                      >
                        View Certificate
                      </a>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {trainingData.upcomingTrainings.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">Upcoming Trainings ({trainingData.upcomingTrainings.length})</h3>
              <div className="space-y-2">
                {trainingData.upcomingTrainings.map((training: any) => (
                  <div key={training.id} className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-900">{training.title}</h4>
                    <p className="text-sm text-blue-700">Start Date: {training.startDate}</p>
                    <p className="text-sm text-blue-700">Duration: {training.duration}</p>
                    {training.mandatory && (
                      <span className="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded mt-2">
                        Mandatory
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {trainingData.completedTrainings.length === 0 && 
           trainingData.upcomingTrainings.length === 0 && 
           trainingData.trainingRequirements.length === 0 && (
            <p className="text-gray-500">No training data available.</p>
          )}
        </div>
      ) : (
        <p className="text-gray-500">Loading training data...</p>
      )}
    </div>
  </div>
);

// Type for tabs
type TabType = 'profile' | 'attendance' | 'leave' | 'payroll' | 'documents' | 'performance' | 'benefits' | 'team' | 'training';

// Interface for tab permissions
interface TabPermission {
  id: TabType;
  label: string;
  icon: React.FC<{ className?: string }>;
  roles: string[];
  component: React.ReactNode;
}

// Removed inline EmployeeAttendanceRealView - using existing UserAttendanceView component instead

// Removed inline EmployeeLeaveRealView - using existing EmployeeLeaveView component instead

// Removed inline EmployeePayrollRealView - using existing EmployeePayrollView component instead
// Removed inline EmployeeDocumentsRealView - using existing EmployeeDocumentsView component instead
// Removed inline EmployeeBenefitsRealView - using existing EmployeeBenefitsView component instead

// Employee Profile View Component specifically for Employee Portal
interface EmployeeProfileViewProps {
  profile: EmployeePortalProfile | null;
  user: any;
}

const EmployeeProfileView: React.FC<EmployeeProfileViewProps> = ({ profile, user }) => {
  if (!profile) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500 dark:text-gray-400">Loading profile data...</p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100';
      case 'inactive':
        return 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100';
    }
  };

  return (
    <div className="space-y-6">
      {/* Employee Header Card */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-700 dark:to-purple-700 rounded-2xl shadow-xl overflow-hidden">
        <div className="px-6 py-6">
          <div className="flex flex-col lg:flex-row items-center lg:items-start gap-4">
            <div className="relative">
              <div className="h-20 w-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white border-3 border-white/30">
                <User size={32} />
              </div>
              <div className="absolute -bottom-1 -right-1 h-6 w-6 bg-green-400 rounded-full border-3 border-white flex items-center justify-center">
                <div className="h-2 w-2 bg-green-600 rounded-full"></div>
              </div>
            </div>
            <div className="flex-1 text-center lg:text-left">
              <h1 className="text-2xl font-bold text-white mb-1">
                {profile?.firstName && profile?.lastName 
                  ? `${profile.firstName} ${profile.middleName ? profile.middleName + ' ' : ''}${profile.lastName}`
                  : user?.name || 'Not specified'
                }
              </h1>
              <p className="text-blue-100 text-base font-medium mb-3">{profile?.designation || 'Not specified'}</p>
              
              <div className="flex flex-wrap justify-center lg:justify-start gap-2 mb-4">
                <div className="flex items-center bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 text-white">
                  <BadgeCheck className="h-3 w-3 mr-1" />
                  <span className="text-xs font-medium">{profile?.employeeId || 'N/A'}</span>
                </div>
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(profile?.status || 'Active')}`}>
                  <div className="h-1.5 w-1.5 bg-current rounded-full mr-1"></div>
                  {profile?.status || 'Active'}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-white">
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                  <div className="text-blue-100 text-xs">Department</div>
                  <div className="font-medium text-sm">{profile?.department || user?.department || 'Not specified'}</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                  <div className="text-blue-100 text-xs">Join Date</div>
                  <div className="font-medium text-sm">{profile?.joinDate || 'Not specified'}</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                  <div className="text-blue-100 text-xs">Role</div>
                  <div className="font-medium text-sm">{user?.role || 'Employee'}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Personal Information */}
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6 border border-gray-100 dark:border-gray-700">
        <div className="flex items-center mb-4">
          <div className="h-10 w-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mr-3">
            <User className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Personal Information</h3>
            <p className="text-gray-500 dark:text-gray-400 text-xs">Basic personal details and identification</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 hover:shadow-md transition-shadow">
            <div className="flex items-center mb-1">
              <div className="h-2 w-2 bg-blue-500 rounded-full mr-2"></div>
              <div className="text-xs font-medium text-gray-600 dark:text-gray-400">Gender</div>
            </div>
            <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.gender || 'Not specified'}</div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 hover:shadow-md transition-shadow">
            <div className="flex items-center mb-1">
              <div className="h-2 w-2 bg-green-500 rounded-full mr-2"></div>
              <div className="text-xs font-medium text-gray-600 dark:text-gray-400">Date of Birth</div>
            </div>
            <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.dateOfBirth || 'Not specified'}</div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 hover:shadow-md transition-shadow">
            <div className="flex items-center mb-1">
              <div className="h-2 w-2 bg-purple-500 rounded-full mr-2"></div>
              <div className="text-xs font-medium text-gray-600 dark:text-gray-400">Religion</div>
            </div>
            <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.religion || 'Not specified'}</div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 hover:shadow-md transition-shadow">
            <div className="flex items-center mb-1">
              <div className="h-2 w-2 bg-orange-500 rounded-full mr-2"></div>
              <div className="text-xs font-medium text-gray-600 dark:text-gray-400">CNIC Number</div>
            </div>
            <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.cnicNumber || 'Not specified'}</div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 hover:shadow-md transition-shadow">
            <div className="flex items-center mb-1">
              <div className="h-2 w-2 bg-red-500 rounded-full mr-2"></div>
              <div className="text-xs font-medium text-gray-600 dark:text-gray-400">CNIC Expiry</div>
            </div>
            <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.cnicExpiryDate || 'Not specified'}</div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 hover:shadow-md transition-shadow">
            <div className="flex items-center mb-1">
              <div className="h-2 w-2 bg-indigo-500 rounded-full mr-2"></div>
              <div className="text-xs font-medium text-gray-600 dark:text-gray-400">Nationality</div>
            </div>
            <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.nationality || 'Not specified'}</div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 hover:shadow-md transition-shadow">
            <div className="flex items-center mb-1">
              <div className="h-2 w-2 bg-pink-500 rounded-full mr-2"></div>
              <div className="text-xs font-medium text-gray-600 dark:text-gray-400">Marital Status</div>
            </div>
            <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.maritalStatus || 'Not specified'}</div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 hover:shadow-md transition-shadow">
            <div className="flex items-center mb-1">
              <div className="h-2 w-2 bg-yellow-500 rounded-full mr-2"></div>
              <div className="text-xs font-medium text-gray-600 dark:text-gray-400">Blood Type</div>
            </div>
            <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.bloodType || 'Not specified'}</div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 hover:shadow-md transition-shadow">
            <div className="flex items-center mb-1">
              <div className="h-2 w-2 bg-teal-500 rounded-full mr-2"></div>
              <div className="text-xs font-medium text-gray-600 dark:text-gray-400">Father's Name</div>
            </div>
            <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.fatherName || 'Not specified'}</div>
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6 border border-gray-100 dark:border-gray-700">
        <div className="flex items-center mb-4">
          <div className="h-10 w-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center mr-3">
            <Mail className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Contact Information</h3>
            <p className="text-gray-500 dark:text-gray-400 text-xs">Communication and address details</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-6">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <Phone className="h-5 w-5 mr-2 text-blue-600" />
                Contact Details
              </h4>
              <div className="space-y-4">
                <div className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                  <div className="h-8 w-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mr-3">
                    <Phone className="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Mobile Number</div>
                    <div className="font-medium text-gray-900 dark:text-white">{profile?.mobileNumber || 'Not specified'}</div>
                  </div>
                </div>
                
                <div className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                  <div className="h-8 w-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                    <Phone className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Official Number</div>
                    <div className="font-medium text-gray-900 dark:text-white">{profile?.officialNumber || 'Not specified'}</div>
                  </div>
                </div>
                
                <div className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                  <div className="h-8 w-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mr-3">
                    <Mail className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Official Email</div>
                    <div className="font-medium text-gray-900 dark:text-white break-all">{profile?.officialEmail || 'Not specified'}</div>
                  </div>
                </div>
                
                <div className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                  <div className="h-8 w-8 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mr-3">
                    <Mail className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Personal Email</div>
                    <div className="font-medium text-gray-900 dark:text-white break-all">{profile?.personalEmail || 'Not specified'}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="space-y-6">
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <MapPin className="h-5 w-5 mr-2 text-purple-600" />
                Addresses & Emergency Contact
              </h4>
              <div className="space-y-4">
                <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                  <div className="flex items-center mb-2">
                    <div className="h-2 w-2 bg-purple-500 rounded-full mr-2"></div>
                    <div className="text-sm font-medium text-gray-600 dark:text-gray-400">Permanent Address</div>
                  </div>
                  <div className="font-medium text-gray-900 dark:text-white">{profile?.permanentAddress || 'Not specified'}</div>
                </div>
                
                <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                  <div className="flex items-center mb-2">
                    <div className="h-2 w-2 bg-blue-500 rounded-full mr-2"></div>
                    <div className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Address</div>
                  </div>
                  <div className="font-medium text-gray-900 dark:text-white">{profile?.currentAddress || 'Not specified'}</div>
                </div>
                
                <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                  <div className="flex items-center mb-2">
                    <div className="h-8 w-8 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mr-3">
                      <Phone className="h-4 w-4 text-red-600 dark:text-red-400" />
                    </div>
                    <div className="text-sm font-medium text-red-700 dark:text-red-400">Emergency Contact</div>
                  </div>
                  <div className="ml-11">
                    <div className="font-semibold text-red-900 dark:text-red-100">{profile?.emergencyContactName || 'Not specified'}</div>
                    <div className="text-sm text-red-600 dark:text-red-400">
                      {profile?.emergencyContactPhone} • {profile?.emergencyContactRelationship}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Education */}
      {profile?.education && profile.education.length > 0 && profile.education.some(edu => edu.educationLevel || edu.degree || edu.institution) && (
        <div className="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6 border border-gray-100 dark:border-gray-700">
          <div className="flex items-center mb-4">
            <div className="h-10 w-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center mr-3">
              <GraduationCap className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Education</h3>
              <p className="text-gray-500 dark:text-gray-400 text-xs">Academic qualifications and certifications</p>
            </div>
          </div>
          <div className="space-y-4">
            {profile.education.filter(edu => edu.educationLevel || edu.degree || edu.institution).map((edu, index) => (
              <div key={edu.id} className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-4 border border-indigo-200 dark:border-indigo-800">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Level</div>
                    <div className="font-medium text-gray-900 dark:text-white text-sm">{edu.educationLevel || 'Not specified'}</div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Degree</div>
                    <div className="font-medium text-gray-900 dark:text-white text-sm">{edu.degree || 'Not specified'}</div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Major</div>
                    <div className="font-medium text-gray-900 dark:text-white text-sm">{edu.major || 'N/A'}</div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Institution</div>
                    <div className="font-medium text-gray-900 dark:text-white text-sm">{edu.institution || 'Not specified'}</div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Graduation Year</div>
                    <div className="font-medium text-gray-900 dark:text-white text-sm">{edu.graduationYear || 'Not specified'}</div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Grade</div>
                    <div className="font-medium text-gray-900 dark:text-white text-sm">{edu.grade || 'N/A'}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Experience */}
      {profile?.experience && profile.experience.length > 0 && profile.experience.some(exp => exp.companyName || exp.position) && (
        <div className="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6 border border-gray-100 dark:border-gray-700">
          <div className="flex items-center mb-4">
            <div className="h-10 w-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mr-3">
              <Briefcase className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Work Experience</h3>
              <p className="text-gray-500 dark:text-gray-400 text-xs">Professional work history and achievements</p>
            </div>
          </div>
          <div className="space-y-4">
            {profile.experience.filter(exp => exp.companyName || exp.position).map((exp, index) => (
              <div key={exp.id} className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Company</div>
                    <div className="font-medium text-gray-900 dark:text-white text-sm">{exp.companyName || 'Not specified'}</div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Position</div>
                    <div className="font-medium text-gray-900 dark:text-white text-sm">{exp.position || 'Not specified'}</div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Start Date</div>
                    <div className="font-medium text-gray-900 dark:text-white text-sm">{exp.startDate || 'Not specified'}</div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">End Date</div>
                    <div className="font-medium text-gray-900 dark:text-white text-sm">{exp.endDate || 'Present'}</div>
                  </div>
                  {exp.description && (
                    <div className="col-span-2 bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                      <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Description</div>
                      <div className="font-medium text-gray-900 dark:text-white text-sm">{exp.description}</div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Skills */}
      {profile?.skills && profile.skills.length > 0 && profile.skills.some(skill => skill.professionalSkills || skill.technicalSkills || skill.certifications || skill.languages) && (
        <div className="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6 border border-gray-100 dark:border-gray-700">
          <div className="flex items-center mb-4">
            <div className="h-10 w-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center mr-3">
              <Star className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Skills & Certifications</h3>
              <p className="text-gray-500 dark:text-gray-400 text-xs">Professional expertise and qualifications</p>
            </div>
          </div>
          <div className="space-y-4">
            {profile.skills.filter(skill => skill.professionalSkills || skill.technicalSkills || skill.certifications || skill.languages).map((skill, index) => (
              <div key={skill.id} className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-4 border border-yellow-200 dark:border-yellow-800">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {skill.professionalSkills && (
                    <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                      <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Professional Skills</div>
                      <div className="font-medium text-gray-900 dark:text-white text-sm">{skill.professionalSkills}</div>
                    </div>
                  )}
                  {skill.technicalSkills && (
                    <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                      <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Technical Skills</div>
                      <div className="font-medium text-gray-900 dark:text-white text-sm">{skill.technicalSkills}</div>
                    </div>
                  )}
                  {skill.certifications && (
                    <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                      <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Certifications</div>
                      <div className="font-medium text-gray-900 dark:text-white text-sm">{skill.certifications}</div>
                    </div>
                  )}
                  {skill.languages && (
                    <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                      <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Languages</div>
                      <div className="font-medium text-gray-900 dark:text-white text-sm">{skill.languages}</div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Employment Details */}
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6 border border-gray-100 dark:border-gray-700">
        <div className="flex items-center mb-4">
          <div className="h-10 w-10 bg-gradient-to-r from-gray-500 to-gray-600 rounded-lg flex items-center justify-center mr-3">
            <Briefcase className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Employment Details</h3>
            <p className="text-gray-500 dark:text-gray-400 text-xs">Job information and work arrangements</p>
          </div>
        </div>
        
        {/* Core Employment Information */}
        <div className="mb-6">
          <h4 className="font-medium text-gray-900 dark:text-white mb-3 text-sm">Position & Department</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Employee ID</div>
              <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.employeeId || 'Not specified'}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Designation</div>
              <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.designation || 'Not specified'}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Department</div>
              <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.department || 'Not specified'}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Employment Type</div>
              <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.employmentType || 'Not specified'}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Employment Status</div>
              <div className="font-medium text-sm">
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  profile?.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                  profile?.status === 'inactive' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                  'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                }`}>
                  {profile?.status || 'Active'}
                </span>
              </div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Employee Level</div>
              <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.employeeLevel || 'Not specified'}</div>
            </div>
          </div>
        </div>

        {/* Work Details */}
        <div className="mb-6">
          <h4 className="font-medium text-gray-900 dark:text-white mb-3 text-sm">Work Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Join Date</div>
              <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.joinDate || 'Not specified'}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Probation End Date</div>
              <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.probationEndDate || 'Not specified'}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Notice Period</div>
              <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.noticePeriod || 'Not specified'}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Work Location</div>
              <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.location || 'Not specified'}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Work Schedule</div>
              <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.workSchedule || 'Not specified'}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Shift Type</div>
              <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.shiftType || 'Not specified'}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Remote Work Eligible</div>
              <div className="font-medium text-sm">
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  profile?.remoteWorkEligible ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                }`}>
                  {profile?.remoteWorkEligible ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Current Project</div>
              <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.project || 'Not specified'}</div>
            </div>
          </div>
        </div>

        {/* Reporting & Reviews */}
        <div className="mb-6">
          <h4 className="font-medium text-gray-900 dark:text-white mb-3 text-sm">Reporting & Performance</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Reporting Manager</div>
              <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.reportingManager || profile?.reportingTo || 'Not specified'}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Next Review Date</div>
              <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.nextReviewDate || 'Not specified'}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg shadow-sm">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Training Requirements</div>
              <div className="font-medium text-gray-900 dark:text-white text-sm">{profile?.trainingRequirements || 'Not specified'}</div>
            </div>
          </div>
        </div>

      </div>

      {/* Compensation & Benefits */}
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-2xl p-8 border border-gray-100 dark:border-gray-700">
        <div className="flex items-center mb-6">
          <div className="h-12 w-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mr-4">
            <DollarSign className="h-6 w-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Compensation & Benefits</h3>
            <p className="text-gray-500 dark:text-gray-400 text-sm">Salary information and allowances</p>
          </div>
        </div>
        
        {/* Salary & Compensation */}
        <div className="mb-8">
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <DollarSign className="h-5 w-5 mr-2 text-green-600" />
              Salary Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-green-200 dark:border-green-800">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">Total Salary</div>
                <div className="font-bold text-2xl text-green-600 dark:text-green-400">{profile?.totalSalary || 'Not specified'}</div>
              </div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">Salary Tier</div>
                <div className="font-semibold text-gray-900 dark:text-white">{profile?.salaryTier || 'Not specified'}</div>
              </div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">Payment Mode</div>
                <div className="font-semibold text-gray-900 dark:text-white">{profile?.paymentMode || 'Not specified'}</div>
              </div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">Cash Amount</div>
                <div className="font-semibold text-gray-900 dark:text-white">{profile?.cashAmount || 'Not specified'}</div>
              </div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">Bank Amount</div>
                <div className="font-semibold text-gray-900 dark:text-white">{profile?.bankAmount || 'Not specified'}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Allowances */}
        <div className="mb-6">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Award className="h-5 w-5 mr-2 text-blue-600" />
              Allowances & Perks
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">Food Allowance in Salary</div>
                <div className="flex items-center">
                  <span className={`px-3 py-1 text-sm font-medium rounded-full ${
                    profile?.foodAllowanceInSalary ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                  }`}>
                    {profile?.foodAllowanceInSalary ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">Fuel Allowance in Salary</div>
                <div className="flex items-center">
                  <span className={`px-3 py-1 text-sm font-medium rounded-full ${
                    profile?.fuelAllowanceInSalary ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                  }`}>
                    {profile?.fuelAllowanceInSalary ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">Number of Meals</div>
                <div className="font-semibold text-gray-900 dark:text-white">{profile?.numberOfMeals || 'Not specified'}</div>
              </div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">Fuel Allowance (Liters)</div>
                <div className="font-semibold text-gray-900 dark:text-white">{profile?.fuelInLiters || 'Not specified'}</div>
              </div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">Food Provided by Company</div>
                <div className="flex items-center">
                  <span className={`px-3 py-1 text-sm font-medium rounded-full ${
                    profile?.foodProvidedByCompany ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                  }`}>
                    {profile?.foodProvidedByCompany ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bank Details */}
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-2xl p-8 border border-gray-100 dark:border-gray-700">
        <div className="flex items-center mb-6">
          <div className="h-12 w-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mr-4">
            <CreditCard className="h-6 w-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Bank Details</h3>
            <p className="text-gray-500 dark:text-gray-400 text-sm">Banking and payment information</p>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-xl p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-blue-200 dark:border-blue-800">
              <div className="flex items-center mb-2">
                <div className="h-8 w-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                  <Building className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="text-sm font-medium text-gray-600 dark:text-gray-400">Bank Name</div>
              </div>
              <div className="font-semibold text-gray-900 dark:text-white">{profile?.bankName || 'Not specified'}</div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <div className="flex items-center mb-2">
                <div className="h-2 w-2 bg-cyan-500 rounded-full mr-2"></div>
                <div className="text-sm font-medium text-gray-600 dark:text-gray-400">Branch</div>
              </div>
              <div className="font-semibold text-gray-900 dark:text-white">{profile?.bankBranch || 'Not specified'}</div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <div className="flex items-center mb-2">
                <div className="h-2 w-2 bg-green-500 rounded-full mr-2"></div>
                <div className="text-sm font-medium text-gray-600 dark:text-gray-400">Account Number</div>
              </div>
              <div className="font-semibold text-gray-900 dark:text-white">{profile?.accountNumber || 'Not specified'}</div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <div className="flex items-center mb-2">
                <div className="h-2 w-2 bg-purple-500 rounded-full mr-2"></div>
                <div className="text-sm font-medium text-gray-600 dark:text-gray-400">Account Title</div>
              </div>
              <div className="font-semibold text-gray-900 dark:text-white">{profile?.accountTitle || 'Not specified'}</div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm lg:col-span-2">
              <div className="flex items-center mb-2">
                <div className="h-2 w-2 bg-orange-500 rounded-full mr-2"></div>
                <div className="text-sm font-medium text-gray-600 dark:text-gray-400">IBAN</div>
              </div>
              <div className="font-semibold text-gray-900 dark:text-white font-mono">{profile?.iban || 'Not specified'}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Insurance Coverage */}
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-2xl p-8 border border-gray-100 dark:border-gray-700">
        <div className="flex items-center mb-6">
          <div className="h-12 w-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mr-4">
            <Shield className="h-6 w-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Insurance Coverage</h3>
            <p className="text-gray-500 dark:text-gray-400 text-sm">Health and life insurance details</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {(profile?.healthInsuranceProvider || profile?.lifeInsuranceProvider) ? (
            <>
              {profile?.healthInsuranceProvider && (
                <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center mb-4">
                    <div className="h-10 w-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                      <Heart className="h-5 w-5 text-white" />
                    </div>
                    <h5 className="font-semibold text-blue-900 dark:text-blue-100 text-lg">Health Insurance</h5>
                  </div>
                  <div className="space-y-4">
                    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                      <div className="text-sm text-blue-700 dark:text-blue-400 mb-1">Provider</div>
                      <div className="font-semibold text-blue-900 dark:text-blue-100">{profile.healthInsuranceProvider}</div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                        <div className="text-sm text-blue-700 dark:text-blue-400 mb-1">Policy Number</div>
                        <div className="font-medium text-blue-900 dark:text-blue-100 font-mono text-sm">{profile?.healthInsurancePolicyNumber || 'N/A'}</div>
                      </div>
                      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                        <div className="text-sm text-blue-700 dark:text-blue-400 mb-1">Expiry Date</div>
                        <div className="font-medium text-blue-900 dark:text-blue-100">{profile?.healthInsuranceExpiryDate || 'N/A'}</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {profile?.lifeInsuranceProvider && (
                <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-xl border border-purple-200 dark:border-purple-800">
                  <div className="flex items-center mb-4">
                    <div className="h-10 w-10 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                      <Shield className="h-5 w-5 text-white" />
                    </div>
                    <h5 className="font-semibold text-purple-900 dark:text-purple-100 text-lg">Life Insurance</h5>
                  </div>
                  <div className="space-y-4">
                    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                      <div className="text-sm text-purple-700 dark:text-purple-400 mb-1">Provider</div>
                      <div className="font-semibold text-purple-900 dark:text-purple-100">{profile.lifeInsuranceProvider}</div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                        <div className="text-sm text-purple-700 dark:text-purple-400 mb-1">Policy Number</div>
                        <div className="font-medium text-purple-900 dark:text-purple-100 font-mono text-sm">{profile?.lifeInsurancePolicyNumber || 'N/A'}</div>
                      </div>
                      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                        <div className="text-sm text-purple-700 dark:text-purple-400 mb-1">Expiry Date</div>
                        <div className="font-medium text-purple-900 dark:text-purple-100">{profile?.lifeInsuranceExpiryDate || 'N/A'}</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="lg:col-span-2 bg-gray-50 dark:bg-gray-700/50 rounded-xl p-8 text-center">
              <Shield className="h-16 w-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">No Insurance Coverage</h4>
              <p className="text-gray-500 dark:text-gray-400">No insurance information is currently available for your profile.</p>
            </div>
          )}
        </div>

        {/* Accommodation */}
        {profile?.accommodationProvidedByEmployer && (
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Accommodation</h4>
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-orange-700">Provided by Employer</div>
                  <div className="font-medium text-orange-900">Yes</div>
                </div>
                <div>
                  <div className="text-sm text-orange-700">Type</div>
                  <div className="font-medium text-orange-900">{profile?.accommodationType || 'Not specified'}</div>
                </div>
                {profile?.accommodationAddress && (
                  <div className="col-span-2">
                    <div className="text-sm text-orange-700">Address</div>
                    <div className="font-medium text-orange-900">{profile.accommodationAddress}</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Notes and Special Instructions */}
        {(profile?.notes || profile?.specialInstructions) && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            {profile?.notes && (
              <div className="mb-4">
                <div className="text-sm text-gray-500">Employment Notes</div>
                <div className="font-medium text-gray-900 bg-gray-50 p-3 rounded">{profile.notes}</div>
              </div>
            )}
            {profile?.specialInstructions && (
              <div>
                <div className="text-sm text-gray-500">Special Instructions</div>
                <div className="font-medium text-gray-900 bg-yellow-50 p-3 rounded border-l-4 border-yellow-400">{profile.specialInstructions}</div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Devices */}
      {profile?.devices && profile.devices.length > 0 && (
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Monitor className="h-5 w-5 mr-2 text-blue-500" />
            Assigned Devices
          </h3>
          <div className="space-y-4">
            {profile.devices.map((device, index) => (
              <div key={device.id} className="border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div>
                    <div className="text-sm text-gray-500">Device Type</div>
                    <div className="font-medium">{device.deviceType}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Brand & Model</div>
                    <div className="font-medium">{device.deviceBrand} {device.deviceModel}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Serial Number</div>
                    <div className="font-medium">{device.serialNumber}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Assigned Date</div>
                    <div className="font-medium">{device.assignedDate}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Condition</div>
                    <div className="font-medium">{device.condition || 'N/A'}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Status</div>
                    <div className="font-medium">{device.returnDate ? 'Returned' : 'Active'}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Projects */}
      {profile?.projects && profile.projects.length > 0 && (
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <FolderOpen className="h-5 w-5 mr-2 text-blue-500" />
            Projects
          </h3>
          <div className="space-y-4">
            {profile.projects.map((project, index) => (
              <div key={project.id} className="border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <div className="text-sm text-gray-500">Project Name</div>
                    <div className="font-medium">{project.projectName}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Role</div>
                    <div className="font-medium">{project.role}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Start Date</div>
                    <div className="font-medium">{project.startDate}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Status</div>
                    <div className="font-medium">{project.status}</div>
                  </div>
                  {project.description && (
                    <div className="col-span-2">
                      <div className="text-sm text-gray-500">Description</div>
                      <div className="font-medium">{project.description}</div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Vehicles */}
      {profile?.vehicles && profile.vehicles.length > 0 && (
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Car className="h-5 w-5 mr-2 text-blue-500" />
            Assigned Vehicles
          </h3>
          <div className="space-y-4">
            {profile.vehicles.map((vehicle, index) => (
              <div key={vehicle.id} className="border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div>
                    <div className="text-sm text-gray-500">Vehicle Type</div>
                    <div className="font-medium">{vehicle.vehicleType}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Make & Model</div>
                    <div className="font-medium">{vehicle.make} {vehicle.model} {vehicle.year}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">License Plate</div>
                    <div className="font-medium">{vehicle.licensePlate}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Assigned Date</div>
                    <div className="font-medium">{vehicle.assignedDate}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Status</div>
                    <div className="font-medium">{vehicle.returnDate ? 'Returned' : 'Active'}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Health Records */}
      {profile?.healthRecords && profile.healthRecords.length > 0 && (
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Heart className="h-5 w-5 mr-2 text-blue-500" />
            Health Records
          </h3>
          <div className="space-y-4">
            {profile.healthRecords.map((health, index) => (
              <div key={health.id} className="border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <div className="text-sm text-gray-500">Record Type</div>
                    <div className="font-medium">{health.recordType}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Date</div>
                    <div className="font-medium">{health.recordDate}</div>
                  </div>
                  {health.doctorName && (
                    <div>
                      <div className="text-sm text-gray-500">Doctor</div>
                      <div className="font-medium">{health.doctorName}</div>
                    </div>
                  )}
                  {health.hospitalName && (
                    <div>
                      <div className="text-sm text-gray-500">Hospital</div>
                      <div className="font-medium">{health.hospitalName}</div>
                    </div>
                  )}
                  {health.description && (
                    <div className="col-span-2">
                      <div className="text-sm text-gray-500">Description</div>
                      <div className="font-medium">{health.description}</div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

const EmployeePortal: React.FC = () => {
  const { user, loading } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>('profile');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [sessionTimeRemaining, setSessionTimeRemaining] = useState<number>(30 * 60); // 30 minutes in seconds
  
  // Real employee data state
  const [employeeData, setEmployeeData] = useState<EmployeePortalData>({
    profile: null,
    leaveData: null,
    payrollData: null,
    performanceData: null,
    documentData: null,
    attendanceData: null,
    benefitsData: null,
    trainingData: null,
    teamData: null
  });

  // Format session time remaining
  const formatSessionTime = () => {
    const minutes = Math.floor(sessionTimeRemaining / 60);
    const seconds = sessionTimeRemaining % 60;
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  // Define access control for tabs
  const tabsConfig: TabPermission[] = [
    { 
      id: 'profile', 
      label: 'Profile', 
      icon: User, 
      roles: ['EMPLOYEE', 'DEPT_HEAD', 'IT_STAFF', 'IT_ADMIN', 'IT_SUPPORT', 'HR_STAFF'],
      component: <EmployeeProfileView profile={employeeData.profile} user={user} />
    },
    { 
      id: 'attendance', 
      label: 'Attendance', 
      icon: Clock, 
      roles: ['EMPLOYEE', 'DEPT_HEAD', 'IT_STAFF', 'IT_ADMIN', 'IT_SUPPORT', 'HR_STAFF'],
      component: <UserAttendanceView />
    },
    { 
      id: 'leave', 
      label: 'Leave', 
      icon: Calendar, 
      roles: ['EMPLOYEE', 'DEPT_HEAD', 'IT_STAFF', 'IT_ADMIN', 'IT_SUPPORT', 'HR_STAFF'],
      component: <EmployeeLeaveView />
    },
    { 
      id: 'payroll', 
      label: 'Payroll', 
      icon: DollarSign, 
      roles: ['EMPLOYEE', 'DEPT_HEAD', 'IT_STAFF', 'IT_ADMIN', 'HR_STAFF'],
      component: <EmployeePayrollView />
    },
    { 
      id: 'documents', 
      label: 'Documents', 
      icon: FileText, 
      roles: ['EMPLOYEE', 'DEPT_HEAD', 'IT_STAFF', 'IT_ADMIN', 'HR_STAFF'],
      component: <EmployeeDocumentsView />
    },
    { 
      id: 'performance', 
      label: 'Performance', 
      icon: BarChart2, 
      roles: ['EMPLOYEE', 'DEPT_HEAD', 'IT_STAFF', 'IT_ADMIN', 'HR_STAFF'],
      component: <EmployeePerformanceView performanceData={employeeData.performanceData} />
    },
    { 
      id: 'benefits', 
      label: 'Benefits', 
      icon: Heart, 
      roles: ['EMPLOYEE', 'DEPT_HEAD', 'IT_STAFF', 'IT_ADMIN', 'HR_STAFF'],
      component: <EmployeeBenefitsView />
    },
    { 
      id: 'team', 
      label: 'Team', 
      icon: Users, 
      roles: ['DEPT_HEAD', 'IT_ADMIN'],
      component: <EmployeeTeamView teamData={employeeData.teamData} />
    },
    { 
      id: 'training', 
      label: 'Training', 
      icon: GraduationCap, 
      roles: ['EMPLOYEE', 'DEPT_HEAD', 'IT_STAFF', 'IT_ADMIN', 'IT_SUPPORT', 'HR_STAFF'],
      component: <EmployeeTrainingView trainingData={employeeData.trainingData} />
    }
  ];

  // Filter tabs based on user role
  const authorizedTabs = tabsConfig.filter(tab => 
    user?.role && tab.roles.includes(user.role)
  );

  // Load employee data from API
  useEffect(() => {
    const loadEmployeeData = async () => {
      if (!user?.email) return;
      
        setIsLoading(true);
      setError('');
      
      try {
        console.log('Loading employee data for email:', user.email);
        
        const [
          profile,
          leaveData,
          payrollData,
          performanceData,
          documentData,
          attendanceData,
          benefitsData,
          trainingData,
          teamData
        ] = await Promise.all([
          employeePortalService.getEmployeeProfile(user.email),
          employeePortalService.getEmployeeLeaveData(Number(user.id ?? 1)),
          employeePortalService.getEmployeePayrollData(Number(user.id ?? 1)),
          employeePortalService.getEmployeePerformanceData(Number(user.id ?? 1)),
          employeePortalService.getEmployeeDocuments(Number(user.id ?? 1)),
          employeePortalService.getEmployeeAttendanceData(Number(user.id ?? 1)),
          employeePortalService.getEmployeeBenefitsData(Number(user.id ?? 1)),
          employeePortalService.getEmployeeTrainingData(Number(user.id ?? 1)),
          employeePortalService.getEmployeeTeamData(Number(user.id ?? 1))
        ]);

        console.log('Employee data loaded:', {
          profile: profile ? {
            id: profile.id,
            name: `${profile.firstName} ${profile.lastName}`,
            educationCount: profile.education?.length || 0,
            experienceCount: profile.experience?.length || 0,
            familyCount: profile.family?.length || 0,
            skillsCount: profile.skills?.length || 0,
            devicesCount: profile.devices?.length || 0,
            projectsCount: profile.projects?.length || 0,
            vehiclesCount: profile.vehicles?.length || 0,
            healthRecordsCount: profile.healthRecords?.length || 0
          } : null,
          leaveData: leaveData ? { balancesCount: leaveData.balances?.length || 0 } : null,
          payrollData: payrollData ? { payslipsCount: payrollData.payslips?.length || 0 } : null,
          documentData: documentData ? { documentsCount: documentData.documents?.length || 0 } : null,
          attendanceData: attendanceData ? { hasData: true } : null,
          benefitsData: benefitsData ? { hasData: true } : null,
          trainingData: trainingData ? { hasData: true } : null,
          teamData: teamData ? { hasData: true } : null
        });

        setEmployeeData({
          profile,
          leaveData,
          payrollData,
          performanceData,
          documentData,
          attendanceData,
          benefitsData,
          trainingData,
          teamData
        });
      } catch (err) {
        console.error('Error loading employee data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load employee data');
      } finally {
        setIsLoading(false);
      }
    };

    loadEmployeeData();
  }, [user]);

  // Set the active tab to the first authorized tab if current one is not authorized
  useEffect(() => {
    if (user && authorizedTabs.length > 0) {
      const isCurrentTabAuthorized = authorizedTabs.some(tab => tab.id === activeTab);
      if (!isCurrentTabAuthorized) {
        setActiveTab(authorizedTabs[0].id);
      }
    }
  }, [user, authorizedTabs]);

  // Session timeout counter
  useEffect(() => {
    const timer = setInterval(() => {
      setSessionTimeRemaining(prev => {
        if (prev <= 0) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  if (loading || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex justify-center items-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading employee portal...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
        <div className="max-w-2xl mx-auto pt-20">
          <div className="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 p-4 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="h-6 w-6 text-red-500 mr-3" />
              <div>
                <p className="text-red-700 dark:text-red-400 font-medium">Error Loading Portal</p>
                <p className="text-red-600 dark:text-red-300 text-sm mt-1">{error}</p>
                <button 
                  className="mt-3 bg-red-100 dark:bg-red-800 text-red-700 dark:text-red-200 px-4 py-2 rounded-lg text-sm hover:bg-red-200 dark:hover:bg-red-700 transition-colors"
                  onClick={() => window.location.reload()}
                >
                  Retry
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Function to render the active tab component
  const renderActiveTabContent = () => {
    const activeTabConfig = authorizedTabs.find(tab => tab.id === activeTab);
    
    if (!activeTabConfig) {
      return (
        <div className="p-4">
          <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  You don't have access to this section or the tab doesn't exist.
                </p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return activeTabConfig.component;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Employee Portal</h1>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-500 dark:text-gray-400 hidden sm:block">
              Session expires in: <span className="font-medium text-orange-600 dark:text-orange-400">{formatSessionTime()}</span>
            </div>
            <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
              <Shield className="h-3 w-3 mr-1" />
              {user?.role || 'Employee'}
            </div>
          </div>
        </div>
        
        {/* Tab Navigation */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm mb-6 overflow-hidden border border-gray-200 dark:border-gray-700">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex overflow-x-auto">
              {authorizedTabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-4 py-3 font-medium text-sm flex items-center whitespace-nowrap min-w-max transition-colors ${
                    activeTab === tab.id
                      ? 'border-b-2 border-blue-500 text-blue-600 bg-blue-50 dark:bg-blue-900/30 dark:text-blue-400'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-700'
                  }`}
                >
                  <tab.icon className="h-4 w-4 mr-2" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>
        
        {/* Tab Content */}
        <div className="mb-12">
          {renderActiveTabContent()}
        </div>

        {/* Footer with version and system info */}
        <div className="mt-12 pt-6 border-t border-gray-200 dark:border-gray-700 text-center text-gray-500 dark:text-gray-400 text-xs">
          <p>Employee Portal v1.0.0 | Last updated: {new Date().toLocaleDateString()}</p>
          <p className="mt-1">© {new Date().getFullYear()} InfraSpine. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
};
  
export default EmployeePortal;