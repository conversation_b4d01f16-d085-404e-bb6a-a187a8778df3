import axios from 'axios';
import { Expense, BudgetItem } from '../types/expense';

const API_BASE_URL = '/api';

export const ExpenseService = {
  // Expense-related API calls
  async getExpenses(): Promise<Expense[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/expenses`);
      return response.data;
    } catch (error) {
      console.error('Error fetching expenses:', error);
      return [];
    }
  },

  async createExpense(expenseData: Partial<Expense>): Promise<Expense | null> {
    try {
      const response = await axios.post(`${API_BASE_URL}/expenses`, expenseData);
      return response.data;
    } catch (error) {
      console.error('Error creating expense:', error);
      return null;
    }
  },

  async updateExpense(id: string, expenseData: Partial<Expense>): Promise<Expense | null> {
    try {
      const response = await axios.put(`${API_BASE_URL}/expenses/${id}`, expenseData);
      return response.data;
    } catch (error) {
      console.error('Error updating expense:', error);
      return null;
    }
  },

  async deleteExpense(id: string): Promise<boolean> {
    try {
      await axios.delete(`${API_BASE_URL}/expenses/${id}`);
      return true;
    } catch (error) {
      console.error('Error deleting expense:', error);
      return false;
    }
  },

  // Budget-related API calls
  async getBudgetItems(): Promise<BudgetItem[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/budget-items`);
      return response.data;
    } catch (error) {
      console.error('Error fetching budget items:', error);
      return [];
    }
  },

  async updateBudgetItem(id: string, budgetData: Partial<BudgetItem>): Promise<BudgetItem | null> {
    try {
      const response = await axios.put(`${API_BASE_URL}/budget-items/${id}`, budgetData);
      return response.data;
    } catch (error) {
      console.error('Error updating budget item:', error);
      return null;
    }
  },

  // Export functionality
  async exportExpenses(format: 'csv' | 'pdf' = 'csv'): Promise<Blob | null> {
    try {
      const response = await axios.get(`${API_BASE_URL}/expenses/export`, {
        params: { format },
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting expenses:', error);
      return null;
    }
  }
}; 