import { Router } from 'express';
import { 
  getRoles, 
  createRole, 
  updateRole, 
  deleteRole, 
  getRoleById,
  assignRolesToUser,
  bulkAssignRoles,
  removeRoleFromUser
} from '../../controllers/roleController';
import { auth, authorize } from '../middleware/auth';
import { UserRole } from '../../types/common';
import rateLimit from 'express-rate-limit';
import { AppDataSource } from '../../config/database';
import { Role } from '../../entities/Role';
import { UserRoleAssignment } from '../../entities/UserRoleAssignment';

const router = Router();

// Configure rate limiting
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});

// Apply rate limiting to all role routes
router.use(apiLimiter);

// Add a fallback route that works even if database is down
router.get('/fallback', async (req, res) => {
  try {
    // Try to get real data from database first
    if (AppDataSource.isInitialized) {
      const roleRepository = AppDataSource.getRepository(Role);
      const userRoleAssignmentRepository = AppDataSource.getRepository(UserRoleAssignment);
      
      const roles = await roleRepository.find();
      
      console.log('🔄 Fallback route: Calculating real user counts for roles...');
      
      // Calculate actual user counts for each role
      const rolesWithRealCounts = await Promise.all(roles.map(async (role) => {
        try {
          // Count actual user assignments for this role
          const userCount = await userRoleAssignmentRepository.count({
            where: { roleId: role.id }
          });
          
          console.log(`📊 Fallback: Role "${role.name}" (ID: ${role.id}) has ${userCount} users`);
          
          return {
            ...role,
            userCount: userCount
          };
        } catch (countError) {
          console.error(`❌ Error counting users for role ${role.id}:`, countError);
          return role;
        }
      }));
      
      // Convert to frontend-compatible format
      const frontendRoles = rolesWithRealCounts.map(role => ({
        id: String(role.id), // Convert numeric ID to string for frontend compatibility
        name: role.name,
        description: role.description,
        permissions: role.permissions,
        createdAt: role.createdAt.toISOString(),
        userCount: role.userCount,
        category: role.category
      }));
      
      console.log('✅ Fallback route: Returning roles with real user counts');
      return res.json({ roles: frontendRoles });
    } else {
      return res.status(503).json({ 
        error: 'Database not available',
        message: 'Database connection not initialized'
      });
    }
  } catch (error) {
    console.error('Error in fallback roles endpoint:', error);
    return res.status(500).json({ 
      error: 'Database error',
      message: 'Failed to fetch roles from database'
    });
  }
});

// Get all roles
router.get('/',
  auth,
  authorize([UserRole.IT_ADMIN]),
  getRoles
);

// Get role by ID
router.get('/:id',
  auth,
  authorize([UserRole.IT_ADMIN]),
  getRoleById
);

// Create new role
router.post('/',
  auth,
  authorize([UserRole.IT_ADMIN]),
  createRole
);

// Update role
router.put('/:id',
  auth,
  authorize([UserRole.IT_ADMIN]),
  updateRole
);

// Delete role
router.delete('/:id',
  auth,
  authorize([UserRole.IT_ADMIN]),
  deleteRole
);

// Assign roles to a user
router.post('/assign',
  auth,
  authorize([UserRole.IT_ADMIN]),
  assignRolesToUser
);

// Bulk assign roles to multiple users
router.post('/bulk-assign',
  auth,
  authorize([UserRole.IT_ADMIN]),
  bulkAssignRoles
);

// Remove a role from a user
router.delete('/assign/:userId/:roleId',
  auth,
  authorize([UserRole.IT_ADMIN]),
  removeRoleFromUser
);

export default router; 