import React, { useState, useEffect } from 'react';
import { Attendance, AttendanceStatus } from '../../../types/attendance';
import { 
  ChevronLeft, 
  ChevronRight, 
  Calendar as CalendarIcon, 
  Users, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  Home,
  Building,
  Filter,
  Info,
  TrendingUp,
  BarChart3,
  Star
} from 'lucide-react';

interface Holiday {
  id: number;
  name: string;
  date: string;
  type: 'public' | 'company' | 'optional';
  description?: string;
  isActive?: boolean;
  metadata?: {
    religion?: string;
    culturalSignificance?: string;
    observanceLevel?: 'major' | 'minor' | 'regional';
    alternativeNames?: string[];
    duration?: number;
    fastingRequired?: boolean;
    lunarCalendar?: boolean;
  };
}

interface AttendanceCalendarProps {
  attendances: Attendance[];
  selectedDate: string;
  onDateChange: (date: string) => void;
  holidays?: Holiday[];
  weekendDays?: number[];
}

interface CalendarDay {
  date: Date;
  dayOfMonth: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  isWeekend: boolean;
  isHoliday: boolean;
  holiday?: Holiday;
  attendances: Attendance[];
  stats: {
    present: number;
    absent: number;
    late: number;
    wfh: number;
    leave: number;
    total: number;
  };
}

const AttendanceCalendar: React.FC<AttendanceCalendarProps> = ({
  attendances,
  selectedDate,
  onDateChange,
  holidays = [],
  weekendDays = [0, 6]
}) => {
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [calendarDays, setCalendarDays] = useState<CalendarDay[]>([]);
  const [viewMode, setViewMode] = useState<'month' | 'week'>('month');
  const [selectedEmployee, setSelectedEmployee] = useState<string>('');
  const [hoveredDay, setHoveredDay] = useState<CalendarDay | null>(null);

  // Helper function to format date as YYYY-MM-DD in local timezone
  const formatLocalDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Get unique employees for filter
  const employees = React.useMemo(() => {
    const uniqueEmployees = attendances.reduce((acc, attendance) => {
      if (!acc.find(emp => emp.id === attendance.employeeId)) {
        acc.push({
          id: attendance.employeeId,
          name: attendance.employeeName
        });
      }
      return acc;
    }, [] as { id: number; name: string }[]);
    return uniqueEmployees.sort((a, b) => a.name.localeCompare(b.name));
  }, [attendances]);

  // Calculate statistics for a specific day
  const calculateDayStats = (dayAttendances: Attendance[]) => {
    return {
      present: dayAttendances.filter(a => a.status === AttendanceStatus.PRESENT).length,
      absent: dayAttendances.filter(a => a.status === AttendanceStatus.ABSENT).length,
      late: dayAttendances.filter(a => a.status === AttendanceStatus.LATE).length,
      wfh: dayAttendances.filter(a => a.status === AttendanceStatus.WORK_FROM_HOME).length,
      leave: dayAttendances.filter(a => 
        [AttendanceStatus.LEAVE, AttendanceStatus.SICK_LEAVE, AttendanceStatus.ANNUAL_LEAVE].includes(a.status)
      ).length,
      total: dayAttendances.length
    };
  };

  // Helper function to check if a date is a weekend
  const isWeekend = (date: Date): boolean => {
    // Use configurable weekend days, fallback to default [0, 6] (Sunday, Saturday)
    const defaultWeekends = [0, 6];
    const weekends = weekendDays.length > 0 ? weekendDays : defaultWeekends;
    const dayOfWeek = date.getDay();
    const result = weekends.includes(dayOfWeek);
    
    // Debug logging for weekends (sample only to avoid spam)
    if (Math.random() < 0.05) { // Only log 5% of checks
      console.log('🗓️ Weekend check:', {
        date: date.toDateString(),
        dayOfWeek,
        weekendDaysConfig: weekendDays,
        effectiveWeekends: weekends,
        isWeekend: result
      });
    }
    
    return result;
  };

  // Generate calendar days
  const generateCalendarDays = () => {
    const days: CalendarDay[] = [];
    const today = new Date();
    
    if (viewMode === 'week') {
      console.log('🗓️ Calendar: Generating WEEK view for', currentMonth.toLocaleDateString());
      
      // Calculate the start of the week (Sunday) for the current date
      const startOfWeek = new Date(currentMonth);
      startOfWeek.setDate(currentMonth.getDate() - currentMonth.getDay());
      
      // Generate 7 days for the week
      for (let i = 0; i < 7; i++) {
        const date = new Date(startOfWeek);
        date.setDate(startOfWeek.getDate() + i);
        const dateString = formatLocalDate(date);
        const dayAttendances = filterAttendances(dateString);
        
        console.log(`📅 Week Day ${i + 1} (${date.toLocaleDateString('en-US', {weekday: 'short'})}):`, {
          date: date.toString(),
          dateString,
          dayOfWeek: date.getDay(),
          attendanceCount: dayAttendances.length
        });
        
        days.push({
          date,
          dayOfMonth: date.getDate(),
          isCurrentMonth: true, // Always show as current for week view
          isToday: date.toDateString() === today.toDateString(),
          isSelected: dateString === selectedDate,
          isWeekend: isWeekend(date),
          isHoliday: false,
          holiday: undefined,
          attendances: dayAttendances,
          stats: calculateDayStats(dayAttendances)
        });
      }
      
      console.log('🗓️ Calendar: Generated WEEK view with', days.length, 'days');
    } else {
      // MONTH VIEW - existing logic
      console.log('🗓️ Calendar: Generating MONTH view for', getDisplayTitle());
      
      const year = currentMonth.getFullYear();
      const month = currentMonth.getMonth();
      const firstDayOfMonth = new Date(year, month, 1);
      const lastDayOfMonth = new Date(year, month + 1, 0);
      const firstDayOfWeek = firstDayOfMonth.getDay();
      const daysInMonth = lastDayOfMonth.getDate();
      
      console.log('📅 First day of month:', firstDayOfMonth.toString());
      console.log('📅 First day of week index:', firstDayOfWeek, '(0=Sunday, 1=Monday, etc.)');
      
      // Add previous month's days
      const prevMonth = new Date(year, month - 1, 0);
      const daysFromPrevMonth = firstDayOfWeek;
      
      for (let i = daysFromPrevMonth; i > 0; i--) {
        const date = new Date(year, month, 1 - i);
        const dateString = formatLocalDate(date);
        const dayAttendances = filterAttendances(dateString);
        
        days.push({
          date,
          dayOfMonth: date.getDate(),
          isCurrentMonth: false,
          isToday: date.toDateString() === today.toDateString(),
          isSelected: dateString === selectedDate,
          isWeekend: isWeekend(date),
          isHoliday: false,
          holiday: undefined,
          attendances: dayAttendances,
          stats: calculateDayStats(dayAttendances)
        });
      }
      
      // Add current month's days
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month, day);
        const dateString = formatLocalDate(date);
        const dayAttendances = filterAttendances(dateString);
        
        // Debug specific dates to track the issue
        if (day <= 7) { // First week
          console.log(`📅 Day ${day} (${date.toLocaleDateString('en-US', {weekday: 'short'})}):`, {
            date: date.toString(),
            dateString,
            dayOfWeek: date.getDay(),
            attendanceCount: dayAttendances.length
          });
        }
        
        days.push({
          date,
          dayOfMonth: day,
          isCurrentMonth: true,
          isToday: date.toDateString() === today.toDateString(),
          isSelected: dateString === selectedDate,
          isWeekend: isWeekend(date),
          isHoliday: false,
          holiday: undefined,
          attendances: dayAttendances,
          stats: calculateDayStats(dayAttendances)
        });
      }
      
      // Add next month's days
      const totalDays = 42; // 6 rows × 7 days
      const remainingDays = totalDays - days.length;
      
      for (let day = 1; day <= remainingDays; day++) {
        const date = new Date(year, month + 1, day);
        const dateString = formatLocalDate(date);
        const dayAttendances = filterAttendances(dateString);
        
        days.push({
          date,
          dayOfMonth: day,
          isCurrentMonth: false,
          isToday: date.toDateString() === today.toDateString(),
          isSelected: dateString === selectedDate,
          isWeekend: isWeekend(date),
          isHoliday: false,
          holiday: undefined,
          attendances: dayAttendances,
          stats: calculateDayStats(dayAttendances)
        });
      }
      
      console.log('🗓️ Calendar: Generated MONTH view with', days.length, 'calendar days');
      console.log('📅 First 7 days mapping:', days.slice(0, 7).map(d => ({
        dayOfMonth: d.dayOfMonth,
        dayOfWeek: d.date.getDay(),
        weekdayName: d.date.toLocaleDateString('en-US', {weekday: 'short'}),
        dateString: formatLocalDate(d.date),
        attendanceCount: d.attendances.length
      })));
    }
    
    // Add holiday detection for all days
    days.forEach(day => {
      const dateString = formatLocalDate(day.date);
      
      // Check for exact date match first
      let holiday = holidays.find(h => h.date === dateString && h.isActive !== false);
      
      // If no exact match, check for multi-day holidays
      if (!holiday) {
        holiday = holidays.find(h => {
          if (h.isActive === false) return false;
          
          const duration = h.metadata?.duration || 1;
          if (duration <= 1) return false; // Skip single-day holidays
          
          const holidayStartDate = new Date(h.date);
          const holidayEndDate = new Date(holidayStartDate);
          holidayEndDate.setDate(holidayStartDate.getDate() + duration - 1);
          
          const currentDate = day.date;
          
          // Check if current date falls within the holiday range
          return currentDate >= holidayStartDate && currentDate <= holidayEndDate;
        });
      }
      
      if (holiday) {
        day.isHoliday = true;
        day.holiday = holiday;
      }
    });
    
    console.log('🎉 Holiday detection complete:', holidays.length, 'holidays configured,', 
      days.filter(d => d.isHoliday).length, 'days marked as holidays');
    console.log('📅 Multi-day holidays detected:', holidays.filter(h => (h.metadata?.duration || 1) > 1).length);
    
    setCalendarDays(days);
  };

  // Filter attendances based on selected employee
  const filterAttendances = (dateString: string) => {
    let filtered = attendances.filter(a => a.date === dateString);
    
    // Debug logging for data flow
    if (dateString === formatLocalDate(new Date())) {
      console.log(`🔍 Filtering attendance for TODAY (${dateString}):`, filtered.length, 'records found');
      if (filtered.length > 0) {
        console.log('📋 Today\'s records sample:', filtered.slice(0, 2));
      }
    }
    
    if (selectedEmployee) {
      filtered = filtered.filter(a => a.employeeName === selectedEmployee);
      console.log(`👤 Employee filter applied for "${selectedEmployee}":`, filtered.length, 'records remain');
    }
    
    return filtered;
  };

  // Update calendar when dependencies change
  useEffect(() => {
    console.log('🗓️ Calendar: Regenerating calendar days...');
    console.log('📊 Attendance data received:', attendances.length, 'records');
    console.log('🔍 Sample attendance records:', attendances.slice(0, 3));
    console.log('🗓️ Weekend configuration received:', weekendDays);
    console.log('🎉 Holiday configuration received:', holidays.length, 'holidays');
    
    if (attendances.length > 0) {
      const dates = attendances.map(a => a.date).sort();
      console.log('📅 Date range in data:', {
        earliest: dates[0],
        latest: dates[dates.length - 1]
      });
    } else {
      console.log('📅 No attendance data available');
    }
    
    generateCalendarDays();
  }, [currentMonth, attendances, selectedDate, selectedEmployee, viewMode, holidays, weekendDays]);

  // Navigation handlers
  const handlePrevMonth = () => {
    if (viewMode === 'week') {
      // Navigate to previous week
      setCurrentMonth(prev => {
        const newDate = new Date(prev);
        newDate.setDate(newDate.getDate() - 7);
        return newDate;
      });
    } else {
      // Navigate to previous month
      setCurrentMonth(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1));
    }
  };

  const handleNextMonth = () => {
    if (viewMode === 'week') {
      // Navigate to next week
      setCurrentMonth(prev => {
        const newDate = new Date(prev);
        newDate.setDate(newDate.getDate() + 7);
        return newDate;
      });
    } else {
      // Navigate to next month
      setCurrentMonth(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1));
    }
  };

  const handleToday = () => {
    const today = new Date();
    if (viewMode === 'week') {
      // Navigate to current week
      setCurrentMonth(new Date(today));
    } else {
      // Navigate to current month
      setCurrentMonth(new Date(today.getFullYear(), today.getMonth(), 1));
    }
  };

  const handleDayClick = (day: CalendarDay) => {
    if (day.isCurrentMonth) {
      // Helper function to format date as YYYY-MM-DD in local timezone
      const dateString = formatLocalDate(day.date);
      console.log('📅 Calendar: User clicked date', dateString);
      console.log('📊 Linking to attendance table - showing data for:', dateString);
      
      onDateChange(dateString);
      
      // Show visual feedback about the data available for this date
      if (day.stats.total > 0) {
        console.log(`✅ Found ${day.stats.total} attendance records for ${dateString}`);
      } else {
        console.log(`ℹ️ No attendance records found for ${dateString}`);
      }
    }
  };

  // Get status color
  const getStatusColor = (status: AttendanceStatus): string => {
    const colors: Record<AttendanceStatus, string> = {
      [AttendanceStatus.PRESENT]: 'bg-green-500',
      [AttendanceStatus.ABSENT]: 'bg-red-500',
      [AttendanceStatus.LATE]: 'bg-yellow-500',
      [AttendanceStatus.WORK_FROM_HOME]: 'bg-blue-500',
      [AttendanceStatus.LEAVE]: 'bg-purple-500',
      [AttendanceStatus.SICK_LEAVE]: 'bg-orange-500',
      [AttendanceStatus.ANNUAL_LEAVE]: 'bg-cyan-500',
      [AttendanceStatus.HALF_DAY]: 'bg-amber-500',
      [AttendanceStatus.HOLIDAY]: 'bg-pink-500',
      [AttendanceStatus.WEEKEND]: 'bg-gray-400',
      [AttendanceStatus.ON_DUTY]: 'bg-emerald-500',
      [AttendanceStatus.PAID_TIME_OFF]: 'bg-violet-500',
      [AttendanceStatus.UNPAID_LEAVE]: 'bg-red-400',
      [AttendanceStatus.COMP_OFF]: 'bg-teal-500',
      [AttendanceStatus.MATERNITY_LEAVE]: 'bg-rose-500',
      [AttendanceStatus.PATERNITY_LEAVE]: 'bg-indigo-500'
    };
    return colors[status] || 'bg-gray-400';
  };

  // Render day popover
  const renderDayPopover = (day: CalendarDay) => {
    if (!hoveredDay || hoveredDay !== day) return null;
    
    return (
      <div className="absolute z-50 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 p-4 w-80 -translate-x-1/2 transform">
        <div className="font-semibold text-gray-900 dark:text-white mb-3">
          {day.date.toLocaleDateString('en-US', { 
            weekday: 'long',
            year: 'numeric',
            month: 'long', 
            day: 'numeric'
          })}
        </div>
        
        {/* Holiday information */}
        {day.isHoliday && day.holiday && (
          <div className="mb-4 p-3 bg-purple-50 dark:bg-purple-900/30 rounded-lg border border-purple-200 dark:border-purple-700">
            <div className="flex items-center mb-2">
              <Star className="h-4 w-4 text-purple-600 dark:text-purple-400 mr-2" />
              <span className="text-sm font-medium text-purple-800 dark:text-purple-300">Holiday</span>
              {day.holiday.metadata?.duration && day.holiday.metadata.duration > 1 && (
                <span className="ml-2 text-xs text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-800/50 px-2 py-0.5 rounded">
                  {(() => {
                    const holidayStart = new Date(day.holiday.date);
                    const currentDate = day.date;
                    const daysDiff = Math.floor((currentDate.getTime() - holidayStart.getTime()) / (1000 * 60 * 60 * 24));
                    const dayNumber = daysDiff + 1;
                    return `Day ${dayNumber} of ${day.holiday.metadata.duration}`;
                  })()}
                </span>
              )}
            </div>
            <div className="text-sm text-purple-700 dark:text-purple-300 font-medium">{day.holiday.name}</div>
            {day.holiday.description && (
              <div className="text-xs text-purple-600 dark:text-purple-400 mt-1">{day.holiday.description}</div>
            )}
            <div className="text-xs text-purple-500 dark:text-purple-400 mt-1 capitalize">
              {day.holiday.type} holiday
            </div>
          </div>
        )}
        
        {/* Weekend indicator */}
        {day.isWeekend && !day.isHoliday && (
          <div className="mb-4 p-2 bg-red-50 dark:bg-red-900/30 rounded-lg border border-red-200 dark:border-red-700">
            <div className="text-sm text-red-700 dark:text-red-300 font-medium">Weekend</div>
          </div>
        )}
        
        {/* Quick stats */}
        <div className="grid grid-cols-2 gap-3 mb-4">
            <div className="flex items-center">
            <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mr-2" />
            <span className="text-sm text-gray-700 dark:text-gray-300">Present: {day.stats.present}</span>
          </div>
            <div className="flex items-center">
            <XCircle className="h-4 w-4 text-red-500 dark:text-red-400 mr-2" />
            <span className="text-sm text-gray-700 dark:text-gray-300">Absent: {day.stats.absent}</span>
          </div>
            <div className="flex items-center">
            <AlertTriangle className="h-4 w-4 text-yellow-500 dark:text-yellow-400 mr-2" />
            <span className="text-sm text-gray-700 dark:text-gray-300">Late: {day.stats.late}</span>
          </div>
            <div className="flex items-center">
            <Home className="h-4 w-4 text-blue-500 dark:text-blue-400 mr-2" />
            <span className="text-sm text-gray-700 dark:text-gray-300">WFH: {day.stats.wfh}</span>
          </div>
          <div className="flex items-center">
            <Clock className="h-4 w-4 text-purple-500 dark:text-purple-400 mr-2" />
            <span className="text-sm text-gray-700 dark:text-gray-300">Leave: {day.stats.leave}</span>
          </div>
        </div>

        {/* Employee list */}
        {day.attendances.length > 0 && (
          <div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Employee Details ({day.attendances.length})
            </div>
            <div className="max-h-32 overflow-y-auto space-y-1">
              {day.attendances.map((attendance, index) => (
                <div key={index} className="flex items-center justify-between py-1 px-2 bg-gray-50 dark:bg-gray-700/50 rounded">
                  <div className="flex items-center">
                  <div className={`w-2 h-2 rounded-full ${getStatusColor(attendance.status)} mr-2`}></div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{attendance.employeeName}</span>
                  </div>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {attendance.checkInTime || '--:--'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Get month/year display
  const getDisplayTitle = () => {
    if (viewMode === 'week') {
      // Calculate week range
      const startOfWeek = new Date(currentMonth);
      startOfWeek.setDate(currentMonth.getDate() - currentMonth.getDay());
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);
      
      const startMonth = startOfWeek.toLocaleDateString('en-US', { month: 'short' });
      const endMonth = endOfWeek.toLocaleDateString('en-US', { month: 'short' });
      const startDay = startOfWeek.getDate();
      const endDay = endOfWeek.getDate();
      const year = startOfWeek.getFullYear();
      
      if (startMonth === endMonth) {
        return `${startMonth} ${startDay}-${endDay}, ${year}`;
      } else {
        return `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${year}`;
      }
    } else {
      return currentMonth.toLocaleDateString('en-US', { 
        month: 'long', 
        year: 'numeric' 
      });
    }
  };

  const getNavigationLabel = () => {
    return viewMode === 'week' ? { prev: 'Previous week', next: 'Next week' } : { prev: 'Previous month', next: 'Next month' };
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 transition-colors duration-200">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-4 mb-4 md:mb-0">
          <div className="flex items-center bg-blue-50 dark:bg-blue-900/30 rounded-lg p-2">
            <CalendarIcon className="h-6 w-6 text-blue-600 dark:text-blue-400 mr-2" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">{getDisplayTitle()}</h2>
            {attendances.length > 0 && (
              <span className="ml-2 text-sm text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/50 px-2 py-1 rounded">
                {attendances.length} records
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handlePrevMonth}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
              aria-label={getNavigationLabel().prev}
            >
              <ChevronLeft className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            </button>
            
            <button
              onClick={handleToday}
              className="px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors duration-200"
            >
              Today
            </button>
            
            <button
              onClick={handleNextMonth}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
              aria-label={getNavigationLabel().next}
            >
              <ChevronRight className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            </button>
          </div>
        </div>
        
        {/* Filters */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            <select
              value={selectedEmployee}
              onChange={(e) => setSelectedEmployee(e.target.value)}
              className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="">All Employees</option>
              {employees.map(employee => (
                <option key={employee.id} value={employee.name}>
                  {employee.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setViewMode('month')}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors duration-200 ${
                viewMode === 'month' 
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' 
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              Month
            </button>
            <button
              onClick={() => setViewMode('week')}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors duration-200 ${
                viewMode === 'week' 
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' 
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              Week
            </button>
          </div>
        </div>
      </div>
      
      {/* Calendar Grid */}
      <div className="p-6">
        {/* Day headers */}
        <div className="grid grid-cols-7 mb-2">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => {
            const defaultWeekends = [0, 6];
            const configuredWeekends = weekendDays.length > 0 ? weekendDays : defaultWeekends;
            const isWeekendHeader = configuredWeekends.includes(index);
            
            return (
              <div 
                key={day} 
                className={`py-3 text-center text-sm font-semibold ${
                  isWeekendHeader ? 'text-red-500 dark:text-red-400' : 'text-gray-700 dark:text-gray-300'
                }`}
              >
                {day}
              </div>
            );
          })}
        </div>
        
        {/* Calendar days */}
        <div className={`grid gap-1 ${viewMode === 'week' ? 'grid-cols-7' : 'grid-cols-7'}`}>
          {calendarDays.map((day, index) => {
            return (
              <div
                key={index}
                className={`
                  relative p-2 border border-gray-100 dark:border-gray-600 rounded-lg cursor-pointer transition-all duration-200
                  ${viewMode === 'week' ? 'min-h-[120px]' : 'min-h-[100px]'}
                  ${!day.isCurrentMonth && viewMode === 'month' ? 'bg-gray-50 dark:bg-gray-700/50 opacity-50' : 'bg-white dark:bg-gray-800 hover:bg-blue-50 dark:hover:bg-blue-900/20'}
                  ${day.isToday ? 'ring-2 ring-blue-500 dark:ring-blue-400 bg-blue-50 dark:bg-blue-900/30' : ''}
                  ${day.isSelected ? 'ring-2 ring-blue-600 dark:ring-blue-400 bg-blue-100 dark:bg-blue-900/40' : ''}
                  ${day.isWeekend && day.isCurrentMonth ? 'bg-red-50 dark:bg-red-900/20' : ''}
                  ${day.isHoliday && day.isCurrentMonth ? 'bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/30 dark:to-pink-900/30 border-purple-300 dark:border-purple-600' : ''}
                `}
                onClick={() => handleDayClick(day)}
                onMouseEnter={() => setHoveredDay(day)}
                onMouseLeave={() => setHoveredDay(null)}
              >
                {/* Day number */}
                <div className={`
                  flex items-center justify-center w-7 h-7 rounded-full text-sm font-medium mb-2
                  ${day.isToday ? 'bg-blue-600 dark:bg-blue-500 text-white' : ''}
                  ${day.isSelected && !day.isToday ? 'bg-blue-600 dark:bg-blue-500 text-white' : ''}
                  ${day.isHoliday && !day.isToday && !day.isSelected ? 'bg-purple-600 dark:bg-purple-500 text-white' : ''}
                  ${!day.isCurrentMonth && viewMode === 'month' ? 'text-gray-400 dark:text-gray-500' : 'text-gray-700 dark:text-gray-300'}
                `}>
                  {day.dayOfMonth}
                </div>
                
                {/* Holiday indicator */}
                {day.isHoliday && day.holiday && (
                  <div className="absolute top-1 right-1">
                    <Star className="h-4 w-4 text-purple-600 dark:text-purple-400 fill-current" />
                  </div>
                )}
                
                {/* Holiday name */}
                {day.isHoliday && day.holiday && (
                  <div className="text-xs text-purple-700 dark:text-purple-300 font-medium mb-1 truncate" title={day.holiday.name}>
                    {day.holiday.name}
                  </div>
                )}
                
                {/* Week view: Show day of week label */}
                {viewMode === 'week' && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 text-center mb-2">
                    {day.date.toLocaleDateString('en-US', { weekday: 'short' })}
                  </div>
                )}
                
                {/* Attendance indicators */}
                {day.stats.total > 0 && (
                  <div className="space-y-1">
                    {/* Quick stats */}
                    <div className="grid grid-cols-2 gap-1 text-xs">
                      <div className="flex items-center space-x-1">
                        <CheckCircle className="h-3 w-3 text-green-500 dark:text-green-400" />
                        <span className="text-green-700 dark:text-green-400 font-medium">{day.stats.present}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <XCircle className="h-3 w-3 text-red-500 dark:text-red-400" />
                        <span className="text-red-700 dark:text-red-400 font-medium">{day.stats.absent}</span>
                      </div>
                      {day.stats.late > 0 && (
                        <div className="flex items-center space-x-1">
                          <AlertTriangle className="h-3 w-3 text-yellow-500 dark:text-yellow-400" />
                          <span className="text-yellow-700 dark:text-yellow-400">{day.stats.late}</span>
                        </div>
                      )}
                      {day.stats.wfh > 0 && (
                        <div className="flex items-center space-x-1">
                          <Home className="h-3 w-3 text-blue-500 dark:text-blue-400" />
                          <span className="text-blue-700 dark:text-blue-400">{day.stats.wfh}</span>
                        </div>
                      )}
                      {day.stats.leave > 0 && (
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3 text-purple-500 dark:text-purple-400" />
                          <span className="text-purple-700 dark:text-purple-400">{day.stats.leave}</span>
                        </div>
                      )}
                    </div>

                    {/* Progress bar */}
                    {day.stats.total > 0 && (
                      <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5 mt-2">
                        <div 
                          className="bg-green-500 dark:bg-green-400 h-1.5 rounded-full transition-all duration-300"
                          style={{ 
                            width: `${(day.stats.present / day.stats.total) * 100}%` 
                          }}
                        ></div>
                      </div>
                    )}
                    
                    {/* Week view: Show more detailed stats */}
                    {viewMode === 'week' && day.stats.total > 5 && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
                        Total: {day.stats.total} employees
                      </div>
                    )}
                  </div>
                )}
                
                {/* Show "No data" message for week view when no attendance */}
                {viewMode === 'week' && day.stats.total === 0 && (
                  <div className="text-xs text-gray-400 dark:text-gray-500 text-center mt-4">
                    No attendance data
                  </div>
                )}
                
                {/* Hover popover */}
                {hoveredDay === day && renderDayPopover(day)}
              </div>
            );
          })}
        </div>
      </div>
      
      {/* Legend */}
      <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
        <div className="flex items-center space-x-4 text-sm flex-wrap">
          <div className="flex items-center">
            <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mr-1" />
            <span className="text-gray-700 dark:text-gray-300">Present</span>
          </div>
          <div className="flex items-center">
            <XCircle className="h-4 w-4 text-red-500 dark:text-red-400 mr-1" />
            <span className="text-gray-700 dark:text-gray-300">Absent</span>
          </div>
          <div className="flex items-center">
            <AlertTriangle className="h-4 w-4 text-yellow-500 dark:text-yellow-400 mr-1" />
            <span className="text-gray-700 dark:text-gray-300">Late</span>
          </div>
          <div className="flex items-center">
            <Home className="h-4 w-4 text-blue-500 dark:text-blue-400 mr-1" />
            <span className="text-gray-700 dark:text-gray-300">Work from Home</span>
          </div>
          <div className="flex items-center">
            <Clock className="h-4 w-4 text-purple-500 dark:text-purple-400 mr-1" />
            <span className="text-gray-700 dark:text-gray-300">Leave</span>
          </div>
          <div className="flex items-center">
            <Star className="h-4 w-4 text-purple-600 dark:text-purple-400 mr-1" />
            <span className="text-gray-700 dark:text-gray-300">Holiday</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-600 rounded mr-1"></div>
            <span className="text-gray-700 dark:text-gray-300">Weekend</span>
          </div>
        </div>
          
        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
          {selectedDate && (
            <div className="flex items-center bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 px-3 py-1 rounded-lg">
              <BarChart3 className="h-4 w-4 mr-1" />
              <span>Table filtered: {new Date(selectedDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</span>
            </div>
          )}
          <div className="flex items-center">
            <Info className="h-4 w-4 mr-1" />
            <span>Click on a date to filter attendance table</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AttendanceCalendar; 