import { Request, Response } from 'express';
import { Asset } from '../../entities/Asset';
import { PrinterMaintenance } from '../../entities/PrinterMaintenance';
import { uploadToStorage, getFileUrl } from '../utils/fileStorage';
import { generateFinancePDF } from '../utils/pdfGenerator';
import { Vendor } from '../../entities/Vendor';
import { AppDataSource } from '../../config/database';
import path from 'path';
import fs from 'fs';
import { generateMaintenanceExport } from '../utils/exportGenerator';

export class PrinterMaintenanceController {
  // Get all printer maintenance records with pagination and filtering
  async getMaintenanceRecords(req: Request, res: Response) {
    try {
      const { page = 1, perPage = 10, search, status, dateRange, vendor_name } = req.query;
      const skip = (Number(page) - 1) * Number(perPage);
      
      // Create query builder for complex filtering
      const queryBuilder = AppDataSource.getRepository(PrinterMaintenance)
        .createQueryBuilder('maintenance')
        .leftJoinAndSelect('maintenance.asset', 'asset')
        .leftJoinAndSelect('maintenance.vendor', 'vendor')
        .orderBy('maintenance.createdAt', 'DESC');
      
      // Apply search filter if provided
      if (search) {
        const searchTerm = `%${search}%`;
        queryBuilder.andWhere(
          '(asset.model LIKE :search OR vendor.vendorName LIKE :search OR maintenance.invoiceNumber LIKE :search)',
          { search: searchTerm }
        );
      }
      
      // Apply vendor name filter if provided
      if (vendor_name) {
        queryBuilder.andWhere('vendor.vendorName LIKE :vendorName', { 
          vendorName: `%${vendor_name}%` 
        });
      }
      
      // Apply status filter if provided
      if (status && status !== 'all') {
        switch (status) {
          case 'no_invoice':
            queryBuilder.andWhere('maintenance.invoiceFilePath IS NULL');
            break;
          case 'pending':
            queryBuilder.andWhere('maintenance.invoiceFilePath IS NOT NULL AND maintenance.approvalStatus = :status', { status: 'pending' });
            break;
          case 'approved':
            queryBuilder.andWhere('maintenance.approvalStatus = :status', { status: 'approved' });
            break;
          case 'rejected':
            queryBuilder.andWhere('maintenance.approvalStatus = :status', { status: 'rejected' });
            break;
        }
      }
      
      // Apply date range filter if provided
      if (dateRange && dateRange !== 'all') {
        const now = new Date();
        let startDate: Date;
        
        switch (dateRange) {
          case 'today':
            startDate = new Date(now.setHours(0, 0, 0, 0));
            queryBuilder.andWhere('maintenance.serviceDate >= :startDate', { startDate });
            break;
          case 'this_week':
            startDate = new Date(now);
            startDate.setDate(now.getDate() - now.getDay());
            startDate.setHours(0, 0, 0, 0);
            queryBuilder.andWhere('maintenance.serviceDate >= :startDate', { startDate });
            break;
          case 'this_month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            queryBuilder.andWhere('maintenance.serviceDate >= :startDate', { startDate });
            break;
          case 'this_year':
            startDate = new Date(now.getFullYear(), 0, 1);
            queryBuilder.andWhere('maintenance.serviceDate >= :startDate', { startDate });
            break;
        }
      }
      
      // Execute query with pagination first to check if there are results
      const records = await queryBuilder
        .skip(skip)
        .take(Number(perPage))
        .getMany()
        .catch(error => {
          console.error('Database query error:', error);
          throw new Error(`Database query failed: ${error.message}`);
        });
      
      if (!records || records.length === 0) {
        // Return empty results instead of throwing an error
        return res.status(200).json({
          records: [],
          total: 0,
          page: Number(page),
          perPage: Number(perPage),
          totalPages: 0
        });
      }
      
      // Get total count for pagination
      const total = await queryBuilder.getCount();
      
      // Format records for response with null checks to prevent undefined errors
      const formattedRecords = records.map(record => {
        // Extract technician name from notes if available
        let technicianName = '';
        let issueDescription = '';
        let department = '';
        let remarks = '';
        
        if (record.notes) {
          // Extract technician name
          const techMatch = record.notes.match(/Technician: ([^\n]+)/);
          if (techMatch && techMatch[1]) {
            technicianName = techMatch[1];
          }
          
          // Extract issue description
          const issueMatch = record.notes.match(/Issue: ([^\n]+)(?:\n|$)/s);
          if (issueMatch && issueMatch[1]) {
            issueDescription = issueMatch[1];
          } else {
            // Try to use serviceDescription if there's no issue in notes
            issueDescription = record.serviceDescription || '';
          }
          
          // Extract department
          const deptMatch = record.notes.match(/Department: ([^\n]+)/);
          if (deptMatch && deptMatch[1]) {
            department = deptMatch[1];
          }
          
          // Extract remarks - everything that doesn't match the other patterns
          const notesWithoutPatterns = record.notes
            .replace(/Technician: [^\n]+(\n|$)/g, '')
            .replace(/Issue: [^\n]+(\n|$)/g, '')
            .replace(/Department: [^\n]+(\n|$)/g, '')
            .replace(/Assignee: [^\n]+(\n|$)/g, '')
            .replace(/Original filename: [^\n]+(\n|$)/g, '')
            .replace(/Approved by: [^\n]+(\n|$)/g, '')
            .trim();
          
          if (notesWithoutPatterns) {
            remarks = notesWithoutPatterns;
          }
        }
        
        return {
        id: record.id,
        asset_id: record.asset?.id || null,
        asset_name: record.asset?.model || 'Unknown Asset',
        asset_model: record.asset?.model || '',
        service_date: record.serviceDate,
        vendor_id: record.vendor?.id || null,
        vendor_name: record.vendor?.vendorName || 'Unknown Vendor',
        action_taken: record.serviceDescription,
          service_type: record.serviceDescription,
        parts_replaced: record.partsReplaced || [],
          technician_name: technicianName,
          issue_description: issueDescription,
        cost: record.invoiceAmount,
        invoice_number: record.invoiceNumber,
        invoice_file: record.invoiceFilePath ? getFileUrl(record.invoiceFilePath) : null,
        invoice_approval_status: record.approvalStatus,
          department: department || '',
        location: record.notes || '',
        assignee: record.assignee,
          remarks: remarks || '',
          status: record.serviceStatus || 'Completed',
        sent_to_finance: record.submittedToFinance,
        approved_by_it_director: record.approvedBy?.id || null,
        created_at: record.createdAt,
        updated_at: record.updatedAt
        };
      });
      
      return res.status(200).json({
        records: formattedRecords,
        total,
        page: Number(page),
        perPage: Number(perPage),
        totalPages: Math.ceil(total / Number(perPage))
      });
    } catch (error: any) {
      console.error('Error getting maintenance records:', error);
      return res.status(500).json({ 
        message: 'Failed to fetch maintenance records', 
        error: error.message 
      });
    }
  }

  // Export maintenance records to various formats (XLSX, CSV, PDF)
  async exportMaintenance(req: Request, res: Response) {
    try {
      console.log('Export request received:', req.body);
      
      // Get parameters from request body
      const { format = 'xlsx', scope = 'all', filters = {} } = req.body;
      
      // Validate format
      if (!['xlsx', 'csv', 'pdf'].includes(format)) {
        return res.status(400).json({ message: 'Invalid export format. Supported formats: xlsx, csv, pdf' });
      }
      
      // Build query
      const queryBuilder = AppDataSource.getRepository(PrinterMaintenance)
        .createQueryBuilder('maintenance')
        .leftJoinAndSelect('maintenance.asset', 'asset')
        .leftJoinAndSelect('maintenance.vendor', 'vendor')
        .orderBy('maintenance.serviceDate', 'DESC');
      
      // Apply filters if scope is 'filtered'
      if (scope === 'filtered' && Object.keys(filters).length > 0) {
        if (filters.start_date) {
          queryBuilder.andWhere('maintenance.serviceDate >= :startDate', {
            startDate: new Date(filters.start_date)
          });
        }
        
        if (filters.end_date) {
          queryBuilder.andWhere('maintenance.serviceDate <= :endDate', {
            endDate: new Date(filters.end_date)
          });
        }
        
        if (filters.printer_id) {
          queryBuilder.andWhere('maintenance.asset.id = :printerId', {
            printerId: filters.printer_id
          });
        }
        
        if (filters.vendor_name) {
          queryBuilder.andWhere('vendor.vendorName LIKE :vendorName', {
            vendorName: `%${filters.vendor_name}%`
          });
        }
        
        if (filters.department) {
          queryBuilder.andWhere('asset.department = :department', {
            department: filters.department
          });
        }
        
        if (filters.search) {
          const searchTerm = `%${filters.search}%`;
          queryBuilder.andWhere(
            '(asset.model LIKE :search OR vendor.vendorName LIKE :search OR maintenance.invoiceNumber LIKE :search)',
            { search: searchTerm }
          );
        }
      }
      
      // Get records
      const records = await queryBuilder.getMany();
      
      // Format records for export
      const formattedRecords = records.map(record => {
        // Extract information from notes field
        let technicianName = '';
        let issueDescription = '';
        
        if (record.notes) {
          const techMatch = record.notes.match(/Technician: ([^\n]+)/);
          if (techMatch && techMatch[1]) {
            technicianName = techMatch[1].trim();
          }
          
          const issueMatch = record.notes.match(/Issue: ([^\n]+)(?:\n|$)/s);
          if (issueMatch && issueMatch[1]) {
            issueDescription = issueMatch[1].trim();
          }
        }
        
        return {
          id: record.id,
          asset_id: record.asset?.id,
          printer_name: record.asset?.model,
          model: record.asset?.model,
          manufacturer: record.asset?.manufacturer || 'HP',
          service_date: record.serviceDate,
          visit_date: record.serviceDate,
          vendor_name: record.vendor?.vendorName,
          service_type: record.serviceDescription,
          action_taken: record.serviceDescription,
          parts_replaced: record.partsReplaced || [],
          technician_name: technicianName,
          issue_description: issueDescription,
          cost: record.invoiceAmount,
          invoice_amount: record.invoiceAmount,
          invoice_number: record.invoiceNumber,
          department: record.asset?.department || 'IT',
          remarks: record.notes,
          status: record.serviceStatus || 'Completed',
          created_at: record.createdAt
        };
      });
      
      // No records found case
      if (formattedRecords.length === 0) {
        return res.status(404).json({ message: 'No records found to export' });
      }
      
      // Generate export using our utility
      const { data, filename, contentType } = await generateMaintenanceExport(formattedRecords, format);
      
      // Set response headers
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', data.length);
      
      // Send the response
      return res.send(data);
    } catch (error: any) {
      console.error('Error exporting maintenance records:', error);
      return res.status(500).json({ 
        message: 'Failed to export maintenance records', 
        error: error.message 
      });
    }
  }

  // Record a new printer maintenance record with robust error handling
  async recordMaintenance(req: Request, res: Response) {
    try {
      const maintenanceRepo = AppDataSource.getRepository(PrinterMaintenance);
      const assetRepo = AppDataSource.getRepository(Asset);
      const vendorRepo = AppDataSource.getRepository(Vendor);
      
      // Check if this is an update (if ID is provided in body)
      const providedId = req.body.id || req.body.record_id;
      if (providedId) {
        console.log(`ID detected in request body (${providedId}). Treating as UPDATE operation.`);
        
        // Check if the record exists
        const existingRecord = await maintenanceRepo.findOne({
          where: { id: String(providedId) }
        });
        
        if (existingRecord) {
          console.log('Existing record found. Redirecting to updateMaintenanceRecord handler.');
          req.params.id = String(providedId);
          return this.updateMaintenanceRecord(req, res);
        }
        
        console.log('WARNING: ID provided but record not found. Continuing as new record creation.');
      }
      
      // Continue with regular record creation
      console.log('Processing as NEW record creation');
      
      const { 
        asset_id, 
        printer_id,
        service_date,
        visit_date,
        assignee_name,
        department,
        vendor_id,
        vendor_name,
        vendor,
        technician_name,
        service_type,
        issue_description,
        parts_replaced,
        invoice_number,
        invoice_amount,
        remarks
      } = req.body;

      // Log critical fields for debugging
      console.log('CRITICAL FIELDS CHECK:');
      console.log('- technician_name:', technician_name);
      console.log('- issue_description:', issue_description);
      console.log('- service_type:', service_type);

      // Use fallbacks for different field names
      const effectiveAssetId = asset_id || printer_id;
      const effectiveServiceDate = service_date || visit_date;
      const effectiveVendorName = vendor || vendor_name;
      
      // Log form data details
      console.log('FORM DATA DEBUG:');
      console.log('- Asset ID:', effectiveAssetId);
      console.log('- Service Date:', effectiveServiceDate);
      console.log('- Vendor:', effectiveVendorName);
      console.log('- Vendor ID:', vendor_id);
      
      // Verify asset exists
      const asset = await assetRepo.findOne({ where: { id: Number(effectiveAssetId) } });
      if (!asset) {
        return res.status(404).json({ 
          message: 'Asset not found', 
          id: effectiveAssetId,
          attempted_lookup: { 
            asset_id, 
            printer_id,
            effective_id: effectiveAssetId
          } 
        });
      }

      // Find vendor - either directly by ID or by name
      let vendorEntity;
      
      // FIRST try to find vendor by ID (prioritize this)
      if (vendor_id) {
        console.log('Looking up vendor by ID:', vendor_id);
        vendorEntity = await vendorRepo.findOne({ where: { id: vendor_id } });
        if (vendorEntity) {
          console.log('Found vendor by ID:', vendorEntity.vendorName);
        }
      }
      
      // If no vendor found by ID and vendor_name is provided, try by name
      if (!vendorEntity && effectiveVendorName) {
        console.log('Looking up vendor by name:', effectiveVendorName);
        vendorEntity = await vendorRepo.findOne({ where: { vendorName: effectiveVendorName } });
        
        if (!vendorEntity) {
          console.log('Exact name match failed, trying LIKE query');
          const vendors = await vendorRepo
            .createQueryBuilder('vendor')
            .where('vendor.vendorName = :name', { name: effectiveVendorName })
            .orWhere('vendor.vendorName LIKE :nameLike', { nameLike: `%${effectiveVendorName}%` })
            .getMany();
          
          vendorEntity = vendors[0]; // Take first match if any
          if (vendorEntity) {
            console.log('Found vendor by LIKE query:', vendorEntity.vendorName);
          }
        } else {
          console.log('Found vendor by exact name match:', vendorEntity.vendorName);
        }
      }
      
      if (!vendorEntity) {
        return res.status(404).json({ 
          message: 'Vendor not found', 
          searchId: vendor_id, 
          searchName: effectiveVendorName 
        });
      }

      // Create new maintenance record - using the correct property names from the entity
      const maintenance = new PrinterMaintenance();
      maintenance.asset = asset;
      maintenance.vendor = vendorEntity;
      maintenance.serviceDate = new Date(effectiveServiceDate);
      maintenance.serviceDescription = service_type ? 
        service_type : (issue_description || 'Regular Maintenance');

      // Initialize notes with department information
      let notesContent = department ? `Department: ${department}` : asset.department ? `Department: ${asset.department}` : '';
      
      // Add technician name to notes for later retrieval
      if (technician_name) {
        notesContent += (notesContent ? '\n' : '') + `Technician: ${technician_name}`;
      }
      
      // Add issue description to notes if available
      if (issue_description) {
        notesContent += (notesContent ? '\n' : '') + `Issue: ${issue_description}`;
      }
      
      // Add remarks if provided
      if (remarks) {
        notesContent += (notesContent ? '\n' : '') + remarks;
      }
      
      // Set the combined notes
      maintenance.notes = notesContent;
      
      // Set other real fields on the entity
      maintenance.invoiceAmount = Number(invoice_amount) || 0;
      maintenance.invoiceNumber = invoice_number || '';
      maintenance.serviceStatus = 'Completed'; // Set status correctly instead of using it for technician name
      
      // Check for file upload - properly handle disk storage files
      if (req.file) {
        console.log('File received:', {
          fieldname: req.file.fieldname,
          originalname: req.file.originalname,
          path: req.file.path,
          size: req.file.size
        });
        
        // Save file path to database
        maintenance.invoiceFilePath = req.file.path;
      } else {
        console.log('No file uploaded with the request');
      }
      
      // Set assigneeId - this is required by the database and doesn't have a default
      maintenance.assigneeId = req.user?.id || '00000000-0000-0000-0000-000000000000'; // Use logged in user or a default
      
      // Parse parts_replaced
      let parsedParts = [];
      
      // Handle parts_replaced which can be a JSON string or direct array
      if (typeof parts_replaced === 'string') {
        try {
          parsedParts = JSON.parse(parts_replaced);
        } catch (e) {
          // Only create a part if the string is not empty
          if (parts_replaced && parts_replaced.trim() !== '') {
            parsedParts = [{
              partName: parts_replaced,
              quantity: 1
            }];
          } else {
            parsedParts = [];
          }
        }
      } else if (parts_replaced && typeof parts_replaced === 'object') {
        // If this is an object with boolean checkboxes (from the form)
        if (!Array.isArray(parts_replaced)) {
          // Convert checkbox object to array of part names
          parsedParts = Object.entries(parts_replaced)
            .filter(([_, value]) => value === true)
            .map(([key]) => ({
              partName: key.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase()), // Format: "fuser_unit" -> "Fuser Unit"
              quantity: 1
            }));
        } else {
          parsedParts = parts_replaced;
        }
      }
      
      // Only set the "Unknown part" if parsedParts is completely empty and we need a value
      if (Array.isArray(parsedParts) && parsedParts.length > 0) {
        maintenance.partsReplaced = parsedParts;
      } else {
        maintenance.partsReplaced = [];
      }
      
      // Add other information
      maintenance.approvalStatus = 'Pending';
      maintenance.submittedToFinance = false;
      
      // Assignee info - needs to be stored in notes since entity doesn't have a field for it
      if (assignee_name) {
        maintenance.notes += (maintenance.notes ? '\n' : '') + `Assignee: ${assignee_name}`;
      }

      // Log what we're saving
      console.log('Saving maintenance record:', {
        asset_id: maintenance.asset.id,
        vendor_id: maintenance.vendor.id,
        serviceDate: maintenance.serviceDate,
        serviceDescription: maintenance.serviceDescription,
        partsReplaced: maintenance.partsReplaced,
        invoiceFilePath: maintenance.invoiceFilePath
      });
      
      // Save the record
      const savedRecord = await maintenanceRepo.save(maintenance);
      console.log('Saved maintenance record:', savedRecord);

      // Update the asset notes
      if (asset.notes) {
        asset.notes += `\nMaintenance performed on ${new Date(effectiveServiceDate).toISOString().split('T')[0]}`;
      } else {
        asset.notes = `Maintenance performed on ${new Date(effectiveServiceDate).toISOString().split('T')[0]}`;
      }
      await assetRepo.save(asset);

      return res.status(201).json({
        message: 'Maintenance record created successfully',
        record: {
          id: savedRecord.id,
          asset_id: savedRecord.asset.id,
          asset_name: savedRecord.asset.model || 'Unknown',
          serviceDate: savedRecord.serviceDate,
          vendor_name: savedRecord.vendor.vendorName,
          serviceDescription: savedRecord.serviceDescription,
          createdAt: savedRecord.createdAt
        }
      });
    } catch (error: any) {
      console.error('Error creating maintenance record:', error);
      return res.status(500).json({
        message: 'Failed to create maintenance record',
        error: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
    }
  }

  // Upload an invoice for a maintenance record
  async uploadInvoice(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ message: 'Maintenance record ID is required' });
      }
      
      if (!req.file) {
        return res.status(400).json({ message: 'Invoice file is required' });
      }
      
      // Get the maintenance repository
      const maintenanceRepo = AppDataSource.getRepository(PrinterMaintenance);
      
      // Find the maintenance record - ensure id is string
      const maintenance = await maintenanceRepo.findOne({ where: { id: id.toString() } });
      if (!maintenance) {
        return res.status(404).json({ message: 'Maintenance record not found' });
      }
      
      // Upload file to storage
      const fileBuffer = req.file.buffer;
      const fileName = req.file.originalname;
      const folderPath = 'invoices';
      
      const filePath = await uploadToStorage(fileBuffer, folderPath, fileName);
      
      // Update maintenance record with invoice file
      maintenance.invoiceFilePath = filePath;
      maintenance.approvalStatus = 'Pending';
      
      // Save the updated record - mark as update to prevent new ID generation
      maintenance.markAsUpdate();
      await maintenanceRepo.save(maintenance);
      
      return res.status(200).json({
        message: 'Invoice uploaded successfully',
        record: {
          id: maintenance.id,
          invoice_file: getFileUrl(filePath),
          invoice_approval_status: maintenance.approvalStatus
        }
      });
    } catch (error: any) {
      console.error('Error uploading invoice:', error);
      return res.status(500).json({
        message: 'Failed to upload invoice',
        error: error.message
      });
    }
  }

  // Approve a maintenance invoice
  async approveInvoice(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { status, approved_by } = req.body;
      
      if (!id) {
        return res.status(400).json({ message: 'Maintenance record ID is required' });
      }
      
      if (!status || !['approved', 'rejected'].includes(status)) {
        return res.status(400).json({ message: 'Valid status (approved/rejected) is required' });
      }
      
      if (!approved_by) {
        return res.status(400).json({ message: 'Approver name is required' });
      }
      
      // Get the maintenance repository
      const maintenanceRepo = AppDataSource.getRepository(PrinterMaintenance);
      
      // Find the maintenance record - ensure id is string
      const maintenance = await maintenanceRepo.findOne({ where: { id: id.toString() } });
      if (!maintenance) {
        return res.status(404).json({ message: 'Maintenance record not found' });
      }
      
      // Ensure there's an invoice to approve
      if (!maintenance.invoiceFilePath) {
        return res.status(400).json({ message: 'This record has no invoice to approve' });
      }
      
      // Update maintenance record approval status
      maintenance.approvalStatus = status.charAt(0).toUpperCase() + status.slice(1);
      // We need to handle approver differently since our entity uses a relation now
      // For now, we'll just store the name in the notes field
      maintenance.notes = `${maintenance.notes || ''}\nApproved by: ${approved_by} on ${new Date().toISOString()}`;
      
      // Save the updated record - mark as update to prevent new ID generation
      maintenance.markAsUpdate();
      await maintenanceRepo.save(maintenance);
      
      return res.status(200).json({
        message: `Invoice ${status} successfully`,
        record: {
          id: maintenance.id,
          invoice_approval_status: maintenance.approvalStatus,
          approved_by: approved_by
        }
      });
    } catch (error: any) {
      console.error('Error approving invoice:', error);
      return res.status(500).json({
        message: 'Failed to approve invoice',
        error: error.message
      });
    }
  }

  // Get pending approval maintenance records
  async getPendingApprovals(req: Request, res: Response) {
    try {
      // Get the maintenance repository
      const maintenanceRepo = AppDataSource.getRepository(PrinterMaintenance);
      
      // Find records with pending invoice approval
      const queryBuilder = maintenanceRepo
        .createQueryBuilder('maintenance')
        .leftJoinAndSelect('maintenance.asset', 'asset')
        .leftJoinAndSelect('maintenance.vendor', 'vendor')
        .where('maintenance.invoiceFilePath IS NOT NULL')
        .andWhere('maintenance.approvalStatus = :status', { status: 'Pending' })
        .orderBy('maintenance.createdAt', 'DESC');
      
      const records = await queryBuilder.getMany();
      
      if (!records || records.length === 0) {
        return res.status(200).json({
          records: [],
          total: 0
        });
      }
      
      // Format records for response
      const formattedRecords = records.map(record => ({
        id: record.id,
        asset_id: record.asset?.id || null,
        asset_name: record.asset?.model || 'Unknown Asset',
        asset_model: record.asset?.model || '',
        service_date: record.serviceDate,
        vendor_id: record.vendor?.id || null,
        vendor_name: record.vendor?.vendorName || 'Unknown Vendor',
        action_taken: record.serviceDescription,
        cost: record.invoiceAmount,
        invoice_number: record.invoiceNumber,
        invoice_file: getFileUrl(record.invoiceFilePath),
        department: record.notes || '',
        assignee: record.assignee,
        created_at: record.createdAt
      }));
      
      return res.status(200).json({
        records: formattedRecords,
        total: records.length
      });
    } catch (error: any) {
      console.error('Error getting pending approvals:', error);
      return res.status(500).json({
        message: 'Failed to fetch pending approvals',
        error: error.message
      });
    }
  }

  // Get maintenance history for a specific asset
  async getMaintenanceHistory(req: Request, res: Response) {
    try {
      const { assetId } = req.params;
      
      if (!assetId) {
        return res.status(400).json({ message: 'Asset ID is required' });
      }
      
      // Get repositories
      const maintenanceRepo = AppDataSource.getRepository(PrinterMaintenance);
      const assetRepo = AppDataSource.getRepository(Asset);
      
      // Verify asset exists
      const asset = await assetRepo.findOne({ where: { id: Number(assetId) } });
      if (!asset) {
        return res.status(404).json({ message: 'Asset not found' });
      }
      
      // Find all maintenance records for this asset
      const queryBuilder = maintenanceRepo
        .createQueryBuilder('maintenance')
        .leftJoinAndSelect('maintenance.vendor', 'vendor')
        .where('maintenance.asset = :assetId', { assetId: Number(assetId) })
        .orderBy('maintenance.serviceDate', 'DESC');
      
      const records = await queryBuilder.getMany();
      
      // Format records for response
      const formattedRecords = records.map(record => {
        // Extract technician name from notes if available
        let technicianName = '';
        let issueDescription = '';
        let department = '';
        let remarks = '';
        
        if (record.notes) {
          // Extract technician name
          const techMatch = record.notes.match(/Technician: ([^\n]+)/);
          if (techMatch && techMatch[1]) {
            technicianName = techMatch[1];
          }
          
          // Extract issue description
          const issueMatch = record.notes.match(/Issue: ([^\n]+)(?:\n|$)/s);
          if (issueMatch && issueMatch[1]) {
            issueDescription = issueMatch[1];
          } else {
            // Try to use serviceDescription if there's no issue in notes
            issueDescription = record.serviceDescription || '';
          }
          
          // Extract department
          const deptMatch = record.notes.match(/Department: ([^\n]+)/);
          if (deptMatch && deptMatch[1]) {
            department = deptMatch[1];
          }
          
          // Extract remarks - everything that doesn't match the other patterns
          const notesWithoutPatterns = record.notes
            .replace(/Technician: [^\n]+(\n|$)/g, '')
            .replace(/Issue: [^\n]+(\n|$)/g, '')
            .replace(/Department: [^\n]+(\n|$)/g, '')
            .replace(/Assignee: [^\n]+(\n|$)/g, '')
            .replace(/Original filename: [^\n]+(\n|$)/g, '')
            .replace(/Approved by: [^\n]+(\n|$)/g, '')
            .trim();
          
          if (notesWithoutPatterns) {
            remarks = notesWithoutPatterns;
          }
        }
        
        return {
        id: record.id,
        service_date: record.serviceDate,
        vendor_name: record.vendor?.vendorName || 'Unknown Vendor',
        action_taken: record.serviceDescription,
        parts_replaced: record.partsReplaced || [],
          technician_name: technicianName,
          issue_description: issueDescription,
          department: department || '',
        cost: record.invoiceAmount,
        invoice_number: record.invoiceNumber,
        invoice_file: record.invoiceFilePath ? getFileUrl(record.invoiceFilePath) : null,
        invoice_approval_status: record.approvalStatus,
          remarks: remarks || '',
        created_at: record.createdAt
        };
      });
      
      return res.status(200).json({
        asset: {
          id: asset.id,
          name: asset.model,
          model: asset.model,
          type: asset.assetType
        },
        records: formattedRecords,
        total: records.length
      });
    } catch (error: any) {
      console.error('Error getting maintenance history:', error);
      return res.status(500).json({
        message: 'Failed to fetch maintenance history',
        error: error.message
      });
    }
  }

  // Generate a finance report
  async generateFinanceReport(req: Request, res: Response) {
    try {
      // Implementation for generating a finance report
      return res.status(200).json({ message: 'Finance report generated successfully' });
    } catch (error) {
      console.error('Error generating finance report:', error);
      return res.status(500).json({ message: 'Failed to generate finance report' });
    }
  }

  // Mark records as submitted to finance
  async markAsSubmittedToFinance(req: Request, res: Response) {
    try {
      // Implementation for marking records as submitted to finance
      return res.status(200).json({ message: 'Records marked as submitted to finance' });
    } catch (error) {
      console.error('Error marking records as submitted:', error);
      return res.status(500).json({ message: 'Failed to mark records as submitted' });
    }
  }

  // Get a single maintenance record by ID
  async getMaintenanceRecord(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ message: 'Maintenance record ID is required' });
      }
      
      const maintenanceRepo = AppDataSource.getRepository(PrinterMaintenance);
      
      const record = await maintenanceRepo.findOne({
        where: { id },
        relations: ['asset', 'vendor']
      });
      
      if (!record) {
        return res.status(404).json({ message: 'Maintenance record not found' });
      }
      
      return res.status(200).json(record);
    } catch (error: any) {
      console.error('Error fetching maintenance record:', error);
      return res.status(500).json({ 
        message: 'Error fetching maintenance record', 
        error: error.message 
      });
    }
  }

  // Delete a maintenance record
  async deleteMaintenanceRecord(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ message: 'Maintenance record ID is required' });
      }
      
      const maintenanceRepo = AppDataSource.getRepository(PrinterMaintenance);
      
      const record = await maintenanceRepo.findOne({
        where: { id }
      });
      
      if (!record) {
        return res.status(404).json({ message: 'Maintenance record not found' });
      }
      
      // Delete the invoice file if it exists
      if (record.invoiceFilePath) {
        const filePath = path.join(process.cwd(), record.invoiceFilePath);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      }
      
      await maintenanceRepo.remove(record);
      
      return res.status(200).json({ 
        message: 'Maintenance record deleted successfully' 
      });
    } catch (error: any) {
      console.error('Error deleting maintenance record:', error);
      return res.status(500).json({ 
        message: 'Error deleting maintenance record', 
        error: error.message 
      });
    }
  }

  // Update a maintenance record
  async updateMaintenanceRecord(req: Request, res: Response) {
    try {
      const id = req.params.id;
      console.log('📝 UPDATE REQUEST - ID:', id);
      console.log('📝 UPDATE REQUEST Headers:', JSON.stringify({
        'content-type': req.headers['content-type'],
        'content-length': req.headers['content-length'],
        'authorization': req.headers['authorization'] ? 'Bearer [REDACTED]' : 'missing'
      }, null, 2));
      console.log('📝 UPDATE REQUEST Params:', JSON.stringify(req.params, null, 2));
      console.log('📝 UPDATE REQUEST Query:', JSON.stringify(req.query, null, 2));
      console.log('📝 Complete req.body:', JSON.stringify(req.body, null, 2));
      
      if (!id) {
        return res.status(400).json({ error: 'ID is required' });
      }
      
      // Get the maintenance repository
      const maintenanceRepo = AppDataSource.getRepository(PrinterMaintenance);
      
      // Find the record first to check if it exists
      const existingRecord = await maintenanceRepo.findOne({
        where: { id: String(id) }
      });
      
      if (!existingRecord) {
        console.error(`❌ Record with ID ${id} not found`);
        return res.status(404).json({ error: 'Maintenance record not found', id });
      }
      
      console.log('📝 FOUND EXISTING RECORD:', existingRecord.id);
      console.log('BEFORE UPDATE - Record details:', {
        id: existingRecord.id,
        assetId: existingRecord.assetId
      });

      // Mark this as an update to avoid BeforeInsert hook generating a new ID
      existingRecord.markAsUpdate();
      
      // Extract data from request
      const { 
        printer_id, service_type, service_date, 
        technician_name, issue_description, invoice_number, 
        invoice_amount, parts_replaced, remarks,
        department, assignee_name, vendor_name
      } = req.body;
      
      // Prepare the notes
      let notesContent = '';
      if (technician_name) notesContent += `Technician: ${technician_name}\n`;
      if (issue_description) notesContent += `Issue: ${issue_description}\n`;
      if (department) notesContent += `Department: ${department}\n`;
      if (assignee_name) notesContent += `Assignee: ${assignee_name}\n`;
      if (remarks) notesContent += remarks;
      
      // Parse parts_replaced if it's a string
      let parsedParts = [];
      if (parts_replaced) {
        try {
          if (typeof parts_replaced === 'string') {
            parsedParts = JSON.parse(parts_replaced);
          } else if (typeof parts_replaced === 'object') {
            // Convert checkbox object to array of part objects
            parsedParts = Object.entries(parts_replaced)
              .filter(([_, value]) => value === true)
              .map(([key]) => ({
                partName: key.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase()),
                quantity: 1
              }));
          }
        } catch (error) {
          console.error('Error parsing parts_replaced:', error);
          // If parsing fails, use an empty array
          parsedParts = [];
        }
      }
      
      // Update record properties
      if (printer_id) existingRecord.assetId = Number(printer_id);
      if (service_date) existingRecord.serviceDate = new Date(service_date);
      if (service_type) existingRecord.serviceDescription = service_type;
      if (invoice_amount) existingRecord.invoiceAmount = Number(invoice_amount);
      if (invoice_number) existingRecord.invoiceNumber = invoice_number;
      if (notesContent) existingRecord.notes = notesContent;
      if (parsedParts.length > 0) existingRecord.partsReplaced = parsedParts;
      
      // Handle vendor name explicitly
      console.log('📝 Vendor name in request:', vendor_name);
      if (vendor_name) {
        try {
          // First try to find the vendor by name
          const vendorRepo = AppDataSource.getRepository(Vendor);
          const vendor = await vendorRepo.findOne({
            where: [
              { vendorName: vendor_name },
              { companyName: vendor_name }
            ]
          });

          if (vendor) {
            console.log('📝 Found matching vendor:', vendor.id);
            existingRecord.vendorId = vendor.id;
          } else {
            console.log('📝 No matching vendor found, setting vendor name directly:', vendor_name);
            // When no vendor is found, store the name directly as a fallback
            existingRecord.vendorId = vendor_name;
          }
        } catch (error) {
          console.error('📝 Error finding vendor:', error);
          // Still ensure the vendor name is stored somehow
          existingRecord.vendorId = vendor_name;
        }
      }
      
      // Set updatedAt
      existingRecord.updatedAt = new Date();
      
      // Save the updated record
      console.log('📝 SAVING UPDATED RECORD');
      const savedRecord = await maintenanceRepo.save(existingRecord);
      
      console.log('✅ UPDATE SUCCESSFUL');
      console.log('AFTER UPDATE - Record details:', {
        id: savedRecord.id,
        assetId: savedRecord.assetId
      });
      
      return res.status(200).json({ 
        message: '*** UPDATE SUCCESSFUL - RECORD WAS EDITED NOT CREATED ***',
        result_type: 'UPDATE_ONLY',
        operation: 'UPDATE',
        operation_type: 'ENTITY_UPDATE',
        id: id,
        record: {
          ...savedRecord,
          technician_name,
          issue_description,
          department,
          assignee_name,
          remarks
        }
      });
    } catch (error: any) {
      console.error('❌ ERROR UPDATING RECORD:', error);
      return res.status(500).json({ 
        error: 'Failed to update maintenance record',
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
    }
  }
} 