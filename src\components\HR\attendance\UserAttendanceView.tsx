import React, { useState } from 'react';
import { 
  CheckCircle, 
  Clock, 
  Calendar,
  User,
  FileText,
  PlusCircle,
  AlertCircle,
  Edit,
  Check,
  Search,
  ArrowLeft,
  ArrowRight,
  CalendarDays,
  Filter
} from 'lucide-react';
import { 
  hrPrimaryButtonStyle, 
  hrSecondaryButtonStyle, 
  hrCardStyle,
  hrInputStyle,
  hrSectionTitleStyle,
  hrSubsectionTitleStyle,
  hrInfoAlertStyle,
  hrSuccessAlertStyle
} from '../../../styles/hrWorkflow';

// Sample data for the user's attendance records
const SAMPLE_ATTENDANCE = [
  {
    id: 1,
    date: '2023-06-15',
    checkInTime: '09:05',
    checkOutTime: '17:30',
    status: 'present',
    workHours: 8.42,
    isRegularized: false,
    location: 'Office - Main Building'
  },
  {
    id: 2,
    date: '2023-06-14',
    checkInTime: '09:02',
    checkOutTime: '17:15',
    status: 'present',
    workHours: 8.22,
    isRegularized: false,
    location: 'Office - Main Building'
  },
  {
    id: 3,
    date: '2023-06-13',
    checkInTime: '09:20',
    checkOutTime: '17:45',
    status: 'present',
    workHours: 8.42,
    isRegularized: true,
    location: 'Remote'
  },
  {
    id: 4,
    date: '2023-06-12',
    checkInTime: null,
    checkOutTime: null,
    status: 'absent',
    workHours: 0,
    isRegularized: false,
    location: null
  },
  {
    id: 5,
    date: '2023-06-09',
    checkInTime: '08:55',
    checkOutTime: '17:10',
    status: 'present',
    workHours: 8.25,
    isRegularized: false,
    location: 'Office - Main Building'
  }
];

// Sample regularization requests
const SAMPLE_REQUESTS = [
  {
    id: 101,
    date: '2023-06-12',
    type: 'Absence Regularization',
    reason: 'Doctor appointment',
    status: 'pending',
    submittedOn: '2023-06-13T10:30:00'
  }
];

const UserAttendanceView: React.FC = () => {
  const [currentView, setCurrentView] = useState<'summary' | 'details' | 'regularize'>('summary');
  const [attendanceRecords, setAttendanceRecords] = useState(SAMPLE_ATTENDANCE);
  const [regularizationRequests, setRegularizationRequests] = useState(SAMPLE_REQUESTS);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [filterMonth, setFilterMonth] = useState<string>(
    new Date().toISOString().split('T')[0].substring(0, 7)
  );
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [regularizationForm, setRegularizationForm] = useState({
    date: '',
    type: 'Missing Clock-in',
    reason: '',
    requestedTime: ''
  });

  // For the current month's summary
  const currentMonthSummary = {
    present: attendanceRecords.filter(record => record.status === 'present').length,
    absent: attendanceRecords.filter(record => record.status === 'absent').length,
    late: attendanceRecords.filter(record => 
      record.status === 'present' && 
      record.checkInTime && 
      record.checkInTime > '09:00'
    ).length,
    totalWorkHours: attendanceRecords.reduce((sum, record) => sum + record.workHours, 0)
  };

  // Filter attendance records by month
  const filteredRecords = attendanceRecords.filter(record => 
    record.date.startsWith(filterMonth)
  );

  // Handle date selection for regularization
  const handleSelectDate = (record: typeof SAMPLE_ATTENDANCE[0]) => {
    setSelectedDate(record.date);
    if (currentView === 'summary') {
      setCurrentView('details');
    }

    // If it's an absence or has missing punches, pre-fill regularization form
    if (record.status === 'absent' || !record.checkInTime || !record.checkOutTime) {
      setRegularizationForm({
        date: record.date,
        type: record.status === 'absent' ? 'Absence Regularization' : 
              !record.checkInTime ? 'Missing Clock-in' : 'Missing Clock-out',
        reason: '',
        requestedTime: !record.checkInTime ? '09:00' : '17:00'
      });
    }
  };

  // Handle regularization form submission
  const handleRegularizationSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!regularizationForm.reason.trim()) {
      alert('Please provide a reason for your regularization request');
      return;
    }
    
    // Create new request
    const newRequest = {
      id: Math.max(...regularizationRequests.map(r => r.id), 0) + 1,
      date: regularizationForm.date,
      type: regularizationForm.type,
      reason: regularizationForm.reason,
      status: 'pending',
      submittedOn: new Date().toISOString()
    };
    
    setRegularizationRequests([...regularizationRequests, newRequest]);
    setSuccessMessage('Your regularization request has been submitted successfully.');
    setCurrentView('summary');
    
    // Clear success message after 5 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 5000);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status badge based on attendance status
  const getStatusBadge = (status: string, isLate: boolean, isRegularized: boolean) => {
    if (status === 'present') {
      if (isLate) {
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="h-3 w-3 mr-1" />
            Late
          </span>
        );
      }
      if (isRegularized) {
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <Edit className="h-3 w-3 mr-1" />
            Regularized
          </span>
        );
      }
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <CheckCircle className="h-3 w-3 mr-1" />
          Present
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <AlertCircle className="h-3 w-3 mr-1" />
          Absent
        </span>
      );
    }
  };

  // Render summary view
  const renderSummaryView = () => (
    <>
      <div className="flex justify-between items-center mb-6">
        <h2 className={hrSectionTitleStyle}>
          <Calendar className="h-6 w-6 mr-2 text-blue-500" />
          My Attendance
        </h2>
        <div className="flex items-center space-x-3">
          <div className="relative">
            <input
              type="month"
              className={hrInputStyle}
              value={filterMonth}
              onChange={(e) => setFilterMonth(e.target.value)}
              max={new Date().toISOString().split('T')[0].substring(0, 7)}
            />
            <Filter className="h-4 w-4 text-gray-400 absolute right-2 top-2.5" />
          </div>
        </div>
      </div>

      {/* Success message */}
      {successMessage && (
        <div className={hrSuccessAlertStyle}>
          <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
          <p>{successMessage}</p>
        </div>
      )}

      {/* Monthly summary cards */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <div className="text-sm font-medium text-gray-500">Present Days</div>
            <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
              <CheckCircle className="h-4 w-4 text-green-600" />
            </div>
          </div>
          <div className="text-2xl font-bold text-gray-900">{currentMonthSummary.present}</div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <div className="text-sm font-medium text-gray-500">Absent Days</div>
            <div className="h-8 w-8 rounded-full bg-red-100 flex items-center justify-center">
              <AlertCircle className="h-4 w-4 text-red-600" />
            </div>
          </div>
          <div className="text-2xl font-bold text-gray-900">{currentMonthSummary.absent}</div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <div className="text-sm font-medium text-gray-500">Late Arrivals</div>
            <div className="h-8 w-8 rounded-full bg-yellow-100 flex items-center justify-center">
              <Clock className="h-4 w-4 text-yellow-600" />
            </div>
          </div>
          <div className="text-2xl font-bold text-gray-900">{currentMonthSummary.late}</div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <div className="text-sm font-medium text-gray-500">Total Hours</div>
            <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
              <Clock className="h-4 w-4 text-blue-600" />
            </div>
          </div>
          <div className="text-2xl font-bold text-gray-900">{currentMonthSummary.totalWorkHours.toFixed(1)}</div>
        </div>
      </div>

      {/* Pending regularization requests */}
      {regularizationRequests.filter(req => req.status === 'pending').length > 0 && (
        <div className={hrInfoAlertStyle + " mb-6"}>
          <div className="flex items-start">
            <Clock className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
            <div>
              <h3 className="font-medium text-blue-800">Pending Regularization Requests</h3>
              <p className="text-sm text-blue-700 mt-1">
                You have {regularizationRequests.filter(req => req.status === 'pending').length} pending regularization requests awaiting approval.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Attendance records table */}
      <div className="border rounded-md overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check In</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check Out</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hours</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredRecords.length === 0 ? (
              <tr>
                <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                  No attendance records found for this month
                </td>
              </tr>
            ) : (
              filteredRecords.map(record => {
                const isLate = record.status === 'present' && record.checkInTime && record.checkInTime > '09:00';
                
                // Check if there's a pending regularization for this date
                const hasPendingRegularization = regularizationRequests.some(
                  req => req.date === record.date && req.status === 'pending'
                );
                
                return (
                  <tr 
                    key={record.id}
                    className="hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleSelectDate(record)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatDate(record.date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.checkInTime || '—'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.checkOutTime || '—'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.workHours.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(record.status, isLate, record.isRegularized)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.location || '—'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {hasPendingRegularization ? (
                        <span className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                          Regularization Pending
                        </span>
                      ) : (
                        <button
                          className="text-blue-600 hover:text-blue-800 font-medium text-sm inline-flex items-center"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedDate(record.date);
                            setRegularizationForm({
                              ...regularizationForm,
                              date: record.date,
                              type: record.status === 'absent' ? 'Absence Regularization' : 
                                    !record.checkInTime ? 'Missing Clock-in' : 'Missing Clock-out',
                            });
                            setCurrentView('regularize');
                          }}
                        >
                          <PlusCircle className="h-3 w-3 mr-1" />
                          Regularize
                        </button>
                      )}
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </>
  );

  // Render details view for a specific date
  const renderDetailsView = () => {
    const selectedRecord = attendanceRecords.find(record => record.date === selectedDate);
    
    if (!selectedRecord) {
      return (
        <div className="text-center py-8">
          <p className="text-gray-500">No record found for the selected date.</p>
          <button
            className={hrSecondaryButtonStyle + " mt-4"}
            onClick={() => setCurrentView('summary')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Summary
          </button>
        </div>
      );
    }
    
    const isLate = selectedRecord.status === 'present' && 
      selectedRecord.checkInTime && selectedRecord.checkInTime > '09:00';
    
    return (
      <>
        <div className="flex items-center mb-6">
          <button
            className={hrSecondaryButtonStyle + " mr-4"}
            onClick={() => setCurrentView('summary')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </button>
          <h2 className={hrSectionTitleStyle}>
            <Calendar className="h-6 w-6 mr-2 text-blue-500" />
            Attendance Details
          </h2>
        </div>
        
        <div className={hrCardStyle}>
          <div className="flex justify-between items-start mb-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900">{formatDate(selectedRecord.date)}</h3>
              <p className="text-sm text-gray-500">
                {selectedRecord.location || 'No location recorded'}
              </p>
            </div>
            <div>
              {getStatusBadge(selectedRecord.status, isLate, selectedRecord.isRegularized)}
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-6 mb-6">
            <div className="border rounded-md p-4 bg-gray-50">
              <div className="text-sm font-medium text-gray-500 mb-1">Check In</div>
              <div className="text-xl font-bold text-gray-900 flex items-center">
                {selectedRecord.checkInTime ? (
                  <>
                    <Clock className="h-5 w-5 mr-2 text-blue-500" />
                    {selectedRecord.checkInTime}
                  </>
                ) : (
                  <span className="text-gray-400">Not recorded</span>
                )}
              </div>
            </div>
            
            <div className="border rounded-md p-4 bg-gray-50">
              <div className="text-sm font-medium text-gray-500 mb-1">Check Out</div>
              <div className="text-xl font-bold text-gray-900 flex items-center">
                {selectedRecord.checkOutTime ? (
                  <>
                    <Clock className="h-5 w-5 mr-2 text-blue-500" />
                    {selectedRecord.checkOutTime}
                  </>
                ) : (
                  <span className="text-gray-400">Not recorded</span>
                )}
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <div className="text-sm font-medium text-gray-500 mb-1">Work Hours</div>
            <div className="text-xl font-bold text-gray-900">
              {selectedRecord.workHours.toFixed(2)} hours
            </div>
          </div>
          
          {/* Regularization section */}
          <div className="border-t pt-4 mt-4">
            <div className="flex justify-between items-center">
              <h4 className="text-md font-medium text-gray-900">Regularization</h4>
              
              {/* Get regularization requests for this date */}
              {regularizationRequests.some(req => req.date === selectedRecord.date) ? (
                <span className="text-sm text-blue-600">Regularization request submitted</span>
              ) : (
                <button
                  className={hrPrimaryButtonStyle}
                  onClick={() => {
                    setRegularizationForm({
                      ...regularizationForm,
                      date: selectedRecord.date,
                      type: selectedRecord.status === 'absent' ? 'Absence Regularization' : 
                            !selectedRecord.checkInTime ? 'Missing Clock-in' : 'Missing Clock-out',
                    });
                    setCurrentView('regularize');
                  }}
                >
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Regularize
                </button>
              )}
            </div>
            
            {/* Show regularization requests if any */}
            {regularizationRequests
              .filter(req => req.date === selectedRecord.date)
              .map(request => (
                <div key={request.id} className="mt-4 border p-4 rounded-md bg-gray-50">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium">{request.type}</div>
                      <div className="text-sm text-gray-500">Submitted on {new Date(request.submittedOn).toLocaleString()}</div>
                    </div>
                    <div>
                      {request.status === 'pending' && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          <Clock className="h-3 w-3 mr-1" />
                          Pending
                        </span>
                      )}
                      {request.status === 'approved' && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Approved
                        </span>
                      )}
                      {request.status === 'rejected' && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          Rejected
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-2 text-sm">
                    <div className="font-medium">Reason:</div>
                    <div className="text-gray-700">{request.reason}</div>
                  </div>
                </div>
              ))}
            
            {regularizationRequests.filter(req => req.date === selectedRecord.date).length === 0 && (
              <div className="mt-4 text-sm text-gray-500">
                No regularization requests have been submitted for this date.
              </div>
            )}
          </div>
        </div>
      </>
    );
  };

  // Render regularization form
  const renderRegularizeView = () => (
    <>
      <div className="flex items-center mb-6">
        <button
          className={hrSecondaryButtonStyle + " mr-4"}
          onClick={() => setCurrentView('summary')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </button>
        <h2 className={hrSectionTitleStyle}>
          <Edit className="h-6 w-6 mr-2 text-blue-500" />
          Attendance Regularization
        </h2>
      </div>
      
      <div className={hrCardStyle}>
        <form onSubmit={handleRegularizationSubmit}>
          <div className="mb-6">
            <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
              Date <span className="text-red-500">*</span>
            </label>
            <input
              type="date"
              id="date"
              className={hrInputStyle}
              value={regularizationForm.date}
              onChange={(e) => setRegularizationForm({...regularizationForm, date: e.target.value})}
              required
              max={new Date().toISOString().split('T')[0]}
            />
          </div>
          
          <div className="mb-6">
            <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
              Regularization Type <span className="text-red-500">*</span>
            </label>
            <select
              id="type"
              className={hrInputStyle}
              value={regularizationForm.type}
              onChange={(e) => setRegularizationForm({...regularizationForm, type: e.target.value})}
              required
            >
              <option value="Missing Clock-in">Missing Clock-in</option>
              <option value="Missing Clock-out">Missing Clock-out</option>
              <option value="Absence Regularization">Absence Regularization</option>
              <option value="Wrong Punch">Wrong Punch</option>
            </select>
          </div>
          
          {(regularizationForm.type === 'Missing Clock-in' || 
            regularizationForm.type === 'Missing Clock-out' || 
            regularizationForm.type === 'Wrong Punch') && (
            <div className="mb-6">
              <label htmlFor="requestedTime" className="block text-sm font-medium text-gray-700 mb-1">
                Requested Time <span className="text-red-500">*</span>
              </label>
              <input
                type="time"
                id="requestedTime"
                className={hrInputStyle}
                value={regularizationForm.requestedTime}
                onChange={(e) => setRegularizationForm({...regularizationForm, requestedTime: e.target.value})}
                required
              />
            </div>
          )}
          
          <div className="mb-6">
            <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-1">
              Reason <span className="text-red-500">*</span>
            </label>
            <textarea
              id="reason"
              rows={4}
              className={hrInputStyle}
              value={regularizationForm.reason}
              onChange={(e) => setRegularizationForm({...regularizationForm, reason: e.target.value})}
              placeholder="Please provide a detailed reason for your regularization request..."
              required
            />
          </div>
          
          <div className={hrInfoAlertStyle + " mb-6"}>
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
              <div>
                <p className="text-sm text-blue-700">
                  Your request will be sent to your manager for approval. Please ensure all details are accurate.
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              className={hrSecondaryButtonStyle}
              onClick={() => setCurrentView('summary')}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={hrPrimaryButtonStyle}
            >
              <Check className="h-4 w-4 mr-2" />
              Submit Request
            </button>
          </div>
        </form>
      </div>
    </>
  );

  // Render the appropriate view based on current state
  return (
    <div className="p-4">
      {currentView === 'summary' && renderSummaryView()}
      {currentView === 'details' && renderDetailsView()}
      {currentView === 'regularize' && renderRegularizeView()}
    </div>
  );
};

export default UserAttendanceView; 