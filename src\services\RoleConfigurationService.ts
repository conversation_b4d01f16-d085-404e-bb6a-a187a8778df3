import { AppDataSource } from '../config/database';
import { Role } from '../server/entities/Role';
import { Permission } from '../server/entities/Permission';
import { UserRoleAssignment } from '../server/entities/UserRoleAssignment';
import { 
  Role as IRole, 
  Permission as IPermission, 
  RoleRule,
  PermissionCategory,
  RoleCategory 
} from '../types/roles';
import logger from '../utils/logger';

export class RoleConfigurationService {
  private static roleRepository = AppDataSource.getRepository(Role);
  private static permissionRepository = AppDataSource.getRepository(Permission);
  private static userRoleRepository = AppDataSource.getRepository(UserRoleAssignment);

  /**
   * Create a new role dynamically
   */
  static async createRole(roleData: {
    name: string;
    description: string;
    category: RoleCategory;
    permissions: string[];
    parentRoleId?: string;
    rules?: RoleRule[];
    metadata?: Record<string, any>;
  }): Promise<Role> {
    try {
      // Check if role already exists
      const existingRole = await this.roleRepository.findOne({
        where: { name: roleData.name }
      });

      if (existingRole) {
        throw new Error(`Role with name '${roleData.name}' already exists`);
      }

      // Create new role
      const role = this.roleRepository.create({
        name: roleData.name,
        description: roleData.description,
        category: roleData.category as any,
        isSystem: false,
        parentRoleId: roleData.parentRoleId
      });

      // Save role first to get ID
      const savedRole = await this.roleRepository.save(role);

      // Find and assign permissions
      if (roleData.permissions.length > 0) {
        const permissions = await this.permissionRepository
          .createQueryBuilder('permission')
          .where('permission.name IN (:...names)', { names: roleData.permissions })
          .getMany();
        savedRole.permissions = permissions;
        await this.roleRepository.save(savedRole);
      }

      logger.info(`Created new role: ${roleData.name}`);
      return savedRole;
    } catch (error) {
      logger.error('Error creating role:', error);
      throw error;
    }
  }

  /**
   * Update an existing role
   */
  static async updateRole(roleId: string, updates: {
    name?: string;
    description?: string;
    permissions?: string[];
    rules?: RoleRule[];
    isActive?: boolean;
    metadata?: Record<string, any>;
  }): Promise<Role> {
    try {
      const role = await this.roleRepository.findOne({
        where: { id: roleId },
        relations: ['permissions']
      });

      if (!role) {
        throw new Error(`Role with ID '${roleId}' not found`);
      }

      // Update basic properties
      if (updates.name) role.name = updates.name;
      if (updates.description) role.description = updates.description;

      // Update permissions if provided
      if (updates.permissions) {
        const permissions = await this.permissionRepository
          .createQueryBuilder('permission')
          .where('permission.name IN (:...names)', { names: updates.permissions })
          .getMany();
        role.permissions = permissions;
      }

      const updatedRole = await this.roleRepository.save(role);
      logger.info(`Updated role: ${role.name}`);
      return updatedRole;
    } catch (error) {
      logger.error('Error updating role:', error);
      throw error;
    }
  }

  /**
   * Delete a role
   */
  static async deleteRole(roleId: string): Promise<void> {
    try {
      const role = await this.roleRepository.findOne({
        where: { id: roleId }
      });

      if (!role) {
        throw new Error(`Role with ID '${roleId}' not found`);
      }

      if (role.isSystem) {
        throw new Error('Cannot delete system roles');
      }

      // Check if role is assigned to any users
      const assignments = await this.userRoleRepository.count({
        where: { roleId }
      });

      if (assignments > 0) {
        throw new Error(`Cannot delete role '${role.name}' - it is assigned to ${assignments} user(s)`);
      }

      await this.roleRepository.remove(role);
      logger.info(`Deleted role: ${role.name}`);
    } catch (error) {
      logger.error('Error deleting role:', error);
      throw error;
    }
  }

  /**
   * Create a new permission dynamically
   */
  static async createPermission(permissionData: {
    name: string;
    description: string;
    category: PermissionCategory;
    resource: string;
    action: string;
    metadata?: Record<string, any>;
  }): Promise<Permission> {
    try {
      // Check if permission already exists
      const existingPermission = await this.permissionRepository.findOne({
        where: { name: permissionData.name }
      });

      if (existingPermission) {
        throw new Error(`Permission with name '${permissionData.name}' already exists`);
      }

      const permission = this.permissionRepository.create({
        name: permissionData.name,
        description: permissionData.description,
        category: permissionData.category as any
      });

      const savedPermission = await this.permissionRepository.save(permission);
      logger.info(`Created new permission: ${permissionData.name}`);
      return savedPermission;
    } catch (error) {
      logger.error('Error creating permission:', error);
      throw error;
    }
  }

  /**
   * Assign role to user
   */
  static async assignRoleToUser(userId: string, roleId: string, options?: {
    isPrimary?: boolean;
    expiresAt?: Date;
    assignedBy: string;
    scope?: {
      departmentId?: string;
      projectId?: string;
      locationId?: string;
    };
  }): Promise<UserRoleAssignment> {
    try {
      // Check if assignment already exists
      const existingAssignment = await this.userRoleRepository.findOne({
        where: { userId, roleId }
      });

      if (existingAssignment) {
        throw new Error('User already has this role assigned');
      }

      const assignment = this.userRoleRepository.create({
        userId,
        roleId,
        isPrimary: options?.isPrimary || false,
        expiresAt: options?.expiresAt,
        assignedBy: options?.assignedBy || 'system',
        scope: options?.scope
      });

      const savedAssignment = await this.userRoleRepository.save(assignment);
      logger.info(`Assigned role ${roleId} to user ${userId}`);
      return savedAssignment;
    } catch (error) {
      logger.error('Error assigning role to user:', error);
      throw error;
    }
  }

  /**
   * Remove role from user
   */
  static async removeRoleFromUser(userId: string, roleId: string): Promise<void> {
    try {
      const assignment = await this.userRoleRepository.findOne({
        where: { userId, roleId }
      });

      if (!assignment) {
        throw new Error('Role assignment not found');
      }

      await this.userRoleRepository.remove(assignment);
      logger.info(`Removed role ${roleId} from user ${userId}`);
    } catch (error) {
      logger.error('Error removing role from user:', error);
      throw error;
    }
  }

  /**
   * Get all roles with their permissions
   */
  static async getAllRoles(): Promise<Role[]> {
    return this.roleRepository.find({
      relations: ['permissions', 'userAssignments']
    });
  }

  /**
   * Get all permissions grouped by category
   */
  static async getAllPermissions(): Promise<Record<string, Permission[]>> {
    const permissions = await this.permissionRepository.find();
    
    return permissions.reduce((grouped, permission) => {
      const category = permission.category;
      if (!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(permission);
      return grouped;
    }, {} as Record<string, Permission[]>);
  }

  /**
   * Get user's role assignments
   */
  static async getUserRoleAssignments(userId: string): Promise<UserRoleAssignment[]> {
    return this.userRoleRepository.find({
      where: { userId },
      relations: ['role', 'role.permissions']
    });
  }

  /**
   * Clone an existing role
   */
  static async cloneRole(sourceRoleId: string, newRoleName: string, newDescription?: string): Promise<Role> {
    try {
      const sourceRole = await this.roleRepository.findOne({
        where: { id: sourceRoleId },
        relations: ['permissions']
      });

      if (!sourceRole) {
        throw new Error(`Source role with ID '${sourceRoleId}' not found`);
      }

      const clonedRole = this.roleRepository.create({
        name: newRoleName,
        description: newDescription || `Cloned from ${sourceRole.name}`,
        category: sourceRole.category,
        isSystem: false,
        parentRoleId: sourceRole.parentRoleId
      });

      const savedRole = await this.roleRepository.save(clonedRole);

      // Copy permissions
      if (sourceRole.permissions) {
        savedRole.permissions = sourceRole.permissions;
        await this.roleRepository.save(savedRole);
      }

      logger.info(`Cloned role '${sourceRole.name}' to '${newRoleName}'`);
      return savedRole;
    } catch (error) {
      logger.error('Error cloning role:', error);
      throw error;
    }
  }

  /**
   * Get role hierarchy
   */
  static async getRoleHierarchy(): Promise<any[]> {
    const roles = await this.roleRepository.find();
    
    // Build hierarchy tree
    const roleMap = new Map(roles.map(role => [role.id, { ...role, children: [] }]));
    const rootRoles: any[] = [];

    roles.forEach(role => {
      const roleWithChildren = roleMap.get(role.id);
      if (role.parentRoleId && roleMap.has(role.parentRoleId)) {
        roleMap.get(role.parentRoleId)!.children.push(roleWithChildren);
      } else {
        rootRoles.push(roleWithChildren);
      }
    });

    return rootRoles;
  }

  /**
   * Validate role configuration
   */
  static async validateRoleConfiguration(roleData: any): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];

    // Check required fields
    if (!roleData.name) errors.push('Role name is required');
    if (!roleData.description) errors.push('Role description is required');
    if (!roleData.category) errors.push('Role category is required');

    // Check if name is unique
    if (roleData.name) {
      const existingRole = await this.roleRepository.findOne({
        where: { name: roleData.name }
      });
      if (existingRole && existingRole.id !== roleData.id) {
        errors.push('Role name must be unique');
      }
    }

    // Validate permissions exist
    if (roleData.permissions && roleData.permissions.length > 0) {
      const existingPermissions = await this.permissionRepository
        .createQueryBuilder('permission')
        .where('permission.name IN (:...names)', { names: roleData.permissions })
        .getMany();
      const missingPermissions = roleData.permissions.filter(
        (perm: string) => !existingPermissions.some(ep => ep.name === perm)
      );
      if (missingPermissions.length > 0) {
        errors.push(`Invalid permissions: ${missingPermissions.join(', ')}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
