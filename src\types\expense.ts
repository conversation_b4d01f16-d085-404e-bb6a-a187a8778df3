export type Currency = 'PKR' | 'USD' | 'EUR' | 'GBP' | 'AED';

export interface Expense {
  id: string;
  category: string;
  subcategory: string;
  description: string;
  amount: number;
  currency: Currency;
  date: string;
  department: string;
  status: 'pending' | 'approved' | 'rejected';
  type: 'operational' | 'capital' | 'maintenance' | 'subscription';
  serviceType?: 'printer' | 'photocopy' | 'network' | 'computer' | 'software' | 'other';
  frequency?: 'one-time' | 'monthly' | 'quarterly' | 'yearly';
  invoiceNumber?: string;
  vendor?: string;
  paymentMethod?: 'cash' | 'bank_transfer' | 'check' | 'credit_card';
  approvedBy?: string;
  approvalDate?: string;
  visitDetails?: {
    date: string;
    technician?: string;
    workDone: string;
    nextVisitDue?: string;
  };
  tags: string[];
  attachments: Array<{
    name: string;
    url: string;
    type: string;
  }>;
}

export interface BudgetItem {
  id: string;
  category: string;
  allocated: number;
  spent: number;
  remaining: number;
  percentageUsed: number;
  fiscalYear: string;
  startDate: string;
  endDate: string;
  description?: string;
  tags?: string[];
}

export interface VendorService {
  id: string;
  name: string;
  description: string;
  hasSLA: boolean;
  sla?: {
    responseTime: number; // in hours
    uptime: number; // percentage
    resolutionTime: number; // in hours
    penalties?: string;
  };
  category: 'internet' | 'hardware' | 'software' | 'support' | 'maintenance' | 'other';
  pricing?: {
    type: 'fixed' | 'hourly' | 'per_unit';
    amount: number;
    currency: Currency;
  };
}

export interface Vendor {
  id: string;
  name: string;
  category: 'hardware' | 'software' | 'service' | 'maintenance';
  services: VendorService[];
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
  contractDetails: {
    startDate: string;
    endDate: string;
    terms: string;
    amount: number;
    frequency: 'monthly' | 'quarterly' | 'yearly';
  };
  visitSchedule: {
    frequency: 'weekly' | 'monthly' | 'quarterly' | 'on_call';
    preferredDay: string;
    lastVisit: string;
    nextVisit: string;
  };
  status: 'active' | 'inactive' | 'pending';
  performance?: {
    rating: number;
    responseTime: number;
    deliveryScore: number;
    qualityScore: number;
  };
  spending?: {
    current: {
      value: number;
      currency: Currency;
    };
    previous: {
      value: number;
      currency: Currency;
    };
    trend: number;
  };
  invoices?: {
    id: string;
    date: string;
    amount: number;
    currency: Currency;
    status: 'pending' | 'paid' | 'overdue';
    dueDate: string;
  }[];
}

export interface Bill {
  id: string;
  billNumber: string;
  vendorId: string;
  vendorName: string;
  amount: number;
  issueDate: string;
  dueDate: string;
  status: 'pending' | 'paid' | 'overdue' | 'disputed';
  category: 'hardware' | 'software' | 'service' | 'maintenance';
  serviceType: 'printer' | 'photocopy' | 'network' | 'computer' | 'software' | 'other';
  paymentDetails?: {
    method: 'cash' | 'bank_transfer' | 'check' | 'credit_card';
    date?: string;
    reference?: string;
  };
  visitDetails?: {
    date: string;
    technician?: string;
    workDone: string;
    nextVisitDue?: string;
  };
  attachments: Array<{
    name: string;
    url: string;
    type: string;
  }>;
}

export interface Invoice {
  id: string;
  invoiceNumber: string;
  clientDepartment: string;
  amount: number;
  issueDate: string;
  dueDate: string;
  status: 'pending' | 'paid' | 'overdue';
  category: 'hardware' | 'software' | 'service' | 'maintenance';
  serviceDetails: {
    type: string;
    description: string;
    period?: {
      start: string;
      end: string;
    };
  };
  paymentReceived?: {
    amount: number;
    date: string;
    method: string;
    reference?: string;
  };
  attachments: Array<{
    name: string;
    url: string;
    type: string;
  }>;
}

export interface BillingInvoice {
  id: string;
  documentNumber: string;
  documentType: 'bill' | 'invoice';
  vendorId?: string;
  vendorName?: string;
  clientDepartment: string;
  amount: number;
  issueDate: string;
  dueDate: string;
  status: 'pending' | 'paid' | 'overdue' | 'disputed';
  category: 'hardware' | 'software' | 'service' | 'maintenance';
  serviceType?: 'printer' | 'photocopy' | 'network' | 'computer' | 'software' | 'other';
  serviceDetails: {
    type: string;
    description: string;
    period?: {
      start: string;
      end: string;
    };
  };
  paymentDetails?: {
    method: 'cash' | 'bank_transfer' | 'check' | 'credit_card';
    date?: string;
    reference?: string;
    amount?: number;
  };
  visitDetails?: {
    date: string;
    technician?: string;
    workDone: string;
    nextVisitDue?: string;
  };
  attachments: Array<{
    name: string;
    url: string;
    type: string;
  }>;
}

export const EXPENSE_CATEGORIES = {
  Hardware: [
    'Printers',
    'Toners & Cartridges',
    'Laptops & Desktops',
    'Networking Devices',
    'Storage Devices',
    'Peripherals'
  ],
  Software: [
    'Operating Systems',
    'Productivity Tools',
    'Security Software',
    'ERP & CRM',
    'Custom Applications'
  ],
  'Cloud Services': [
    'Hosting & Domain',
    'Cloud Storage',
    'Virtual Machines',
    'Backup & Disaster Recovery'
  ],
  Maintenance: [
    'Printer Maintenance',
    'Photocopier Repair',
    'CCTV/Camera Maintenance',
    'Server & Network Maintenance',
    'Biometric System Repairs'
  ],
  Services: [
    'IT Support & Consultancy',
    'Vendor Service Contracts',
    'Annual Maintenance Contracts',
    'Managed IT Services'
  ],
  Training: [
    'Employee IT Training',
    'Certification Courses',
    'Cybersecurity & Networking Training'
  ],
  'Utilities & Communication': [
    'Internet Bills',
    'Telecommunication',
    'Call Center Software'
  ],
  'Accessories & Peripherals': [
    'Mouse, Keyboards, Headsets',
    'Cables, Adapters, Docking Stations',
    'External Hard Drives'
  ],
  'Miscellaneous IT Expenses': [
    'Unclassified Costs',
    'Unexpected Repairs'
  ]
} as const;

export type ExpenseCategory = keyof typeof EXPENSE_CATEGORIES;
export type ExpenseSubcategory = typeof EXPENSE_CATEGORIES[ExpenseCategory][number]; 