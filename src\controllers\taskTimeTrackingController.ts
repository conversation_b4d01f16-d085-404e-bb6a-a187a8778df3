import { Request, Response } from 'express';
import { AppDataSource } from '../config/database';
import { TaskTimeEntry, TimeEntryType } from '../entities/TaskTimeEntry';
import { Task } from '../entities/Task';
import { User } from '../entities/User';
import { validate } from 'class-validator';

export class TaskTimeTrackingController {
  private taskTimeEntryRepository = AppDataSource.getRepository(TaskTimeEntry);
  private taskRepository = AppDataSource.getRepository(Task);
  private userRepository = AppDataSource.getRepository(User);

  // Start time tracking (timer)
  async startTimer(req: Request, res: Response) {
    try {
      const { taskId } = req.params;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Check if task exists and user has access
      const task = await this.taskRepository.findOne({
        where: { id: Number(taskId), isActive: true },
        relations: ['project', 'project.members']
      });

      if (!task) {
        return res.status(404).json({ error: 'Task not found' });
      }

      // Check if user has permission to track time on this task
      const hasPermission = task.assignedToId === userId ||
                           task.createdById === userId ||
                           task.project.createdById === userId ||
                           task.project.managerId === userId ||
                           task.project.members?.some(member => member.userId === userId && member.isActive);

      if (!hasPermission) {
        return res.status(403).json({ error: 'You do not have permission to track time on this task' });
      }

      // Check if user already has an active timer for this task
      const activeTimer = await this.taskTimeEntryRepository.findOne({
        where: {
          taskId: Number(taskId),
          userId,
          endTime: null,
          type: TimeEntryType.TIMER
        }
      });

      if (activeTimer) {
        return res.status(400).json({ error: 'Timer is already running for this task' });
      }

      // Create new time entry
      const timeEntry = this.taskTimeEntryRepository.create({
        taskId: Number(taskId),
        userId,
        startTime: new Date(),
        type: TimeEntryType.TIMER,
        durationMinutes: 0
      });

      const savedTimeEntry = await this.taskTimeEntryRepository.save(timeEntry);

      res.status(201).json(savedTimeEntry);
    } catch (error) {
      console.error('Error starting timer:', error);
      res.status(500).json({ error: 'Failed to start timer' });
    }
  }

  // Stop time tracking (timer)
  async stopTimer(req: Request, res: Response) {
    try {
      const { taskId } = req.params;
      const { description } = req.body;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Find active timer
      const activeTimer = await this.taskTimeEntryRepository.findOne({
        where: {
          taskId: Number(taskId),
          userId,
          endTime: null,
          type: TimeEntryType.TIMER
        }
      });

      if (!activeTimer) {
        return res.status(404).json({ error: 'No active timer found for this task' });
      }

      // Calculate duration
      const endTime = new Date();
      const durationMs = endTime.getTime() - activeTimer.startTime.getTime();
      const durationMinutes = Math.round(durationMs / (1000 * 60));

      // Update time entry
      await this.taskTimeEntryRepository.update(activeTimer.id, {
        endTime,
        durationMinutes,
        description: description || activeTimer.description
      });

      // Update task's total time spent
      const task = await this.taskRepository.findOne({
        where: { id: Number(taskId) }
      });

      if (task) {
        await this.taskRepository.update(Number(taskId), {
          timeSpentMinutes: (task.timeSpentMinutes || 0) + durationMinutes
        });
      }

      const updatedTimeEntry = await this.taskTimeEntryRepository.findOne({
        where: { id: activeTimer.id },
        relations: ['task', 'user']
      });

      res.json(updatedTimeEntry);
    } catch (error) {
      console.error('Error stopping timer:', error);
      res.status(500).json({ error: 'Failed to stop timer' });
    }
  }

  // Add manual time entry
  async addManualTimeEntry(req: Request, res: Response) {
    try {
      const { taskId } = req.params;
      const { durationMinutes, description, startTime, isBillable, hourlyRate } = req.body;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Check if task exists and user has access
      const task = await this.taskRepository.findOne({
        where: { id: Number(taskId), isActive: true },
        relations: ['project', 'project.members']
      });

      if (!task) {
        return res.status(404).json({ error: 'Task not found' });
      }

      // Check permissions
      const hasPermission = task.assignedToId === userId ||
                           task.createdById === userId ||
                           task.project.createdById === userId ||
                           task.project.managerId === userId ||
                           task.project.members?.some(member => member.userId === userId && member.isActive);

      if (!hasPermission) {
        return res.status(403).json({ error: 'You do not have permission to track time on this task' });
      }

      // Create manual time entry
      const timeEntry = this.taskTimeEntryRepository.create({
        taskId: Number(taskId),
        userId,
        durationMinutes,
        description,
        startTime: startTime ? new Date(startTime) : new Date(),
        endTime: new Date(),
        type: TimeEntryType.MANUAL,
        isBillable: isBillable || false,
        hourlyRate: hourlyRate || null
      });

      // Validate
      const errors = await validate(timeEntry);
      if (errors.length > 0) {
        return res.status(400).json({ 
          error: 'Validation failed', 
          details: errors.map(err => Object.values(err.constraints || {})).flat()
        });
      }

      const savedTimeEntry = await this.taskTimeEntryRepository.save(timeEntry);

      // Update task's total time spent
      await this.taskRepository.update(Number(taskId), {
        timeSpentMinutes: (task.timeSpentMinutes || 0) + durationMinutes
      });

      const completeTimeEntry = await this.taskTimeEntryRepository.findOne({
        where: { id: savedTimeEntry.id },
        relations: ['task', 'user']
      });

      res.status(201).json(completeTimeEntry);
    } catch (error) {
      console.error('Error adding manual time entry:', error);
      res.status(500).json({ error: 'Failed to add time entry' });
    }
  }

  // Get time entries for a task
  async getTaskTimeEntries(req: Request, res: Response) {
    try {
      const { taskId } = req.params;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Check if task exists and user has access
      const task = await this.taskRepository.findOne({
        where: { id: Number(taskId), isActive: true },
        relations: ['project', 'project.members']
      });

      if (!task) {
        return res.status(404).json({ error: 'Task not found' });
      }

      // Check permissions
      const hasPermission = task.assignedToId === userId ||
                           task.createdById === userId ||
                           task.project.createdById === userId ||
                           task.project.managerId === userId ||
                           task.project.members?.some(member => member.userId === userId && member.isActive);

      if (!hasPermission) {
        return res.status(403).json({ error: 'You do not have permission to view time entries for this task' });
      }

      const timeEntries = await this.taskTimeEntryRepository.find({
        where: { taskId: Number(taskId), isActive: true },
        relations: ['user'],
        order: { createdAt: 'DESC' }
      });

      res.json(timeEntries);
    } catch (error) {
      console.error('Error fetching time entries:', error);
      res.status(500).json({ error: 'Failed to fetch time entries' });
    }
  }

  // Get user's time entries
  async getUserTimeEntries(req: Request, res: Response) {
    try {
      const userId = (req as any).user?.id;
      const { startDate, endDate, taskId } = req.query;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const queryBuilder = this.taskTimeEntryRepository
        .createQueryBuilder('timeEntry')
        .leftJoinAndSelect('timeEntry.task', 'task')
        .leftJoinAndSelect('task.project', 'project')
        .where('timeEntry.userId = :userId', { userId })
        .andWhere('timeEntry.isActive = :isActive', { isActive: true });

      if (startDate && endDate) {
        queryBuilder.andWhere('timeEntry.startTime BETWEEN :startDate AND :endDate', {
          startDate: new Date(startDate as string),
          endDate: new Date(endDate as string)
        });
      }

      if (taskId) {
        queryBuilder.andWhere('timeEntry.taskId = :taskId', { taskId });
      }

      const timeEntries = await queryBuilder
        .orderBy('timeEntry.startTime', 'DESC')
        .getMany();

      res.json(timeEntries);
    } catch (error) {
      console.error('Error fetching user time entries:', error);
      res.status(500).json({ error: 'Failed to fetch time entries' });
    }
  }

  // Delete time entry
  async deleteTimeEntry(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const timeEntry = await this.taskTimeEntryRepository.findOne({
        where: { id: Number(id), isActive: true },
        relations: ['task']
      });

      if (!timeEntry) {
        return res.status(404).json({ error: 'Time entry not found' });
      }

      // Check if user owns this time entry or has admin permissions
      if (timeEntry.userId !== userId) {
        return res.status(403).json({ error: 'You can only delete your own time entries' });
      }

      // Update task's total time spent
      const task = await this.taskRepository.findOne({
        where: { id: timeEntry.taskId }
      });

      if (task) {
        await this.taskRepository.update(timeEntry.taskId, {
          timeSpentMinutes: Math.max(0, (task.timeSpentMinutes || 0) - timeEntry.durationMinutes)
        });
      }

      // Soft delete time entry
      await this.taskTimeEntryRepository.update(id, { isActive: false });

      res.json({ message: 'Time entry deleted successfully' });
    } catch (error) {
      console.error('Error deleting time entry:', error);
      res.status(500).json({ error: 'Failed to delete time entry' });
    }
  }
}
