import React, { useState, FormEvent } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { LogIn, AlertCircle, Mail } from 'lucide-react';

export const LoginPage: React.FC = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showActivationRequest, setShowActivationRequest] = useState(false);
  const [userId, setUserId] = useState<number | null>(null);
  const [activationRequestSent, setActivationRequestSent] = useState(false);

  const validateForm = () => {
    setError('');
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setError('Email is required');
      return false;
    }
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address');
      return false;
    }
    if (!password) {
      setError('Password is required');
      return false;
    }
    if (password.length < 6) {
      setError('Password must be at least 6 characters long');
      return false;
    }
    return true;
  };

  const handleActivationRequest = async () => {
    if (!userId) return;
    
    try {
      setIsLoading(true);
      setActivationRequestSent(true);
      setError('');
    } catch (err) {
      setError('Failed to send activation request. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError('');
    setShowActivationRequest(false);
    setActivationRequestSent(false);

    try {
      await login(email, password);
      navigate('/');
    } catch (error: any) {
      console.error('Login error:', error);
      
      // Handle different error scenarios
      if (error.response?.status === 401) {
        setError('Invalid email or password. Please try again.');
      } else if (error.response?.status === 403) {
        setError('Your account is not activated. Please check your email for activation instructions.');
        setShowActivationRequest(true);
      } else if (!navigator.onLine) {
        setError('No internet connection. Please check your network and try again.');
      } else {
        setError(error.response?.data?.message || 'An error occurred while logging in. Please try again.');
      }
      
      // Clear password on error
      setPassword('');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-start justify-center pt-48 p-4 relative bg-cover bg-no-repeat"
      style={{
        backgroundImage: 'url("/images/wall.jpg")',
        backgroundSize: 'cover',
        backgroundPosition: 'center 0%'
      }}>
      <div className="absolute inset-0 bg-black/30" />

      <div className="max-w-md w-full relative">
        <div className="bg-white/90 backdrop-blur-md rounded-xl shadow-2xl p-8 space-y-6 max-w-md mx-auto">
          <h1 className="text-2xl font-bold text-center text-gray-800 mb-2">
            IT Management System
          </h1>

          {error && (
            <div className="bg-red-50 text-red-800 p-4 rounded-lg flex items-center gap-2">
              <AlertCircle className="h-5 w-5 flex-shrink-0" />
              <p>{error}</p>
            </div>
          )}

          {activationRequestSent && (
            <div className="bg-green-50 text-green-800 p-4 rounded-lg flex items-center gap-2">
              <Mail className="h-5 w-5 flex-shrink-0" />
              <p>Activation request sent successfully. The IT team will review your request.</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-5" noValidate>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  setError('');
                  setShowActivationRequest(false);
                  setActivationRequestSent(false);
                }}
                className="w-full pl-4 pr-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/80"
                placeholder="Enter your email"
                autoComplete="email"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  setError('');
                }}
                className="w-full pl-4 pr-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/80"
                placeholder="Enter your password"
                autoComplete="current-password"
              />
            </div>

            <div className="space-y-3">
              <button
                type="submit"
                disabled={isLoading}
                className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="h-5 w-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <LogIn className="h-5 w-5" />
                )}
                Sign In
              </button>

              {showActivationRequest && !activationRequestSent && (
                <button
                  type="button"
                  onClick={handleActivationRequest}
                  disabled={isLoading}
                  className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 focus:ring-4 focus:ring-green-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <div className="h-5 w-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Mail className="h-5 w-5" />
                  )}
                  Request Account Activation
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;