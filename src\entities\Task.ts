import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  Join<PERSON>olumn,
  JoinTable
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsEnum, IsDateString, Length, IsNumber } from 'class-validator';
import { User } from './User';
import { Project } from './Project';
import { TaskComment } from './TaskComment';
import { TaskAttachment } from './TaskAttachment';
import { TaskTimeEntry } from './TaskTimeEntry';

export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  IN_REVIEW = 'in_review',
  DONE = 'done',
  CANCELLED = 'cancelled'
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum TaskType {
  TASK = 'task',
  BUG = 'bug',
  FEATURE = 'feature',
  IMPROVEMENT = 'improvement',
  RESEARCH = 'research',
  SUBTASK = 'subtask',
  CHECKLIST_ITEM = 'checklist_item'
}

export enum TaskRecurrenceType {
  NONE = 'none',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly'
}

export enum TaskApprovalStatus {
  NOT_REQUIRED = 'not_required',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

@Entity('tasks')
export class Task {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Task title is required' })
  @Length(2, 255, { message: 'Task title must be between 2 and 255 characters' })
  title: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  description: string;

  @Column({
    type: 'enum',
    enum: TaskStatus,
    default: TaskStatus.TODO
  })
  @IsEnum(TaskStatus, { message: 'Invalid task status' })
  status: TaskStatus;

  @Column({
    type: 'enum',
    enum: TaskPriority,
    default: TaskPriority.MEDIUM
  })
  @IsEnum(TaskPriority, { message: 'Invalid task priority' })
  priority: TaskPriority;

  @Column({
    type: 'enum',
    enum: TaskType,
    default: TaskType.TASK
  })
  @IsEnum(TaskType, { message: 'Invalid task type' })
  type: TaskType;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid start date format' })
  startDate: string;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid due date format' })
  dueDate: string;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid completion date format' })
  completedDate: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  @IsOptional()
  @IsNumber({}, { message: 'Progress must be a number' })
  progress: number;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Estimated hours must be a number' })
  estimatedHours: number;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Actual hours must be a number' })
  actualHours: number;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  tags: string[];

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  notes: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  // Enhanced Task Management Fields
  @Column({ type: 'boolean', default: false })
  isTemplate: boolean;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  templateName: string;

  @Column({
    type: 'enum',
    enum: TaskRecurrenceType,
    default: TaskRecurrenceType.NONE
  })
  @IsEnum(TaskRecurrenceType, { message: 'Invalid recurrence type' })
  recurrenceType: TaskRecurrenceType;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  recurrenceConfig: {
    interval?: number;
    daysOfWeek?: number[];
    dayOfMonth?: number;
    endDate?: string;
    maxOccurrences?: number;
  };

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  originalTaskId: number;

  @Column({
    type: 'enum',
    enum: TaskApprovalStatus,
    default: TaskApprovalStatus.NOT_REQUIRED
  })
  @IsEnum(TaskApprovalStatus, { message: 'Invalid approval status' })
  approvalStatus: TaskApprovalStatus;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  approvalWorkflow: {
    approvers: string[];
    currentApproverIndex: number;
    approvalHistory: {
      approverId: string;
      status: 'approved' | 'rejected';
      comment?: string;
      timestamp: string;
    }[];
  };

  @Column({ type: 'boolean', default: false })
  requiresApproval: boolean;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  checklist: {
    id: string;
    text: string;
    completed: boolean;
    createdAt: string;
    completedAt?: string;
    completedBy?: string;
  }[];

  @Column({ type: 'int', default: 0 })
  @IsOptional()
  timeSpentMinutes: number;

  @Column({ type: 'boolean', default: false })
  isBlocked: boolean;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  blockReason: string;

  // Relations
  @ManyToOne(() => Project, project => project.tasks, { nullable: false })
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @Column({ type: 'int' })
  projectId: number;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @Column({ type: 'uuid' })
  createdById: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assignedToId' })
  assignedTo: User;

  @Column({ type: 'uuid', nullable: true })
  assignedToId: string;

  @ManyToOne(() => Task, { nullable: true })
  @JoinColumn({ name: 'parentTaskId' })
  parentTask: Task;

  @Column({ type: 'int', nullable: true })
  parentTaskId: number;

  @OneToMany(() => Task, task => task.parentTask)
  subtasks: Task[];

  @OneToMany(() => TaskComment, comment => comment.task, { cascade: true })
  comments: TaskComment[];

  @OneToMany(() => TaskAttachment, attachment => attachment.task, { cascade: true })
  attachments: TaskAttachment[];

  @OneToMany(() => TaskTimeEntry, timeEntry => timeEntry.task, { cascade: true })
  timeEntries: TaskTimeEntry[];

  // Task Dependencies - Many-to-Many self-referencing relationship
  @ManyToMany(() => Task, task => task.dependentTasks)
  @JoinTable({
    name: 'task_dependencies',
    joinColumn: { name: 'taskId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'dependsOnTaskId', referencedColumnName: 'id' }
  })
  dependencies: Task[];

  @ManyToMany(() => Task, task => task.dependencies)
  dependentTasks: Task[];

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;
}
