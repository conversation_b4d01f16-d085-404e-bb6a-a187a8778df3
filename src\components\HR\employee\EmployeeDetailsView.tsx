import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { User, GraduationCap, Briefcase, DollarSign, FileText, Award, Car, Home, Edit, Trash2, Calendar, Phone, Mail, Target, Shield, Maximize2, Minimize2, Download } from 'lucide-react';
import { Employee } from './HR/employee/EmployeeManagement';
import jsPDF from 'jspdf';

// First, update the extended interface to explicitly define what the benefit property contains
interface BenefitDetails {
  totalSalary: string;
  basicSalary?: string;
  housingAllowance?: string;
  transportAllowance?: string;
  medicalAllowance?: string;
  otherAllowances?: string;
  bonus?: string;
  incentives?: string;
  overtimeRate?: string;
  taxDeductions?: string;
  providentFund?: string;
  insurance?: string;
  otherDeductions?: string;
  netSalary?: string;
  salaryTier?: string;
  paymentMode?: string;
  cashAmount?: string;
  bankAmount?: string;
  bankName?: string;
  bankBranch?: string;
  accountNumber?: string;
  accountTitle?: string;
  iban?: string;
  healthInsuranceProvider?: string;
  healthInsurancePolicyNumber?: string;
  healthInsuranceExpiryDate?: string;
  lifeInsuranceProvider?: string;
  lifeInsurancePolicyNumber?: string;
  lifeInsuranceExpiryDate?: string;
  salaryTrench?: string;
}

// Extend the Employee interface with consistent naming and optional properties
interface ExtendedEmployee extends Omit<Employee, 'certifications' | 'totalSalary'> {
  // Projects information
  projectEntries?: Array<ProjectEntry>;
  projects?: Array<ProjectEntry>; // For backward compatibility
  
  // Skills information
  skills?: Array<string | SkillItem>;
  professionalSkills?: string;
  technicalSkills?: string;
  languages?: string;
  
  // Experience information
  experienceEntries?: Array<ExperienceEntry>;
  experience?: Array<ExperienceEntry>; // For backward compatibility
  
  // Certifications
  certifications?: Array<CertificationEntry> | string;
  
  // Assets information
  deviceEntries?: Array<DeviceEntry>;
  vehicleType?: string;
  providedByCompany?: boolean;
  vehicleMakeModel?: string;
  vehicleColor?: string;
  registrationNumber?: string;
  mileageAtIssuance?: string;
  handingOverDate?: string;
  returnDate?: string;
  
  // Documents
  documentEntries?: Array<DocumentEntry>;
  
  // Salary and compensation
  totalSalary?: string | number;
  basicSalary?: string | number;
  currency?: string;
  paymentMethod?: string;
  accountNumber?: string;
  bankName?: string;
  bankBranch?: string;
  taxIdentificationNumber?: string;
  lastSalaryReviewDate?: string;
  updatedAt?: string;
  salaryTier?: string;
  paymentMode?: string; 
  cashAmount?: string;
  bankAmount?: string;
  accountTitle?: string;
  iban?: string;
  salaryTrench?: string;
  
  // Allowances
  foodAllowanceInSalary?: boolean;
  fuelAllowanceInSalary?: boolean;
  numberOfMeals?: string;
  fuelInLiters?: string;
  fuelAmount?: string;
  foodProvidedByCompany?: boolean;
  
  // Health & Insurance
  healthInsuranceProvider?: string;
  healthInsurancePolicyNumber?: string;
  healthInsuranceExpiryDate?: string;
  lifeInsuranceProvider?: string;
  lifeInsurancePolicyNumber?: string;
  lifeInsuranceExpiryDate?: string;
  vaccinationRecords?: string;
  medicalHistory?: string;
  bloodGroup?: string;
  allergies?: string;
  chronicConditions?: string;
  regularMedications?: string;
  
  // Accommodation
  accommodationProvidedByEmployer?: boolean;
  accommodationType?: string;
  accommodationAddress?: string;
  accommodationAllowance?: string | number;
  
  // Nested objects with specific types
  benefit?: BenefitDetails;
  benefits?: Record<string, any>;
  
  // Add index signature to allow dynamic access to properties
  [key: string]: any;
}

interface DeviceEntry {
  deviceName?: string;
  makeModel?: string;
  serialNumber?: string;
  handoverDate?: string;
  returnDate?: string;
  condition?: string;
}

interface DocumentEntry {
  name: string;
  type: string;
  documentType?: string;
  uploadDate?: string;
  fileName?: string;
  filePath?: string;
  serverPath?: string;
  url?: string;
  id?: string;
}

interface ExperienceEntry {
  company?: string;
  position?: string;
  startDate?: string;
  endDate?: string;
  responsibilities?: string;
  companyName?: string;
  jobTitle?: string;
  jobDescription?: string;
  currentlyWorking?: boolean;
}

interface CertificationEntry {
  name: string;
  issuedBy?: string;
  issueDate?: string;
}

interface ProjectEntry {
  projectName?: string;
  role?: string;
  startDate?: string;
  endDate?: string;
  description?: string;
  technologies?: string;
  teamSize?: string;
  clientName?: string;
  currentProject?: boolean;
}

interface EmployeeDetailsViewProps {
  employee: ExtendedEmployee;
  onClose: () => void;
  onEdit: (employee: ExtendedEmployee) => void;
  onDelete: (employee: ExtendedEmployee) => void;
}

// Define a cleaner skills interface 
interface SkillItem {
  professionalSkills?: string;
  technicalSkills?: string;
  certifications?: string;
  languages?: string;
  id?: number;
  [key: string]: any;
}

// Type guard function to check if an item is a skill object with a specific property
const hasSkillProperty = (skill: any, property: string): skill is SkillItem => {
  return typeof skill === 'object' && skill !== null && property in skill && !!skill[property];
};

// Add the salary breakdown calculation function
const calculateSalaryBreakdown = (totalSalary: string | number | undefined): { basic: string; housing: string; medical: string; other: string } => {
  if (!totalSalary) return { basic: '0', housing: '0', medical: '0', other: '0' };
  
  const total = typeof totalSalary === 'string' ? parseFloat(totalSalary) : totalSalary;
  if (isNaN(total)) return { basic: '0', housing: '0', medical: '0', other: '0' };
  
  return {
    basic: (total * 0.65).toFixed(2),
    housing: (total * 0.20).toFixed(2),
    medical: (total * 0.10).toFixed(2),
    other: (total * 0.05).toFixed(2)
  };
};

// Helper function to format dates
const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return 'Not specified';
  const date = new Date(dateString);
  return isNaN(date.getTime()) ? 'Invalid date' : date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// Define the DocumentsList component
interface DocumentsListProps {
  employee: ExtendedEmployee;
  formatDate: (dateString: string | undefined) => string;
  refreshDocuments?: () => void; // Add refresh callback
}

const DocumentsList: React.FC<DocumentsListProps> = ({ employee, formatDate, refreshDocuments }) => {
  // Define document types in the same order as the form
  const documentTypes = [
    'CV/Resume',
    'National ID/CNIC', // Added full version
    'CNIC', // Keep this for backward compatibility
    'Passport',
    'Driving License',
    'Educational Certificate',
    'Experience Certificate',
    'Bank Account Details',
    'Tax Documents', // Match the plural version in uploader
    'Tax Document', // Keep this for backward compatibility
    'Medical Certificate',
    'Police Clearance', // Match uploader version
    'Police Clearance Certificate', // Keep for backward compatibility
    'Affidavit',
    'Other'
  ];

  // Get existing documents by type - modified to support multiple docs per type and normalize types
  const documentsByType: Record<string, DocumentEntry[]> = {};
  
  // Helper function to normalize document types for matching
  const normalizeDocType = (type: string): string => {
    // Map similar document types to a standard format
    const typeMap: Record<string, string> = {
      'National ID/CNIC': 'CNIC',
      'Tax Documents': 'Tax Document',
      'Police Clearance': 'Police Clearance Certificate'
    };
    
    return typeMap[type] || type;
  };
  
  if (Array.isArray(employee.documentEntries)) {
    console.log('Processing document entries:', employee.documentEntries.length);
    
    employee.documentEntries.forEach((doc) => {
      // Check both type and documentType fields to handle both formats
      const originalDocType = doc.type || doc.documentType;
      if (!originalDocType) return;
      
      // Use original type for display but normalize for categorization
      const normalizedType = normalizeDocType(originalDocType);
      
      // Log each document for debugging
      console.log('Document:', {
        original: originalDocType,
        normalized: normalizedType,
        id: doc.id,
        fileName: doc.fileName
      });
      
      if (!documentsByType[normalizedType]) {
        documentsByType[normalizedType] = [];
      }
      
      // Store with original type to preserve display name
      documentsByType[normalizedType].push({
        ...doc,
        type: originalDocType // Ensure we keep the original type for display
      });
    });
  }

  // Helper function to get document URL
  const getDocumentUrl = (doc: DocumentEntry): string => {
    // Check for the document ID first - this is the most reliable approach
    if (doc.id) {
      // The server expects /api/employees/:employeeId/documents/:documentId
      // Use the employee ID from props if available
      return `/api/employees/${employee.id}/documents/${doc.id}`;
    }
    
    // Direct file paths - add /public prefix if needed
    if (doc.filePath) {
      // Make sure the path has correct formatting
      if (doc.filePath.startsWith('/')) {
        return doc.filePath;
      } else {
        return `/${doc.filePath}`;
      }
    }
    
    if (doc.serverPath) {
      if (doc.serverPath.startsWith('/')) {
        return doc.serverPath;
      } else {
        return `/${doc.serverPath}`;
      }
    }
    
    if (doc.url) {
      return doc.url;
    }
    
    // Fallback if no valid URL can be determined
    return '#';
  };

  // Function to open document in a new tab
  const openDocumentInNewTab = (doc: DocumentEntry) => {
    const url = getDocumentUrl(doc);
    
    if (url && url !== '#') {
      console.log('Opening document URL:', url);
      
      // Create full URL with origin for external URLs
      const fullUrl = url.startsWith('http') ? url : `${window.location.origin}${url}`;
      
      // Directly open the URL in a new tab - no iframe needed
      // The server is configured to handle Content-Type and Content-Disposition
      console.log('Opening URL directly:', fullUrl);
      window.open(fullUrl, '_blank');
    } else {
      console.error('Unable to open document: No valid URL found', doc);
      alert('Document preview not available');
    }
  };

  // Function to determine the appropriate icon for each document type
  const getDocumentIcon = (docType: string) => {
    switch (docType) {
      case 'CV/Resume':
        return <FileText className="h-6 w-6 text-indigo-500" />;
      case 'CNIC':
        return <User className="h-6 w-6 text-indigo-500" />;
      case 'Passport':
        return <FileText className="h-6 w-6 text-blue-500" />;
      case 'Driving License':
        return <Car className="h-6 w-6 text-green-500" />;
      case 'Educational Certificate':
        return <GraduationCap className="h-6 w-6 text-purple-500" />;
      case 'Experience Certificate':
        return <Briefcase className="h-6 w-6 text-orange-500" />;
      case 'Bank Account Details':
        return <DollarSign className="h-6 w-6 text-emerald-500" />;
      case 'Tax Document':
        return <FileText className="h-6 w-6 text-red-500" />;
      case 'Medical Certificate':
        return <Award className="h-6 w-6 text-pink-500" />;
      case 'Police Clearance Certificate':
        return <Shield className="h-6 w-6 text-blue-500" />;
      case 'Affidavit':
        return <FileText className="h-6 w-6 text-amber-500" />;
      default:
        return <FileText className="h-6 w-6 text-gray-500" />;
    }
  };

  // Add useEffect to refresh data if needed
  useEffect(() => {
    console.log('DocumentsList received employee with', 
      employee.documentEntries?.length || 0, 
      'document entries');
  }, [employee.documentEntries]);

  return (
    <div className="w-full">
      {(() => {
        const renderedTypes = new Set();
        return documentTypes.map((docType) => {
          const normalizedType = normalizeDocType(docType);

          // Skip if already rendered
          if (renderedTypes.has(normalizedType)) return null;
          renderedTypes.add(normalizedType);

          const docs = (documentsByType[normalizedType] || []).filter(doc => !!doc.filePath);
          const isAvailable = docs.length > 0;
          
          // Skip duplicate normalized types (like CNIC and National ID/CNIC) if they're empty
          if (!isAvailable && documentTypes.some(t => 
            t !== docType && normalizeDocType(t) === normalizedType && 
            documentsByType[normalizedType]?.length > 0
          )) {
            return null;
          }
          
          return (
            <div 
              key={docType} 
              className={`border rounded-lg p-5 bg-white dark:bg-gray-800 shadow-sm mb-4 ${isAvailable ? 'border-green-200 dark:border-green-700' : 'border-red-200 dark:border-red-700'}`}
            >
              <div className="flex items-center gap-4">
                <div className={`h-12 w-12 rounded-lg flex items-center justify-center ${isAvailable ? 'bg-green-50 dark:bg-green-900/30' : 'bg-red-50 dark:bg-red-900/30'}`}>
                  {getDocumentIcon(docType)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium text-gray-900 dark:text-white">{docType}</h3>
                    <span className={`text-xs px-2 py-0.5 rounded-full ${isAvailable ? 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-300' : 'bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-300'}`}>
                      {isAvailable ? (docs.length > 1 ? `${docs.length} Available` : 'Available') : 'Missing'}
                    </span>
                  </div>
                  
                  {!isAvailable && (
                    <div className="mt-1 text-sm text-gray-500 dark:text-gray-400">Document has not been uploaded yet</div>
                  )}
                </div>
              </div>

              {/* Show multiple documents of the same type if they exist */}
              {isAvailable && (
                <div className="mt-3 pl-16">
                  {docs.map((doc, idx) => (
                    <div key={idx} className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-0">
                      <div className="flex-1">
                        <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                          <span>{doc.name || doc.fileName || `${docType} Document`}</span>
                          {doc.uploadDate && <span>Uploaded: {formatDate(doc.uploadDate)}</span>}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        {doc.filePath ? (
                          <>
                            <button 
                              className="px-3 py-1.5 bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 text-sm font-medium rounded hover:bg-indigo-100 dark:hover:bg-indigo-800/40 transition-colors"
                              onClick={() => openDocumentInNewTab(doc)}
                            >
                              View
                            </button>
                            <a 
                              href={`${getDocumentUrl(doc)}?download=true`}
                              download
                              className="px-3 py-1.5 bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-sm font-medium rounded hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                            >
                              Download
                            </a>
                          </>
                        ) : (
                          <span className="text-gray-500 dark:text-gray-400">Missing Document</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          );
        });
      })()}
    </div>
  );
};

const EmployeeDetailsView: React.FC<EmployeeDetailsViewProps> = ({ employee, onClose, onEdit, onDelete }) => {
  const [activeTab, setActiveTab] = useState<'personal' | 'education' | 'experience' | 'skills' | 'projects' | 'job' | 'compensation' | 'assets' | 'docs'>('personal');
  const [refreshKey, setRefreshKey] = useState<number>(0); // Add state for forcing refreshes
  const [isFullView, setIsFullView] = useState(false);

  // Debug employee data
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Employee Data Debug:', {
        id: employee.id,
        salaryTier: employee.salaryTier,
        paymentMode: employee.paymentMode,
        salaryTrench: employee.salaryTrench,
        salaryTierType: typeof employee.salaryTier,
        paymentModeType: typeof employee.paymentMode,
        salaryTierEmpty: employee.salaryTier === '',
        paymentModeEmpty: employee.paymentMode === '',
        salaryTierUndefined: employee.salaryTier === undefined,
        paymentModeUndefined: employee.paymentMode === undefined,
        benefitSalaryTier: employee.benefit?.salaryTier,
        benefitPaymentMode: employee.benefit?.paymentMode,
        benefitSalaryTrench: employee.benefit?.salaryTrench,
        nestedBenefits: employee.benefits ? 'Present' : 'Not present',
        // Bank details debugging
        bankName: employee.bankName,
        bankNameEmpty: employee.bankName === '',
        bankNameUndefined: employee.bankName === undefined,
        benefitBankName: employee.benefit?.bankName,
        
        accountNumber: employee.accountNumber,
        accountNumberEmpty: employee.accountNumber === '',
        accountNumberUndefined: employee.accountNumber === undefined,
        benefitAccountNumber: employee.benefit?.accountNumber,
        
        iban: employee.iban,
        ibanEmpty: employee.iban === '',
        ibanUndefined: employee.iban === undefined,
        benefitIban: employee.benefit?.iban
      });
    }
  }, [employee]);

  // Use normalized versions of the data to handle inconsistencies
  const normalizedExperience = useMemo(() => {
    return employee.experienceEntries || employee.experience || [];
  }, [employee.experienceEntries, employee.experience]);

  const normalizedProjects = useMemo(() => {
    return employee.projectEntries || employee.projects || [];
  }, [employee.projectEntries, employee.projects]);

  const formatCurrency = (amount: string | number | undefined): string => {
    if (!amount) return 'Not specified';
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return isNaN(numericAmount) ? 'Not specified' : numericAmount.toLocaleString('en-US');
  };

  const getStatusBadgeColor = (status: string = 'active') => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-300';
      case 'inactive':
        return 'bg-gray-100 dark:bg-gray-900/50 text-gray-800 dark:text-gray-300';
      case 'onleave':
        return 'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300';
      case 'resigned':
        return 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-300';
      case 'terminated':
        return 'bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-300';
      case 'suspended':
        return 'bg-orange-100 dark:bg-orange-900/50 text-orange-800 dark:text-orange-300';
      case 'layoff':
        return 'bg-pink-100 dark:bg-pink-900/50 text-pink-800 dark:text-pink-300';
      default:
        return 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-300';
    }
  };

  const getLabelValuePair = (
    label: string,
    value: string | number | undefined | boolean,
    formatter?: (val: any) => string,
    IconComponent?: React.ReactNode
  ) => {
    let displayValue: string;
    
    if (typeof value === 'boolean') {
      displayValue = value ? 'Yes' : 'No';
    } else if (value === '' || value === undefined || value === null) {
      displayValue = 'Not specified';
    } else {
      displayValue = formatter ? formatter(value) : value?.toString();
    }
    
    return (
      <div>
        <p className="text-xs font-bold text-gray-900 dark:text-white tracking-wider mb-1 flex items-center">
          {IconComponent} <span className="ml-2">{label}</span>
        </p>
        <p className="text-sm text-gray-900 dark:text-gray-300">{displayValue}</p>
      </div>
    );
  };

  // Helper function to extract skills from different sources
  const getSkillsFromAllSources = (property: string) => {
    const skills: string[] = [];
    
    // Add from direct property if it exists
    if (typeof employee[property] === 'string' && employee[property]?.trim() !== '') {
      skills.push(...(employee[property] as string).split(',').map(s => s.trim()));
    }
    
    // Add from skills array if it exists
    if (Array.isArray(employee.skills)) {
      employee.skills.forEach(skill => {
        if (hasSkillProperty(skill, property)) {
          skills.push(...(skill[property] as string).split(',').map(s => s.trim()));
        } else if (typeof skill === 'string' && property === 'professionalSkills') {
          // Plain strings in skills array are treated as professional skills
          skills.push(skill.trim());
        }
      });
    }
    
    // Remove duplicates and empty strings
    return [...new Set(skills)].filter(s => s !== '');
  };

  // Helper to render skills properly
  const renderSkills = (skills: string[], colorClass: string, iconClass: string) => {
    if (skills.length === 0) {
      return <div className="text-gray-500 dark:text-gray-400">None specified</div>;
    }
    
    return (
      <div className="grid grid-cols-1 gap-3">
        {skills.map((skill, idx) => (
          <div 
            key={`skill-${idx}`} 
            className={`flex items-center gap-3 ${colorClass} px-5 py-3.5 rounded-lg shadow-sm hover:shadow transition-shadow duration-200`}
          >
            <Award className={`w-5 h-5 ${iconClass} flex-shrink-0`} />
            <span className="text-gray-800 dark:text-gray-200 font-medium">{skill}</span>
        </div>
        ))}
      </div>
    );
  };

  // Helper function to render certifications from all sources
  const getAllCertifications = () => {
    const certifications: Array<{name: string, issuedBy?: string, issueDate?: string}> = [];
    
    // Add certifications from certifications array if it's an array of objects
    if (Array.isArray(employee.certifications)) {
      certifications.push(...employee.certifications);
    }
    
    // Add certifications from certifications if it's a string
    if (typeof employee.certifications === 'string' && employee.certifications.trim() !== '') {
      certifications.push(...employee.certifications.split(',').map(cert => ({
        name: cert.trim()
      })));
    }
    
    // Add certifications from skills array
    if (Array.isArray(employee.skills)) {
      employee.skills.forEach(skill => {
        if (hasSkillProperty(skill, 'certifications')) {
          certifications.push(...(skill.certifications as string).split(',').map(cert => ({
            name: cert.trim()
          })));
        }
      });
    }
    
    return certifications;
  };

  // Determine if there is vehicle information to display
  const hasVehicleInfo = employee.providedByCompany || 
                         employee.vehicleType || 
                         employee.registrationNumber || 
                         employee.vehicleMakeModel;

  // Function to refresh document data
  const refreshDocuments = useCallback(() => {
    console.log('Refreshing documents...');
    setRefreshKey(prev => prev + 1);
  }, []);

  // Export CV as PDF
  const handleExportCV = () => {
    const doc = new jsPDF();
    // Colors
    const primary = [67, 56, 202]; // Indigo-700
    const gray = [55, 65, 81]; // Gray-700
    const lightGray = [243, 244, 246]; // Gray-100
    let y = 20;

    // Header: Name
    doc.setFontSize(24);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(primary[0], primary[1], primary[2]);
    doc.text(`${employee.firstName || ''} ${employee.middleName || ''} ${employee.lastName || ''}`.trim(), 15, y);
    y += 10;

    // Designation
    doc.setFontSize(14);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(gray[0], gray[1], gray[2]);
    doc.text(`${employee.designation || ''}`, 15, y);
    y += 8;

    // Department badge style
    if (employee.department) {
      doc.setFillColor(primary[0], primary[1], primary[2]);
      doc.roundedRect(15, y - 6, doc.getTextWidth(employee.department) + 12, 10, 3, 3, 'F');
      doc.setTextColor(255, 255, 255);
      doc.text(employee.department, 21, y);
      y += 12;
    }

    // Line separator
    doc.setDrawColor(primary[0], primary[1], primary[2]);
    doc.setLineWidth(1);
    doc.line(15, y, 195, y);
    y += 6;

    // Two-column: Contact/Skills/Certifications (left), Experience/Education (right)
    const leftX = 15;
    const rightX = 110;
    let leftY = y;
    let rightY = y;

    // Contact Info
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(primary[0], primary[1], primary[2]);
    doc.text('Contact', leftX, leftY);
    leftY += 7;
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(gray[0], gray[1], gray[2]);
    const contactLines = [
      `Employee ID: ${employee.employeeId || ''}`,
      `Joined: ${employee.joinDate ? formatDate(employee.joinDate) : ''}`,
      `Mobile: ${employee.mobileNumber || ''}`,
      `Email: ${employee.officialEmail || employee.personalEmail || ''}`,
      `Address: ${employee.currentAddress || ''}`
    ];
    contactLines.forEach(line => {
      doc.text(line, leftX, leftY);
      leftY += 6;
    });
    leftY += 2;

    // Skills
    if (employee.skills && employee.skills.length > 0) {
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(primary[0], primary[1], primary[2]);
      doc.text('Skills', leftX, leftY);
      leftY += 7;
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(gray[0], gray[1], gray[2]);
      const skills = Array.isArray(employee.skills) ? employee.skills.map(s => typeof s === 'string' ? s : (s.professionalSkills || s.technicalSkills || '')).join(', ') : '';
      doc.text(skills, leftX, leftY, { maxWidth: 80 });
      leftY += 10;
    }

    // Certifications
    if (employee.certifications && employee.certifications.length > 0) {
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(primary[0], primary[1], primary[2]);
      doc.text('Certifications', leftX, leftY);
      leftY += 7;
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(gray[0], gray[1], gray[2]);
      if (Array.isArray(employee.certifications)) {
        employee.certifications.forEach((cert) => {
          doc.text(`- ${cert.name || ''}`, leftX, leftY);
          leftY += 6;
        });
      } else if (typeof employee.certifications === 'string') {
        doc.text(employee.certifications, leftX, leftY);
        leftY += 6;
      }
      leftY += 2;
    }

    // Personal Info (right col)
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(primary[0], primary[1], primary[2]);
    doc.text('Personal', rightX, rightY);
    rightY += 7;
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(gray[0], gray[1], gray[2]);
    const personalLines = [
      `Gender: ${employee.gender || ''}`,
      `DOB: ${employee.dateOfBirth ? formatDate(employee.dateOfBirth) : ''}`,
      `Religion: ${employee.religion || ''}`,
      `CNIC: ${employee.cnicNumber || ''}`,
      `Nationality: ${employee.nationality || ''}`,
      `Marital: ${employee.maritalStatus || ''}`,
      `Blood: ${employee.bloodType || ''}`
    ];
    personalLines.forEach(line => {
      doc.text(line, rightX, rightY);
      rightY += 6;
    });
    rightY += 2;

    // Education (right col)
    if (employee.educationEntries && employee.educationEntries.length > 0) {
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(primary[0], primary[1], primary[2]);
      doc.text('Education', rightX, rightY);
      rightY += 7;
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(gray[0], gray[1], gray[2]);
      employee.educationEntries.forEach((edu) => {
        doc.text(`- ${edu.degree || ''}, ${edu.institution || ''} (${edu.graduationYear || ''})`, rightX, rightY);
        rightY += 6;
      });
      rightY += 2;
    }

    // Experience (right col)
    if (employee.experienceEntries && employee.experienceEntries.length > 0) {
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(primary[0], primary[1], primary[2]);
      doc.text('Experience', rightX, rightY);
      rightY += 7;
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(gray[0], gray[1], gray[2]);
      employee.experienceEntries.forEach((exp) => {
        doc.text(`- ${exp.position || exp.jobTitle || ''}, ${exp.company || exp.companyName || ''} (${exp.startDate ? formatDate(exp.startDate) : ''} - ${exp.endDate ? formatDate(exp.endDate) : 'Present'})`, rightX, rightY, { maxWidth: 90 });
        rightY += 6;
      });
      rightY += 2;
    }

    // Save
    doc.save(`${employee.firstName || 'employee'}_CV.pdf`);
  };

  // First, I need to add the calculateExperience function similar to the one in EmployeeManagement.tsx
  // Add this function around line 230, near the formatDate function

  const calculateExperience = (joinDateString: string | undefined): string => {
    try {
      if (!joinDateString) return 'N/A';
      
      const joinDate = new Date(joinDateString);
      if (isNaN(joinDate.getTime())) return 'N/A'; // Check if date is valid
      
      const currentDate = new Date();
      
      // Calculate difference in years, months, and days
      let years = currentDate.getFullYear() - joinDate.getFullYear();
      let months = currentDate.getMonth() - joinDate.getMonth();
      
      // Adjust years and months if needed
      if (months < 0) {
        years--;
        months += 12;
      }
      
      // Calculate days
      const joinDay = joinDate.getDate();
      const currentDay = currentDate.getDate();
      let days = currentDay - joinDay;
      
      if (days < 0) {
        // Borrow a month
        months--;
        if (months < 0) {
          years--;
          months += 12;
        }
        
        // Calculate days from previous month
        const lastMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0);
        days += lastMonth.getDate();
      }
      
      // Format the result
      const yearText = years > 0 ? `${years} ${years === 1 ? 'year' : 'years'}` : '';
      const monthText = months > 0 ? `${months} ${months === 1 ? 'month' : 'months'}` : '';
      const dayText = days > 0 ? `${days} ${days === 1 ? 'day' : 'days'}` : '';
      
      // Combine parts with commas and 'and'
      const parts = [yearText, monthText, dayText].filter(Boolean);
      
      if (parts.length === 0) return '0 days';
      if (parts.length === 1) return parts[0];
      if (parts.length === 2) return `${parts[0]} and ${parts[1]}`;
      return `${parts[0]}, ${parts[1]}, and ${parts[2]}`;
    } catch (e) {
      console.error('Error calculating experience:', e);
      return 'N/A';
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm overflow-y-auto h-full w-full flex items-center justify-center z-50 animate-in fade-in duration-200"
      onClick={(e) => {
        // Close modal when clicking on backdrop
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div 
        className={`relative bg-white dark:bg-gray-800 shadow-2xl flex flex-col overflow-hidden transition-all duration-300 animate-in zoom-in-95 ${isFullView ? 'w-full h-full max-w-none max-h-none rounded-none' : 'w-full max-w-4xl max-h-[90vh] rounded-lg'}`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Full View Toggle Button */}
        <button
          onClick={() => setIsFullView(v => !v)}
          className="absolute top-4 right-14 z-10 text-gray-400 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 focus:outline-none text-2xl font-bold"
          aria-label="Full View"
          title={isFullView ? 'Exit Full View' : 'Full View'}
        >
          {isFullView ? <Minimize2 className="w-6 h-6" /> : <Maximize2 className="w-6 h-6" />}
        </button>
        {/* X Close Button at top right */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 text-gray-400 dark:text-gray-300 hover:text-red-500 dark:hover:text-red-400 focus:outline-none text-2xl font-bold"
          aria-label="Close"
        >
          ×
        </button>
        {/* Export CV Button */}
        <button
          onClick={handleExportCV}
          className="absolute top-4 right-24 z-10 text-gray-400 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 focus:outline-none text-2xl font-bold"
          aria-label="Export CV"
          title="Export CV"
        >
          <Download className="w-6 h-6" />
        </button>
        {/* Header with profile info */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <div className="flex items-center">
            <div className="flex-shrink-0 mr-4">
              {employee.profileImagePath ? (
                <>
                <img 
                  src={employee.profileImagePath.startsWith('/') 
                    ? employee.profileImagePath 
                    : employee.profileImagePath.includes('/uploads/') 
                      ? `${employee.profileImagePath}` 
                      : `/uploads/${employee.profileImagePath}`} 
                  alt={`${employee.firstName} ${employee.lastName}`} 
                  className="h-32 w-32 rounded-full object-cover border-4 border-white shadow"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                      
                      // Show fallback initials
                      const parent = target.parentElement as HTMLElement;
                      if (parent.querySelector('.fallback-initials')) {
                        const fallback = parent.querySelector('.fallback-initials') as HTMLElement;
                        fallback.style.display = 'flex';
                    }
                  }}
                />
                  <div 
                    className="fallback-initials h-32 w-32 rounded-full bg-gray-200 items-center justify-center text-gray-500 text-2xl font-semibold border-4 border-white shadow" 
                    style={{ display: 'none' }}
                  >
                    {employee.firstName?.charAt(0) || ''}
                    {employee.lastName?.charAt(0) || ''}
                  </div>
                </>
              ) : (
                <div className="fallback-initials h-32 w-32 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-300 text-2xl font-semibold border-4 border-white dark:border-gray-700 shadow">
                  {employee.firstName?.charAt(0) || ''}
                  {employee.lastName?.charAt(0) || ''}
                </div>
              )}
            </div>
            <div className="flex-1 flex flex-col items-start">
              <div className="flex items-center">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {employee.firstName} {employee.middleName} {employee.lastName}
                </h2>
                <span className={`ml-4 px-3 py-1 text-xs font-medium rounded-full ${getStatusBadgeColor(employee.employmentStatus)}`}>
                  {employee.employmentStatus || 'Active'}
                </span>
              </div>
              <div className="mt-2 flex items-center text-sm font-medium">
                <Briefcase className="w-4 h-4 mr-2 text-indigo-500 dark:text-indigo-400" />
                <span className="text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30 px-3 py-1 rounded-full whitespace-normal tracking-normal max-w-sm">
                  {employee.department}
                </span>
              </div>
              
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 p-3 rounded-lg mt-2 border border-blue-100 dark:border-blue-800/30 w-full">
                <div className="flex items-center text-lg">
                  <Award className="w-5 h-5 mr-2 text-indigo-600 dark:text-indigo-400" />
                  <span className="font-semibold text-indigo-700 dark:text-indigo-400">{employee.designation}</span>
                </div>
                
                <div className="mt-2 flex flex-col gap-2 text-sm">
                  <div className="flex items-center gap-1">
                    <FileText className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    <span className="font-medium text-gray-700 dark:text-gray-300">Employee ID:</span>
                    <span className="text-blue-700 dark:text-blue-400 font-medium">{employee.employeeId}</span>
                  </div>
                  
                  <div className="flex flex-row gap-8">
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4 text-green-600 dark:text-green-400" />
                      <span className="font-medium text-gray-700 dark:text-gray-300">Joined:</span>
                      <span className="text-green-700 dark:text-green-400">{formatDate(employee.joinDate)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4 text-purple-600 dark:text-purple-400">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12 6 12 12 16 14"></polyline>
                      </svg>
                      <span className="font-medium text-gray-700 dark:text-gray-300">Experience:</span>
                      <span className="text-purple-700 dark:text-purple-400">{calculateExperience(employee.joinDate)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-gray-900 dark:text-white">
                {employee.totalSalary ? formatCurrency(employee.totalSalary) : (employee.salary ? formatCurrency(employee.salary) : '—')}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">/month</div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
          <nav className="flex px-4">
            <button
              onClick={() => setActiveTab('personal')}
              className={`px-3 py-2 text-xs font-medium flex items-center border-b-2 ${
                activeTab === 'personal'
                  ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400 font-bold'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <User className="h-3 w-3 mr-1" />
              Personal
            </button>
            <button
              onClick={() => setActiveTab('education')}
              className={`px-3 py-2 text-xs font-medium flex items-center border-b-2 ${
                activeTab === 'education'
                  ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400 font-bold'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <GraduationCap className="h-3 w-3 mr-1" />
              Education
            </button>
            <button
              onClick={() => setActiveTab('experience')}
              className={`px-3 py-2 text-xs font-medium flex items-center border-b-2 ${
                activeTab === 'experience'
                  ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400 font-bold'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <Briefcase className="h-3 w-3 mr-1" />
              Experience
            </button>
            <button
              onClick={() => setActiveTab('skills')}
              className={`px-3 py-2 text-xs font-medium flex items-center border-b-2 ${
                activeTab === 'skills'
                  ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400 font-bold'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <Award className="h-3 w-3 mr-1" />
              Skills
            </button>
            <button
              onClick={() => setActiveTab('projects')}
              className={`px-3 py-2 text-xs font-medium flex items-center border-b-2 ${
                activeTab === 'projects'
                  ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400 font-bold'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <Target className="h-3 w-3 mr-1" />
              Projects
            </button>
            <button
              onClick={() => setActiveTab('job')}
              className={`px-3 py-2 text-xs font-medium flex items-center border-b-2 ${
                activeTab === 'job'
                  ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400 font-bold'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <Briefcase className="h-3 w-3 mr-1" />
              Job
            </button>
            <button
              onClick={() => setActiveTab('compensation')}
              className={`px-3 py-2 text-xs font-medium flex items-center border-b-2 ${
                activeTab === 'compensation'
                  ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400 font-bold'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <DollarSign className="h-3 w-3 mr-1" />
              Compensation
            </button>
            <button
              onClick={() => setActiveTab('assets')}
              className={`px-3 py-2 text-xs font-medium flex items-center border-b-2 ${
                activeTab === 'assets'
                  ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400 font-bold'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <Car className="h-3 w-3 mr-1" />
              Company Assets
            </button>
            <button
              onClick={() => setActiveTab('docs')}
              className={`px-3 py-2 text-xs font-medium flex items-center border-b-2 ${
                activeTab === 'docs'
                  ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400 font-bold'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <FileText className="h-3 w-3 mr-1" />
              Docs
            </button>
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto bg-gray-50 dark:bg-gray-900" style={{ maxHeight: 'calc(90vh - 120px)' }}>
          {/* Personal Tab Content */}
          {activeTab === 'personal' && (
            <div className="flex flex-col gap-6">
              {/* Personal Information */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                                  <h4 className="text-base font-bold text-gray-900 dark:text-white flex items-center">
                  <User className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Personal Information
                </h4>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {getLabelValuePair('Full Name', `${employee.firstName}${employee.middleName ? ' ' + employee.middleName : ''} ${employee.lastName}`.trim(), undefined, <User className="w-4 h-4" />)}
                    {getLabelValuePair('Gender', employee.gender, undefined, <User className="w-4 h-4" />)}
                    {getLabelValuePair('Date of Birth', employee.dateOfBirth, formatDate, <Calendar className="w-4 h-4" />)}
                    {getLabelValuePair('Religion', employee.religion, undefined, <Home className="w-4 h-4" />)}
                    {getLabelValuePair('CNIC Number', employee.cnicNumber, undefined, <FileText className="w-4 h-4" />)}
                    {getLabelValuePair('CNIC Expiry Date', employee.cnicExpiryDate, formatDate, <Calendar className="w-4 h-4" />)}
                    {getLabelValuePair('Nationality', employee.nationality, undefined, <Home className="w-4 h-4" />)}
                    {getLabelValuePair('Marital Status', employee.maritalStatus, undefined, <User className="w-4 h-4" />)}
                    {getLabelValuePair('Blood Type', employee.bloodType, undefined, <Award className="w-4 h-4" />)}
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                  <h4 className="text-sm font-bold text-gray-900 dark:text-white flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Contact Information
                  </h4>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {getLabelValuePair('Mobile Number', employee.mobileNumber, undefined, <Phone className="w-4 h-4" />)}
                    {getLabelValuePair('Official Number', employee.officialNumber, undefined, <Phone className="w-4 h-4" />)}
                    {getLabelValuePair('Official Email', employee.officialEmail, undefined, <Mail className="w-4 h-4" />)}
                    {getLabelValuePair('Personal Email', employee.personalEmail, undefined, <Mail className="w-4 h-4" />)}
                    {getLabelValuePair('Current Address', employee.currentAddress, undefined, <Home className="w-4 h-4" />)}
                    {getLabelValuePair('Permanent Address', employee.permanentAddress, undefined, <Home className="w-4 h-4" />)}
                    {getLabelValuePair('Emergency Contact', employee.emergencyContactName, undefined, <User className="w-4 h-4" />)}
                    {getLabelValuePair('Emergency Phone', employee.emergencyContactPhone, undefined, <Phone className="w-4 h-4" />)}
                    {getLabelValuePair('Emergency Relationship', employee.emergencyContactRelationship, undefined, <User className="w-4 h-4" />)}
                  </div>
                </div>
              </div>

              {/* Family Information */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                  <h4 className="text-sm font-bold text-gray-900 dark:text-white flex items-center">
                    <User className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                    Family Information
                  </h4>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {getLabelValuePair('Spouse Name', employee.spouseName, undefined, <User className="w-4 h-4" />)}
                    {getLabelValuePair('Spouse Date of Birth', employee.spouseDateOfBirth, formatDate, <Calendar className="w-4 h-4" />)}
                    {getLabelValuePair('Spouse Occupation', employee.spouseOccupation, undefined, <Briefcase className="w-4 h-4" />)}
                    {getLabelValuePair('Spouse Employer', employee.spouseEmployer, undefined, <Home className="w-4 h-4" />)}
                    {getLabelValuePair('Spouse Contact Number', employee.spouseContactNumber, undefined, <Phone className="w-4 h-4" />)}
                    {getLabelValuePair('Spouse CNIC', employee.spouseCNIC, undefined, <FileText className="w-4 h-4" />)}
                    {getLabelValuePair('Children', Array.isArray(employee.children) ? employee.children.length : undefined, undefined, <User className="w-4 h-4" />)}
                    {getLabelValuePair('Dependents', Array.isArray(employee.dependents) ? employee.dependents.length : undefined, undefined, <User className="w-4 h-4" />)}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Education Tab Content */}
          {activeTab === 'education' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                <h4 className="text-base font-bold text-gray-900 dark:text-white flex items-center">
                  <GraduationCap className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Education Details
                </h4>
              </div>
              <div className="p-6">
                {Array.isArray(employee.educationEntries) && employee.educationEntries.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {employee.educationEntries.map((edu, idx) => (
                      <div key={idx} className="border border-gray-200 dark:border-gray-700 rounded-lg p-5 bg-white dark:bg-gray-800 shadow-sm flex flex-col gap-2">
                        <div className="flex items-center gap-2 mb-1">
                          <GraduationCap className="w-5 h-5 text-indigo-500 dark:text-indigo-400" />
                          <span className="font-bold text-lg text-gray-900 dark:text-white">{edu.degree || ''}</span>
                        </div>
                        <div className="flex items-center gap-2 mb-1">
                          <Home className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                          <span className="text-gray-700 dark:text-gray-300 font-medium">{edu.institution || ''}</span>
                        </div>
                        <div className="flex items-center gap-6 mt-2">
                          <div className="flex items-center gap-1">
                            <Target className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                            <span className="text-gray-600 dark:text-gray-400 text-sm">{edu.major || ''}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                            <span className="text-gray-600 dark:text-gray-400 text-sm">{edu.graduationYear || ''}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 dark:text-gray-400">No education records found.</div>
                )}
              </div>
            </div>
          )}

          {/* Experience Tab Content */}
          {activeTab === 'experience' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                <h4 className="text-base font-bold text-gray-900 dark:text-white flex items-center">
                  <Briefcase className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Experience Details
                </h4>
              </div>
              <div className="p-6">
                {(Array.isArray(employee.experienceEntries) && employee.experienceEntries.length > 0) || 
                  (Array.isArray(employee.experience) && employee.experience.length > 0) ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Use the first available experience array, preferring experienceEntries if both exist */}
                    {(Array.isArray(employee.experienceEntries) && employee.experienceEntries.length > 0
                      ? employee.experienceEntries
                      : employee.experience || []).map((exp: ExperienceEntry, idx: number) => (
                      <div key={idx} className="border border-gray-200 dark:border-gray-700 rounded-lg p-5 bg-white dark:bg-gray-800 shadow-sm flex flex-col gap-2">
                        <div className="flex items-center gap-2 mb-1">
                          <Briefcase className="w-5 h-5 text-indigo-500 dark:text-indigo-400" />
                          <span className="font-bold text-lg text-gray-900 dark:text-white">{exp.position || exp.jobTitle || ''}</span>
                        </div>
                        <div className="flex items-center gap-2 mb-1">
                          <Home className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                          <span className="text-gray-700 dark:text-gray-300 font-medium">{exp.company || exp.companyName || ''}</span>
                        </div>
                        <div className="flex items-center gap-6 mb-2">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                            <span className="text-gray-600 dark:text-gray-400 text-sm">
                              {exp.startDate ? formatDate(exp.startDate) : ''} - 
                              {exp.currentlyWorking ? 'Present' : (exp.endDate ? formatDate(exp.endDate) : 'Present')}
                            </span>
                          </div>
                        </div>
                        {(exp.responsibilities || exp.jobDescription) && (
                          <div className="mt-2">
                            <div className="text-xs font-bold text-gray-900 dark:text-white tracking-wider mb-1">Responsibilities</div>
                            <p className="text-sm text-gray-700 dark:text-gray-300">{exp.responsibilities || exp.jobDescription}</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 dark:text-gray-400">No experience records found.</div>
                )}
              </div>
            </div>
          )}

          {/* Skills Tab Content */}
          {activeTab === 'skills' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                <h4 className="text-base font-bold text-gray-900 dark:text-white flex items-center">
                  <Award className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Skills & Certifications
                </h4>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Professional Skills */}
                  <div>
                    <h5 className="text-md font-bold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                      <Briefcase className="h-5 w-5 mr-2 text-indigo-500 dark:text-indigo-400" />
                      Professional Skills
                    </h5>
                    {renderSkills(getSkillsFromAllSources('professionalSkills'), 'bg-indigo-50 dark:bg-indigo-900/30', 'text-indigo-500 dark:text-indigo-400')}
                  </div>
                  
                  {/* Technical Skills */}
                  <div>
                    <h5 className="text-md font-bold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                      </svg>
                      Technical Skills
                    </h5>
                    {renderSkills(getSkillsFromAllSources('technicalSkills'), 'bg-blue-50 dark:bg-blue-900/30', 'text-blue-500 dark:text-blue-400')}
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                  {/* Languages */}
                  <div>
                    <h5 className="text-md font-bold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-500 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                      </svg>
                      Languages
                    </h5>
                    {renderSkills(getSkillsFromAllSources('languages'), 'bg-green-50 dark:bg-green-900/30', 'text-green-500 dark:text-green-400')}
                  </div>
                  
                  {/* Certifications */}
                  <div>
                    <h5 className="text-md font-bold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                      <FileText className="h-5 w-5 mr-2 text-orange-500 dark:text-orange-400" />
                      Certifications
                    </h5>
                    <div className="grid grid-cols-1 gap-4">
                      {(() => {
                        const certifications = getAllCertifications();
                        
                        if (certifications.length === 0) {
                          return <div className="text-gray-500 dark:text-gray-400">None specified</div>;
                        }
                        
                        return certifications.map((cert, idx) => (
                          <div key={`cert-${idx}`} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800 shadow-sm">
                            <div className="flex items-center gap-2 mb-1">
                              <FileText className="w-4 h-4 text-orange-500 dark:text-orange-400" />
                              <span className="font-medium text-gray-900 dark:text-white">{cert.name}</span>
                            </div>
                            {(cert.issuedBy || cert.issueDate) && (
                              <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-2">
                                {cert.issuedBy && (
                                  <div className="flex items-center gap-1">
                                    <Home className="w-3 h-3" />
                                    <span>{cert.issuedBy}</span>
                                  </div>
                                )}
                                {cert.issueDate && (
                                  <div className="flex items-center gap-1">
                                    <Calendar className="w-3 h-3" />
                                    <span>{formatDate(cert.issueDate)}</span>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        ));
                      })()}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Projects Tab Content */}
          {activeTab === 'projects' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                <h4 className="text-base font-bold text-gray-900 dark:text-white flex items-center">
                  <Target className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Project Details
                </h4>
              </div>
              <div className="p-6">
                {(Array.isArray(employee.projectEntries) && employee.projectEntries.length > 0) || 
                  (Array.isArray(employee.projects) && employee.projects.length > 0) ? (
                  <div className="grid grid-cols-1 gap-6">
                    {(Array.isArray(employee.projectEntries) && employee.projectEntries.length > 0 
                      ? employee.projectEntries 
                      : employee.projects || []).map((project: ProjectEntry, idx: number) => (
                      <div key={idx} className="border rounded-lg p-5 bg-white shadow-sm">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                          <div className="flex items-center gap-2 mb-2 md:mb-0">
                            <Target className="w-5 h-5 text-indigo-500 flex-shrink-0" />
                            <span className="font-bold text-lg text-gray-900">{project.projectName || 'Unnamed Project'}</span>
                            {project.currentProject && (
                              <span className="ml-2 px-2.5 py-0.5 bg-green-100 text-green-800 text-xs rounded-full">
                                Current
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Calendar className="w-4 h-4 text-gray-400" />
                            <span>
                              {project.startDate ? formatDate(project.startDate) : 'Unknown start'} - 
                              {project.currentProject ? 'Present' : (project.endDate ? formatDate(project.endDate) : 'Present')}
                            </span>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                          <div>
                            <div className="text-xs font-bold text-gray-900 tracking-wider mb-1">Role</div>
                            <div className="text-sm font-medium text-gray-800">{project.role || 'Not specified'}</div>
                          </div>
                          <div>
                            <div className="text-xs font-bold text-gray-900 tracking-wider mb-1">Client</div>
                            <div className="text-sm font-medium text-gray-800">{project.clientName || 'Not specified'}</div>
                          </div>
                          <div>
                            <div className="text-xs font-bold text-gray-900 tracking-wider mb-1">Team Size</div>
                            <div className="text-sm font-medium text-gray-800">{project.teamSize || 'Not specified'}</div>
                          </div>
                          <div>
                            <div className="text-xs font-bold text-gray-900 tracking-wider mb-1">Technologies</div>
                            <div className="text-sm font-medium text-gray-800">{project.technologies || 'Not specified'}</div>
                          </div>
                        </div>

                        {project.description && (
                          <div className="mt-3">
                            <div className="text-xs font-bold text-gray-900 tracking-wider mb-1">Description</div>
                            <p className="text-sm text-gray-700">{project.description}</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500">No project records found.</div>
                )}
              </div>
            </div>
          )}

          {/* Job Tab Content */}
          {activeTab === 'job' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                <h4 className="text-base font-bold text-gray-900 dark:text-white flex items-center">
                  <Briefcase className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Job Details
                </h4>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {getLabelValuePair('Employee ID', employee.employeeId, undefined, <FileText className="w-4 h-4" />)}
                  {getLabelValuePair('Designation', employee.designation, undefined, <Briefcase className="w-4 h-4" />)}
                  {getLabelValuePair('Department', employee.department, undefined, <Briefcase className="w-4 h-4" />)}
                  {getLabelValuePair('Project', employee.project, undefined, <Target className="w-4 h-4" />)}
                  {getLabelValuePair('Location', typeof employee.location === 'string' ? employee.location : employee.location?.name, undefined, <Home className="w-4 h-4" />)}
                  {getLabelValuePair('Employment Type', employee.employmentType, undefined, <Briefcase className="w-4 h-4" />)}
                  {getLabelValuePair('Employment Status', employee.employmentStatus, undefined, <User className="w-4 h-4" />)}
                  {getLabelValuePair('Employee Level', employee.employeeLevel, undefined, <User className="w-4 h-4" />)}
                  {getLabelValuePair('Joining Date', employee.joinDate, formatDate, <Calendar className="w-4 h-4" />)}
                  {getLabelValuePair('Probation End Date', employee.probationEndDate, formatDate, <Calendar className="w-4 h-4" />)}
                  {getLabelValuePair('Notice Period', employee.noticePeriod, undefined, <Calendar className="w-4 h-4" />)}
                  {getLabelValuePair('Reporting To', employee.reportingTo, undefined, <User className="w-4 h-4" />)}
                  {getLabelValuePair('Confirmation Date', employee.confirmationDate, formatDate, <Calendar className="w-4 h-4" />)}
                  {getLabelValuePair('Contract End Date', employee.contractEndDate, formatDate, <Calendar className="w-4 h-4" />)}
                  {getLabelValuePair('Remote Work', employee.remoteWork ? 'Yes' : 'No', undefined, <Home className="w-4 h-4" />)}
                </div>
              </div>
            </div>
          )}

          {/* Compensation Tab Content */}
          {activeTab === 'compensation' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                <h4 className="text-base font-bold text-gray-900 dark:text-white flex items-center">
                  <DollarSign className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Compensation & Financial Details
                </h4>
              </div>
              
              {/* Salary Information */}
              <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                <h5 className="text-md font-bold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                  <DollarSign className="h-5 w-5 mr-2 text-indigo-500 dark:text-indigo-400" />
                  Salary Information
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {getLabelValuePair('Total Salary', employee.totalSalary, formatCurrency, <DollarSign className="w-4 h-4" />)}
                  
                  {/* Explicit handling for salary tier */}
                  <div>
                    <p className="text-xs font-bold text-gray-900 dark:text-white tracking-wider mb-1 flex items-center">
                      <DollarSign className="w-4 h-4" /> <span className="ml-2">Salary Tier</span>
                    </p>
                    <p className="text-sm text-gray-900 dark:text-gray-300">
                      {(employee.salaryTier && employee.salaryTier.trim() !== '') ? 
                        employee.salaryTier : 
                        (employee.benefit?.salaryTier ? 
                          employee.benefit.salaryTier : 
                          'Not specified'
                        )
                      }
                    </p>
                  </div>
                  
                  {/* Explicit handling for salary trench */}
                  <div>
                    <p className="text-xs font-bold text-gray-900 dark:text-white tracking-wider mb-1 flex items-center">
                      <DollarSign className="w-4 h-4" /> <span className="ml-2">Salary Trench</span>
                    </p>
                    <p className="text-sm text-gray-900 dark:text-gray-300">
                      {(employee.salaryTrench && employee.salaryTrench.trim() !== '') ? 
                        employee.salaryTrench : 
                        (employee.benefit?.salaryTrench ? 
                          employee.benefit.salaryTrench : 
                          'Not specified'
                        )
                      }
                    </p>
                  </div>
                  
                  {/* Explicit handling for payment mode */}
                  <div>
                    <p className="text-xs font-bold text-gray-900 dark:text-white tracking-wider mb-1 flex items-center">
                      <DollarSign className="w-4 h-4" /> <span className="ml-2">Payment Mode</span>
                    </p>
                    <p className="text-sm text-gray-900 dark:text-gray-300">
                      {(employee.paymentMode && employee.paymentMode.trim() !== '') ? 
                        employee.paymentMode : 
                        (employee.benefit?.paymentMode ? 
                          employee.benefit.paymentMode : 
                          'Not specified'
                        )
                      }
                    </p>
                  </div>
                  
                  {employee.cashAmount && getLabelValuePair('Cash Amount', employee.cashAmount, formatCurrency, <DollarSign className="w-4 h-4" />)}
                  {employee.bankAmount && getLabelValuePair('Bank Amount', employee.bankAmount, formatCurrency, <DollarSign className="w-4 h-4" />)}
                  {employee.salary && getLabelValuePair('Basic Salary', employee.salary, formatCurrency, <DollarSign className="w-4 h-4" />)}
                </div>
                
                {/* Salary Breakdown Section */}
                {employee.totalSalary && (
                  <div className="mt-6">
                    <h6 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                      <DollarSign className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                      Salary Breakdown (65%-20%-10%-5%)
                    </h6>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {(() => {
                        const breakdown = calculateSalaryBreakdown(employee.totalSalary);
                        return (
                          <>
                            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                              <div className="text-xs font-bold text-gray-900 dark:text-white mb-1">Basic Salary (65%)</div>
                              <div className="text-sm font-semibold text-gray-900 dark:text-gray-200">{formatCurrency(breakdown.basic)}</div>
                            </div>
                            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                              <div className="text-xs font-bold text-gray-900 dark:text-white mb-1">Housing Allowance (20%)</div>
                              <div className="text-sm font-semibold text-gray-900 dark:text-gray-200">{formatCurrency(breakdown.housing)}</div>
                            </div>
                            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                              <div className="text-xs font-bold text-gray-900 dark:text-white mb-1">Medical Allowance (10%)</div>
                              <div className="text-sm font-semibold text-gray-900 dark:text-gray-200">{formatCurrency(breakdown.medical)}</div>
                            </div>
                            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                              <div className="text-xs font-bold text-gray-900 dark:text-white mb-1">Other Allowances (5%)</div>
                              <div className="text-sm font-semibold text-gray-900 dark:text-gray-200">{formatCurrency(breakdown.other)}</div>
                            </div>
                          </>
                        );
                      })()}
                    </div>
                  </div>
                )}
              </div>
              
              {/* Bank Information */}
              <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                <h5 className="text-md font-bold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-indigo-500 dark:text-indigo-400" />
                  Bank Details
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {getLabelValuePair('Bank Name', 
                    (employee.bankName && employee.bankName.trim() !== '') ? 
                      employee.bankName : 
                      (employee.benefit?.bankName ? 
                        employee.benefit.bankName : 
                        'Not specified'
                      ), 
                    undefined, <Home className="w-4 h-4" />)}
                    
                  {getLabelValuePair('Bank Branch', 
                    (employee.bankBranch && employee.bankBranch.trim() !== '') ? 
                      employee.bankBranch : 
                      (employee.benefit?.bankBranch ? 
                        employee.benefit.bankBranch : 
                        'Not specified'
                      ), 
                    undefined, <Home className="w-4 h-4" />)}
                    
                  {getLabelValuePair('Account Number', 
                    (employee.accountNumber && employee.accountNumber.trim() !== '') ? 
                      employee.accountNumber : 
                      (employee.benefit?.accountNumber ? 
                        employee.benefit.accountNumber : 
                        'Not specified'
                      ), 
                    undefined, <FileText className="w-4 h-4" />)}
                    
                  {getLabelValuePair('Account Title', 
                    (employee.accountTitle && typeof employee.accountTitle === 'string' && employee.accountTitle.trim() !== '') ? 
                      employee.accountTitle : 
                      (employee.benefit && 'accountTitle' in employee.benefit ? 
                        employee.benefit.accountTitle as string : 
                        'Not specified'
                      ), 
                    undefined, <User className="w-4 h-4" />)}
                    
                  {getLabelValuePair('IBAN', 
                    (employee.iban && employee.iban.trim() !== '') ? 
                      employee.iban : 
                      (employee.benefit?.iban ? 
                        employee.benefit.iban : 
                        'Not specified'
                      ), 
                    undefined, <FileText className="w-4 h-4" />)}
                </div>
              </div>
              
              {/* Allowances */}
              <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                <h5 className="text-md font-bold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                  <Award className="h-5 w-5 mr-2 text-indigo-500 dark:text-indigo-400" />
                  Allowances
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {getLabelValuePair('Food Allowance in Salary', employee.foodAllowanceInSalary ? 'Yes' : 'No', undefined, <DollarSign className="w-4 h-4" />)}
                  {getLabelValuePair('Fuel Allowance in Salary', employee.fuelAllowanceInSalary ? 'Yes' : 'No', undefined, <Car className="w-4 h-4" />)}
                  {getLabelValuePair('Number of Meals', employee.numberOfMeals, undefined, <DollarSign className="w-4 h-4" />)}
                  {getLabelValuePair('Fuel in Liters', employee.fuelInLiters, undefined, <Car className="w-4 h-4" />)}
                  {getLabelValuePair('Fuel Amount', employee.fuelAmount, formatCurrency, <DollarSign className="w-4 h-4" />)}
                  {getLabelValuePair('Food Provided by Company', employee.foodProvidedByCompany ? 'Yes' : 'No', undefined, <DollarSign className="w-4 h-4" />)}
                </div>
              </div>
              
              {/* Health & Insurance */}
              <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                <h5 className="text-md font-bold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                  <Award className="h-5 w-5 mr-2 text-indigo-500 dark:text-indigo-400" />
                  Health & Insurance
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {getLabelValuePair('Health Insurance', employee.healthInsuranceProvider || (employee.benefit?.healthInsuranceProvider ? employee.benefit.healthInsuranceProvider : 'Not specified'), undefined, <Award className="w-4 h-4" />)}
                  {getLabelValuePair('Health Insurance Policy Number', employee.healthInsurancePolicyNumber || (employee.benefit?.healthInsurancePolicyNumber ? employee.benefit.healthInsurancePolicyNumber : 'Not specified'), undefined, <FileText className="w-4 h-4" />)}
                  {getLabelValuePair('Health Insurance Expiry Date', employee.healthInsuranceExpiryDate || (employee.benefit?.healthInsuranceExpiryDate ? employee.benefit.healthInsuranceExpiryDate : 'Not specified'), formatDate, <Calendar className="w-4 h-4" />)}
                  {getLabelValuePair('Life Insurance Provider', employee.lifeInsuranceProvider || (employee.benefit?.lifeInsuranceProvider ? employee.benefit.lifeInsuranceProvider : 'Not specified'), undefined, <Award className="w-4 h-4" />)}
                  {getLabelValuePair('Life Insurance Policy Number', employee.lifeInsurancePolicyNumber || (employee.benefit?.lifeInsurancePolicyNumber ? employee.benefit.lifeInsurancePolicyNumber : 'Not specified'), undefined, <FileText className="w-4 h-4" />)}
                  {getLabelValuePair('Life Insurance Expiry Date', employee.lifeInsuranceExpiryDate || (employee.benefit?.lifeInsuranceExpiryDate ? employee.benefit.lifeInsuranceExpiryDate : 'Not specified'), formatDate, <Calendar className="w-4 h-4" />)}
                </div>
              </div>
              
              {/* Accommodation */}
              <div className="p-6">
                <h5 className="text-md font-bold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                  <Home className="h-5 w-5 mr-2 text-indigo-500 dark:text-indigo-400" />
                  Accommodation Details
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {getLabelValuePair('Accommodation Provided', employee.accommodationProvidedByEmployer ? 'Yes' : 'No', undefined, <Home className="w-4 h-4" />)}
                  {getLabelValuePair('Accommodation Type', employee.accommodationType, undefined, <Home className="w-4 h-4" />)}
                  {getLabelValuePair('Accommodation Address', employee.accommodationAddress, undefined, <Home className="w-4 h-4" />)}
                  {getLabelValuePair('Accommodation Allowance', employee.accommodationAllowance, formatCurrency, <DollarSign className="w-4 h-4" />)}
                </div>
              </div>
            </div>
          )}

          {/* Assets Tab Content */}
          {activeTab === 'assets' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                <h4 className="text-base font-bold text-gray-900 dark:text-white flex items-center">
                  <Car className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Company Assets
                </h4>
              </div>
              
              {/* Vehicle Section if applicable */}
              {hasVehicleInfo && (
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h5 className="text-md font-bold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                    <Car className="h-5 w-5 mr-2 text-indigo-500 dark:text-indigo-400" />
                    Vehicle Information
                  </h5>
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-5 bg-white dark:bg-gray-800 shadow-sm">
                    <div className="flex items-center gap-2 mb-3">
                      <Car className="w-5 h-5 text-indigo-500 dark:text-indigo-400" />
                      <span className="font-bold text-lg text-gray-900 dark:text-white">
                        {employee.vehicleMakeModel || employee.vehicleType || 'Company Vehicle'}
                      </span>
                      {employee.providedByCompany && (
                        <span className="ml-2 px-2 py-0.5 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 text-xs font-medium rounded-full">
                          Company Provided
                        </span>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {employee.vehicleType && (
                        <div className="flex items-center gap-2">
                          <span className="text-xs font-bold text-gray-900 dark:text-white tracking-wider">Vehicle Type:</span>
                          <span className="text-sm text-gray-700 dark:text-gray-300">{employee.vehicleType}</span>
                        </div>
                      )}
                      {employee.registrationNumber && (
                        <div className="flex items-center gap-2">
                          <span className="text-xs font-bold text-gray-900 dark:text-white tracking-wider">Registration Number:</span>
                          <span className="text-sm text-gray-700 dark:text-gray-300">{employee.registrationNumber}</span>
                        </div>
                      )}
                      {employee.vehicleColor && (
                        <div className="flex items-center gap-2">
                          <span className="text-xs font-bold text-gray-900 dark:text-white tracking-wider">Color:</span>
                          <span className="text-sm text-gray-700 dark:text-gray-300">{employee.vehicleColor}</span>
                        </div>
                      )}
                      {employee.mileageAtIssuance && (
                        <div className="flex items-center gap-2">
                          <span className="text-xs font-bold text-gray-900 dark:text-white tracking-wider">Mileage at Issuance:</span>
                          <span className="text-sm text-gray-700 dark:text-gray-300">{employee.mileageAtIssuance}</span>
                        </div>
                      )}
                      {employee.handingOverDate && (
                        <div className="flex items-center gap-2">
                          <span className="text-xs font-bold text-gray-900 dark:text-white tracking-wider">Handover Date:</span>
                          <span className="text-sm text-gray-700 dark:text-gray-300">{formatDate(employee.handingOverDate)}</span>
                        </div>
                      )}
                      {employee.returnDate && (
                        <div className="flex items-center gap-2">
                          <span className="text-xs font-bold text-gray-900 dark:text-white tracking-wider">Return Date:</span>
                          <span className="text-sm text-gray-700 dark:text-gray-300">{formatDate(employee.returnDate)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
              
              {/* Devices Section */}
              <div className="p-6">
                <h5 className="text-md font-bold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                  <Briefcase className="h-5 w-5 mr-2 text-indigo-500 dark:text-indigo-400" />
                  Company Devices
                </h5>
                {Array.isArray(employee.deviceEntries) && employee.deviceEntries.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {employee.deviceEntries.map((device: DeviceEntry, idx: number) => (
                      <div key={idx} className="border border-gray-200 dark:border-gray-700 rounded-lg p-5 bg-white dark:bg-gray-800 shadow-sm flex flex-col gap-2">
                        <div className="flex items-center gap-2 mb-1">
                          <Briefcase className="w-5 h-5 text-indigo-500 dark:text-indigo-400" />
                          <span className="font-bold text-lg text-gray-900 dark:text-white">{device.deviceName || 'Unknown Device'}</span>
                        </div>
                        <div className="grid grid-cols-1 gap-2">
                          {device.makeModel && (
                            <div className="flex items-center gap-2">
                              <span className="text-xs font-bold text-gray-900 dark:text-white tracking-wider">Make & Model:</span>
                              <span className="text-sm text-gray-700 dark:text-gray-300">{device.makeModel}</span>
                            </div>
                          )}
                          {device.serialNumber && (
                            <div className="flex items-center gap-2">
                              <span className="text-xs font-bold text-gray-900 dark:text-white tracking-wider">Serial Number:</span>
                              <span className="text-sm text-gray-700 dark:text-gray-300">{device.serialNumber}</span>
                            </div>
                          )}
                          {device.handoverDate && (
                            <div className="flex items-center gap-2">
                              <span className="text-xs font-bold text-gray-900 dark:text-white tracking-wider">Handover Date:</span>
                              <span className="text-sm text-gray-700 dark:text-gray-300">{formatDate(device.handoverDate)}</span>
                            </div>
                          )}
                          {device.returnDate && (
                            <div className="flex items-center gap-2">
                              <span className="text-xs font-bold text-gray-900 dark:text-white tracking-wider">Return Date:</span>
                              <span className="text-sm text-gray-700 dark:text-gray-300">{formatDate(device.returnDate)}</span>
                            </div>
                          )}
                          {device.condition && (
                            <div className="flex items-center gap-2">
                              <span className="text-xs font-bold text-gray-900 dark:text-white tracking-wider">Condition:</span>
                              <span className="text-sm text-gray-700 dark:text-gray-300">{device.condition}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-gray-500 dark:text-gray-400">No company devices assigned to this employee.</div>
                )}
              </div>
            </div>
          )}

          {/* Documents Tab Content */}
          {activeTab === 'docs' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                <h4 className="text-base font-bold text-gray-900 dark:text-white flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Documents
                </h4>
              </div>
              <div className="p-6">
                <div className="mb-4 flex justify-between items-center">
                  <h5 className="text-sm font-bold text-gray-700 dark:text-gray-300">Employee Documents</h5>
                  <div className="flex items-center gap-2">
                    <span className="inline-block w-3 h-3 rounded-full bg-red-500"></span>
                    <span className="text-xs text-gray-600 dark:text-gray-400">Missing documents</span>
                    <span className="inline-block w-3 h-3 rounded-full bg-green-500 ml-3"></span>
                    <span className="text-xs text-gray-600 dark:text-gray-400">Uploaded documents</span>
                  </div>
                </div>
                
                {Array.isArray(employee.documentEntries) ? (
                  <div className="grid grid-cols-1 gap-4">
                    <DocumentsList 
                      employee={employee} 
                      formatDate={formatDate} 
                      refreshDocuments={refreshDocuments}
                      key={`docs-list-${refreshKey}`} // Add key to force re-render
                    />
                  </div>
                ) : (
                  <div className="text-gray-500">No documents found for this employee.</div>
                )}
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="mt-6 flex items-center justify-between">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              <span>Last updated: {employee.updatedAt ? formatDate(employee.updatedAt) : 'Not available'}</span>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded-md text-gray-800 dark:text-gray-200 text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-400 dark:focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors duration-200"
              >
                Close
              </button>
              <button
                onClick={() => {
                  console.log('Editing employee:', employee);
                  onEdit(employee);
                }}
                className="px-4 py-2 bg-indigo-600 dark:bg-indigo-700 rounded-md text-white text-sm font-medium hover:bg-indigo-700 dark:hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors duration-200 flex items-center"
              >
                <Edit className="h-4 w-4 mr-1.5" />
                Edit Details
              </button>
              <button
                onClick={() => {
                  onClose();
                  onDelete(employee);
                }}
                className="px-4 py-2 bg-red-600 dark:bg-red-700 rounded-md text-white text-sm font-medium hover:bg-red-700 dark:hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 dark:focus:ring-red-400 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors duration-200 flex items-center"
              >
                <Trash2 className="h-4 w-4 mr-1.5" />
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeDetailsView; 