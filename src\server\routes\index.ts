import express from 'express';
import authRoutes from './authRoutes';
import userRoutes from './userRoutes';
import ticketRoutes from './ticketRoutes';
import knowledgeRoutes from './knowledgeBaseRoutes';
import dashboardRoutes from './dashboardRoutes';
import assetRoutes from './assetRoutes';
import hrRoutes from './hrRoutes';
import employeeRoutes from './employeeRoutes';
import systemLogRoutes from './systemLogRoutes';
import vendorRoutes from './vendorRoutes';
import { AppDataSource } from '../config/database';
import roleRoutes from './roleRoutes';
import roleTemplateRoutes from './roleTemplateRoutes';
import permissionGroupRoutes from './permissionGroupRoutes';
import leavePolicyRoutes from '../../routes/leavePolicyRoutes';

const router = express.Router();

// Health check route
router.get('/health', (req, res) => {
  const healthStatus = {
    status: 'UP',
    timestamp: new Date().toISOString(),
    database: {
      status: AppDataSource.isInitialized ? 'UP' : 'DOWN'
    }
  };
  res.status(200).json(healthStatus);
});

// Database reconnect route
router.get('/database-reconnect', async (req, res) => {
  try {
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      res.status(200).json({ message: 'Database reconnected successfully' });
    } else {
      res.status(200).json({ message: 'Database is already connected' });
    }
  } catch (error) {
    console.error('Failed to reconnect to database:', error);
    res.status(500).json({ message: 'Failed to reconnect to database', error });
  }
});

// Fallback routes to bypass auth
router.get('/roles/fallback', (req, res) => {
  const mockRoles = [
    {
      id: 'role-1',
      name: 'IT Administrator',
      description: 'Full system access with all permissions across all modules',
      permissions: [
        'canAccessAllModules', 'canConfigureSystem', 'canManageRoles', 
        'canAddUsers', 'canEditUsers', 'canDeleteUsers', 'canViewReports', 
        'canExportData', 'canImportData', 'canViewAllDepartments',
        'canCreateTickets', 'canEditTickets', 'canDeleteTickets', 'canCloseTickets',
        'canCreateEmployee', 'canEditEmployee', 'canDeleteEmployee', 'canViewEmployees',
        'canViewDashboards', 'canViewAllDashboards', 'canCreateDashboards', 'canEditDashboards'
      ],
      createdAt: new Date().toISOString(),
      userCount: 1,
      category: 'system'
    },
    {
      id: 'role-2',
      name: 'IT Staff',
      description: 'Manage IT resources and tickets',
      permissions: ['canCreateTickets', 'canEditTickets', 'canCloseTickets'],
      createdAt: new Date().toISOString(),
      userCount: 5,
      category: 'system'
    },
    {
      id: 'role-3',
      name: 'Dashboard Manager',
      description: 'Full access to create and manage dashboards',
      permissions: [
        'canViewDashboards', 
        'canViewAllDashboards', 
        'canCreateDashboards', 
        'canEditDashboards', 
        'canShareDashboards', 
        'canExportDashboardData'
      ],
      createdAt: new Date().toISOString(),
      userCount: 2,
      category: 'dashboard'
    }
  ];
  
  res.json({ roles: mockRoles });
});

router.get('/permission-groups/fallback', (req, res) => {
  const mockPermissionGroups = [
    {
      id: 'group-admin',
      name: 'Administrative Permissions',
      description: 'Core system administration permissions',
      permissions: [
        'canAccessAllModules', 'canConfigureSystem', 'canManageRoles', 
        'canAddUsers', 'canEditUsers', 'canDeleteUsers'
      ],
      createdAt: new Date().toISOString()
    },
    {
      id: 'group-tickets',
      name: 'Ticket Management',
      description: 'Permissions for managing help desk tickets',
      permissions: [
        'canCreateTickets', 'canEditTickets', 'canDeleteTickets', 
        'canCloseTickets', 'canViewAllTickets'
      ],
      createdAt: new Date().toISOString()
    },
    {
      id: 'group-dashboards',
      name: 'Dashboard Access',
      description: 'Permissions for accessing and managing dashboards',
      permissions: [
        'canViewDashboards', 'canViewAllDashboards', 'canCreateDashboards', 
        'canEditDashboards', 'canShareDashboards'
      ],
      createdAt: new Date().toISOString()
    }
  ];
  
  res.json({ permissionGroups: mockPermissionGroups });
});

router.get('/role-templates/fallback', (req, res) => {
  const mockTemplates = [
    {
      id: 'template-it-admin',
      name: 'IT Administrator',
      description: 'Complete system access with all permissions',
      permissions: [
        'canAccessAllModules', 'canConfigureSystem', 'canManageRoles', 
        'canAddUsers', 'canEditUsers', 'canDeleteUsers', 'canViewReports'
      ],
      category: 'predefined',
      iconName: 'Shield',
      iconColor: 'text-red-600'
    },
    {
      id: 'template-helpdesk',
      name: 'Help Desk Agent',
      description: 'Permissions for help desk staff',
      permissions: [
        'canCreateTickets', 'canEditTickets', 'canCloseTickets', 
        'canViewAllTickets', 'canAssignTickets'
      ],
      category: 'predefined',
      iconName: 'Users',
      iconColor: 'text-blue-600'
    },
    {
      id: 'template-dashboard',
      name: 'Dashboard User',
      description: 'Access to view and interact with dashboards',
      permissions: [
        'canViewDashboards', 'canExportDashboardData'
      ],
      category: 'predefined',
      iconName: 'BarChart2',
      iconColor: 'text-purple-600'
    }
  ];
  
  res.json({ templates: mockTemplates });
});

// Mount API routes
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/tickets', ticketRoutes);
router.use('/knowledge', knowledgeRoutes);
router.use('/dashboard', dashboardRoutes);
router.use('/assets', assetRoutes);
router.use('/hr', hrRoutes);
router.use('/employees', employeeRoutes);
router.use('/system-logs', systemLogRoutes);
router.use('/vendors', vendorRoutes);
router.use('/roles', roleRoutes);
router.use('/role-templates', roleTemplateRoutes);
router.use('/permission-groups', permissionGroupRoutes);
router.use('/leave-policies', leavePolicyRoutes);

export default router; 