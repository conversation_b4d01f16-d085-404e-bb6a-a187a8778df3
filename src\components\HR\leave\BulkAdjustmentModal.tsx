import React, { useState } from 'react';
import { X, Users, User, Globe } from 'lucide-react';

interface LeaveBalance {
  employeeId: number;
  leaveType: string;
  total: number;
  used: number;
  pending: number;
  remaining: number;
}

interface EmployeeLeaveData {
  employeeId: number;
  employeeName: string;
  employeeCode: string;
  department: string;
  position: string;
  leaveBalances: LeaveBalance[];
  recentRequests: any[];
  totalDaysUsed: number;
  totalDaysRemaining: number;
}

interface BulkAdjustmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedEmployees: number[];
  allEmployees: EmployeeLeaveData[];
  configuredLeaveTypes: any[];
  adjustmentType: 'individual' | 'group' | 'all';
  onAdjustmentTypeChange: (type: 'individual' | 'group' | 'all') => void;
  onSuccess: () => void;
}

const BulkAdjustmentModal: React.FC<BulkAdjustmentModalProps> = ({
  isOpen,
  onClose,
  selectedEmployees,
  allEmployees,
  configuredLeaveTypes,
  adjustmentType,
  onAdjustmentTypeChange,
  onSuccess
}) => {
  const [adjustments, setAdjustments] = useState<Record<string, number>>({});
  const [adjustmentMode, setAdjustmentMode] = useState<'set' | 'add' | 'subtract'>('set');
  const [adjustmentReason, setAdjustmentReason] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  if (!isOpen) return null;

  const getTargetEmployees = () => {
    console.log('🔍 Getting target employees:', {
      adjustmentType,
      selectedEmployees,
      allEmployeesCount: allEmployees.length,
      allEmployees: allEmployees.map(emp => ({ id: emp.employeeId, name: emp.employeeName, dept: emp.department }))
    });

    switch (adjustmentType) {
      case 'individual':
        const individualEmployees = allEmployees.filter(emp => selectedEmployees.includes(emp.employeeId));
        console.log('👤 Individual employees:', individualEmployees);
        return individualEmployees;
      case 'group':
        // Get all employees from the same departments as selected employees
        const selectedDepartments = allEmployees
          .filter(emp => selectedEmployees.includes(emp.employeeId))
          .map(emp => emp.department);
        console.log('🏢 Selected departments:', selectedDepartments);
        const groupEmployees = allEmployees.filter(emp => selectedDepartments.includes(emp.department));
        console.log('👥 Group employees:', groupEmployees);
        return groupEmployees;
      case 'all':
        console.log('🌍 All employees:', allEmployees);
        return allEmployees;
      default:
        console.log('❌ Unknown adjustment type:', adjustmentType);
        return [];
    }
  };

  const targetEmployees = getTargetEmployees();

  const handleAdjustmentChange = (leaveType: string, value: string) => {
    const numValue = parseInt(value) || 0;
    setAdjustments(prev => ({
      ...prev,
      [leaveType]: numValue
    }));
  };

  const handleApplyAdjustments = async () => {
    try {
      setIsProcessing(true);
      
      // Prepare adjustment data
      const adjustmentData = {
        employeeIds: targetEmployees.map(emp => emp.employeeId),
        adjustments: Object.entries(adjustments).map(([leaveType, value]) => ({
          leaveType,
          adjustmentType: adjustmentMode,
          value: value
        })),
        reason: adjustmentReason
      };

      // Make API call to apply bulk adjustments
      const response = await fetch('/api/leave-balances/bulk-adjust', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(adjustmentData)
      });

      const result = await response.json();

      if (result.success) {
        onSuccess();
        onClose();
      } else {
        console.error('Bulk adjustment failed:', result.message);
      }
    } catch (error) {
      console.error('Error applying bulk adjustments:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-gray-900">Leave Quota Adjustment</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Adjustment Type Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">Adjustment Scope</label>
          <div className="flex space-x-4">
            <button
              onClick={() => onAdjustmentTypeChange('individual')}
              className={`flex items-center px-4 py-2 rounded-lg border ${
                adjustmentType === 'individual'
                  ? 'bg-blue-50 border-blue-200 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-600 hover:bg-gray-50'
              }`}
            >
              <User className="h-4 w-4 mr-2" />
              Selected ({selectedEmployees.length})
            </button>
            <button
              onClick={() => onAdjustmentTypeChange('group')}
              className={`flex items-center px-4 py-2 rounded-lg border ${
                adjustmentType === 'group'
                  ? 'bg-blue-50 border-blue-200 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-600 hover:bg-gray-50'
              }`}
            >
              <Users className="h-4 w-4 mr-2" />
              Department Group
            </button>
            <button
              onClick={() => onAdjustmentTypeChange('all')}
              className={`flex items-center px-4 py-2 rounded-lg border ${
                adjustmentType === 'all'
                  ? 'bg-blue-50 border-blue-200 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-600 hover:bg-gray-50'
              }`}
            >
              <Globe className="h-4 w-4 mr-2" />
              All Employees ({allEmployees.length})
            </button>
          </div>
        </div>

        {/* Adjustment Mode */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">Adjustment Mode</label>
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                name="adjustmentMode"
                value="set"
                checked={adjustmentMode === 'set'}
                onChange={(e) => setAdjustmentMode(e.target.value as 'set' | 'add' | 'subtract')}
                className="mr-2"
              />
              Set to value
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="adjustmentMode"
                value="add"
                checked={adjustmentMode === 'add'}
                onChange={(e) => setAdjustmentMode(e.target.value as 'set' | 'add' | 'subtract')}
                className="mr-2"
              />
              Add to current
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="adjustmentMode"
                value="subtract"
                checked={adjustmentMode === 'subtract'}
                onChange={(e) => setAdjustmentMode(e.target.value as 'set' | 'add' | 'subtract')}
                className="mr-2"
              />
              Subtract from current
            </label>
          </div>
        </div>

        {/* Target Employees Summary */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-2">
            Target: {targetEmployees.length} employees
          </h4>
          <div className="text-xs text-gray-600 max-h-20 overflow-y-auto">
            {targetEmployees.slice(0, 10).map(emp => emp.employeeName).join(', ')}
            {targetEmployees.length > 10 && ` and ${targetEmployees.length - 10} more...`}
          </div>
        </div>

        {/* Leave Type Adjustments */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Leave Type Adjustments</h4>
          <div className="grid grid-cols-2 gap-4">
            {configuredLeaveTypes.map((leaveType) => (
              <div key={leaveType.leaveType} className="flex items-center space-x-3">
                <label className="text-sm text-gray-600 w-24 flex-shrink-0">
                  {leaveType.displayName}:
                </label>
                <input
                  type="number"
                  min="0"
                  max="365"
                  value={adjustments[leaveType.leaveType] || ''}
                  onChange={(e) => handleAdjustmentChange(leaveType.leaveType, e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0"
                />
                <span className="text-xs text-gray-500">days</span>
              </div>
            ))}
          </div>
        </div>

        {/* Adjustment Reason */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">Adjustment Reason</label>
          <textarea
            value={adjustmentReason}
            onChange={(e) => setAdjustmentReason(e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter the reason for the adjustment"
            rows={3}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            disabled={isProcessing}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={() => setShowPreview(!showPreview)}
            disabled={Object.keys(adjustments).length === 0}
            className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {showPreview ? 'Hide Preview' : 'Preview Changes'}
          </button>
          <button
            onClick={handleApplyAdjustments}
            disabled={isProcessing || Object.keys(adjustments).length === 0 || !adjustmentReason.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? 'Applying...' : `Apply to ${targetEmployees.length} employees`}
          </button>
        </div>

        {/* Preview Section */}
        {showPreview && Object.keys(adjustments).length > 0 && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg border">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Preview of Changes</h4>
            <div className="max-h-40 overflow-y-auto">
              <table className="min-w-full text-xs">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-1">Employee</th>
                    {Object.keys(adjustments).map(leaveType => (
                      <th key={leaveType} className="text-left py-1">{leaveType}</th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {targetEmployees.slice(0, 10).map(employee => (
                    <tr key={employee.employeeId} className="border-b">
                      <td className="py-1">{employee.employeeName}</td>
                      {Object.entries(adjustments).map(([leaveType, value]) => {
                        const currentBalance = employee.leaveBalances.find(b => b.leaveType === leaveType);
                        const currentTotal = Math.round(currentBalance?.total || 0);
                        let newTotal = 0;
                        
                        switch (adjustmentMode) {
                          case 'set':
                            newTotal = value;
                            break;
                          case 'add':
                            newTotal = currentTotal + value;
                            break;
                          case 'subtract':
                            newTotal = Math.max(0, currentTotal - value);
                            break;
                        }
                        
                        return (
                          <td key={leaveType} className="py-1">
                            {currentTotal} → {Math.round(newTotal)}
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                  {targetEmployees.length > 10 && (
                    <tr>
                      <td colSpan={Object.keys(adjustments).length + 1} className="py-1 text-gray-500 italic">
                        ... and {targetEmployees.length - 10} more employees
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BulkAdjustmentModal; 