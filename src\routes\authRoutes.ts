import express from 'express';
import { register, login, logout, getCurrentUser, refreshToken } from '../controllers/authController';
import { requireAuth } from '../middleware/auth';

const router = express.Router();

// Public routes
router.post('/register', register);
router.post('/login', login);
router.post('/logout', logout);
router.post('/refresh', refreshToken);

// Protected routes
router.get('/me', requireAuth, getCurrentUser);

export default router; 