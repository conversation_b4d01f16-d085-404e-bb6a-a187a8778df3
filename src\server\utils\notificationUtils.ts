import { Server as SocketIOServer } from 'socket.io';
import logger from '../../utils/logger';

export interface NotificationData {
  id: string;
  type: 'ticket' | 'escalation' | 'email' | 'system';
  title: string;
  message: string;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
  link?: string;
  ticketData?: {
    ticketId: string;
    subject: string;
    status?: string;
    priority?: string;
    department?: string;
    assignedTo?: string;
    updatedBy?: string;
  };
}

/**
 * Send a notification to a specific user
 * @param io Socket.IO server instance
 * @param userId User ID to send the notification to
 * @param notification Notification data
 */
export const sendNotificationToUser = (io: SocketIOServer, userId: string, notification: NotificationData) => {
  try {
    logger.info(`Attempting to send notification to user ${userId}`, { 
      notificationId: notification.id,
      type: notification.type,
      title: notification.title
    });

    // Check if io is properly initialized
    if (!io) {
      logger.error('Socket.IO instance is not initialized');
      return;
    }

    // Get the notifications namespace
    const namespace = io.of('/notifications');
    
    // Log connected clients for debugging
    const connectedClients = Array.from(namespace.sockets.keys()).length;
    logger.info(`Connected clients in notifications namespace: ${connectedClients}`);
    
    // Log rooms for debugging
    const rooms = namespace.adapter.rooms;
    const userRoom = `user:${userId}`;
    const isRoomExists = rooms.has(userRoom);
    logger.info(`Room ${userRoom} exists: ${isRoomExists}`);
    
    // Send to both events to ensure compatibility
    namespace.to(`user:${userId}`).emit('notification', notification);
    namespace.to(`user:${userId}`).emit('ticket:update', notification);
    
    logger.info(`Notification sent to user ${userId}`, { 
      notificationId: notification.id,
      type: notification.type,
      title: notification.title
    });
  } catch (error) {
    logger.error('Error sending notification to user', { userId, error });
  }
};

/**
 * Broadcast a notification to all connected clients
 * @param io Socket.IO server instance
 * @param notification Notification data
 */
export const broadcastNotification = (io: SocketIOServer, notification: NotificationData) => {
  try {
    const namespace = io.of('/notifications');
    namespace.emit('notification', notification);
    namespace.emit('ticket:update', notification);
    
    logger.info('Notification broadcasted to all users', { 
      notificationId: notification.id,
      type: notification.type,
      title: notification.title
    });
  } catch (error) {
    logger.error('Error broadcasting notification', { error });
  }
};

/**
 * Send a notification about a ticket being created
 * @param io Socket.IO server instance
 * @param userId User ID to send the notification to
 * @param ticketId Ticket ID
 * @param ticketTitle Ticket title
 * @param ticketNumber Ticket number (e.g., T101)
 */
export const sendTicketCreatedNotification = (
  io: SocketIOServer, 
  userId: string, 
  ticketId: string, 
  ticketTitle: string,
  ticketNumber?: string
) => {
  const notificationId = `ticket-created-${ticketId}-${Date.now()}`;
  const displayNumber = ticketNumber || ticketId;
  
  const notification: NotificationData = {
    id: notificationId,
    type: 'ticket',
    title: 'New Ticket Created',
    message: `Ticket #${displayNumber}: ${ticketTitle} has been created`,
    read: false,
    priority: 'low',
    link: `/tickets/${ticketId}`,
    ticketData: {
      ticketId,
      subject: ticketTitle
    }
  };
  
  sendNotificationToUser(io, userId, notification);
};

/**
 * Send a notification about a ticket being assigned
 * @param io Socket.IO server instance
 * @param userId User ID to send the notification to
 * @param ticketId Ticket ID
 * @param ticketTitle Ticket title
 * @param assignedBy User who assigned the ticket
 */
export const sendTicketAssignedNotification = (
  io: SocketIOServer, 
  userId: string, 
  ticketId: string, 
  ticketTitle: string,
  assignedBy: string
) => {
  const notification: NotificationData = {
    id: Math.random().toString(36).substring(2, 15),
    type: 'ticket',
    title: 'Ticket Assigned',
    message: `Ticket #${ticketId}: ${ticketTitle} has been assigned to you by ${assignedBy}`,
    read: false,
    priority: 'medium',
    link: `/tickets/${ticketId}`,
    ticketData: {
      ticketId,
      subject: ticketTitle,
      updatedBy: assignedBy
    }
  };
  
  sendNotificationToUser(io, userId, notification);
};

/**
 * Send a notification about a ticket being escalated
 * @param io Socket.IO server instance
 * @param userId User ID to send the notification to
 * @param ticketId Ticket ID
 * @param ticketTitle Ticket title
 * @param escalatedBy User who escalated the ticket
 */
export const sendTicketEscalatedNotification = (
  io: SocketIOServer, 
  userId: string, 
  ticketId: string, 
  ticketTitle: string,
  escalatedBy: string
) => {
  const notification: NotificationData = {
    id: Math.random().toString(36).substring(2, 15),
    type: 'escalation',
    title: 'Ticket Escalated',
    message: `Ticket #${ticketId}: ${ticketTitle} has been escalated by ${escalatedBy}`,
    read: false,
    priority: 'high',
    link: `/tickets/${ticketId}`,
    ticketData: {
      ticketId,
      subject: ticketTitle,
      updatedBy: escalatedBy
    }
  };
  
  sendNotificationToUser(io, userId, notification);
};

/**
 * Send a system notification to a user
 * @param io Socket.IO server instance
 * @param userId User ID to send the notification to
 * @param title Notification title
 * @param message Notification message
 * @param priority Notification priority
 */
export const sendSystemNotification = (
  io: SocketIOServer, 
  userId: string, 
  title: string, 
  message: string,
  priority: 'low' | 'medium' | 'high' | 'critical' = 'low'
) => {
  const notificationId = `system-${userId}-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
  const notification: NotificationData = {
    id: notificationId,
    type: 'system',
    title,
    message,
    read: false,
    priority
  };
  
  sendNotificationToUser(io, userId, notification);
};

/**
 * Send a notification about a ticket status being changed to IN_PROGRESS
 * @param io Socket.IO server instance
 * @param userId User ID to send the notification to (ticket creator)
 * @param ticketId Ticket ID
 * @param ticketTitle Ticket title
 * @param ticketNumber Ticket number
 * @param agentName Name of the agent who started working on the ticket
 */
export const sendTicketInProgressNotification = (
  io: SocketIOServer, 
  userId: string, 
  ticketId: string, 
  ticketTitle: string,
  ticketNumber: string,
  agentName: string
) => {
  const notificationId = `ticket-in-progress-${ticketId}-${Date.now()}`;
  
  const notification: NotificationData = {
    id: notificationId,
    type: 'ticket',
    title: 'Ticket In Progress',
    message: `Your ticket #${ticketNumber}: ${ticketTitle} is now being worked on by ${agentName}`,
    read: false,
    priority: 'medium',
    link: `/tickets/${ticketId}`,
    ticketData: {
      ticketId,
      subject: ticketTitle,
      status: 'IN_PROGRESS',
      assignedTo: agentName
    }
  };
  
  sendNotificationToUser(io, userId, notification);
}; 