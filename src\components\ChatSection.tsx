import React, { useState, useEffect, useRef, useCallback, Fragment } from 'react';
import { 
  UserIcon, FileText, Download, Eye, X, Calendar, 
  Building, Tag, AlertCircle, Clock, ChevronRight,
  Image as ImageIcon, FileIcon, Video, MoreVertical, Clipboard
} from 'lucide-react';
import RichTextEditor from './RichTextEditor';
import { socketService } from '../services/socket';
import { Marked } from 'marked';
import DOMPurify from 'dompurify';
import { Comment, UserRole, TicketStatus, TicketPriority, ExtendedComment } from '../types/common';
import { toast } from 'react-hot-toast';
import { Menu, Transition } from '@headlessui/react';

interface TicketDetails {
  id: number;
  ticketNumber: string;
  title: string;
  description: string;
  status: TicketStatus;
  priority: TicketPriority;
  category: string;
  department: string;
  createdAt: string;
  createdBy: {
    id: string;
    name: string;
    email: string;
    department: string;
    role: string;
  };
  assignedTo?: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  lockedBy?: {
    id: string;
    name: string;
    email: string;
    role: string;
  } | null;
  lockedById?: string | null;
}

interface FileViewerProps {
  fileUrl: string;
  fileName: string;
  fileType: string;
  onClose: () => void;
}

const marked = new Marked();

const FileViewer: React.FC<FileViewerProps> = ({ fileUrl, fileName, fileType, onClose }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const getFileIcon = () => {
    if (fileType.startsWith('image/')) return <ImageIcon className="w-12 h-12 text-blue-500" />;
    if (fileType.startsWith('video/')) return <Video className="w-12 h-12 text-purple-500" />;
    return <FileIcon className="w-12 h-12 text-gray-500" />;
  };

  const getFileContent = () => {
    if (fileType.startsWith('image/')) {
      return (
        <div className="relative max-h-[80vh] flex items-center justify-center bg-gray-900 rounded-lg overflow-hidden">
          <img
            src={fileUrl}
            alt={fileName}
            className="max-w-full max-h-[80vh] object-contain"
            onLoad={() => setIsLoading(false)}
            onError={(e) => {
              console.error('Error loading image:', fileUrl);
              setError('Failed to load image');
              setIsLoading(false);
            }}
          />
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
            </div>
          )}
          {error && (
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-900 text-white">
              <AlertCircle className="w-12 h-12 mb-2" />
              <p>{error}</p>
            </div>
          )}
        </div>
      );
    }

    if (fileType.startsWith('video/')) {
      return (
        <div className="relative max-h-[80vh] bg-black rounded-lg overflow-hidden">
          <video
            controls
            className="max-w-full max-h-[80vh]"
            src={fileUrl}
            onLoadStart={() => setIsLoading(true)}
            onLoadedData={() => setIsLoading(false)}
            onError={() => {
              setError('Failed to load video');
              setIsLoading(false);
            }}
          >
            Your browser does not support the video tag.
          </video>
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
            </div>
          )}
        </div>
      );
    }

    // For other file types, show a download prompt
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-white rounded-lg">
        {getFileIcon()}
        <p className="text-lg font-medium mt-4 mb-2">{fileName}</p>
        <p className="text-sm text-gray-500 mb-4">{fileType}</p>
        <a
          href={fileUrl}
          download={fileName}
          className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          <Download className="w-4 h-4" />
          Download File
        </a>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="relative max-w-4xl w-full bg-white rounded-lg overflow-hidden">
        <div className="absolute top-4 right-4 z-10">
          <button
            onClick={onClose}
            className="p-2 bg-black bg-opacity-50 hover:bg-opacity-75 rounded-full text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="p-4 bg-gray-900">
          {getFileContent()}
        </div>
        
        <div className="flex items-center justify-between p-4 bg-white border-t">
          <p className="font-medium text-gray-900 truncate max-w-[60%]">{fileName}</p>
          <a
            href={fileUrl}
            download={fileName}
            className="flex items-center gap-2 px-3 py-1.5 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            onClick={(e) => e.stopPropagation()}
          >
            <Download className="w-4 h-4" />
            Download
          </a>
        </div>
      </div>
    </div>
  );
};

const TicketHeader: React.FC<{ ticket: TicketDetails }> = ({ ticket }) => {
  const getPriorityColor = (priority: TicketPriority) => {
    switch (priority) {
      case TicketPriority.CRITICAL:
        return 'bg-red-100 text-red-700';
      case TicketPriority.HIGH:
        return 'bg-orange-100 text-orange-700';
      case TicketPriority.MEDIUM:
        return 'bg-yellow-100 text-yellow-700';
      case TicketPriority.LOW:
        return 'bg-green-100 text-green-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusColor = (status: TicketStatus) => {
    switch (status) {
      case TicketStatus.OPEN:
        return 'bg-blue-100 text-blue-700';
      case TicketStatus.IN_PROGRESS:
        return 'bg-yellow-100 text-yellow-700';
      case TicketStatus.RESOLVED:
        return 'bg-green-100 text-green-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  return (
    <div className="bg-white border-b p-4 space-y-4">
      {/* Ticket Title and Number */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-xl font-semibold">{ticket.title}</h1>
          <p className="text-sm text-gray-500">Ticket #{ticket.ticketNumber}</p>
        </div>
        <div className="flex gap-2">
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(ticket.priority)}`}>
            {ticket.priority}
          </span>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(ticket.status)}`}>
            {ticket.status}
          </span>
        </div>
      </div>

      {/* Ticket Details Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Created By */}
        <div className="flex items-center gap-2">
          <UserIcon className="w-5 h-5 text-gray-400" />
          <div>
            <p className="text-sm text-gray-500">Created By</p>
            <p className="text-sm font-medium">{ticket.createdBy.name}</p>
          </div>
        </div>

        {/* Department */}
        <div className="flex items-center gap-2">
          <Building className="w-5 h-5 text-gray-400" />
          <div>
            <p className="text-sm text-gray-500">Department</p>
            <p className="text-sm font-medium">{ticket.department}</p>
          </div>
        </div>

        {/* Category */}
        <div className="flex items-center gap-2">
          <Tag className="w-5 h-5 text-gray-400" />
          <div>
            <p className="text-sm text-gray-500">Category</p>
            <p className="text-sm font-medium">{ticket.category}</p>
          </div>
        </div>

        {/* Created At */}
        <div className="flex items-center gap-2">
          <Calendar className="w-5 h-5 text-gray-400" />
          <div>
            <p className="text-sm text-gray-500">Created On</p>
            <p className="text-sm font-medium">
              {new Date(ticket.createdAt).toLocaleDateString()}
            </p>
          </div>
        </div>
      </div>

      {/* Assigned To (if any) */}
      {ticket.assignedTo && (
        <div className="flex items-center gap-2 pt-2 border-t">
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <Clock className="w-4 h-4" />
            <span>Assigned to</span>
            <ChevronRight className="w-4 h-4" />
          </div>
          <div className="flex items-center gap-2">
            <span className="font-medium">{ticket.assignedTo.name}</span>
            <span className="text-sm text-gray-500">({ticket.assignedTo.role})</span>
          </div>
        </div>
      )}

      {/* Description */}
      <div className="pt-2 border-t">
        <p className="text-sm text-gray-500 mb-1">Description</p>
        <p className="text-sm whitespace-pre-wrap">{ticket.description}</p>
      </div>
    </div>
  );
};

interface ChatSectionProps {
  ticketId: number;
  comments: Comment[];
  currentUser: {
    id: string;
    name: string;
    role: UserRole;
  } | null;
  onAddComment: (content: string, files: File[]) => Promise<void>;
  status: string;
  canAddComment: boolean;
  ticket: TicketDetails;
  hideHeader?: boolean;
}

export const ChatSection: React.FC<ChatSectionProps> = ({
  ticketId,
  comments,
  currentUser,
  onAddComment,
  status,
  canAddComment,
  ticket,
  hideHeader = false
}) => {
  const [typingUsers, setTypingUsers] = useState<{[key: string]: string}>({});
  const [onlineUsers, setOnlineUsers] = useState<{[key: string]: string}>({});
  const [zoomedImage, setZoomedImage] = useState<{url: string, fileName: string} | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const commentContainerRef = useRef<HTMLDivElement>(null);
  const socketInitialized = useRef(false);
  const [selectedFile, setSelectedFile] = useState<{
    url: string;
    fileName: string;
    fileType: string;
  } | null>(null);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    
    if (!e.dataTransfer.files.length || !canAddComment) return;

    try {
      const files = Array.from(e.dataTransfer.files);
      const fileNames = files.map(f => f.name).join(', ');
      const content = `Uploaded files: ${fileNames}`;
      await onAddComment(content, files);
    } catch (error) {
      console.error('Error handling file drop:', error);
      toast.error('Failed to upload files');
    }
  }, [canAddComment, onAddComment]);

  useEffect(() => {
    if (commentContainerRef.current) {
      commentContainerRef.current.scrollTop = commentContainerRef.current.scrollHeight;
    }
  }, [comments]);

  const renderComments = () => {
    if (!comments?.length) return null;

    const uniqueComments = comments.reduce((acc: Comment[], current) => {
      if (!acc.some(item => String(item.id) === String(current.id))) {
        acc.push(current);
      }
      return acc;
    }, []);

    return (
      <div className="flex flex-col space-y-4">
        {uniqueComments.map((comment: ExtendedComment, index: number) => {
          if (!comment || !comment.createdBy) return null;
          
          const isCurrentUser = currentUser && comment.createdBy.id === currentUser.id;
          const isTemporary = typeof comment.id === 'string' && comment.id.startsWith('temp-');
          const isSystemComment = comment.isSystemComment;
          
          if (isSystemComment) {
            return (
              <div key={`${comment.id}-${index}`} className="flex justify-center">
                <div className="bg-gray-100 text-gray-600 rounded-full px-4 py-1 text-sm">
                  {comment.content}
                </div>
              </div>
            );
          }
          
          return (
            <div 
              key={`${comment.id}-${index}`}
              className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'} ${isTemporary ? 'opacity-70' : ''}`}
            >
              <div 
                className={`max-w-[70%] ${
                  isCurrentUser ? 'bg-blue-500 text-white' : 'bg-gray-100'
                } rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow`}
              >
                <div className="flex items-center justify-between gap-3 mb-2">
                  <div className="flex items-center gap-2">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      isCurrentUser ? 'bg-blue-600' : 'bg-gray-200'
                    }`}>
                      <UserIcon className={`w-5 h-5 ${
                        isCurrentUser ? 'text-white' : 'text-gray-600'
                      }`} />
                    </div>
                    <div>
                      <span className="font-medium text-sm">
                        {comment.createdBy.name || 'Unknown User'}
                      </span>
                      <div className="text-xs opacity-75">
                        {new Date(comment.createdAt).toLocaleString()}
                      </div>
                    </div>
                  </div>
                  
                  {!isTemporary && (
                    <Menu as="div" className="relative">
                      <Menu.Button className="p-1 rounded-full hover:bg-opacity-10 hover:bg-black focus:outline-none">
                        <MoreVertical className="w-4 h-4 opacity-70" />
                      </Menu.Button>
                      <Transition
                        as={Fragment}
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95"
                      >
                        <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                          <Menu.Item>
                            {({ active }) => (
                              <button
                                className={`${
                                  active ? 'bg-gray-100' : ''
                                } flex w-full items-center px-4 py-2 text-sm text-gray-700`}
                                onClick={() => {
                                  // Copy comment text to clipboard
                                  navigator.clipboard.writeText(comment.content);
                                  toast.success('Comment copied to clipboard');
                                }}
                              >
                                <Clipboard className="mr-2 h-4 w-4" />
                                Copy text
                              </button>
                            )}
                          </Menu.Item>
                        </Menu.Items>
                      </Transition>
                    </Menu>
                  )}
                </div>
                
                <div className="prose prose-sm max-w-none">
                  {comment.content.split('\n').map((line, i) => (
                    <p key={i}>{line}</p>
                  ))}
                </div>
                
                {/* Always show attachments regardless of ticket status */}
                {comment.attachments && comment.attachments.length > 0 && (
                  <div className="mt-3 space-y-2">
                    {comment.attachments.map((attachment, attachIndex) => {
                      if (!attachment) return null;

                      const isImage = attachment.fileType?.startsWith('image/');

                      if (isImage) {
                        return (
                          <div key={`${attachment.id}-${attachIndex}`} className="relative bg-black rounded-lg overflow-hidden">
                            <img
                              src={getFileUrl(attachment.fileUrl)}
                              alt={attachment.fileName}
                              className="w-full h-auto cursor-pointer mx-auto object-contain"
                              style={{ 
                                maxHeight: '200px', 
                                minHeight: '120px',
                                backgroundColor: 'black',
                                display: 'block'
                              }}
                              onClick={() => setSelectedFile({
                                url: getFileUrl(attachment.fileUrl),
                                fileName: attachment.fileName,
                                fileType: attachment.fileType
                              })}
                              onError={(e) => {
                                console.error('Error loading image:', attachment.fileUrl, e);
                                const img = e.currentTarget;
                                if (!img.dataset.retryCount || parseInt(img.dataset.retryCount) < 3) {
                                  img.dataset.retryCount = String((parseInt(img.dataset.retryCount || '0') + 1));
                                  img.src = `${getFileUrl(attachment.fileUrl)}?t=${Date.now()}`;
                                } else {
                                  img.src = '/placeholder-image.svg';
                                }
                              }}
                              loading="lazy"
                            />
                            <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-sm p-2 truncate">
                              {attachment.fileName}
                            </div>
                          </div>
                        );
                      }

                      return (
                        <a 
                          key={`${attachment.id}-${attachIndex}`}
                          href={getFileUrl(attachment.fileUrl)}
                          download={attachment.fileName}
                          className={`flex items-center gap-2 p-2 rounded-lg hover:bg-opacity-80 transition-colors cursor-pointer ${
                            isCurrentUser ? 'bg-blue-600' : 'bg-gray-200'
                          }`}
                          target="_blank"
                          rel="noopener noreferrer"
                          onClick={(e) => {
                            e.preventDefault();
                            setSelectedFile({
                              url: getFileUrl(attachment.fileUrl),
                              fileName: attachment.fileName,
                              fileType: attachment.fileType
                            });
                          }}
                        >
                          <FileText className="w-5 h-5 opacity-70" />
                          <span className="text-sm truncate flex-1">{attachment.fileName}</span>
                          <Download className="w-4 h-4 opacity-70" />
                        </a>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const handleSubmit = async (content: string, files: File[]) => {
    if (!content.trim() && !files.length) {
      toast.error('Please enter a message or attach files');
      return;
    }

    try {
      await onAddComment(content.trim(), files);
    } catch (error) {
      console.error('Error submitting comment:', error);
      toast.error('Failed to submit comment');
    }
  };

  return (
    <div 
      className="flex flex-col h-full"
      onDragEnter={handleDragEnter}
      onDragOver={(e) => e.preventDefault()}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {!hideHeader && <TicketHeader ticket={ticket} />}
      
      <div 
        className={`flex-1 overflow-y-auto p-4 space-y-4 ${
          isDragging ? 'bg-blue-50 border-2 border-dashed border-blue-500' : ''
        }`}
        ref={commentContainerRef}
      >
        {renderComments()}
      </div>
      
      {status === 'RESOLVED' ? (
        <div className="border-t bg-gray-50 p-4 text-center text-gray-500">
          This ticket is resolved. Comments are disabled.
        </div>
      ) : (
        <div className="border-t bg-gray-50 p-4">
          <RichTextEditor
            onSubmit={handleSubmit}
            onTyping={() => {
              if (currentUser) {
                socketService.emitTyping(ticket.id.toString(), currentUser.id, true);
              }
            }}
            mentions={[]}
            placeholder={
              status === 'RESOLVED' 
                ? "This ticket has been resolved. Comments are no longer allowed."
                : ticket.lockedBy && ticket.lockedBy.name && !canAddComment
                  ? currentUser?.id === ticket.createdBy.id
                    ? "You can still add comments as the ticket creator while " + ticket.lockedBy.name + " is working on your ticket."
                    : "This ticket is currently being worked on by " + ticket.lockedBy.name + ". Only they can add comments at this time."
                  : "Type your message or drop files here..."
            }
            isDisabled={!canAddComment || status === 'RESOLVED'}
          />
        </div>
      )}

      {selectedFile && (
        <FileViewer
          fileUrl={selectedFile.url}
          fileName={selectedFile.fileName}
          fileType={selectedFile.fileType}
          onClose={() => setSelectedFile(null)}
        />
      )}
    </div>
  );
};

const getFileUrl = (fileUrl: string) => {
  if (!fileUrl) return '';
  if (fileUrl.startsWith('http') || fileUrl.startsWith('//')) return fileUrl;
  return `${window.location.origin}${fileUrl}`;
}; 