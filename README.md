# 🏢 InfraSpine - IT Management System

> **A comprehensive enterprise IT management solution for modern organizations**

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/your-repo)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Node.js](https://img.shields.io/badge/node.js-16%2B-brightgreen.svg)](https://nodejs.org/)
[![React](https://img.shields.io/badge/react-18-blue.svg)](https://reactjs.org/)

## 📋 Table of Contents

1. [🌟 Overview](#-overview)
2. [✨ Key Features](#-key-features)
3. [🚀 Quick Start](#-quick-start)
4. [⚙️ Installation & Setup](#️-installation--setup)
5. [🗄️ Database Management](#️-database-management)
6. [📊 Data Import Guides](#-data-import-guides)
7. [🏗️ System Architecture](#️-system-architecture)
8. [🔐 Security & Authentication](#-security--authentication)
9. [📚 API Documentation](#-api-documentation)
10. [🛠️ Troubleshooting](#️-troubleshooting)
11. [🔧 Development](#-development)
12. [📖 Additional Resources](#-additional-resources)

---

## 🌟 Overview

**InfraSpine** is a comprehensive IT Management System designed to streamline enterprise operations, employee management, and service desk functionalities. Built with modern web technologies, it provides a robust platform for managing IT resources, tracking employee attendance, handling service requests, and maintaining organizational workflows.

### 🎯 Perfect For
- **Enterprise IT Departments**
- **HR Management Teams**
- **Service Desk Operations**
- **Employee Attendance Tracking**
- **Asset & Vendor Management**

---

## ✨ Key Features

### 🏢 **Core Modules**
- **👥 Employee Management**: Complete CRUD operations with import/export capabilities
- **🎫 Service Desk**: Advanced ticketing system with file attachments and real-time updates
- **⏰ Attendance Tracking**: Professional attendance system with shift management
- **💼 Asset Management**: IT asset tracking and maintenance scheduling
- **🤝 Vendor Management**: Vendor contracts, performance tracking, and SLA monitoring
- **💰 Financial Management**: Billing, invoicing, and budget tracking
- **📊 Project Management**: Task management with dependencies and collaboration
- **👨‍💼 HR Management**: Recruitment, leave management, and employee records

### 🔧 **Technical Features**
- **🔐 Role-Based Access Control**: Dynamic permission system with hierarchical roles
- **🔄 Real-time Notifications**: Socket.io integration for live updates
- **📁 File Management**: Secure file upload and storage (up to 50MB)
- **📈 Analytics & Reporting**: Comprehensive reporting capabilities
- **🌙 Dark/Light Theme**: Modern UI with theme switching
- **📱 Responsive Design**: Works seamlessly across all devices

### 🛡️ **Security & Performance**
- **JWT Authentication** with secure token management
- **Input Validation** and sanitization
- **Rate Limiting** and CORS protection
- **Error Handling** with user-friendly messages
- **Performance Optimization** with caching and lazy loading

---

## 🚀 Quick Start

### Prerequisites
- **Node.js** (v16 or higher)
- **MySQL** (v8.0 or higher)
- **npm** or **yarn** package manager

### ⚡ 5-Minute Setup

```bash
# 1. Clone the repository
git clone <repository-url>
cd IT-MS

# 2. Install dependencies
npm install

# 3. Setup environment
cp .env.example .env
# Edit .env with your database credentials

# 4. Setup database
npm run db:setup

# 5. Start the application
npm run dev
```

🎉 **That's it!** Open [http://localhost:3000](http://localhost:3000) and login with:
- **Email**: `<EMAIL>`
- **Password**: `admin123`

---

## ⚙️ Installation & Setup

### 📋 Detailed Installation Steps

#### 1. **Environment Configuration**
Create a `.env` file in the root directory:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=ims_db

# Application Configuration
JWT_SECRET=your_jwt_secret_key_here
PORT=5000
NODE_ENV=development

# File Upload Configuration
MAX_FILE_SIZE=52428800  # 50MB
UPLOAD_PATH=./uploads

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

#### 2. **Database Setup**

```bash
# Create MySQL database
mysql -u root -p
CREATE DATABASE ims_db;
EXIT;

# Run database migrations
npm run migration:run

# Seed initial data
npm run db:seed
```

#### 3. **Application Startup**

```bash
# Development mode (with hot reload)
npm run dev

# Production mode
npm run build
npm start

# Run tests
npm test
```

### 🔧 **Technology Stack**

| Category | Technology | Version |
|----------|------------|---------|
| **Frontend** | React | 18.x |
| **Frontend** | TypeScript | 5.x |
| **Frontend** | Vite | 4.x |
| **Frontend** | TailwindCSS | 3.x |
| **Backend** | Node.js | 16+ |
| **Backend** | Express.js | 4.x |
| **Database** | MySQL | 8.0+ |
| **ORM** | TypeORM | 0.3.x |
| **Real-time** | Socket.io | 4.x |
| **Authentication** | JWT | Latest |
| **File Upload** | Multer | Latest |

---

## 🗄️ Database Management

### 🔄 **Database Restoration**

#### Method 1: Automated Restoration (Recommended)
```bash
# Windows
restore-db.bat

# Linux/Mac
./restore-database.sh
```

#### Method 2: Manual Restoration
```bash
# Import backup file
mysql -u root -p ims_db < backup/ims_db_backup.sql

# Fix admin credentials if needed
node scripts/fix-admin-credentials.js

# Verify restoration
npm run db:verify
```

### 🔑 **Default Credentials**

After database setup, use these credentials:

| Role | Email | Password | Access Level |
|------|-------|----------|--------------|
| **Super Admin** | <EMAIL> | admin123 | Full system access |
| **IT Staff** | <EMAIL> | password123 | IT operations |
| **HR Staff** | <EMAIL> | password123 | HR management |
| **Employee** | <EMAIL> | password123 | Basic access |

### 🛠️ **Database Maintenance**

```bash
# Create backup
npm run db:backup

# Reset database
npm run db:reset

# Check database health
npm run db:health

# Run migrations
npm run migration:run

# Revert last migration
npm run migration:revert
```

---

## 📊 Data Import Guides

### 👥 **Employee Data Import**

#### Supported Formats
- **CSV** (.csv)
- **Excel** (.xlsx, .xls)
- **Maximum file size**: 50MB

#### Required Columns
| Column | Type | Required | Description |
|--------|------|----------|-------------|
| `firstName` | Text | ✅ | Employee first name |
| `lastName` | Text | ✅ | Employee last name |
| `middleName` | Text | ❌ | Employee middle name |
| `gender` | Text | ✅ | Gender (male/female/other) |
| `dateOfBirth` | Date | ✅ | Format: YYYY-MM-DD |
| `nationality` | Text | ❌ | Employee nationality |
| `cnicNumber` | Text | ✅ | National ID number |
| `cnicExpiryDate` | Date | ❌ | ID expiry date |
| `employeeId` | Text | ❌ | Unique identifier (auto-generated if empty) |
| `department` | Text | ✅ | Department name |
| `designation` | Text | ✅ | Job title/position |
| `employmentStatus` | Text | ❌ | Status (active/inactive/onleave) |
| `employmentType` | Text | ❌ | Type (full-time/part-time/contract) |
| `joinDate` | Date | ✅ | Employment start date |
| `mobileNumber` | Text | ✅ | Mobile phone number |
| `officialEmail` | Text | ✅ | Official email address |

#### Sample Employee Data
```csv
firstName,lastName,gender,dateOfBirth,cnicNumber,department,designation,joinDate,mobileNumber,officialEmail
John,Doe,male,1990-01-15,1234567890,IT,Software Engineer,2022-05-10,************,<EMAIL>
Jane,Smith,female,1992-03-20,0987654321,HR,HR Manager,2021-08-15,************,<EMAIL>
```

#### Import Steps
1. Navigate to **Employee Management** → **Import**
2. Drag & drop your file or click to browse
3. Review validation results
4. Confirm import to add employees

### ⏰ **Attendance Data Import**

#### Required Columns
| Column | Type | Required | Description |
|--------|------|----------|-------------|
| `employeeId` | Number | ✅ | Employee identifier |
| `employeeName` | Text | ✅ | Full employee name |
| `date` | Date | ✅ | Attendance date (YYYY-MM-DD) |
| `checkInTime` | Time | ❌ | Check-in time (HH:MM) |
| `checkOutTime` | Time | ❌ | Check-out time (HH:MM) |
| `status` | Text | ✅ | Attendance status |
| `workHours` | Number | ❌ | Total work hours |
| `isRemote` | Boolean | ❌ | Remote work (true/false) |
| `location` | Text | ❌ | Work location |
| `notes` | Text | ❌ | Additional notes |

#### Valid Status Values
- `present` - Employee was present
- `absent` - Employee was absent
- `late` - Employee arrived late
- `half_day` - Half day work
- `leave` - On leave
- `work_from_home` - Remote work
- `holiday` - Public holiday
- `sick_leave` - Sick leave
- `annual_leave` - Annual leave

#### Sample Attendance Data
```csv
employeeId,employeeName,date,checkInTime,checkOutTime,status,workHours,isRemote,location
1001,John Smith,2023-05-15,09:00,17:30,present,8.5,false,Office
1002,Jane Doe,2023-05-15,09:15,17:45,present,8.5,true,Remote
```

### 📋 **Import Best Practices**

- **Batch Size**: Import max 500 employees or 1000 attendance records per file
- **Data Validation**: Ensure all required fields are filled
- **Date Format**: Always use YYYY-MM-DD format
- **Backup**: Create backups before large imports
- **Testing**: Test with small files first
- **File Size**: Keep files under 20MB for optimal performance

---

## 🏗️ System Architecture

### 📁 **Project Structure**
```
InfraSpine/
├── 📂 src/
│   ├── 📂 components/          # React components
│   │   ├── 📂 HR/             # HR management components
│   │   ├── 📂 ServiceDesk/    # Service desk components
│   │   ├── 📂 Dashboard/      # Dashboard components
│   │   └── 📂 common/         # Shared components
│   ├── 📂 server/             # Backend server code
│   │   ├── 📂 entities/       # TypeORM entities
│   │   ├── 📂 controllers/    # API controllers
│   │   ├── 📂 services/       # Business logic
│   │   ├── 📂 repositories/   # Data access layer
│   │   ├── 📂 routes/         # Express routes
│   │   └── 📂 middleware/     # Custom middleware
│   ├── 📂 contexts/           # React contexts
│   ├── 📂 hooks/              # Custom React hooks
│   ├── 📂 services/           # Frontend services
│   ├── 📂 types/              # TypeScript type definitions
│   └── 📂 utils/              # Utility functions
├── 📂 scripts/                # Database and utility scripts
├── 📂 public/                 # Static assets
├── 📂 uploads/                # File upload directory
├── 📂 backup/                 # Database backups
├── 📂 logs/                   # Application logs
└── 📂 migrations/             # Database migrations
```

### 🔄 **API Architecture**

The system follows RESTful API principles:

| Module | Base Route | Description |
|--------|------------|-------------|
| **Authentication** | `/api/auth/*` | Login, logout, token management |
| **Users** | `/api/users/*` | User management operations |
| **Employees** | `/api/employees/*` | Employee CRUD operations |
| **Tickets** | `/api/tickets/*` | Service desk ticketing |
| **Attendance** | `/api/attendance/*` | Attendance tracking |
| **Assets** | `/api/assets/*` | IT asset management |
| **Vendors** | `/api/vendors/*` | Vendor management |
| **Projects** | `/api/projects/*` | Project management |
| **Reports** | `/api/reports/*` | Reporting and analytics |

### 🔌 **Real-time Features**

- **Socket.io Integration**: Live notifications and updates
- **Real-time Ticket Updates**: Instant status changes
- **Live Chat Support**: Real-time communication
- **Notification System**: Push notifications for important events

---

## 🔐 Security & Authentication

### 🛡️ **Security Features**

- **JWT Authentication**: Secure token-based authentication
- **Role-Based Access Control (RBAC)**: Granular permission system
- **Input Validation**: Server-side validation with class-validator
- **SQL Injection Protection**: TypeORM query builder protection
- **XSS Protection**: Input sanitization and output encoding
- **CORS Configuration**: Cross-origin request security
- **Rate Limiting**: API rate limiting to prevent abuse
- **File Upload Security**: MIME type validation and size limits
- **Password Hashing**: bcrypt for secure password storage

### 👥 **Role System**

| Role ID | Role Name | Description | Key Permissions |
|---------|-----------|-------------|----------------|
| 1 | **SUPER_ADMIN** | System Administrator | All system permissions |
| 2 | **HR_ADMIN** | HR Administrator | Full HR module access |
| 3 | **IT_ADMIN** | IT Administrator | IT operations and asset management |
| 4 | **DEPARTMENT_HEAD** | Department Manager | Department oversight and reporting |
| 5 | **IT_STAFF** | IT Staff Member | Service desk and IT support |
| 6 | **HR_STAFF** | HR Staff Member | HR operations and employee management |
| 7 | **EMPLOYEE** | Regular Employee | Basic access and self-service |
| 8 | **DASHBOARD_MANAGER** | Dashboard Manager | Dashboard and analytics management |

### 🔑 **Permission Categories**

- **User Management**: Create, read, update, delete users
- **Employee Management**: Manage employee records and data
- **Ticket Management**: Handle service desk operations
- **Attendance Management**: Track and manage attendance
- **Asset Management**: Manage IT assets and inventory
- **Report Access**: View and generate reports
- **System Administration**: System configuration and maintenance

---

## 📚 API Documentation

### 🔐 **Authentication Endpoints**

#### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "name": "Administrator",
    "email": "<EMAIL>",
    "role": "SUPER_ADMIN"
  }
}
```

#### Logout
```http
POST /api/auth/logout
Authorization: Bearer <token>
```

### 👥 **Employee Management**

#### Get All Employees
```http
GET /api/employees?page=1&limit=10&search=john
Authorization: Bearer <token>
```

#### Create Employee
```http
POST /api/employees
Authorization: Bearer <token>
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "department": "IT",
  "designation": "Software Engineer",
  "joinDate": "2024-01-15"
}
```

#### Update Employee
```http
PUT /api/employees/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "designation": "Senior Software Engineer",
  "department": "Engineering"
}
```

### 🎫 **Service Desk API**

#### Create Ticket
```http
POST /api/tickets
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Computer not starting",
  "description": "My computer won't turn on after the weekend",
  "priority": "HIGH",
  "category": "Hardware",
  "assignedTo": 5
}
```

#### Add Comment to Ticket
```http
POST /api/tickets/:id/comments
Authorization: Bearer <token>
Content-Type: application/json

{
  "content": "I've checked the power cable and it seems fine",
  "isInternal": false
}
```

#### Upload Attachment
```http
POST /api/tickets/:id/attachments
Authorization: Bearer <token>
Content-Type: multipart/form-data

files: [file1.jpg, file2.pdf]
```

### ⏰ **Attendance API**

#### Clock In
```http
POST /api/attendance/clock-in
Authorization: Bearer <token>
Content-Type: application/json

{
  "employeeId": 123,
  "timestamp": "2024-01-15T09:00:00Z",
  "location": "Main Office"
}
```

#### Get Attendance Report
```http
GET /api/attendance/report?employeeId=123&startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer <token>
```

### 📊 **Common Response Formats**

#### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
```

#### Error Response
```json
{
  "success": false,
  "error": "VALIDATION_ERROR",
  "message": "Invalid input data",
  "details": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ]
}
```

#### Paginated Response
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 150,
    "totalPages": 15
  }
}
```

---

## 🛠️ Troubleshooting

### 🚨 **Common Issues & Solutions**

#### Database Connection Issues
```bash
# Check MySQL service status
# Windows
net start mysql80

# Linux/Mac
sudo systemctl start mysql
sudo systemctl status mysql

# Test connection
mysql -u root -p -e "SELECT 1;"
```

#### Admin Access Issues
```bash
# Reset admin credentials
node scripts/fix-admin-credentials.js

# Verify admin role
node scripts/verify-admin-role.js

# Check user roles
npm run db:check-roles
```

#### File Upload Issues

**Large File Errors (>50MB)**
- System supports up to 50MB files
- Split large files into smaller chunks
- Check server disk space

**Permission Errors**
```bash
# Fix upload directory permissions
chmod 755 uploads/
chown -R www-data:www-data uploads/
```

**MIME Type Issues**
- Verify allowed file types in configuration
- Check file extension matches content type

#### Frontend Issues
```bash
# Clear cache and restart
npm run clean
rm -rf node_modules package-lock.json
npm install
npm run dev

# Check for port conflicts
lsof -i :3000
lsof -i :5000
```

#### Performance Issues
```bash
# Check system resources
npm run health-check

# Optimize database
npm run db:optimize

# Clear application logs
npm run logs:clear
```

### 🔍 **Debug Mode**

Enable debug mode for detailed logging:

```bash
# Set environment variable
export DEBUG=app:*

# Or in .env file
DEBUG=app:*
LOG_LEVEL=debug

# Start with debug
npm run dev:debug
```

### 📊 **Health Checks**

```bash
# Check application health
curl http://localhost:5000/api/health

# Check database connectivity
npm run db:ping

# Verify all services
npm run system:check
```

### 🔧 **Migration Issues**

```bash
# Check migration status
npm run migration:status

# Force migration reset
npm run migration:reset

# Rollback last migration
npm run migration:revert

# Re-run all migrations
npm run migration:fresh
```

---

## 🔧 Development

### 🛠️ **Development Setup**

```bash
# Install development dependencies
npm install --include=dev

# Start development servers
npm run dev          # Start both frontend and backend
npm run dev:client   # Frontend only (port 3000)
npm run dev:server   # Backend only (port 5000)

# Run tests
npm test            # Run all tests
npm run test:watch  # Watch mode
npm run test:coverage # Coverage report

# Code quality
npm run lint        # ESLint
npm run lint:fix    # Auto-fix linting issues
npm run format      # Prettier formatting
npm run type-check  # TypeScript checking
```

### 📝 **Scripts Reference**

| Script | Description |
|--------|-------------|
| `npm run dev` | Start development mode |
| `npm run build` | Build for production |
| `npm start` | Start production server |
| `npm test` | Run test suite |
| `npm run lint` | Run ESLint |
| `npm run db:setup` | Setup database |
| `npm run db:backup` | Create database backup |
| `npm run db:restore` | Restore from backup |
| `npm run migration:run` | Run pending migrations |
| `npm run migration:revert` | Revert last migration |

### 🏗️ **Building for Production**

```bash
# Build the application
npm run build

# Test production build locally
npm run preview

# Start production server
npm start

# Build with environment
NODE_ENV=production npm run build
```

### 🧪 **Testing**

```bash
# Run all tests
npm test

# Run specific test file
npm test -- employee.test.ts

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run integration tests
npm run test:integration
```

### 📦 **Environment Variables**

Create different environment files for different stages:

- `.env.development` - Development environment
- `.env.production` - Production environment
- `.env.test` - Testing environment
- `.env.local` - Local overrides (git-ignored)

---

## 📖 Additional Resources

### 📚 **Documentation Links**

- **API Documentation**: [Swagger UI](http://localhost:5000/api-docs) (when running)
- **Database Schema**: See `src/entities/` for TypeORM entities
- **Component Library**: See `src/components/` for React components
- **Business Logic**: See `src/services/` for service layer

### 🤝 **Contributing**

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit changes**: `git commit -m 'Add amazing feature'`
4. **Push to branch**: `git push origin feature/amazing-feature`
5. **Open a Pull Request**

### 📋 **Code Standards**

- **TypeScript**: Strict mode enabled
- **ESLint**: Airbnb configuration
- **Prettier**: Code formatting
- **Husky**: Pre-commit hooks
- **Conventional Commits**: Commit message format

### 🐛 **Bug Reports**

When reporting bugs, please include:

- **Environment details** (OS, Node.js version, browser)
- **Steps to reproduce** the issue
- **Expected vs actual behavior**
- **Screenshots** or error logs
- **Browser console errors** (if applicable)

### 💡 **Feature Requests**

For feature requests, please provide:

- **Clear description** of the feature
- **Use case** and business justification
- **Proposed implementation** (if technical)
- **Mockups or wireframes** (if UI-related)

### 📞 **Support**

- **Email**: <EMAIL>
- **Documentation**: [Wiki](https://github.com/your-repo/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-repo/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-repo/discussions)

### 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### 🙏 **Acknowledgments**

- **React Team** for the amazing framework
- **TypeORM Team** for the excellent ORM
- **TailwindCSS** for the utility-first CSS framework
- **Socket.io** for real-time communication
- **All contributors** who helped build this system

---

## 🎯 **Quick Reference**

### 🔑 **Default Credentials**
- **Admin**: <EMAIL> / admin123
- **IT Staff**: <EMAIL> / password123
- **HR Staff**: <EMAIL> / password123

### 🌐 **Default URLs**
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **API Documentation**: http://localhost:5000/api-docs

### 📞 **Emergency Commands**
```bash
# Reset everything
npm run reset:all

# Fix admin access
npm run fix:admin

# Restore database
npm run db:restore

# Check system health
npm run health:check
```

---

<div align="center">

**🌟 Star this repository if you find it helpful!**

**Built with ❤️ by the InfraSpine Team**

[⬆ Back to Top](#-infraspine---it-management-system)

</div>