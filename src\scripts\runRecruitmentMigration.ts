import { AppDataSource } from '../config/database';
import { CreateRecruitmentTables1750200000000 } from '../migrations/1750200000000-CreateRecruitmentTables';
import { AddCriticalIndexes1750100000000 } from '../migrations/1750100000000-AddCriticalIndexes';
import { AddRecruitmentIndexes1750100000001 } from '../migrations/1750100000001-AddRecruitmentIndexes';

async function runRecruitmentMigration() {
  try {
    console.log('🚀 Starting recruitment module migration...');
    
    // Initialize database connection
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ Database connection established');
    }

    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();

    try {
      console.log('\n📊 Creating recruitment tables...');
      const recruitmentMigration = new CreateRecruitmentTables1750200000000();
      await recruitmentMigration.up(queryRunner);
      console.log('✅ Recruitment tables created successfully');

      console.log('\n📈 Adding critical indexes...');
      const criticalIndexesMigration = new AddCriticalIndexes1750100000000();
      await criticalIndexesMigration.up(queryRunner);
      console.log('✅ Critical indexes added successfully');

      console.log('\n👥 Adding recruitment-specific indexes...');
      const recruitmentIndexesMigration = new AddRecruitmentIndexes1750100000001();
      await recruitmentIndexesMigration.up(queryRunner);
      console.log('✅ Recruitment indexes added successfully');

      // Verify tables were created
      console.log('\n🔍 Verifying table creation...');
      const tables = [
        'job_postings',
        'job_applications', 
        'interviews',
        'interview_interviewers',
        'application_evaluations',
        'interview_feedback'
      ];

      for (const tableName of tables) {
        try {
          const result = await queryRunner.query(`SHOW TABLES LIKE '${tableName}'`);
          if (result.length > 0) {
            console.log(`  ✅ Table ${tableName} exists`);
            
            // Get table info
            const tableInfo = await queryRunner.query(`DESCRIBE ${tableName}`);
            console.log(`    📋 ${tableName}: ${tableInfo.length} columns`);
          } else {
            console.log(`  ❌ Table ${tableName} not found`);
          }
        } catch (error) {
          console.log(`  ⚠️  Error checking table ${tableName}: ${error.message}`);
        }
      }

      // Check indexes
      console.log('\n📊 Verifying indexes...');
      try {
        const indexes = await queryRunner.query(`
          SELECT 
            TABLE_NAME,
            INDEX_NAME,
            COLUMN_NAME,
            NON_UNIQUE
          FROM information_schema.STATISTICS 
          WHERE TABLE_SCHEMA = DATABASE()
          AND TABLE_NAME IN ('job_postings', 'job_applications', 'interviews', 'application_evaluations', 'interview_feedback')
          AND INDEX_NAME != 'PRIMARY'
          ORDER BY TABLE_NAME, INDEX_NAME
        `);

        const indexesByTable = {};
        indexes.forEach(index => {
          if (!indexesByTable[index.TABLE_NAME]) {
            indexesByTable[index.TABLE_NAME] = [];
          }
          indexesByTable[index.TABLE_NAME].push(index.INDEX_NAME);
        });

        Object.keys(indexesByTable).forEach(tableName => {
          const uniqueIndexes = [...new Set(indexesByTable[tableName])];
          console.log(`  📋 ${tableName}: ${uniqueIndexes.length} indexes`);
        });

      } catch (error) {
        console.log(`  ⚠️  Error checking indexes: ${error.message}`);
      }

      // Insert sample data
      console.log('\n📝 Inserting sample recruitment data...');
      await insertSampleData(queryRunner);

      console.log('\n🎉 Recruitment module migration completed successfully!');
      
      console.log('\n📋 Migration Summary:');
      console.log('  • ✅ Recruitment tables created');
      console.log('  • ✅ Indexes optimized for performance');
      console.log('  • ✅ Foreign key relationships established');
      console.log('  • ✅ Sample data inserted');
      console.log('  • ✅ Database ready for recruitment module');
      
      console.log('\n🚀 Next Steps:');
      console.log('  1. Start the server: npm run dev');
      console.log('  2. Access recruitment dashboard: /recruitment');
      console.log('  3. Create your first job posting');
      console.log('  4. Configure recruitment permissions');

    } finally {
      await queryRunner.release();
    }

  } catch (error) {
    console.error('❌ Error running recruitment migration:', error);
    throw error;
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('✅ Database connection closed');
    }
  }
}

async function insertSampleData(queryRunner: any) {
  try {
    // Check if admin user exists
    const adminUser = await queryRunner.query(`
      SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1
    `);

    if (adminUser.length === 0) {
      console.log('  ⚠️  No admin user found, skipping sample data insertion');
      return;
    }

    const adminUserId = adminUser[0].id;

    // Insert sample job posting
    const jobPostingResult = await queryRunner.query(`
      INSERT INTO job_postings (
        title, description, requirements, department, location, 
        jobType, experienceLevel, workLocation, status, 
        createdById, isActive, numberOfPositions
      ) VALUES (
        'Senior Software Engineer',
        'We are looking for a Senior Software Engineer to join our dynamic team. You will be responsible for designing, developing, and maintaining high-quality software solutions.',
        'Bachelor''s degree in Computer Science or related field. 5+ years of experience in software development. Proficiency in JavaScript, TypeScript, React, Node.js. Experience with databases and cloud platforms.',
        'Engineering',
        'New York, NY',
        'full_time',
        'senior',
        'hybrid',
        'published',
        ?,
        1,
        2
      )
    `, [adminUserId]);

    const jobPostingId = jobPostingResult.insertId;

    // Insert sample job application
    await queryRunner.query(`
      INSERT INTO job_applications (
        firstName, lastName, email, phone, 
        coverLetter, status, source, jobPostingId,
        skills, expectedSalary, salaryCurrency, salaryPeriod
      ) VALUES (
        'John',
        'Doe',
        '<EMAIL>',
        '******-0123',
        'I am excited to apply for the Senior Software Engineer position. With over 6 years of experience in full-stack development, I believe I would be a great fit for your team.',
        'submitted',
        'company_website',
        ?,
        '["JavaScript", "TypeScript", "React", "Node.js", "PostgreSQL"]',
        120000,
        'USD',
        'yearly'
      )
    `, [jobPostingId]);

    console.log('  ✅ Sample job posting and application created');

    // Insert another sample job posting
    await queryRunner.query(`
      INSERT INTO job_postings (
        title, description, requirements, department, location, 
        jobType, experienceLevel, workLocation, status, 
        createdById, isActive, numberOfPositions, isUrgent
      ) VALUES (
        'HR Manager',
        'We are seeking an experienced HR Manager to lead our human resources department and drive our people strategy.',
        'Bachelor''s degree in HR, Business Administration, or related field. 7+ years of HR experience. Strong knowledge of employment law and HR best practices.',
        'Human Resources',
        'San Francisco, CA',
        'full_time',
        'manager',
        'onsite',
        'published',
        ?,
        1,
        1,
        1
      )
    `, [adminUserId]);

    console.log('  ✅ Additional sample job posting created');

  } catch (error) {
    console.log(`  ⚠️  Error inserting sample data: ${error.message}`);
  }
}

// Performance monitoring function
async function checkRecruitmentPerformance() {
  try {
    console.log('\n🔍 Checking recruitment module performance...');
    
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }

    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();

    try {
      // Check table sizes and performance
      const tableStats = await queryRunner.query(`
        SELECT 
          TABLE_NAME,
          TABLE_ROWS,
          ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'SIZE_MB',
          ROUND((INDEX_LENGTH / 1024 / 1024), 2) AS 'INDEX_SIZE_MB'
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME IN ('job_postings', 'job_applications', 'interviews', 'application_evaluations', 'interview_feedback')
        ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC
      `);

      console.log('\n📊 Recruitment Table Performance:');
      tableStats.forEach(table => {
        console.log(`  📋 ${table.TABLE_NAME}:`);
        console.log(`    • Rows: ${table.TABLE_ROWS || 0}`);
        console.log(`    • Total Size: ${table.SIZE_MB}MB`);
        console.log(`    • Index Size: ${table.INDEX_SIZE_MB}MB`);
      });

      // Test query performance
      console.log('\n⚡ Testing Query Performance:');
      
      const start1 = Date.now();
      await queryRunner.query(`
        SELECT COUNT(*) as total FROM job_postings WHERE status = 'published'
      `);
      const time1 = Date.now() - start1;
      console.log(`  • Active job postings query: ${time1}ms`);

      const start2 = Date.now();
      await queryRunner.query(`
        SELECT COUNT(*) as total FROM job_applications WHERE status = 'submitted'
      `);
      const time2 = Date.now() - start2;
      console.log(`  • Pending applications query: ${time2}ms`);

      const start3 = Date.now();
      await queryRunner.query(`
        SELECT jp.title, COUNT(ja.id) as application_count
        FROM job_postings jp
        LEFT JOIN job_applications ja ON jp.id = ja.jobPostingId
        WHERE jp.status = 'published'
        GROUP BY jp.id, jp.title
        LIMIT 10
      `);
      const time3 = Date.now() - start3;
      console.log(`  • Job posting with applications query: ${time3}ms`);

      console.log('\n✅ Performance check completed');

    } finally {
      await queryRunner.release();
    }

  } catch (error) {
    console.error('❌ Error checking performance:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--performance') || args.includes('-p')) {
    await checkRecruitmentPerformance();
  } else if (args.includes('--help') || args.includes('-h')) {
    console.log(`
📚 Recruitment Migration Script Usage:

  npm run migrate:recruitment              # Run recruitment migration
  npm run migrate:recruitment --performance # Check performance
  npm run migrate:recruitment --help       # Show this help

🎯 What this script does:
  • Creates all recruitment module tables
  • Sets up optimized indexes for performance
  • Establishes foreign key relationships
  • Inserts sample data for testing
  • Verifies successful migration

📊 Tables Created:
  • job_postings - Job posting management
  • job_applications - Application tracking
  • interviews - Interview scheduling
  • application_evaluations - Candidate evaluation
  • interview_feedback - Interview feedback
  • interview_interviewers - Interviewer assignments

⚡ Performance Features:
  • Optimized indexes for fast queries
  • Composite indexes for complex searches
  • Full-text search capabilities
  • Efficient foreign key relationships
    `);
  } else {
    await runRecruitmentMigration();
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

export { runRecruitmentMigration, checkRecruitmentPerformance };
