import React, { useState, useCallback, useEffect } from 'react';
import { Save, X, AlertCircle, Shield, User, Building, Database, LinkIcon, FilePlus, ArrowLeft, Mail, UserCheck, Globe, Briefcase, Activity, CreditCard, MailQuestion, Phone, Lock, HelpCircle, Calendar, CalendarClock, History, Clock, Upload, Ticket, FileText, LogOut, Link, Server, Key, ShieldCheck, Laptop, Package, MapPin } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import EmailAccountService, { EmailAccountData } from '../services/EmailAccountService';
import AssetSoftwareService from '../services/AssetSoftwareService';

// InfoIcon component for better UI
const InfoIcon = ({ className }: { className?: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="12" y1="16" x2="12" y2="12"></line>
    <line x1="12" y1="8" x2="12.01" y2="8"></line>
  </svg>
);

interface EmailFormProps {
  initialData?: {
    id?: string;
    emailAddress: string;
    assignedToType: 'User' | 'Department';
    assignedToName: string;
    assignedToId?: string;
    designation?: string;
    department: string;
    accountType: string;
    platform: string;
    status: string;
    loginUrl?: string;
    hostingProvider?: string;
    recoveryEmail?: string;
    recoveryPhone?: string;
    twoFactorEnabled: boolean;
    primaryUser: string;
    createdBy: string;
    notes?: string;
    subscriptionType?: string;
    software?: string;
    asset?: string;
    password?: string;
    secretQuestion?: string;
    secretAnswer?: string;
    creationDate?: string;
    lastAccessDate?: string;
    ownershipChangeLog?: string;
    passwordAge?: number;
    ticketId?: string;
    licenseRecord?: string;
    userOffboardingProcess?: string;
    project?: string;
    location?: string;
  };
  mode: 'create' | 'edit' | 'view';
  onClose: () => void;
}

// Update the SelectOption type definition to use a discriminated union
type StringOption = string;
type ObjectOption = {
  value: string;
  label: string;
  disabled?: boolean;
  metadata?: Record<string, any>;
  icon?: string;
  isHeader?: boolean;
};

type SelectOption = StringOption | ObjectOption;

// Define type guard functions
function isObjectOption(option: SelectOption | undefined): option is ObjectOption {
  return option !== undefined && typeof option !== 'string' && option !== null && typeof option === 'object';
}

function isStringOption(option: SelectOption | undefined): option is StringOption {
  return option !== undefined && typeof option === 'string';
}

// Input render props interface
interface RenderInputProps {
  type?: 'text' | 'email' | 'password' | 'select' | 'textarea' | 'checkbox' | 'date' | 'number';
  value: string | number | boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  placeholder?: string;
  className?: string;
  icon?: React.ReactNode;
  error?: string | null;
  options?: SelectOption[];
  checked?: boolean;
  rows?: number;
}

// Projects and Locations
const PROJECTS = [
  'Eurobiz Corporations',
  'Guardian International',
  'Guardian Developers',
  'Grand Developers',
  'ARD Developers'
];

const PROJECT_LOCATIONS: Record<string, string[]> = {
  'Eurobiz Corporations': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Kharian'
  ],
  'Guardian International': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Arifwala'
  ],
  'Guardian Developers': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Vehari'
  ],
  'Grand Developers': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Faisalabad',
    'Grand City Site Office Murree'
  ],
  'ARD Developers': [
    'Grand City Head Office Lahore'
  ]
};

// Get all unique locations
const ALL_LOCATIONS = Array.from(
  new Set(
    Object.values(PROJECT_LOCATIONS).flat()
  )
);

export function EmailForm({ initialData, mode, onClose }: EmailFormProps) {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    emailAddress: initialData?.emailAddress || '',
    assignedToType: initialData?.assignedToType || 'Department',
    assignedToName: initialData?.assignedToName || '',
    assignedToId: initialData?.assignedToId || '',
    designation: initialData?.designation || '',
    department: initialData?.department || '',
    project: initialData?.project || '',
    location: initialData?.location || '',
    accountType: initialData?.accountType || '',
    platform: initialData?.platform || '',
    status: initialData?.status || '',
    loginUrl: initialData?.loginUrl || '',
    hostingProvider: initialData?.hostingProvider || '',
    recoveryEmail: initialData?.recoveryEmail || '',
    recoveryPhone: initialData?.recoveryPhone || '',
    twoFactorEnabled: initialData?.twoFactorEnabled || false,
    primaryUser: initialData?.primaryUser || '',
    createdBy: initialData?.createdBy || '',
    notes: initialData?.notes || '',
    subscriptionType: initialData?.subscriptionType || '',
    customSubscriptionType: '',
    software: initialData?.software || '',
    asset: initialData?.asset || '',
    password: initialData?.password || '',
    secretQuestion: initialData?.secretQuestion || '',
    secretAnswer: initialData?.secretAnswer || '',
    creationDate: initialData?.creationDate || '',
    lastAccessDate: initialData?.lastAccessDate || '',
    ownershipChangeLog: initialData?.ownershipChangeLog || '',
    passwordAge: initialData?.passwordAge || 0,
    ticketId: initialData?.ticketId || '',
    licenseRecord: initialData?.licenseRecord || '',
    userOffboardingProcess: initialData?.userOffboardingProcess || '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showSecretAnswer, setShowSecretAnswer] = useState(false);
  const [assetOptions, setAssetOptions] = useState<SelectOption[]>([]);
  const [softwareOptions, setSoftwareOptions] = useState<SelectOption[]>([]);
  const [isLoadingOptions, setIsLoadingOptions] = useState(false);

  const departments = [
    'IT', 'HR', 'FINANCE', 'MARKETING', 'SALES', 'OPERATIONS',
    'CSD', 'LAND', 'LEGAL', 'MANAGEMENT', 'PND'
  ];

  // Fetch asset and software options from the database
  useEffect(() => {
    const fetchOptions = async () => {
      setIsLoadingOptions(true);
      try {
        // Fetch asset options
        const assetList = await AssetSoftwareService.getAssetOptions();
        setAssetOptions(assetList);

        // Fetch software options
        const softwareList = await AssetSoftwareService.getSoftwareOptions();
        setSoftwareOptions(softwareList);
      } catch (error) {
        console.error('Error fetching options:', error);
      } finally {
        setIsLoadingOptions(false);
      }
    };

    fetchOptions();
  }, []);

  // Add a useEffect to update location options when project changes
  useEffect(() => {
    if (formData.project && PROJECT_LOCATIONS[formData.project]) {
      // If the current location is not in the new project's locations, reset it
      if (formData.location && !PROJECT_LOCATIONS[formData.project].includes(formData.location)) {
        setFormData(prev => ({ ...prev, location: '' }));
      }
    }
  }, [formData.project]);

  const handleClose = useCallback(() => {
    console.log('Closing form');
    onClose();
  }, [onClose]);

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // First, set defaults for all required fields to ensure nothing is missing
    const currentDate = new Date().toISOString().split('T')[0];
    
    // Create a complete copy with all fields populated
    let submittingData = {
      ...formData,
      // Core required fields
      emailAddress: formData.emailAddress.trim(),
      assignedToType: formData.assignedToType || 'Department',
      assignedToName: formData.assignedToName.trim() || 'IT',
      assignedToId: formData.assignedToId || '',
      designation: formData.designation || '',
      department: formData.department.trim() || formData.assignedToName.trim() || 'IT',
      project: formData.project || '',
      location: formData.location || '',
      
      // Account fields
      accountType: formData.accountType || 'Personal',
      platform: formData.platform || 'Office365',
      status: formData.status || 'Active',
      loginUrl: formData.loginUrl || '',
      hostingProvider: formData.hostingProvider || '',
      
      // Security information
      recoveryEmail: formData.recoveryEmail || '',
      recoveryPhone: formData.recoveryPhone || '',
      twoFactorEnabled: formData.twoFactorEnabled !== undefined ? formData.twoFactorEnabled : false,
      password: formData.password || '',
      secretQuestion: formData.secretQuestion || '',
      secretAnswer: formData.secretAnswer || '',
      passwordAge: formData.passwordAge || 0,
      
      // Date fields - ensure they're empty strings if not provided (not null)
      creationDate: formData.creationDate || '',
      lastAccessDate: formData.lastAccessDate || '',
      
      // Ownership fields
      primaryUser: formData.primaryUser.trim() || formData.assignedToName.trim() || 'Default User',
      createdBy: formData.createdBy || 'IT Admin',
      ownershipChangeLog: formData.ownershipChangeLog || '',
      
      // Additional information
      subscriptionType: formData.subscriptionType || '',
      software: formData.software || '',
      asset: formData.asset || '',
      ticketId: formData.ticketId || '',
      licenseRecord: formData.licenseRecord || '',
      userOffboardingProcess: formData.userOffboardingProcess || '',
      notes: formData.notes || '',
    };
    
    // Handle custom platform name when 'Other' is selected
    if (formData.platform === 'Other' && formData.hostingProvider) {
      submittingData.platform = formData.hostingProvider;
    }
    
    // Handle custom subscription type when 'Other' is selected
    if (formData.subscriptionType === 'Other' && formData.customSubscriptionType) {
      submittingData.subscriptionType = formData.customSubscriptionType;
    }
    
    // Remove the customSubscriptionType field before sending to the backend
    const { customSubscriptionType, ...dataWithoutCustomType } = submittingData;
    
    // Further data preparation - format dates properly for database
    // Only include dates if they're properly formatted, otherwise use empty string
    const dataToSubmit: EmailAccountData = {
      ...dataWithoutCustomType,
      // Make sure date strings are properly formatted or omitted
      creationDate: dataWithoutCustomType.creationDate 
        ? new Date(dataWithoutCustomType.creationDate).toISOString().split('T')[0] 
        : '',
      lastAccessDate: dataWithoutCustomType.lastAccessDate 
        ? new Date(dataWithoutCustomType.lastAccessDate).toISOString().split('T')[0] 
        : ''
    };
    
    // Instead of updating the state and then validating (which is async),
    // validate directly using the submittingData object
    const newErrors: Record<string, string> = {};
    
    if (!submittingData.emailAddress.trim()) {
      newErrors.emailAddress = 'Email address is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(submittingData.emailAddress)) {
      newErrors.emailAddress = 'Invalid email format';
    }

    if (!submittingData.assignedToName.trim()) {
      newErrors.assignedToName = 'Assignment is required';
    }
    
    if (!submittingData.accountType.trim()) {
      newErrors.accountType = 'Account type is required';
    }
    
    if (!submittingData.platform.trim()) {
      newErrors.platform = 'Platform is required';
    } else if (submittingData.platform === 'Other' && !submittingData.hostingProvider.trim()) {
      newErrors.hostingProvider = 'Custom platform name is required';
    }
    
    if (!submittingData.status.trim()) {
      newErrors.status = 'Status is required';
    }

    if (!submittingData.primaryUser.trim()) {
      newErrors.primaryUser = 'Primary user is required';
    }
    
    if (!submittingData.department.trim()) {
      newErrors.department = 'Department is required';
    }

    // Validate custom subscription type
    if (submittingData.subscriptionType === 'Other' && !formData.customSubscriptionType.trim()) {
      newErrors.customSubscriptionType = 'Custom subscription type is required';
    }
    
    // Validate dates if provided
    if (submittingData.creationDate && isNaN(Date.parse(submittingData.creationDate))) {
      newErrors.creationDate = 'Invalid creation date format';
    }
    
    if (submittingData.lastAccessDate && isNaN(Date.parse(submittingData.lastAccessDate))) {
      newErrors.lastAccessDate = 'Invalid last access date format';
    }
    
    // Validate numeric fields
    if (submittingData.passwordAge !== undefined && (isNaN(submittingData.passwordAge) || submittingData.passwordAge < 0)) {
      newErrors.passwordAge = 'Password age must be a positive number';
    }

    // Log validation results for debugging
    console.log('Form validation:', { formData: submittingData, errors: newErrors, isValid: Object.keys(newErrors).length === 0 });

    setErrors(newErrors);
    
    // If there are errors, update the form data and return early
    if (Object.keys(newErrors).length > 0) {
      setFormData(submittingData); // Still update the form with default values
      return;
    }

    // Now proceed with form submission
    setIsSubmitting(true);
    try {
      console.log('About to submit form with data:', dataToSubmit);
      
      let result;
      if (mode === 'create') {
        // Create a new email account
        console.log('Creating new email account with data:', dataToSubmit);
        result = await EmailAccountService.createAccount(dataToSubmit);
        if (result.error) {
          console.error('Error from API when creating account:', result.error);
          throw new Error(result.error);
        }
        console.log('Email account created successfully:', result.data);
      } else {
        // Update an existing email account
        if (!initialData?.id) {
          console.error('Cannot update account: Account ID is missing');
          throw new Error('Account ID not found');
        }
        console.log(`Updating email account ${initialData.id} with data:`, dataToSubmit);
        result = await EmailAccountService.updateAccount(initialData.id, dataToSubmit);
        if (result.error) {
          console.error('Error from API when updating account:', result.error);
          throw new Error(result.error);
        }
        console.log('Email account updated successfully:', result.data);
      }
      
      // Navigate back to the accounts list using the correct path
      console.log('Navigating to /email-management/accounts');
      
      // Use setTimeout to ensure navigation happens after the state updates
      setTimeout(() => {
        navigate('/email-management/accounts');
      }, 100);
      
      // Call onClose to notify the parent component
      onClose();
    } catch (error: any) {
      console.error('Error saving account:', error);
      const errorMessage = error.message || 'Failed to save account. Please try again.';
      setErrors({ submit: errorMessage });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to render form inputs based on mode
  const renderInput = ({ 
    type = 'text', 
    value, 
    onChange, 
    placeholder = '', 
    className = '', 
    icon = null,
    error = null,
    options = [] as SelectOption[],
    checked = false,
    rows = 3
  }: RenderInputProps) => {
    const isViewMode = mode === 'view';
    const baseClassName = `w-full rounded-lg ${isViewMode ? 'bg-gray-50 text-gray-700' : 'border border-gray-300'} px-3 py-2 ${isViewMode ? '' : 'focus:ring-2 focus:ring-blue-500 focus:border-blue-500'}`;
    
    // For view mode, render a div with the value instead of input
    if (isViewMode) {
      // Handle different display formats based on input type
      if (type === 'checkbox') {
        return (
          <div className="flex items-center h-10 px-3 py-2">
            <div className={`h-4 w-4 ${checked ? 'bg-blue-500' : 'bg-gray-300'} rounded`}></div>
            <span className="ml-2">{checked ? 'Yes' : 'No'}</span>
          </div>
        );
      } else if (type === 'select' && options.length > 0) {
        // For selects, display the selected option text
        let displayValue = value.toString();
        const selectedOption = options.find((opt: SelectOption) => 
          typeof opt === 'string' ? opt === value : opt.value === value
        );
        displayValue = typeof selectedOption === 'string' ? 
          selectedOption : 
          selectedOption?.label || value.toString();
        
        return (
          <div className={`${baseClassName} flex items-center`}>
            {icon && <span className="mr-2">{icon}</span>}
            {displayValue || '-'}
          </div>
        );
      } else if (type === 'textarea') {
        return (
          <div className={`${baseClassName} whitespace-pre-wrap`}>
            {value || '-'}
          </div>
        );
      } else if (type === 'password') {
        return (
          <div className={`${baseClassName} flex items-center`}>
            {icon && <span className="mr-2">{icon}</span>}
            {value ? '••••••••' : '-'}
          </div>
        );
      } else {
        return (
          <div className={`${baseClassName} flex items-center`}>
            {icon && <span className="mr-2">{icon}</span>}
            {value || '-'}
          </div>
        );
      }
    }
    
    // For edit/create modes, render the actual input
    if (type === 'select' && options.length > 0) {
      return (
        <select
          value={typeof value === 'boolean' ? (value ? 'true' : 'false') : value.toString()}
          onChange={onChange}
          className={`${baseClassName} ${className} ${error ? 'border-red-300' : ''}`}
          disabled={isViewMode}
        >
          {options.map((option: SelectOption, index: number) => (
            <option 
              key={index} 
              value={typeof option === 'string' ? option : option.value}
              disabled={typeof option !== 'string' && option.disabled}
            >
              {typeof option === 'string' ? option : option.label}
            </option>
          ))}
        </select>
      );
    } else if (type === 'textarea') {
      return (
        <textarea
          value={typeof value === 'boolean' ? (value ? 'true' : 'false') : value.toString()}
          onChange={onChange}
          className={`${baseClassName} ${className} ${error ? 'border-red-300' : ''}`}
          placeholder={placeholder}
          rows={rows}
          readOnly={isViewMode}
        />
      );
    } else if (type === 'checkbox') {
      return (
        <input
          type="checkbox"
          checked={!!checked}
          onChange={onChange}
          className={`h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${className}`}
          disabled={isViewMode}
        />
      );
    } else {
      return (
        <input
          type={type}
          value={typeof value === 'boolean' ? (value ? 'true' : 'false') : value.toString()}
          onChange={onChange}
          className={`${baseClassName} ${className} ${error ? 'border-red-300' : ''}`}
          placeholder={placeholder}
          readOnly={isViewMode}
        />
      );
    }
  };

  const renderOption = (option: SelectOption) => {
    // Check if this is a string option first
    if (isStringOption(option)) {
      return (
        <div className="py-1 px-2">
          {option}
        </div>
      );
    }
    
    // Now we know it's an ObjectOption
    // Check if this is a category header
    if (option.isHeader) {
      return (
        <div className="py-1 px-2 text-xs font-semibold text-gray-500 bg-gray-100">
          🗂️ {option.label}
        </div>
      );
    }

    // For asset or software options, display the corresponding icon
    if (option.icon) {
      return (
        <div className="py-1 px-2">
          {option.icon} {option.label}
        </div>
      );
    }

    // For other options, display without icon
    return (
      <div className="py-1 px-2">
        {option.label}
      </div>
    );
  };

  return (
    <div className="w-full h-full min-h-screen bg-gray-100">
      <div className="w-full h-full bg-white rounded-lg shadow-lg">
        <div className="flex justify-between items-center p-2">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {mode === 'create' ? 'Create Email Account' : mode === 'edit' ? 'Edit Email Account' : 'View Email Account'}
          </h1>
          <button
            type="button"
            onClick={handleClose}
            className="flex items-center gap-2 px-4 py-2 text-lg font-medium text-gray-700 hover:text-gray-900"
          >
            <X className="h-6 w-6" />
            Close
          </button>
        </div>
        <form onSubmit={handleFormSubmit} className="space-y-6 p-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <User className="h-5 w-5" />
              Basic Information
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <Mail className="h-4 w-4 text-gray-400" />
                  Email Address <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  {renderInput({
                    type: 'email',
                    value: formData.emailAddress,
                    onChange: (e) => setFormData({ ...formData, emailAddress: e.target.value }),
                    placeholder: '<EMAIL>',
                    error: errors.emailAddress || null
                  })}
                </div>
                {errors.emailAddress && (
                  <p className="mt-1 text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.emailAddress}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <User className="h-4 w-4 text-gray-400" />
                  Assigned To <span className="text-red-500">*</span>
                </label>
                <div className="flex gap-2">
                  {mode === 'view' ? (
                    <div className="w-full flex items-center rounded-lg bg-gray-50 px-3 py-2">
                      {formData.assignedToType === 'Department' ? 
                        <Building className="h-5 w-5 text-gray-500 mr-2" /> : 
                        <User className="h-5 w-5 text-gray-500 mr-2" />
                      }
                      <span>{formData.assignedToType}: {formData.assignedToName}</span>
                    </div>
                  ) : (
                    <>
                      {renderInput({
                        type: 'select',
                        value: formData.assignedToType,
                        onChange: (e) => setFormData({ ...formData, assignedToType: e.target.value as 'User' | 'Department' }),
                        className: 'w-1/3',
                        options: [
                          { value: 'Department', label: 'Department' },
                          { value: 'User', label: 'User' }
                        ]
                      })}
                      {renderInput({
                        type: 'select',
                        value: formData.assignedToName,
                        onChange: (e) => setFormData({ ...formData, assignedToName: e.target.value }),
                        className: 'w-2/3',
                        options: [
                          { value: '', label: 'Select Department', disabled: true },
                          ...departments.map(dept => ({ value: dept, label: dept }))
                        ]
                      })}
                    </>
                  )}
                </div>
                {errors.assignedToName && (
                  <p className="mt-1 text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.assignedToName}
                  </p>
                )}
              </div>

              {formData.assignedToType === 'User' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                      <User className="h-4 w-4 text-gray-400" />
                      Employee ID
                    </label>
                    <input
                      type="text"
                      value={formData.assignedToId}
                      onChange={(e) => setFormData({ ...formData, assignedToId: e.target.value })}
                      className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="EMP001"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                      <Briefcase className="h-4 w-4 text-gray-400" />
                      Designation
                    </label>
                    <input
                      type="text"
                      value={formData.designation}
                      onChange={(e) => setFormData({ ...formData, designation: e.target.value })}
                      className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Job Title"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                      <FilePlus className="h-4 w-4 text-gray-400" />
                      Project
                    </label>
                    {mode === 'view' ? (
                      <div className="w-full rounded-lg bg-gray-50 px-3 py-2 flex items-center">
                        <Building className="h-5 w-5 text-gray-500 mr-2" />
                        <span>{formData.project || 'Not assigned'}</span>
                      </div>
                    ) : (
                    <select
                        value={formData.project || ''}
                        onChange={(e) => setFormData({ ...formData, project: e.target.value })}
                      className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">Select Project</option>
                        {PROJECTS.map((project) => (
                          <option key={project} value={project}>
                            {project}
                          </option>
                        ))}
                    </select>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                      <Building className="h-4 w-4 text-gray-400" />
                      Location
                    </label>
                    {mode === 'view' ? (
                      <div className="w-full rounded-lg bg-gray-50 px-3 py-2 flex items-center">
                        <MapPin className="h-5 w-5 text-gray-500 mr-2" />
                        <span>{formData.location || 'Not assigned'}</span>
                      </div>
                    ) : (
                      <select
                        value={formData.location || ''}
                        onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                        className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        disabled={!formData.project}
                      >
                        <option value="">Select Location</option>
                        {formData.project ? 
                          PROJECT_LOCATIONS[formData.project].map((location) => (
                            <option key={location} value={location}>
                              {location}
                            </option>
                          )) : 
                          ALL_LOCATIONS.map((location) => (
                            <option key={location} value={location}>
                              {location}
                            </option>
                          ))
                        }
                      </select>
                    )}
                    {!formData.project && mode !== 'view' && (
                      <p className="mt-1 text-xs text-gray-500 flex items-center">
                        <InfoIcon className="h-3 w-3 mr-1" />
                        Select a project first to see location options
                      </p>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Account Configuration */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <UserCheck className="h-5 w-5" />
              Account Configuration
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <UserCheck className="h-4 w-4 text-gray-400" />
                  Account Type <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.accountType}
                  onChange={(e) => setFormData({ ...formData, accountType: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="" disabled>Select Account Type</option>
                  <option value="Personal">Personal</option>
                  <option value="Shared">Shared</option>
                  <option value="Group">Group</option>
                  <option value="Alias">Alias</option>
                  <option value="System">System</option>
                </select>
                {errors.accountType && (
                  <p className="mt-1 text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.accountType}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <Globe className="h-4 w-4 text-gray-400" />
                  Platform/Provider <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.platform}
                  onChange={(e) => setFormData({ ...formData, platform: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="" disabled>Select Platform</option>
                  <option value="Office365">Office 365</option>
                  <option value="Gmail">Gmail</option>
                  <option value="SiteGround">SiteGround</option>
                  <option value="Hostinger">Hostinger</option>
                  <option value="cPanel">cPanel</option>
                  <option value="Exchange">Exchange</option>
                  <option value="Other">Other</option>
                </select>
                {formData.platform === 'Other' && (
                  <div className="mt-2">
                    <input
                      type="text"
                      value={formData.hostingProvider || ''}
                      onChange={(e) => setFormData({ ...formData, hostingProvider: e.target.value })}
                      className={`w-full rounded-lg border px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.hostingProvider ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Enter custom platform name"
                      required
                    />
                    {errors.hostingProvider && (
                      <p className="mt-1 text-sm text-red-500 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.hostingProvider}
                      </p>
                    )}
                  </div>
                )}
                {errors.platform && (
                  <p className="mt-1 text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.platform}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4 text-gray-400" />
                  Account Status <span className="text-red-500">*</span>
                </label>
                {renderInput({
                  type: 'select',
                  value: formData.status,
                  onChange: (e) => setFormData({ ...formData, status: e.target.value }),
                  options: [
                    { value: '', label: 'Select Status', disabled: true },
                    { value: 'Active', label: 'Active' },
                    { value: 'Suspended', label: 'Suspended' },
                    { value: 'Blocked', label: 'Blocked' },
                    { value: 'Deleted', label: 'Deleted' },
                    { value: 'Reassigned', label: 'Reassigned' }
                  ],
                  error: errors.status || null
                })}
                {errors.status && (
                  <p className="mt-1 text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.status}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <User className="h-4 w-4 text-gray-400" />
                  Primary User <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.primaryUser}
                  onChange={(e) => setFormData({ ...formData, primaryUser: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Primary user of this account"
                  required
                />
                {errors.primaryUser && (
                  <p className="mt-1 text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.primaryUser}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <Link className="h-4 w-4 text-gray-400" />
                  Login URL
                </label>
                <input
                  type="url"
                  value={formData.loginUrl}
                  onChange={(e) => setFormData({ ...formData, loginUrl: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="https://example.com/login"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <Server className="h-4 w-4 text-gray-400" />
                  Hosting Provider
                </label>
                <input
                  type="text"
                  value={formData.hostingProvider}
                  onChange={(e) => setFormData({ ...formData, hostingProvider: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Provider Name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <CreditCard className="h-4 w-4 text-gray-400" />
                  Subscription Type
                </label>
                <select
                  value={formData.subscriptionType}
                  onChange={(e) => setFormData({ ...formData, subscriptionType: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="" disabled>Select Subscription</option>
                  <option value="Free">Free</option>
                  <option value="Business">Business</option>
                  <option value="Enterprise">Enterprise</option>
                  <option value="Other">Other</option>
                </select>
                {formData.subscriptionType === 'Other' && (
                  <div className="mt-2">
                    <input
                      type="text"
                      value={formData.customSubscriptionType || ''}
                      onChange={(e) => setFormData({ ...formData, customSubscriptionType: e.target.value })}
                      className={`w-full rounded-lg border px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.customSubscriptionType ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Enter custom subscription type"
                      required
                    />
                    {errors.customSubscriptionType && (
                      <p className="mt-1 text-sm text-red-500 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.customSubscriptionType}
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Security Information */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security Information
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <MailQuestion className="h-4 w-4 text-gray-400" />
                  Recovery Email
                </label>
                <input
                  type="email"
                  value={formData.recoveryEmail}
                  onChange={(e) => setFormData({ ...formData, recoveryEmail: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <Phone className="h-4 w-4 text-gray-400" />
                  Recovery Phone
                </label>
                <input
                  type="text"
                  value={formData.recoveryPhone}
                  onChange={(e) => setFormData({ ...formData, recoveryPhone: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="+1234567890"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <Lock className="h-4 w-4 text-gray-400" />
                  Password
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? 
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                        <line x1="1" y1="1" x2="23" y2="23"></line>
                      </svg> : 
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                      </svg>
                    }
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <HelpCircle className="h-4 w-4 text-gray-400" />
                  Secret Question
                </label>
                <input
                  type="text"
                  value={formData.secretQuestion}
                  onChange={(e) => setFormData({ ...formData, secretQuestion: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Your secret question"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <Key className="h-4 w-4 text-gray-400" />
                  Secret Answer
                </label>
                <div className="relative">
                  <input
                    type={showSecretAnswer ? "text" : "password"}
                    value={formData.secretAnswer}
                    onChange={(e) => setFormData({ ...formData, secretAnswer: e.target.value })}
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Your secret answer"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowSecretAnswer(!showSecretAnswer)}
                  >
                    {showSecretAnswer ? 
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                        <line x1="1" y1="1" x2="23" y2="23"></line>
                      </svg> : 
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                      </svg>
                    }
                  </button>
                </div>
              </div>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="twoFactorEnabled"
                checked={formData.twoFactorEnabled}
                onChange={(e) => setFormData({ ...formData, twoFactorEnabled: e.target.checked })}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="twoFactorEnabled" className="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                <ShieldCheck className="h-4 w-4 text-gray-400" />
                Two-Factor Authentication (2FA) Enabled
              </label>
            </div>
          </div>

          {/* Lifecycle & Status Tracking */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Lifecycle & Status Tracking
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  Creation Date
                </label>
                <input
                  type="date"
                  value={formData.creationDate}
                  onChange={(e) => setFormData({ ...formData, creationDate: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <CalendarClock className="h-4 w-4 text-gray-400" />
                  Last Access Date
                </label>
                <input
                  type="date"
                  value={formData.lastAccessDate}
                  onChange={(e) => setFormData({ ...formData, lastAccessDate: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <History className="h-4 w-4 text-gray-400" />
                  Ownership Change Log
                </label>
                <textarea
                  value={formData.ownershipChangeLog}
                  onChange={(e) => setFormData({ ...formData, ownershipChangeLog: e.target.value })}
                  rows={3}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Ownership change details"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <Clock className="h-4 w-4 text-gray-400" />
                  Password Age (days)
                </label>
                <input
                  type="number"
                  value={formData.passwordAge}
                  onChange={(e) => setFormData({ ...formData, passwordAge: parseInt(e.target.value) })}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0"
                />
              </div>
            </div>
          </div>

          {/* Compliance & Authorization */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Compliance & Authorization
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <Laptop className="h-4 w-4 text-gray-400" />
                  Link to Asset
                </label>
                {isLoadingOptions ? (
                  <div className="w-full rounded-lg border border-gray-300 px-3 py-2 flex items-center">
                    <span className="text-gray-500">Loading assets...</span>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="relative">
                      <div className="relative w-full">
                        <select
                          value={formData.asset || ''}
                          onChange={(e) => setFormData({ ...formData, asset: e.target.value })}
                          className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          disabled={mode === 'view'}
                        >
                          {/* Custom dropdown options with more visual details */}
                          {assetOptions.map((option) => {
                            // Add styling for category headers
                            const isCategoryHeader = isObjectOption(option) && 
                              typeof option.value === 'string' && option.value.startsWith('category-');
                            
                            const optionStyle = isCategoryHeader ? 
                              'font-bold text-gray-500 bg-gray-100' : '';
                            
                            // Get the appropriate icon
                            let icon = '💻'; // Default asset icon
                            if (isObjectOption(option) && 'icon' in option && option.icon) {
                              icon = option.icon; // Use the icon from the option if available
                            } else if (isCategoryHeader) {
                              icon = '🗂️'; // Folder icon for categories
                            }
                            
                            return (
                              <option 
                                key={isStringOption(option) ? option : option.value} 
                                value={isStringOption(option) ? option : option.value}
                                disabled={isObjectOption(option) && option.disabled}
                                className={optionStyle}
                              >
                                {/* Using unicode symbols with the specific asset icon */}
                                {isStringOption(option) ? 
                                  option : 
                                  `${icon} ${option.label}`
                                }
                              </option>
                            );
                          })}
                        </select>
                      </div>
                    </div>
                    
                    {formData.asset && formData.asset !== 'None' && formData.asset !== '' && (
                      <div className="border border-blue-200 bg-blue-50 rounded-lg p-3 text-sm">
                        <div className="flex items-start">
                          <div className="bg-blue-100 p-2 rounded-full mr-3">
                            {(() => {
                              // Find the selected asset option to get its icon
                              const selectedOption = assetOptions.find(opt => 
                                isObjectOption(opt) && opt.value === formData.asset
                              );
                              
                              // Get the icon from metadata or use default
                              let icon = '💻'; // Default asset icon
                              if (selectedOption && isObjectOption(selectedOption)) {
                                if (selectedOption.icon) {
                                  icon = selectedOption.icon;
                                } else if (selectedOption.metadata?.icon) {
                                  icon = selectedOption.metadata.icon;
                                }
                              }
                              
                              return (
                                <span className="text-lg">{icon}</span>
                              );
                            })()}
                          </div>
                          <div>
                            <div className="font-medium text-blue-900">Asset Linked</div>
                            <div className="text-blue-600">
                              {(() => {
                                const selectedOption = assetOptions.find(opt => 
                                  isObjectOption(opt) && opt.value === formData.asset
                                );
                                return (selectedOption && isObjectOption(selectedOption)) ? selectedOption.label : formData.asset;
                              })()}
                            </div>
                            <div className="mt-1 text-xs text-blue-700 flex items-center">
                              <Link className="h-3 w-3 mr-1" />
                              This email account is linked to the selected asset
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    <div className="mt-1 text-xs text-gray-500 flex items-center">
                      <InfoIcon className="h-3 w-3 mr-1 text-gray-400" />
                      Link this email account to a physical asset for better tracking and management.
                    </div>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <Package className="h-4 w-4 text-gray-400" />
                  Link to Software
                </label>
                {isLoadingOptions ? (
                  <div className="w-full rounded-lg border border-gray-300 px-3 py-2 flex items-center">
                    <span className="text-gray-500">Loading software...</span>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="relative">
                      <div className="relative w-full">
                        <select
                          value={formData.software || ''}
                          onChange={(e) => setFormData({ ...formData, software: e.target.value })}
                          className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          disabled={mode === 'view'}
                        >
                          {/* Custom dropdown options with more visual details */}
                          {softwareOptions.map((option) => {
                            // Add styling for category headers
                            const isCategoryHeader = isObjectOption(option) && 
                              typeof option.value === 'string' && option.value.startsWith('category-');
                            
                            const optionStyle = isCategoryHeader ? 
                              'font-bold text-gray-500 bg-gray-100' : '';
                            
                            // Get the appropriate icon
                            let icon = '📊'; // Default icon
                            if (isObjectOption(option) && 'icon' in option && option.icon) {
                              icon = option.icon; // Use the icon from the option if available
                            } else if (isCategoryHeader) {
                              icon = '🗂️'; // Folder icon for categories
                            }
                            
                            return (
                              <option 
                                key={isStringOption(option) ? option : option.value} 
                                value={isStringOption(option) ? option : option.value}
                                disabled={isObjectOption(option) && option.disabled}
                                className={optionStyle}
                              >
                                {isStringOption(option) ? 
                                  option : 
                                  `${icon} ${option.label}`
                                }
                              </option>
                            );
                          })}
                        </select>
                      </div>
                    </div>
                    
                    {formData.software && formData.software !== 'None' && formData.software !== '' && (
                      <div className="border border-green-200 bg-green-50 rounded-lg p-3 text-sm">
                        <div className="flex items-start">
                          <div className="bg-green-100 p-2 rounded-full mr-3">
                            {(() => {
                              // Find the selected software option to get its icon
                              const selectedOption = softwareOptions.find(opt => 
                                isObjectOption(opt) && opt.value === formData.software
                              );
                              
                              // Get the icon from metadata or use default
                              let icon = '📊'; // Default software icon
                              if (selectedOption && isObjectOption(selectedOption)) {
                                if (selectedOption.icon) {
                                  icon = selectedOption.icon;
                                } else if (selectedOption.metadata?.icon) {
                                  icon = selectedOption.metadata.icon;
                                }
                              }
                              
                              return (
                                <span className="text-lg">{icon}</span>
                              );
                            })()}
                          </div>
                          <div>
                            <div className="font-medium text-green-900">Software Linked</div>
                            <div className="text-green-600">
                              {(() => {
                                const selectedOption = softwareOptions.find(opt => 
                                  isObjectOption(opt) && opt.value === formData.software
                                );
                                return (selectedOption && isObjectOption(selectedOption)) ? selectedOption.label : formData.software;
                              })()}
                            </div>
                            <div className="mt-1 text-xs text-green-700 flex items-center">
                              <Link className="h-3 w-3 mr-1" />
                              This email account is linked to the selected software license
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    <div className="mt-1 text-xs text-gray-500 flex items-center">
                      <InfoIcon className="h-3 w-3 mr-1 text-gray-400" />
                      Link this email account to software licenses for compliance tracking and license management.
                    </div>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <Ticket className="h-4 w-4 text-gray-400" />
                  Ticket ID
                </label>
                <input
                  type="text"
                  value={formData.ticketId}
                  onChange={(e) => setFormData({ ...formData, ticketId: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Ticket ID"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                  <FileText className="h-4 w-4 text-gray-400" />
                  License Record
                </label>
                <input
                  type="text"
                  value={formData.licenseRecord}
                  onChange={(e) => setFormData({ ...formData, licenseRecord: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="License Record"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-1">
                <FileText className="h-4 w-4 text-gray-400" />
                Remarks / Internal IT Notes
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                rows={3}
                className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Additional notes about the account"
              />
            </div>
          </div>

          {errors.submit && (
            <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.submit}
              </p>
            </div>
          )}

          <div className="flex justify-end gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={handleClose}
              className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
            >
              <X className="h-4 w-4" />
              {mode === 'view' ? 'Back' : 'Cancel'}
            </button>
            
            {mode !== 'view' && (
              <button
                type="submit"
                disabled={isSubmitting}
                className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Save className="h-4 w-4" />
                {isSubmitting ? 'Saving...' : mode === 'create' ? 'Create Account' : 'Update Account'}
              </button>
            )}
            
            {mode === 'view' && initialData?.id && (
              <button
                type="button"
                onClick={() => {
                  // Navigate to edit mode
                  navigate(`/email-management/accounts/edit/${initialData.id}`);
                }}
                className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-lg hover:bg-blue-600"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
                Edit
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
} 