import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateRolesTables1747700000000 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create roles table
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS \`roles\` (
                \`id\` INT NOT NULL AUTO_INCREMENT,
                \`name\` VARCHAR(100) NOT NULL UNIQUE,
                \`description\` TEXT NULL,
                \`category\` ENUM('system', 'dashboard', 'custom') NOT NULL DEFAULT 'system',
                \`permissions\` TEXT NOT NULL,
                \`dashboardAccess\` TEXT NULL,
                \`parentId\` VARCHAR(255) NULL,
                \`userCount\` INT NOT NULL DEFAULT 0,
                \`updatedBy\` VARCHAR(255) NULL,
                \`createdAt\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                \`updatedAt\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        // Create user_role_assignments table
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS \`user_role_assignments\` (
                \`id\` VARCHAR(36) NOT NULL,
                \`userId\` VARCHAR(255) NOT NULL,
                \`roleId\` INT NOT NULL,
                \`expiresAt\` TIMESTAMP NULL,
                \`notes\` TEXT NULL,
                \`assignedBy\` VARCHAR(255) NOT NULL,
                \`projectId\` VARCHAR(255) NOT NULL,
                \`locationId\` VARCHAR(255) NULL,
                \`departmentId\` VARCHAR(255) NULL,
                \`assignedAt\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (\`id\`),
                INDEX \`IDX_USER_ROLE_ASSIGNMENTS_USER_ID\` (\`userId\`),
                INDEX \`IDX_USER_ROLE_ASSIGNMENTS_ROLE_ID\` (\`roleId\`),
                CONSTRAINT \`FK_USER_ROLE_ASSIGNMENTS_ROLE\` FOREIGN KEY (\`roleId\`) REFERENCES \`roles\`(\`id\`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        // Create permission_groups table (referenced in the code)
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS \`permission_groups\` (
                \`id\` VARCHAR(36) NOT NULL,
                \`name\` VARCHAR(100) NOT NULL,
                \`description\` TEXT NULL,
                \`permissions\` TEXT NOT NULL,
                \`createdBy\` VARCHAR(255) NULL,
                \`createdAt\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                \`isCustom\` BOOLEAN NOT NULL DEFAULT FALSE,
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        // Create role_templates table (referenced in the code)
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS \`role_templates\` (
                \`id\` VARCHAR(36) NOT NULL,
                \`name\` VARCHAR(100) NOT NULL,
                \`description\` TEXT NULL,
                \`permissions\` TEXT NOT NULL,
                \`category\` ENUM('predefined', 'custom') NOT NULL DEFAULT 'custom',
                \`iconName\` VARCHAR(50) NULL,
                \`iconColor\` VARCHAR(50) NULL,
                \`createdAt\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);

        // Insert default roles
        await queryRunner.query(`
            INSERT INTO \`roles\` (\`name\`, \`description\`, \`category\`, \`permissions\`, \`dashboardAccess\`, \`userCount\`) VALUES
            ('IT Administrator', 'Full system access with all permissions across all modules', 'system', 'canAccessAllModules,canConfigureSystem,canManageRoles,canAddUsers,canEditUsers,canDeleteUsers,canViewReports,canExportData,canImportData,canViewAllDepartments,canCreateTickets,canEditTickets,canDeleteTickets,canCloseTickets,canCreateEmployee,canEditEmployee,canDeleteEmployee,canViewEmployees,canViewDashboards,canViewAllDashboards,canCreateDashboards,canEditDashboards', NULL, 0),
            ('IT Staff', 'Manage IT resources and tickets', 'system', 'canCreateTickets,canEditTickets,canCloseTickets', NULL, 0),
            ('Department Head', 'Manage department resources and approve requests', 'system', 'canViewDepartmentTickets,canApproveRequests', NULL, 0),
            ('Employee', 'Basic access to create tickets and view own data', 'system', 'canCreateTickets', NULL, 0),
            ('Dashboard Manager', 'Full access to create and manage dashboards', 'dashboard', 'canViewDashboards,canViewAllDashboards,canCreateDashboards,canEditDashboards,canShareDashboards,canExportDashboardData', 'sales,finance,operations,hr,it,executive', 0)
            ON DUPLICATE KEY UPDATE \`name\` = VALUES(\`name\`)
        `);

        // Insert default permission groups
        await queryRunner.query(`
            INSERT INTO \`permission_groups\` (\`id\`, \`name\`, \`description\`, \`permissions\`, \`createdBy\`, \`isCustom\`) VALUES
            ('group-it-admin', 'IT Administrator Group', 'Complete IT system administration access', 'canAccessAllModules,canConfigureSystem,canManageRoles,canAddUsers,canEditUsers,canDeleteUsers,canViewReports,canExportData,canImportData,canViewAllDepartments', 'system', false),
            ('group-service-desk', 'Service Desk Group', 'Service desk and ticket management permissions', 'canCreateTickets,canEditTickets,canCloseTickets,canAssignTickets,canEscalateTickets', 'system', false),
            ('group-hr-admin', 'HR Administrator Group', 'HR management permissions', 'canCreateEmployee,canEditEmployee,canDeleteEmployee,canViewEmployees,canManageAttendance,canManageLeave,canManagePayroll,canManagePerformance', 'system', false),
            ('group-dashboard-admin', 'Dashboard Administrator Group', 'Dashboard management permissions', 'canViewDashboards,canViewAllDashboards,canCreateDashboards,canEditDashboards,canShareDashboards,canExportDashboardData,canConfigureDashboardAlerts,canManageDashboardUsers', 'system', false)
            ON DUPLICATE KEY UPDATE \`name\` = VALUES(\`name\`)
        `);

        // Insert default role templates
        await queryRunner.query(`
            INSERT INTO \`role_templates\` (\`id\`, \`name\`, \`description\`, \`permissions\`, \`category\`, \`iconName\`, \`iconColor\`) VALUES
            ('template-it-admin', 'IT Administrator Template', 'Complete system administration template', 'canAccessAllModules,canConfigureSystem,canManageRoles,canAddUsers,canEditUsers,canDeleteUsers,canViewReports,canExportData,canImportData,canViewAllDepartments', 'predefined', 'Shield', 'text-purple-600'),
            ('template-service-desk', 'Service Desk Template', 'Service desk agent template', 'canCreateTickets,canEditTickets,canCloseTickets,canAssignTickets,canEscalateTickets', 'predefined', 'Users', 'text-blue-600'),
            ('template-hr-admin', 'HR Administrator Template', 'HR management template', 'canCreateEmployee,canEditEmployee,canDeleteEmployee,canViewEmployees,canManageAttendance,canManageLeave,canManagePayroll,canManagePerformance', 'predefined', 'User', 'text-green-600'),
            ('template-dashboard-manager', 'Dashboard Manager Template', 'Dashboard management template', 'canViewDashboards,canViewAllDashboards,canCreateDashboards,canEditDashboards,canShareDashboards,canExportDashboardData', 'predefined', 'Monitor', 'text-indigo-600'),
            ('template-employee', 'Employee Template', 'Basic employee access template', 'canCreateTickets', 'predefined', 'FileText', 'text-gray-600')
            ON DUPLICATE KEY UPDATE \`name\` = VALUES(\`name\`)
        `);

        console.log('✅ Roles and permissions tables created successfully with default data');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop tables in reverse order to avoid foreign key constraint issues
        await queryRunner.query(`DROP TABLE IF EXISTS \`user_role_assignments\``);
        await queryRunner.query(`DROP TABLE IF EXISTS \`role_templates\``);
        await queryRunner.query(`DROP TABLE IF EXISTS \`permission_groups\``);
        await queryRunner.query(`DROP TABLE IF EXISTS \`roles\``);
        
        console.log('✅ Roles and permissions tables dropped successfully');
    }
} 