import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, CreateDateColumn } from 'typeorm';
import { Ticket } from './Ticket';
import { User } from './User';
import { Comment } from './Comment';
import { UserRole } from '../types/common';

@Entity('attachments')
export class Attachment {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'varchar', length: 255 })
  fileName!: string;

  @Column({ type: 'varchar', length: 255 })
  fileUrl!: string;

  @Column({ type: 'varchar', length: 100 })
  fileType!: string;

  @ManyToOne(() => Ticket, ticket => ticket.attachments, {
    onDelete: 'CASCADE',
    nullable: false
  })
  @JoinColumn({ name: 'ticketId' })
  ticket!: Ticket;

  @Column({ type: 'int' })
  ticketId!: number;

  @ManyToOne(() => Comment, comment => comment.attachments, {
    onDelete: 'CASCADE',
    nullable: true
  })
  @JoinColumn({ name: 'commentId' })
  comment?: Comment;

  @Column({ type: 'int', nullable: true })
  commentId?: number;

  @ManyToOne(() => User, user => user.uploads, {
    onDelete: 'CASCADE',
    nullable: false
  })
  @JoinColumn({ name: 'uploadedById' })
  uploadedBy!: User;

  @Column({ type: 'varchar', length: 36 })
  uploadedById!: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.EMPLOYEE
  })
  role!: UserRole;

  @CreateDateColumn()
  uploadedAt!: Date;
} 