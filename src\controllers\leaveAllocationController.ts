import { Request, Response } from 'express';
import { AppDataSource } from '../config/database';
import { LeaveAllocation } from '../entities/LeaveAllocation';
import { LeaveBalance } from '../entities/LeaveBalance';
import { Employee } from '../server/entities/Employee';
import { LeavePolicy } from '../entities/LeavePolicy';

export class LeaveAllocationController {

  /**
   * Get all active employees with their leave allocations
   */
  async getAllActiveEmployeesWithAllocations(req: Request, res: Response): Promise<void> {
    try {
      const year = req.query.year ? parseInt(req.query.year as string) : new Date().getFullYear();
      const department = req.query.department as string;
      const page = req.query.page ? parseInt(req.query.page as string) : 1;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
      const search = req.query.search as string;

      const employeeRepository = AppDataSource.getRepository('Employee');
      const leaveAllocationRepository = AppDataSource.getRepository(LeaveAllocation);
      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);

      // Get all active employees
      let employeeQuery = employeeRepository.createQueryBuilder('employee')
        .leftJoinAndSelect('employee.job', 'job')
        .where('employee.status = :status', { status: 'active' });

      if (department) {
        employeeQuery = employeeQuery.andWhere('job.department = :department', { department });
      }

      if (search) {
        employeeQuery = employeeQuery.andWhere(
          '(employee.firstName LIKE :search OR employee.lastName LIKE :search OR employee.employeeId LIKE :search OR job.department LIKE :search)',
          { search: `%${search}%` }
        );
      }

      const totalCount = await employeeQuery.getCount();
      const offset = (page - 1) * limit;
      employeeQuery = employeeQuery.skip(offset).take(limit);

      const activeEmployees = await employeeQuery.getMany();

      // Get all allocations for the year
      const allAllocations = await leaveAllocationRepository.find({
        where: {
          year,
          isActive: true
        }
      });

      // Get all balances for the year to show usage
      const allBalances = await leaveBalanceRepository.find({
        where: {
          year,
          isActive: true
        }
      });

      // Group allocations and balances by employee ID
      const allocationsByEmployee: { [key: number]: LeaveAllocation[] } = {};
      const balancesByEmployee: { [key: number]: LeaveBalance[] } = {};

      allAllocations.forEach(allocation => {
        if (!allocationsByEmployee[allocation.employeeId]) {
          allocationsByEmployee[allocation.employeeId] = [];
        }
        allocationsByEmployee[allocation.employeeId].push(allocation);
      });

      allBalances.forEach(balance => {
        if (!balancesByEmployee[balance.employeeId]) {
          balancesByEmployee[balance.employeeId] = [];
        }
        balancesByEmployee[balance.employeeId].push(balance);
      });

      // Combine employee data with their allocations and balances
      const result = activeEmployees.map((employee: any) => {
        const employeeAllocations = allocationsByEmployee[employee.id] || [];
        const employeeBalances = balancesByEmployee[employee.id] || [];
        const fullName = `${employee.firstName || ''} ${employee.lastName || ''}`.trim();

        return {
          employeeId: employee.id,
          employeeName: fullName,
          employeeCode: employee.employeeId || `EMP${String(employee.id).padStart(3, '0')}`,
          department: employee.job?.department || 'N/A',
          position: employee.job?.designation || 'N/A',
          allocations: employeeAllocations.map(allocation => {
            // Find corresponding balance for usage info
            const balance = employeeBalances.find(b => b.leaveType === allocation.leaveType);
            
            return {
              leaveType: allocation.leaveType,
              policyAllocation: allocation.policyAllocation,
              manualAdjustment: allocation.manualAdjustment,
              carriedForward: allocation.carriedForward,
              totalAllocated: allocation.totalAllocated,
              used: balance?.used || 0,
              pending: balance?.pending || 0,
              remaining: allocation.totalAllocated - (balance?.used || 0) - (balance?.pending || 0),
              source: allocation.source,
              notes: allocation.notes,
              lastUpdated: allocation.updatedAt
            };
          })
        };
      });

      const totalPages = Math.ceil(totalCount / limit);

      res.json({
        success: true,
        data: result,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        }
      });
    } catch (error: any) {
      console.error('Error fetching employees with allocations:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch employees with allocations',
        error: error.message
      });
    }
  }

  /**
   * Bulk save allocation adjustments
   */
  async bulkSaveAllocationAdjustments(req: Request, res: Response): Promise<void> {
    try {
      const { adjustments } = req.body;

      if (!adjustments || !Array.isArray(adjustments)) {
        res.status(400).json({
          success: false,
          message: 'Invalid adjustments data. Expected array of adjustment objects.'
        });
        return;
      }

      console.log(`📥 Received bulk allocation adjustment request for ${adjustments.length} records`);

      const leaveAllocationRepository = AppDataSource.getRepository(LeaveAllocation);
      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
      const employeeRepository = AppDataSource.getRepository('Employee');

      let processedCount = 0;
      let createdCount = 0;
      let updatedCount = 0;
      const errors: string[] = [];

      for (const adjustment of adjustments) {
        try {
          const { employeeId, leaveType, year, totalAllocated, notes } = adjustment;

          console.log(`🔍 Processing adjustment:`, {
            employeeId,
            leaveType,
            year,
            totalAllocated,
            notes
          });

          if (!employeeId || !leaveType || !year || totalAllocated === undefined) {
            errors.push(`Missing required fields for adjustment: ${JSON.stringify(adjustment)}`);
            continue;
          }

          // Verify employee exists
          const employee = await employeeRepository.findOne({ where: { id: employeeId } });
          if (!employee) {
            console.log(`❌ Employee not found: ${employeeId}`);
            errors.push(`Employee not found: ${employeeId}`);
            continue;
          }

          const employeeName = `${employee.firstName || ''} ${employee.lastName || ''}`.trim();
          console.log(`✅ Employee found: ${employeeName} (ID: ${employeeId})`);

          // Check if allocation already exists
          let allocation = await leaveAllocationRepository.findOne({
            where: { employeeId, leaveType, year }
          });

          if (allocation) {
            console.log(`📝 Updating existing allocation for ${employeeName} - ${leaveType}: ${allocation.totalAllocated} → ${totalAllocated}`);
            
            // Calculate the adjustment amount
            const adjustmentAmount = totalAllocated - allocation.totalAllocated;
            allocation.manualAdjustment += adjustmentAmount;
            allocation.source = 'BULK_ADJUSTMENT';
            allocation.notes = notes || `Bulk adjustment: ${adjustmentAmount > 0 ? '+' : ''}${adjustmentAmount} - ${new Date().toISOString()}`;
            allocation.isActive = true;
            updatedCount++;
          } else {
            console.log(`🆕 Creating new allocation for ${employeeName} - ${leaveType}: ${totalAllocated}`);
            
            // Create new allocation (assuming it's a manual allocation)
            allocation = leaveAllocationRepository.create({
              employeeId,
              leaveType,
              year,
              policyAllocation: 0,
              manualAdjustment: totalAllocated,
              carriedForward: 0,
              source: 'BULK_ADJUSTMENT',
              notes: notes || `Manual allocation via bulk adjustment - ${new Date().toISOString()}`,
              isActive: true
            });
            createdCount++;
          }

          const savedAllocation = await leaveAllocationRepository.save(allocation);
          console.log(`💾 Saved allocation to database:`, {
            id: savedAllocation.id,
            employeeId: savedAllocation.employeeId,
            leaveType: savedAllocation.leaveType,
            totalAllocated: savedAllocation.totalAllocated,
            year: savedAllocation.year
          });

          // Now update or create the corresponding balance record
          let balance = await leaveBalanceRepository.findOne({
            where: { employeeId, leaveType, year }
          });

          if (balance) {
            // Update existing balance
            balance.totalAllocated = savedAllocation.totalAllocated;
            balance.notes = `Updated from allocation - ${new Date().toISOString()}`;
          } else {
            // Create new balance
            balance = leaveBalanceRepository.create({
              employeeId,
              leaveType,
              year,
              totalAllocated: savedAllocation.totalAllocated,
              used: 0,
              pending: 0,
              carriedForward: savedAllocation.carriedForward,
              lapsed: 0,
              notes: `Created from allocation - ${new Date().toISOString()}`,
              isActive: true
            });
          }

          await leaveBalanceRepository.save(balance);
          processedCount++;

        } catch (error: any) {
          console.error('Error processing adjustment:', adjustment, error);
          errors.push(`Error processing adjustment for employee ${adjustment.employeeId}: ${error.message}`);
        }
      }

      console.log(`✅ Bulk allocation adjustment completed: ${processedCount} processed, ${createdCount} created, ${updatedCount} updated, ${errors.length} errors`);

      res.json({
        success: true,
        data: {
          totalProcessed: processedCount,
          createdCount,
          updatedCount,
          errorCount: errors.length,
          errors: errors.length > 0 ? errors : undefined
        },
        message: `Successfully processed ${processedCount} allocation adjustments (${createdCount} created, ${updatedCount} updated)${errors.length > 0 ? ` with ${errors.length} errors` : ''}`
      });

    } catch (error: any) {
      console.error('Error in bulk save allocation adjustments:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to save allocation adjustments',
        error: error.message
      });
    }
  }

  /**
   * Auto-allocate leaves based on policies
   */
  async autoAllocateFromPolicies(req: Request, res: Response): Promise<void> {
    try {
      const { year, employeeIds, leaveTypes } = req.body;
      const currentYear = year || new Date().getFullYear();

      console.log(`🚀 Starting auto-allocation for year ${currentYear}`);

      const employeeRepository = AppDataSource.getRepository('Employee');
      const leavePolicyRepository = AppDataSource.getRepository(LeavePolicy);
      const leaveAllocationRepository = AppDataSource.getRepository(LeaveAllocation);
      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);

      // Get employees to allocate for
      let employees;
      if (employeeIds && employeeIds.length > 0) {
        employees = await employeeRepository.findByIds(employeeIds);
      } else {
        employees = await employeeRepository.find({
          where: { status: 'active' },
          relations: ['job']
        });
      }

      // Get leave policies
      let policies;
      if (leaveTypes && leaveTypes.length > 0) {
        policies = await leavePolicyRepository.find({
          where: { leaveType: leaveTypes, isActive: true }
        });
      } else {
        policies = await leavePolicyRepository.find({
          where: { isActive: true }
        });
      }

      let allocatedCount = 0;
      let updatedCount = 0;
      const results: any[] = [];

      for (const employee of employees) {
        for (const policy of policies) {
          try {
            const employeeName = `${employee.firstName} ${employee.lastName}`.trim();

            // Check if allocation already exists
            let allocation = await leaveAllocationRepository.findOne({
              where: {
                employeeId: employee.id,
                leaveType: policy.leaveType,
                year: currentYear
              }
            });

            if (allocation) {
              // Update policy allocation but keep manual adjustments
              const oldPolicyAllocation = allocation.policyAllocation;
              allocation.policyAllocation = policy.defaultAllocation;
              allocation.notes = `Policy updated: ${oldPolicyAllocation} → ${policy.defaultAllocation} - ${new Date().toISOString()}`;
              updatedCount++;
            } else {
              // Create new allocation
              allocation = leaveAllocationRepository.create({
                employeeId: employee.id,
                leaveType: policy.leaveType,
                year: currentYear,
                policyAllocation: policy.defaultAllocation,
                manualAdjustment: 0,
                carriedForward: 0,
                source: 'POLICY',
                notes: `Auto-allocated based on ${policy.name} policy - ${new Date().toISOString()}`,
                isActive: true
              });
              allocatedCount++;
            }

            const savedAllocation = await leaveAllocationRepository.save(allocation);

            // Update or create corresponding balance
            let balance = await leaveBalanceRepository.findOne({
              where: {
                employeeId: employee.id,
                leaveType: policy.leaveType,
                year: currentYear
              }
            });

            if (balance) {
              balance.totalAllocated = savedAllocation.totalAllocated;
              balance.notes = `Updated from policy allocation - ${new Date().toISOString()}`;
            } else {
              balance = leaveBalanceRepository.create({
                employeeId: employee.id,
                leaveType: policy.leaveType,
                year: currentYear,
                totalAllocated: savedAllocation.totalAllocated,
                used: 0,
                pending: 0,
                carriedForward: 0,
                lapsed: 0,
                notes: `Created from policy allocation - ${new Date().toISOString()}`,
                isActive: true
              });
            }

            await leaveBalanceRepository.save(balance);

            results.push({
              employeeId: employee.id,
              employeeName,
              leaveType: policy.leaveType,
              allocated: savedAllocation.totalAllocated,
              source: 'policy'
            });

          } catch (error: any) {
            console.error(`Error allocating ${policy.leaveType} for employee ${employee.id}:`, error);
            results.push({
              employeeId: employee.id,
              employeeName: `${employee.firstName} ${employee.lastName}`.trim(),
              leaveType: policy.leaveType,
              error: error.message
            });
          }
        }
      }

      console.log(`✅ Auto-allocation completed: ${allocatedCount} created, ${updatedCount} updated`);

      res.json({
        success: true,
        data: {
          allocatedCount,
          updatedCount,
          totalEmployees: employees.length,
          totalPolicies: policies.length,
          results
        },
        message: `Auto-allocation completed: ${allocatedCount} new allocations, ${updatedCount} updated`
      });

    } catch (error: any) {
      console.error('Error in auto-allocate from policies:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to auto-allocate from policies',
        error: error.message
      });
    }
  }
} 