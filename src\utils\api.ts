import axios from 'axios';
import { toast } from 'react-hot-toast';

// Get the base URL for API calls
export const getApiBaseUrl = () => {
  // Always use port 5000 for development
  return 'http://localhost:5000';
};

// Get the full URL for a file
export const getFileUrl = (fileUrl: string) => {
  if (!fileUrl) return '';
  
  // If it's already an absolute URL, return it as is
  if (fileUrl.startsWith('http') || fileUrl.startsWith('//')) return fileUrl;
  
  // For development environment, hardcode the server URL
  // This ensures we're always using the correct port (5000)
  const serverUrl = 'http://localhost:5000';
  
  // Extract the filename from the path
  let filename = '';
  
  // Handle different path formats
  if (fileUrl.includes('/')) {
    // Split by / and get the last part
    const parts = fileUrl.split('/');
    filename = parts[parts.length - 1];
  } else {
    // It's already just a filename
    filename = fileUrl;
  }
  
  // Make sure we have a filename
  if (!filename) {
    console.error('Could not extract filename from:', fileUrl);
    return '';
  }
  
  // Log the extracted filename
  console.log('Extracted filename:', filename, 'from path:', fileUrl);
  
  // Use our direct file route for more reliable file serving
  const directFileUrl = `${serverUrl}/direct-file/${encodeURIComponent(filename)}`;
  console.log('Direct File URL:', directFileUrl);
  
  return directFileUrl;
};

// Get common API configuration
export const getApiConfig = () => {
  const token = localStorage.getItem('authToken');
  
  return {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
      'Accept': 'application/json'
    },
    withCredentials: true
  };
};

// Create an axios instance with the configuration
export const createApiInstance = () => {
  const api = axios.create({
    baseURL: getApiBaseUrl(),
    ...getApiConfig()
  });
  
  // Request interceptor
  api.interceptors.request.use(
    (config) => {
      // Make sure all API requests have /api prefix
      if (!config.url?.startsWith('/api/') && !config.url?.startsWith('http')) {
        config.url = '/api' + (config.url?.startsWith('/') ? config.url : `/${config.url}`);
      }
      
      // Log the complete URL being requested for debugging - commented out for production
      // const fullUrl = config.baseURL + config.url;
      // console.log(`API Request: ${config.method?.toUpperCase()} ${fullUrl}`);
      
      // Try to get auth token from localStorage or sessionStorage
      const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      
      return config;
    },
    (error) => {
      console.error('Request error:', error);
      return Promise.reject(error);
    }
  );
  
  // Response interceptor to handle common errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle 401 errors - redirect to login if needed
    if (error.response?.status === 401) {
      console.warn('⚠️ Authentication failed. User needs to log in.');
      // You can add redirect to login logic here if needed
      // window.location.href = '/login';
    }
    
    // Handle 404 errors for missing endpoints
    if (error.response?.status === 404) {
      console.warn(`⚠️ API endpoint not found: ${error.config?.url}`);
      if (error.config?.url?.includes('/expenses') || error.config?.url?.includes('/budget-items')) {
        console.log('🔧 Expense/Budget endpoints are placeholder implementations');
      }
    }
    
    return Promise.reject(error);
  }
);
  
  return api;
};

// Helper function to handle API errors
export const handleApiError = (error: any) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.error('API Error Response:', error.response);
    return {
      status: error.response.status,
      message: error.response.data?.message || 'An error occurred',
      data: error.response.data
    };
  } else if (error.request) {
    // The request was made but no response was received
    console.error('API Error Request:', error.request);
    return {
      status: 0,
      message: 'No response received from server',
      data: null
    };
  } else {
    // Something happened in setting up the request that triggered an Error
    console.error('API Error Setup:', error.message);
    return {
      status: 0,
      message: error.message || 'Unknown error',
      data: null
    };
  }
};

// Default API instance
export default createApiInstance(); 