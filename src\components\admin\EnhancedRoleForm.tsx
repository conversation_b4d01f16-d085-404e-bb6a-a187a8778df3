import React, { useState, useEffect } from 'react';
import { Plus, Trash2, <PERSON>ert<PERSON>ircle, CheckCircle, <PERSON>ting<PERSON>, Clock, Users, Shield } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  critical?: boolean;
  impacts?: string[];
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  parentId?: string;
  category: string;
  dashboardAccess?: string[];
  isDynamic?: boolean;
  dynamicRules?: DynamicRule[];
}

interface DynamicRule {
  id: string;
  name: string;
  description: string;
  conditions: RuleCondition[];
  actions: RuleAction[];
  priority: number;
  isActive: boolean;
}

interface RuleCondition {
  type: 'user_attribute' | 'resource_attribute' | 'time' | 'context';
  field: string;
  operator: 'equals' | 'not_equals' | 'in' | 'not_in' | 'greater_than' | 'less_than' | 'contains';
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

interface RuleAction {
  type: 'grant_permission' | 'deny_permission' | 'require_approval' | 'log_action' | 'notify';
  target: string;
  parameters?: Record<string, any>;
}

interface EnhancedRoleFormProps {
  role: Role | null;
  permissions: Permission[];
  permissionsByCategory: Record<string, Permission[]>;
  roles: Role[];
  onSave: (role: any) => void;
  onCancel: () => void;
  enableDynamicFeatures?: boolean;
}

export const EnhancedRoleForm: React.FC<EnhancedRoleFormProps> = ({
  role,
  permissions,
  permissionsByCategory,
  roles,
  onSave,
  onCancel,
  enableDynamicFeatures = false
}) => {
  const [formData, setFormData] = useState({
    id: role?.id || '',
    name: role?.name || '',
    description: role?.description || '',
    permissions: role?.permissions || [],
    parentId: role?.parentId || '',
    category: role?.category || 'system',
    dashboardAccess: role?.dashboardAccess || [],
    isDynamic: role?.isDynamic || false,
    dynamicRules: role?.dynamicRules || []
  });

  const [showAdvanced, setShowAdvanced] = useState(false);
  const [newRule, setNewRule] = useState<Partial<DynamicRule>>({
    name: '',
    description: '',
    conditions: [],
    actions: [],
    priority: 1,
    isActive: true
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handlePermissionToggle = (permissionId: string) => {
    setFormData(prev => {
      if (prev.permissions.includes(permissionId)) {
        return {
          ...prev,
          permissions: prev.permissions.filter(id => id !== permissionId)
        };
      } else {
        return {
          ...prev,
          permissions: [...prev.permissions, permissionId]
        };
      }
    });
  };

  const handleSelectAllInCategory = (category: string) => {
    const categoryPermissionIds = permissionsByCategory[category].map(p => p.id);
    const allSelected = categoryPermissionIds.every(id => formData.permissions.includes(id));
    
    setFormData(prev => {
      if (allSelected) {
        return {
          ...prev,
          permissions: prev.permissions.filter(id => !categoryPermissionIds.includes(id))
        };
      } else {
        const newPermissions = [...new Set([...prev.permissions, ...categoryPermissionIds])];
        return {
          ...prev,
          permissions: newPermissions
        };
      }
    });
  };

  const addCondition = () => {
    setNewRule(prev => ({
      ...prev,
      conditions: [
        ...(prev.conditions || []),
        {
          type: 'user_attribute',
          field: 'department',
          operator: 'equals',
          value: ''
        }
      ]
    }));
  };

  const addAction = () => {
    setNewRule(prev => ({
      ...prev,
      actions: [
        ...(prev.actions || []),
        {
          type: 'grant_permission',
          target: ''
        }
      ]
    }));
  };

  const addRule = () => {
    if (!newRule.name || !newRule.description) {
      toast.error('Rule name and description are required');
      return;
    }

    const rule: DynamicRule = {
      id: `rule-${Date.now()}`,
      name: newRule.name!,
      description: newRule.description!,
      conditions: newRule.conditions || [],
      actions: newRule.actions || [],
      priority: newRule.priority || 1,
      isActive: newRule.isActive !== false
    };

    setFormData(prev => ({
      ...prev,
      dynamicRules: [...prev.dynamicRules, rule]
    }));

    setNewRule({
      name: '',
      description: '',
      conditions: [],
      actions: [],
      priority: 1,
      isActive: true
    });
  };

  const removeRule = (ruleId: string) => {
    setFormData(prev => ({
      ...prev,
      dynamicRules: prev.dynamicRules.filter(rule => rule.id !== ruleId)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Role name is required');
      return;
    }

    // Prepare data for submission
    const roleData = {
      ...formData,
      dynamicRules: enableDynamicFeatures ? formData.dynamicRules : undefined
    };

    onSave(roleData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Role Name *
          </label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <select
              name="category"
              value={formData.category}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="system">System</option>
              <option value="dashboard">Dashboard</option>
              <option value="custom">Custom</option>
              <option value="operational">Operational</option>
              <option value="administrative">Administrative</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Parent Role
            </label>
            <select
              name="parentId"
              value={formData.parentId}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">No Parent</option>
              {roles.filter(r => r.id !== formData.id).map(role => (
                <option key={role.id} value={role.id}>
                  {role.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {enableDynamicFeatures && (
          <div className="flex items-center">
            <input
              type="checkbox"
              name="isDynamic"
              checked={formData.isDynamic}
              onChange={handleChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-700">
              Enable dynamic permissions and rules
            </label>
          </div>
        )}
      </div>

      {/* Permissions */}
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-3">Permissions</h4>
        <div className="space-y-4">
          {Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => (
            <div key={category} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h5 className="font-medium text-gray-800">{category}</h5>
                <button
                  type="button"
                  onClick={() => handleSelectAllInCategory(category)}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  {categoryPermissions.every(p => formData.permissions.includes(p.id)) 
                    ? 'Deselect All' 
                    : 'Select All'
                  }
                </button>
              </div>
              <div className="grid grid-cols-2 gap-2">
                {categoryPermissions.map(permission => (
                  <label key={permission.id} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.permissions.includes(permission.id)}
                      onChange={() => handlePermissionToggle(permission.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">{permission.name}</span>
                    {permission.critical && (
                      <AlertCircle className="h-4 w-4 text-red-500 ml-1" />
                    )}
                  </label>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Dynamic Rules (if enabled) */}
      {enableDynamicFeatures && formData.isDynamic && (
        <div>
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-lg font-medium text-gray-900">Dynamic Rules</h4>
            <button
              type="button"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
            >
              <Settings className="h-4 w-4" />
              {showAdvanced ? 'Hide Advanced' : 'Show Advanced'}
            </button>
          </div>

          {showAdvanced && (
            <div className="space-y-4 border border-gray-200 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4">
                <input
                  type="text"
                  placeholder="Rule name"
                  value={newRule.name || ''}
                  onChange={(e) => setNewRule(prev => ({ ...prev, name: e.target.value }))}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <input
                  type="text"
                  placeholder="Rule description"
                  value={newRule.description || ''}
                  onChange={(e) => setNewRule(prev => ({ ...prev, description: e.target.value }))}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={addCondition}
                  className="px-3 py-1 bg-blue-100 text-blue-700 rounded text-sm hover:bg-blue-200"
                >
                  Add Condition
                </button>
                <button
                  type="button"
                  onClick={addAction}
                  className="px-3 py-1 bg-green-100 text-green-700 rounded text-sm hover:bg-green-200"
                >
                  Add Action
                </button>
                <button
                  type="button"
                  onClick={addRule}
                  className="px-3 py-1 bg-purple-100 text-purple-700 rounded text-sm hover:bg-purple-200"
                >
                  Save Rule
                </button>
              </div>

              {/* Display existing rules */}
              {formData.dynamicRules.length > 0 && (
                <div className="space-y-2">
                  <h5 className="font-medium text-gray-800">Active Rules:</h5>
                  {formData.dynamicRules.map(rule => (
                    <div key={rule.id} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                      <div>
                        <span className="font-medium">{rule.name}</span>
                        <span className="text-sm text-gray-600 ml-2">{rule.description}</span>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeRule(rule.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Form Actions */}
      <div className="flex justify-end gap-3 pt-4 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          {role ? 'Update Role' : 'Create Role'}
        </button>
      </div>
    </form>
  );
};
