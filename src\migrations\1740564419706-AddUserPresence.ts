import { MigrationInterface, QueryRunner } from "typeorm";

export class AddUserPresence1740564419706 implements MigrationInterface {
    name = 'AddUserPresence1740564419706'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`attachments\` DROP FOREIGN KEY \`fk_attachment_comment\``);
        await queryRunner.query(`DROP INDEX \`idx_comment_attachments\` ON \`attachments\``);
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`presence\` enum ('online', 'away', 'offline') NOT NULL DEFAULT 'offline'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`presence\``);
        await queryRunner.query(`CREATE INDEX \`idx_comment_attachments\` ON \`attachments\` (\`commentId\`)`);
        await queryRunner.query(`ALTER TABLE \`attachments\` ADD CONSTRAINT \`fk_attachment_comment\` FOREIGN KEY (\`commentId\`) REFERENCES \`comments\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
    }

}
