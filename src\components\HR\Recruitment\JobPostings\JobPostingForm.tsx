import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Chip,
  Alert,
  CircularProgress,
  Autocomplete,
  Divider
} from '@mui/material';
import {
  Save as SaveIcon,
  Publish as PublishIcon,
  Preview as PreviewIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { recruitmentAPI } from '../../../../services/recruitmentAPI';

interface JobPostingData {
  title: string;
  description: string;
  requirements: string;
  responsibilities: string;
  benefits: string;
  department: string;
  location: string;
  jobType: string;
  experienceLevel: string;
  workLocation: string;
  minSalary: number;
  maxSalary: number;
  salaryCurrency: string;
  salaryPeriod: string;
  numberOfPositions: number;
  applicationDeadline: string;
  requiredSkills: string[];
  preferredSkills: string[];
  qualifications: string[];
  applicationInstructions: string;
  isActive: boolean;
  isUrgent: boolean;
  isFeatured: boolean;
  allowExternalApplications: boolean;
  externalApplicationUrl: string;
  hiringManagerId: string;
}

const JobPostingForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEdit = Boolean(id && id !== 'new');

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState<JobPostingData>({
    title: '',
    description: '',
    requirements: '',
    responsibilities: '',
    benefits: '',
    department: '',
    location: '',
    jobType: 'full_time',
    experienceLevel: 'mid_level',
    workLocation: 'onsite',
    minSalary: 0,
    maxSalary: 0,
    salaryCurrency: 'USD',
    salaryPeriod: 'yearly',
    numberOfPositions: 1,
    applicationDeadline: '',
    requiredSkills: [],
    preferredSkills: [],
    qualifications: [],
    applicationInstructions: '',
    isActive: true,
    isUrgent: false,
    isFeatured: false,
    allowExternalApplications: true,
    externalApplicationUrl: '',
    hiringManagerId: ''
  });

  useEffect(() => {
    if (isEdit) {
      fetchJobPosting();
    }
  }, [id, isEdit]);

  const fetchJobPosting = async () => {
    try {
      setLoading(true);
      const response = await recruitmentAPI.getJobPostingById(Number(id));
      const posting = response.data.jobPosting;
      
      setFormData({
        ...posting,
        applicationDeadline: posting.applicationDeadline 
          ? new Date(posting.applicationDeadline).toISOString().split('T')[0] 
          : '',
        requiredSkills: posting.requiredSkills || [],
        preferredSkills: posting.preferredSkills || [],
        qualifications: posting.qualifications || []
      });
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch job posting');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof JobPostingData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSkillsChange = (field: 'requiredSkills' | 'preferredSkills', value: string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async (publish: boolean = false) => {
    try {
      setSaving(true);
      const submitData = {
        ...formData,
        applicationDeadline: formData.applicationDeadline || null,
        status: publish ? 'published' : 'draft'
      };

      if (isEdit) {
        await recruitmentAPI.updateJobPosting(Number(id), submitData);
        if (publish) {
          await recruitmentAPI.publishJobPosting(Number(id));
        }
        setSuccess(`Job posting ${publish ? 'published' : 'updated'} successfully`);
      } else {
        const response = await recruitmentAPI.createJobPosting(submitData);
        if (publish) {
          await recruitmentAPI.publishJobPosting(response.data.jobPosting.id);
        }
        setSuccess(`Job posting ${publish ? 'created and published' : 'created'} successfully`);
        
        if (!publish) {
          navigate(`/hr/recruitment/job-postings/${response.data.jobPosting.id}/edit`);
        }
      }
      
      setError(null);
      
      if (publish) {
        setTimeout(() => navigate('/hr/recruitment/job-postings'), 2000);
      }
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to save job posting');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="between" alignItems="center" mb={3}>
        <Box display="flex" alignItems="center">
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/hr/recruitment/job-postings')}
            sx={{ mr: 2 }}
          >
            Back
          </Button>
          <Typography variant="h4" component="h1">
            {isEdit ? 'Edit Job Posting' : 'Create Job Posting'}
          </Typography>
        </Box>
        <Box>
          <Button
            variant="outlined"
            startIcon={<SaveIcon />}
            onClick={() => handleSave(false)}
            disabled={saving}
            sx={{ mr: 1 }}
          >
            Save Draft
          </Button>
          <Button
            variant="contained"
            startIcon={<PublishIcon />}
            onClick={() => handleSave(true)}
            disabled={saving}
          >
            {saving ? <CircularProgress size={20} /> : 'Publish'}
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      {/* Form */}
      <Grid container spacing={3}>
        {/* Basic Information */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Job Title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    required
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Department</InputLabel>
                    <Select
                      value={formData.department}
                      label="Department"
                      onChange={(e) => handleInputChange('department', e.target.value)}
                    >
                      <MenuItem value="Engineering">Engineering</MenuItem>
                      <MenuItem value="Marketing">Marketing</MenuItem>
                      <MenuItem value="Sales">Sales</MenuItem>
                      <MenuItem value="HR">HR</MenuItem>
                      <MenuItem value="Finance">Finance</MenuItem>
                      <MenuItem value="Operations">Operations</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Location"
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    required
                  />
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Job Type</InputLabel>
                    <Select
                      value={formData.jobType}
                      label="Job Type"
                      onChange={(e) => handleInputChange('jobType', e.target.value)}
                    >
                      <MenuItem value="full_time">Full Time</MenuItem>
                      <MenuItem value="part_time">Part Time</MenuItem>
                      <MenuItem value="contract">Contract</MenuItem>
                      <MenuItem value="temporary">Temporary</MenuItem>
                      <MenuItem value="internship">Internship</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Experience Level</InputLabel>
                    <Select
                      value={formData.experienceLevel}
                      label="Experience Level"
                      onChange={(e) => handleInputChange('experienceLevel', e.target.value)}
                    >
                      <MenuItem value="entry_level">Entry Level</MenuItem>
                      <MenuItem value="junior">Junior</MenuItem>
                      <MenuItem value="mid_level">Mid Level</MenuItem>
                      <MenuItem value="senior">Senior</MenuItem>
                      <MenuItem value="lead">Lead</MenuItem>
                      <MenuItem value="manager">Manager</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Work Location</InputLabel>
                    <Select
                      value={formData.workLocation}
                      label="Work Location"
                      onChange={(e) => handleInputChange('workLocation', e.target.value)}
                    >
                      <MenuItem value="onsite">Onsite</MenuItem>
                      <MenuItem value="remote">Remote</MenuItem>
                      <MenuItem value="hybrid">Hybrid</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Job Description"
                    multiline
                    rows={4}
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    required
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Requirements"
                    multiline
                    rows={4}
                    value={formData.requirements}
                    onChange={(e) => handleInputChange('requirements', e.target.value)}
                    required
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Responsibilities"
                    multiline
                    rows={3}
                    value={formData.responsibilities}
                    onChange={(e) => handleInputChange('responsibilities', e.target.value)}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Settings & Options */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Settings
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <TextField
                  fullWidth
                  label="Number of Positions"
                  type="number"
                  value={formData.numberOfPositions}
                  onChange={(e) => handleInputChange('numberOfPositions', Number(e.target.value))}
                  sx={{ mb: 2 }}
                />
                
                <TextField
                  fullWidth
                  label="Application Deadline"
                  type="date"
                  value={formData.applicationDeadline}
                  onChange={(e) => handleInputChange('applicationDeadline', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                  sx={{ mb: 2 }}
                />
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" gutterBottom>
                Options
              </Typography>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => handleInputChange('isActive', e.target.checked)}
                  />
                }
                label="Active"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isUrgent}
                    onChange={(e) => handleInputChange('isUrgent', e.target.checked)}
                  />
                }
                label="Urgent"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isFeatured}
                    onChange={(e) => handleInputChange('isFeatured', e.target.checked)}
                  />
                }
                label="Featured"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.allowExternalApplications}
                    onChange={(e) => handleInputChange('allowExternalApplications', e.target.checked)}
                  />
                }
                label="Allow External Applications"
              />

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" gutterBottom>
                Salary Range
              </Typography>
              
              <Grid container spacing={1}>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Min Salary"
                    type="number"
                    value={formData.minSalary}
                    onChange={(e) => handleInputChange('minSalary', Number(e.target.value))}
                    size="small"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Max Salary"
                    type="number"
                    value={formData.maxSalary}
                    onChange={(e) => handleInputChange('maxSalary', Number(e.target.value))}
                    size="small"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Skills & Qualifications */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Skills & Qualifications
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Autocomplete
                    multiple
                    freeSolo
                    options={[]}
                    value={formData.requiredSkills}
                    onChange={(event, newValue) => handleSkillsChange('requiredSkills', newValue)}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip variant="outlined" label={option} {...getTagProps({ index })} />
                      ))
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Required Skills"
                        placeholder="Type and press Enter"
                      />
                    )}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Autocomplete
                    multiple
                    freeSolo
                    options={[]}
                    value={formData.preferredSkills}
                    onChange={(event, newValue) => handleSkillsChange('preferredSkills', newValue)}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip variant="outlined" label={option} {...getTagProps({ index })} />
                      ))
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Preferred Skills"
                        placeholder="Type and press Enter"
                      />
                    )}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Benefits"
                    multiline
                    rows={3}
                    value={formData.benefits}
                    onChange={(e) => handleInputChange('benefits', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Application Instructions"
                    multiline
                    rows={2}
                    value={formData.applicationInstructions}
                    onChange={(e) => handleInputChange('applicationInstructions', e.target.value)}
                    placeholder="Special instructions for applicants..."
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default JobPostingForm;
