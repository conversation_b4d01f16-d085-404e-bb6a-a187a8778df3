import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { 
  Bar<PERSON>hart3, 
  ClipboardList, 
  Clock, 
  Users, 
  UserPlus, 
  PieC<PERSON>, 
  Settings 
} from 'lucide-react';

interface LeaveManagementLayoutProps {
  children: React.ReactNode;
}

const LeaveManagementLayout: React.FC<LeaveManagementLayoutProps> = ({ children }) => {
  const location = useLocation();

  const tabs = [
    { name: 'Overview', path: '/hr/leave-management/overview', icon: BarChart3 },
    { name: 'All Requests', path: '/hr/leave-management/requests', icon: ClipboardList },
    { name: 'Pending Approvals', path: '/hr/leave-management/approvals', icon: Clock },
    { name: 'Employee Balances', path: '/hr/leave-management/balances', icon: Users },
    { name: 'Leave Allocations', path: '/hr/leave-management/allocations', icon: UserPlus },
    { name: 'Reports & Analytics', path: '/hr/leave-management/reports', icon: Pie<PERSON><PERSON> },
    { name: 'Policies', path: '/hr/leave-management/policies', icon: Settings }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Leave Management</h1>
            <p className="text-gray-600">Comprehensive leave management system</p>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-0 px-6 overflow-x-auto">
          {tabs.map((tab) => (
            <NavLink
              key={tab.name}
              to={tab.path}
              className={({ isActive }) =>
                `py-3 px-4 text-sm font-medium border-b-2 transition-colors flex items-center whitespace-nowrap ${
                  isActive
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`
              }
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.name}
            </NavLink>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6">
        {children}
      </div>
    </div>
  );
};

export default LeaveManagementLayout; 