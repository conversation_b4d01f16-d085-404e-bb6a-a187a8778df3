import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  RefreshCw, 
  LayoutGrid,
  List,
  Users,
  Moon,
  Sun,
  Eye,
  GraduationCap,
  Download,
  Upload,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { useAuth } from '../../../contexts/AuthContext';
import EmployeeService from '../../../services/EmployeeService';
import toast from 'react-hot-toast';
import ConfirmationDialog from '../../ConfirmationDialog';
import EmployeeDetailsView from './EmployeeDetailsView';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import EmployeeImport from './EmployeeImport';

// First define the CertificationEntry type
interface CertificationEntry {
  id?: string;
  name: string;
  issuingOrganization?: string;
  issueDate?: string;
  expiryDate?: string;
  credentialId?: string;
  credentialUrl?: string;
}

// Employee type definition
export interface Employee {
  id: number;
  employeeId: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  gender?: string;
  dateOfBirth?: string;
  religion?: string;
  cnicNumber?: string;
  cnicExpiryDate?: string;
  nationality?: string;
  maritalStatus?: string;
  bloodType?: string;
  
  // Additional Personal Info
  passportNumber?: string;
  passportExpiryDate?: string;
  drivingLicenseNumber?: string;
  drivingLicenseExpiryDate?: string;
  linkedinProfile?: string;
  otherSocialProfiles?: string;
  professionalMemberships?: string;
  hobbiesInterests?: string;
  
  // Contact Info
  officialEmail?: string;
  personalEmail?: string;
  mobileNumber: string;
  officialNumber?: string;
  currentAddress?: string;
  permanentAddress?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelationship?: string;
  
  // Job Details
  department: string;
  designation: string;
  joinDate: string;
  employmentStatus?: string;
  employmentType?: string;
  employeeLevel?: string;
  project?: string;
  location?: string | { name: string };
  probationEndDate?: string;
  noticePeriod?: string;
  reportingTo?: string;
  confirmationDate?: string;
  contractEndDate?: string;
  workLocation?: string;
  reportsTo?: string;
  remoteWork?: boolean;
  
  // Attendance & Leave
  annualLeaveBalance?: number;
  sickLeaveBalance?: number;
  lastLeaveTaken?: string;
  attendanceRate?: number;
  
  // Compensation & Benefits
  salary?: number;
  salaryTier?: string;
  paymentMode?: string;
  bankName?: string;
  bankBranch?: string;
  accountNumber?: string;
  accountTitle?: string;
  iban?: string;
  cashAmount?: string;
  bankAmount?: string;
  totalSalary?: string | number;
  benefit?: {
    totalSalary: string;
    basicSalary?: string;
    housingAllowance?: string;
    transportAllowance?: string;
    medicalAllowance?: string;
    otherAllowances?: string;
    bonus?: string;
    incentives?: string;
    overtimeRate?: string;
    taxDeductions?: string;
    providentFund?: string;
    insurance?: string;
    otherDeductions?: string;
    netSalary?: string;
    salaryTier?: string;
    paymentMode?: string;
    cashAmount?: string;
    bankAmount?: string;
    bankName?: string;
    bankBranch?: string;
    accountNumber?: string;
    iban?: string;
  };
  
  // Allowances
  foodAllowanceInSalary?: boolean;
  foodProvidedByCompany?: boolean;
  numberOfMeals?: string;
  fuelAllowanceInSalary?: boolean;
  fuelInLiters?: string;
  fuelAmount?: string;
  
  // Accommodation Details
  accommodationProvidedByEmployer?: boolean;
  accommodationType?: string;
  accommodationAddress?: string;
  
  // Skills & Certifications
  professionalSkills?: string;
  technicalSkills?: string;
  certifications?: string | CertificationEntry[];
  languages?: string;
  
  // Health & Insurance Info
  healthInsuranceProvider?: string;
  healthInsurancePolicyNumber?: string;
  healthInsuranceExpiryDate?: string;
  lifeInsuranceProvider?: string;
  lifeInsurancePolicyNumber?: string;
  lifeInsuranceExpiryDate?: string;
  vaccinationRecords?: string;
  medicalHistory?: string;
  bloodGroup?: string;
  allergies?: string;
  chronicConditions?: string;
  regularMedications?: string;
  
  // Vehicle Details
  vehicleType?: string;
  registrationNumber?: string;
  providedByCompany?: boolean;
  handingOverDate?: string;
  returnDate?: string;
  vehicleMakeModel?: string;
  vehicleColor?: string;
  mileageAtIssuance?: string;
  
  // Professional/Work-Related Info
  workSchedule?: string;
  shiftType?: string;
  previousEmployeeId?: string;
  remoteWorkEligible?: boolean;
  nextReviewDate?: string;
  trainingRequirements?: string;
  
  // Family Information
  spouseName?: string;
  spouseDateOfBirth?: string;
  spouseOccupation?: string;
  spouseEmployer?: string;
  spouseContactNumber?: string;
  spouseCNIC?: string;
  
  // Arrays and complex objects
  children?: Array<any>;
  dependents?: Array<any>;
  documents?: Array<any>;
  deviceEntries?: Array<any>;
  educationEntries?: Array<any>;
  experienceEntries?: Array<any>;
  projectEntries?: Array<any>;
  
  // Document Management
  requiredDocuments?: Record<string, boolean>;
  
  // Additional metadata
  profileImagePath?: string;
  updatedAt?: string;
  notes?: string;
  specialInstructions?: string;
  
  // Nested objects
  contact?: any;
  job?: any;
  benefits?: any;
  education?: any;
  experience?: any;
  skills?: any;
}

// Department options from your organization
const departments = [
  'IT',
  'HR',
  'FINANCE',
  'MARKETING',
  'SALES',
  'OPERATIONS',
  'CSD',
  'LAND',
  'LEGAL',
  'MANAGEMENT',
  'PND',
  'DIGITAL SALES'
];

// Department display names (for the dropdown)
const departmentNames: Record<string, string> = {
  'IT': 'Information Technology',
  'HR': 'Human Resources',
  'FINANCE': 'Finance & Accounting',
  'MARKETING': 'Marketing & Communications',
  'SALES': 'Sales',
  'DIGITAL SALES': 'Digital Sales',
  'OPERATIONS': 'Operations',
  'CSD': 'Customer Service Department',
  'LAND': 'Land Department',
  'LEGAL': 'Legal',
  'MANAGEMENT': 'Management',
  'PND': 'Planning and Development'
};

// Status options with colors (including dark mode support)
const statusOptions = [
  { value: 'all', label: 'All Status' },
  { value: 'active', label: 'Active', color: 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-300' },
  { value: 'inactive', label: 'Inactive', color: 'bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-300' },
  { value: 'onleave', label: 'On Leave', color: 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-300' },
  { value: 'resigned', label: 'Resigned', color: 'bg-gray-100 dark:bg-gray-900/50 text-gray-800 dark:text-gray-300' },
  { value: 'terminated', label: 'Terminated', color: 'bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-300' },
  { value: 'suspended', label: 'Suspended', color: 'bg-orange-100 dark:bg-orange-900/50 text-orange-800 dark:text-orange-300' },
  { value: 'layoff', label: 'Layoff', color: 'bg-pink-100 dark:bg-pink-900/50 text-pink-800 dark:text-pink-300' },
  { value: 'retired', label: 'Retired', color: 'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300' }
];

// Location options
const locations = [
  'Grand City Head Office Lahore',
  'Grand City Site Office Kharian',
  'Grand City Site Office Arifwala',
  'Grand City Site Office Vehari',
  'Grand City Site Office Faisalabad',
  'Grand City Site Office Murree',
  'ARD Head Office Lahore',
  'ARD Site Office RUDA'
];

// Project options
const projects = [
  'Eurobiz Corporations',
  'Guardian International',
  'Guardian Developers',
  'Grand Developers',
  'ARD Developers'
];

const EmployeeManagement: React.FC<{}> = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [positionFilter, setPositionFilter] = useState('');
  const [locationFilter, setLocationFilter] = useState('');
  const [projectFilter, setProjectFilter] = useState('');
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [uniquePositions, setUniquePositions] = useState<string[]>([]);
  const [uniqueDepartments, setUniqueDepartments] = useState<string[]>([]);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'personal' | 'employment' | 'compensation' | 'skills' | 'documents' | 'experience' | 'education' | 'assets'>('personal');
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [exportScope, setExportScope] = useState<'filtered' | 'all'>('filtered');
  const [exportFormat, setExportFormat] = useState<'excel' | 'import_template' | 'csv' | 'pdf'>('excel');
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [paginatedEmployees, setPaginatedEmployees] = useState<Employee[]>([]);
  
  // Check user permissions - ensure IT_ADMIN has full rights
  const canCreateEmployee = user?.role === 'IT_ADMIN' ? true : (user?.permissions?.canCreateEmployee || user?.role === 'HR_ADMIN' || user?.role === 'HR_STAFF');
  const canEditEmployee = user?.role === 'IT_ADMIN' ? true : (user?.permissions?.canEditEmployee || user?.role === 'HR_ADMIN' || user?.role === 'HR_STAFF');
  const canDeleteEmployee = user?.role === 'IT_ADMIN' ? true : (user?.permissions?.canDeleteEmployee || user?.role === 'HR_ADMIN');
  const canViewEmployees = user?.role === 'IT_ADMIN' ? true : (user?.permissions?.canViewEmployees || user?.role === 'HR_ADMIN' || user?.role === 'HR_STAFF' || user?.role === 'IT_STAFF');
  
  // Load employees from API
  const fetchEmployees = async () => {
    setIsLoading(true);
    try {
      console.log('Fetching employees from API...');
      const response = await EmployeeService.getEmployees();
      
      if (response.error) {
        console.error('Error in response:', response.error);
        toast.error(`Failed to fetch employees: ${response.error}`);
        return;
      }
      
      if (response.data?.success === false) {
        console.error('API returned success:false:', response.data.message);
        toast.error(`Failed to fetch employees: ${response.data.message}`);
        return;
      }
      
      if (response.data?.employees) {
        console.log('Employees loaded successfully:', response.data.employees.length);
        // Log example employee to check data
        if (response.data.employees.length > 0) {
          console.log('Sample employee data:', JSON.stringify(response.data.employees[0], null, 2));
          
          // Debug profile image paths
          const firstThreeEmployees = response.data.employees.slice(0, 3);
          console.log('Profile image paths (first 3 employees):');
          firstThreeEmployees.forEach((emp: any, index: number) => {
            console.log(`Employee ${index + 1} - ID: ${emp.id}, Name: ${emp.firstName} ${emp.lastName}`);
            console.log(`Original profileImagePath: ${emp.profileImagePath || 'null/undefined'}`);
            if (emp.profileImagePath) {
              const formattedPath = `/uploads/${emp.profileImagePath.replace(/^\/uploads\//, '')}`;
              console.log(`Formatted path: ${formattedPath}`);
            }
          });
        }
        
        // Map the API response to include profileImagePath and correct paths
        const processedEmployees = response.data.employees.map((emp: any) => {
          // Ensure profileImagePath is properly formatted
          if (emp.profileImagePath) {
            const originalPath = emp.profileImagePath;
            emp.profileImagePath = emp.profileImagePath.startsWith('/') 
              ? emp.profileImagePath 
              : `/uploads/${emp.profileImagePath.replace(/^uploads\//, '')}`;
            
            console.log(`Employee ${emp.id} (${emp.firstName} ${emp.lastName}) - Profile image path: 
              Original: ${originalPath}
              Processed: ${emp.profileImagePath}`);
          } else {
            console.log(`Employee ${emp.id} (${emp.firstName} ${emp.lastName}) - No profile image path found`);
          }
          return emp;
        });
        
        // Sort employees alphabetically by name
        processedEmployees.sort((a: Employee, b: Employee) => {
          const nameA = `${a.firstName || ''} ${a.lastName || ''}`.trim().toLowerCase();
          const nameB = `${b.firstName || ''} ${b.lastName || ''}`.trim().toLowerCase();
          return nameA.localeCompare(nameB);
        });
        
        console.log('Sample of processed employees (first 3):', 
          processedEmployees.slice(0, 3).map((emp: Employee) => ({
            id: emp.id,
            name: `${emp.firstName} ${emp.lastName}`,
            profileImagePath: emp.profileImagePath || 'none'
          }))
        );
        
        setEmployees(processedEmployees);
        setFilteredEmployees(processedEmployees);
        
        // Extract unique positions from employee data
        const positions = processedEmployees
          .map((emp: Employee) => emp.designation)
          .filter((designation: unknown): designation is string => !!designation)
          .filter((value: string, index: number, self: string[]) => self.indexOf(value) === index)
          .sort();
        
        setUniquePositions(positions);
        
        // Extract unique departments from employee data
        const departments = processedEmployees
          .map((emp: Employee) => emp.department)
          .filter((department: unknown): department is string => !!department)
          .filter((value: string, index: number, self: string[]) => self.indexOf(value) === index)
          .sort();
        
        setUniqueDepartments(departments);
        
        if (processedEmployees.length === 0) {
          toast('No employees found in the database');
        }
      } else {
        console.warn('No employees data in response:', response.data);
        toast.error('No employee data returned from server');
      }
    } catch (err) {
      console.error("Error fetching employees:", err);
      toast.error('Failed to load employees. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchEmployees();
  }, []);
  
  // Apply filters when search term, department filter, or status filter changes
  useEffect(() => {
    let filtered = employees;
    
    // Apply search filter
    if (searchTerm) {
      const searchTermLower = searchTerm.toLowerCase();
      filtered = filtered.filter(
        employee => {
          // Check individual fields
          const firstNameMatch = employee.firstName?.toLowerCase().includes(searchTermLower);
          const lastNameMatch = employee.lastName?.toLowerCase().includes(searchTermLower);
          const emailMatch = employee.officialEmail?.toLowerCase().includes(searchTermLower);
          const employeeIdMatch = employee.employeeId?.toLowerCase().includes(searchTermLower);
          
          // Check full name (first + last)
          const fullName = `${employee.firstName || ''} ${employee.lastName || ''}`.toLowerCase();
          const fullNameMatch = fullName.includes(searchTermLower);
          
          // Return true if any of the searches match
          return firstNameMatch || lastNameMatch || fullNameMatch || emailMatch || employeeIdMatch;
        }
      );
    }
    
    // Apply department filter
    if (departmentFilter) {
      filtered = filtered.filter(employee => 
        employee.department && employee.department.toLowerCase() === departmentFilter.toLowerCase()
      );
    }
    
    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(employee => {
        const status = employee.employmentStatus || employee.status;
        return status?.toLowerCase() === statusFilter;
      });
    }
    
    // Apply position filter
    if (positionFilter) {
      filtered = filtered.filter(employee => employee.designation === positionFilter);
    }
    
    // Apply location filter
    if (locationFilter) {
      filtered = filtered.filter(employee => 
        employee.location === locationFilter || // If location is directly on employee
        (typeof employee.location === 'object' && employee.location?.name === locationFilter) // If location is an object
      );
    }
    
    // Apply project filter
    if (projectFilter) {
      filtered = filtered.filter(employee => employee.project === projectFilter);
    }
    
    setFilteredEmployees(filtered);
    
    // Reset to first page whenever filters change
    setCurrentPage(1);
  }, [searchTerm, departmentFilter, statusFilter, positionFilter, locationFilter, projectFilter, employees]);
  
  // Add useEffect to handle pagination
  useEffect(() => {
    const startIndex = (currentPage - 1) * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    setPaginatedEmployees(filteredEmployees.slice(startIndex, endIndex));
  }, [filteredEmployees, currentPage, rowsPerPage]);
  
  // Function to navigate to a specific page
  const goToPage = (page: number) => {
    const totalPages = Math.ceil(filteredEmployees.length / rowsPerPage);
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  
  const refreshEmployees = () => {
    fetchEmployees();
    toast.success('Employee list refreshed');
  };
  
  const navigateToAddEmployee = () => {
    if (canCreateEmployee) {
    navigate('/hr/addemployee');
    } else {
      toast.error("You don't have permission to add employees");
    }
  };
  
  const navigateToEditForm = (employee: Employee) => {
    if (canEditEmployee) {
    navigate(`/hr/addemployee/${employee.id}`);
    } else {
      toast.error("You don't have permission to edit employees");
    }
  };
  
  const openDeleteModal = (employee: Employee) => {
    if (canDeleteEmployee) {
    setSelectedEmployee(employee);
    setIsDeleteModalOpen(true);
    } else {
      toast.error("You don't have permission to delete employees");
    }
  };
  
  const deleteEmployee = async () => {
    if (!selectedEmployee) return;
    
    try {
      const { data, error } = await EmployeeService.deleteEmployee(selectedEmployee.id.toString());
      
      if (error) {
        toast.error(`Failed to delete employee: ${error}`);
        return;
      }
      
      // Remove the employee from the local state
      setEmployees(prev => prev.filter(emp => emp.id !== selectedEmployee.id));
      setIsDeleteModalOpen(false);
      toast.success('Employee deleted successfully');
    } catch (err) {
      console.error("Error deleting employee:", err);
      toast.error('Failed to delete employee. Please try again.');
    }
  };
  
  const getStatusBadgeColor = (status: string = 'active') => {
    const statusOption = statusOptions.find(option => option.value === status.toLowerCase());
    return statusOption?.color || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString: string): string => {
    try {
      if (!dateString) return '';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return ''; // Check if date is valid
      return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
    } catch (e) {
      return '';
    }
  };
  
  // Function to format dates specifically for Excel export
  const formatExcelDate = (dateString: string): string => {
    try {
      if (!dateString) return '';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return ''; // Check if date is valid
      
      // Format as YYYY-MM-DD for Excel to recognize it as a date
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (e) {
      return '';
    }
  };

  const openViewModal = async (employee: Employee) => {
    try {
      // Set loading state
      setIsLoading(true);
      
      // First set the basic employee data we have
      setSelectedEmployee(employee);
      setIsViewModalOpen(true);
      
      console.log("Initial employee data:", employee);
      
      // Fetch detailed employee data with all related information
      const { data, error } = await EmployeeService.getEmployee(employee.id.toString());
      
      if (error) {
        console.error("Error fetching employee details:", error);
        toast.error(`Failed to fetch complete employee details: ${error}`);
        setIsLoading(false);
        return;
      }

      // Log the API response to debug
      console.log("Complete API response:", data);

      // Check for the correct data structure
      let completeEmployeeData = null;
      
      if (data?.employee) {
        console.log('Using employee data from response:', data.employee);
        completeEmployeeData = data.employee;
      } else if (data?.data?.employee) {
        console.log('Using nested employee data from response:', data.data.employee);
        completeEmployeeData = data.data.employee;
      }
      
      if (completeEmployeeData) {
        // Flatten nested objects (contact, job, etc.)
        const contactData = completeEmployeeData.contact || {};
        const jobData = completeEmployeeData.job || {};
        const benefitsData = completeEmployeeData.benefits || {};
        
        // Extract salary data from benefit object if it exists
        const benefitSalaryTier = completeEmployeeData.benefit?.salaryTier || benefitsData.salaryTier || null;
        const benefitPaymentMode = completeEmployeeData.benefit?.paymentMode || benefitsData.paymentMode || null;
        
        // Extract bank details from benefit object if it exists
        const benefitBankName = completeEmployeeData.benefit?.bankName || benefitsData.bankName || null;
        const benefitBankBranch = completeEmployeeData.benefit?.bankBranch || benefitsData.bankBranch || null;
        const benefitAccountNumber = completeEmployeeData.benefit?.accountNumber || benefitsData.accountNumber || null;
        const benefitAccountTitle = completeEmployeeData.benefit?.accountTitle || benefitsData.accountTitle || null;
        const benefitIban = completeEmployeeData.benefit?.iban || benefitsData.iban || null;
        
        // Merge all sources, prioritizing backend data, then nested, then old
        const mergedData = {
          ...employee, // fallback
          ...completeEmployeeData, // backend top-level
          ...contactData, // flatten contact
          ...jobData, // flatten job
          ...benefitsData, // flatten benefits
          // Explicitly handle salary-related fields with priority order
          salaryTier: benefitSalaryTier || completeEmployeeData.salaryTier || employee.salaryTier || '',
          paymentMode: benefitPaymentMode || completeEmployeeData.paymentMode || employee.paymentMode || '',
          // Explicitly handle bank details with priority order
          bankName: benefitBankName || completeEmployeeData.bankName || employee.bankName || '',
          bankBranch: benefitBankBranch || completeEmployeeData.bankBranch || employee.bankBranch || '',
          accountNumber: benefitAccountNumber || completeEmployeeData.accountNumber || employee.accountNumber || '',
          accountTitle: benefitAccountTitle || completeEmployeeData.accountTitle || employee.accountTitle || '',
          iban: benefitIban || completeEmployeeData.iban || employee.iban || '',
          bloodType: completeEmployeeData.bloodType || completeEmployeeData.bloodGroup || contactData.bloodType || contactData.bloodGroup || employee.bloodType || employee.bloodGroup || '',
          id: employee.id, // Ensure the correct id is preserved
        };
        
        console.log('Merged employee data with payment and bank details:', {
          originalSalaryTier: employee.salaryTier,
          completeSalaryTier: completeEmployeeData.salaryTier,
          benefitSalaryTier: benefitSalaryTier,
          mergedSalaryTier: mergedData.salaryTier,
          
          originalPaymentMode: employee.paymentMode,
          completePaymentMode: completeEmployeeData.paymentMode,
          benefitPaymentMode: benefitPaymentMode,
          mergedPaymentMode: mergedData.paymentMode,
          
          // Log bank details
          originalBankName: employee.bankName,
          completeBankName: completeEmployeeData.bankName,
          benefitBankName: benefitBankName,
          mergedBankName: mergedData.bankName,
          
          originalAccountNumber: employee.accountNumber,
          completeAccountNumber: completeEmployeeData.accountNumber,
          benefitAccountNumber: benefitAccountNumber,
          mergedAccountNumber: mergedData.accountNumber,
        });
        
        setSelectedEmployee(mergedData);
      }
      
      setIsLoading(false);
    } catch (err) {
      console.error("Error in openViewModal:", err);
      toast.error('Error loading complete employee details.');
      setIsLoading(false);
    }
  };
  
  const getSalaryDisplay = (employee: Employee): string => {
    if (employee.benefit?.totalSalary) {
      return formatCurrency(employee.benefit.totalSalary);
    }
    
    if (employee.totalSalary !== undefined) {
      return formatCurrency(String(employee.totalSalary));
    }
    
    if (employee.salary !== undefined) {
      return formatCurrency(String(employee.salary));
    }
    
    return '0';
  };

  const formatCurrency = (amount: string): string => {
    try {
      // Remove any non-numeric characters except decimal point
      const numericValue = amount.replace(/[^0-9.]/g, '');
      const number = parseFloat(numericValue);
      
      if (isNaN(number)) {
        return '0';
      }
      
      // Format with commas for thousands and no decimal places
      return number.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      });
    } catch (error) {
      return '0';
    }
  };

  const exportEmployees = () => {
    setIsExportModalOpen(true);
  };

  // For Excel exports, consolidate bloodType and bloodGroup to ensure consistent exports
  const getBloodTypeData = (emp: any) => {
    return emp.bloodType || emp.bloodGroup || '';
  };

  const handleExport = () => {
    try {
      // Determine which employees to export
      const employeesToExport = exportScope === 'filtered' ? filteredEmployees : employees;
      
      if (employeesToExport.length === 0) {
        toast.error('No employees to export');
        return;
      }

      setIsLoading(true);
      toast.loading('Preparing export data, please wait...');

      // Fetch complete employee data for each employee
      const fetchCompleteData = async () => {
        const completeEmployees = [];
        
        for (const emp of employeesToExport) {
          try {
            // Fetch detailed employee data with all related information
            const { data, error } = await EmployeeService.getEmployee(emp.id.toString());
            
            if (error) {
              console.error(`Error fetching details for employee ${emp.id}:`, error);
              // Still include basic data we have
              completeEmployees.push(emp);
            } else if (data?.employee) {
              completeEmployees.push(data.employee);
            }
          } catch (error) {
            console.error(`Error fetching employee ${emp.id}:`, error);
            completeEmployees.push(emp);
          }
        }
        
        return completeEmployees;
      };

      // Wait for all the data to be fetched, then perform the export
      fetchCompleteData().then(completeEmployees => {
        // Convert employee data to the appropriate format
        const exportData = completeEmployees.map(emp => {
          if (exportFormat === 'excel') {
            // Full data with all fields for Excel export
            return {
              // Personal Info
              'ID': emp.employeeId,
              'First Name': emp.firstName,
              'Middle Name': emp.middleName || '',
              'Last Name': emp.lastName,
              'Gender': emp.gender || '',
              'Date of Birth': formatExcelDate(emp.dateOfBirth || ''),
              'Religion': emp.religion || '',
              'CNIC Number': emp.cnicNumber || '',
              'CNIC Expiry Date': formatExcelDate(emp.cnicExpiryDate || ''),
              'Nationality': emp.nationality || '',
              'Marital Status': emp.maritalStatus || '',
              'Blood Type': getBloodTypeData(emp),
              'Passport Number': emp.passportNumber || '',
              
              // Contact Info
              'Mobile Number': emp.mobileNumber || '',
              'Official Number': emp.officialNumber || '',
              'Official Email': emp.officialEmail || '',
              'Personal Email': emp.personalEmail || '',
              'Current Address': emp.currentAddress || '',
              'Permanent Address': emp.permanentAddress || '',
              'Emergency Contact Name': emp.emergencyContactName || '',
              'Emergency Contact Phone': emp.emergencyContactPhone || '',
              'Emergency Contact Relationship': emp.emergencyContactRelationship || '',
              
              // Education Info
              'Degree': Array.isArray(emp.educationEntries) && emp.educationEntries.length > 0
                ? emp.educationEntries[0].degree || ''
                : emp.education?.degree || '',
              'Institute': Array.isArray(emp.educationEntries) && emp.educationEntries.length > 0
                ? emp.educationEntries[0].institution || ''
                : emp.education?.institution || '',
              'Year of Graduation': Array.isArray(emp.educationEntries) && emp.educationEntries.length > 0
                ? (emp.educationEntries[0].endDate || emp.educationEntries[0].graduationYear || '')
                : (emp.education?.graduationYear || ''),
              'Education Level': emp.education?.level || emp.educationLevel || '',
              'Major/Field': emp.education?.major || emp.educationMajor || '',
              
              // Job Details
              'Department': emp.department || '',
              'Designation': emp.designation || '',
              'Joining Date': formatExcelDate(emp.joinDate || ''),
              'Experience': calculateExperience(emp.joinDate || ''),
              'Employment Status': emp.employmentStatus || '',
              'Employment Type': emp.employmentType || '',
              'Employee Level': emp.employeeLevel || '',
              'Project': emp.project || '',
              'Location': typeof emp.location === 'string' ? emp.location : (emp.location?.name || ''),
              'Probation End Date': formatExcelDate(emp.probationEndDate || ''),
              'Notice Period': emp.noticePeriod || '',
              'Reporting To': emp.reportingTo || '',
              'Confirmation Date': formatExcelDate(emp.confirmationDate || ''),
              'Contract End Date': formatExcelDate(emp.contractEndDate || ''),
              'Work Location': emp.workLocation || '',
              'Remote Work': emp.remoteWork ? 'Yes' : 'No',
              
              // Attendance & Leave
              'Last Leave Taken': formatExcelDate(emp.lastLeaveTaken || ''),
              
              // Compensation & Benefits
              'Salary': emp.salary?.toString() || '',
              'Salary Tier': emp.salaryTier || '',
              'Payment Mode': emp.paymentMode || '',
              'Bank Name': emp.bankName || '',
              'Bank Branch': emp.bankBranch || '',
              'Account Number': emp.accountNumber || '',
              'Account Title': emp.accountTitle || '',
              'IBAN': emp.iban || '',
              'Cash Amount': emp.cashAmount || '',
              'Bank Amount': emp.bankAmount || '',
              'Total Salary': emp.totalSalary || emp.benefit?.totalSalary || '',
              
              // Allowances
              'Food Allowance In Salary': emp.foodAllowanceInSalary ? 'Yes' : 'No',
              'Food Provided By Company': emp.foodProvidedByCompany ? 'Yes' : 'No',
              'Number Of Meals': emp.numberOfMeals || '',
              'Fuel Allowance In Salary': emp.fuelAllowanceInSalary ? 'Yes' : 'No',
              'Fuel In Liters': emp.fuelInLiters || '',
              'Fuel Amount': emp.fuelAmount || '',
              
              // Accommodation Details
              'Accommodation Provided By Employer': emp.accommodationProvidedByEmployer ? 'Yes' : 'No',
              'Accommodation Type': emp.accommodationType || '',
              'Accommodation Address': emp.accommodationAddress || '',
              
              // Skills & Certifications
              'Professional Skills': emp.professionalSkills || '',
              'Technical Skills': emp.technicalSkills || '',
              'Certifications': typeof emp.certifications === 'string' ? emp.certifications : '',
              'Languages': emp.languages || '',
              
              // Health & Insurance Info
              'Health Insurance Provider': emp.healthInsuranceProvider || '',
              'Health Insurance Policy Number': emp.healthInsurancePolicyNumber || '',
              'Health Insurance Expiry Date': formatExcelDate(emp.healthInsuranceExpiryDate || ''),
              'Life Insurance Provider': emp.lifeInsuranceProvider || '',
              'Life Insurance Policy Number': emp.lifeInsurancePolicyNumber || '',
              'Life Insurance Expiry Date': formatExcelDate(emp.lifeInsuranceExpiryDate || ''),
              'Vaccination Records': emp.vaccinationRecords || '',
              'Medical History': emp.medicalHistory || '',
              // Removed duplicate Blood Group field since we've consolidated it with Blood Type above
              
              // Vehicle Details
              'Vehicle Type': emp.vehicleType || '',
              'Registration Number': emp.registrationNumber || '',
              'Provided By Company': emp.providedByCompany ? 'Yes' : 'No',
              'Handing Over Date': formatExcelDate(emp.handingOverDate || ''),
              'Return Date': formatExcelDate(emp.returnDate || ''),
              'Vehicle Make Model': emp.vehicleMakeModel || '',
              'Vehicle Color': emp.vehicleColor || '',
              'Mileage At Issuance': emp.mileageAtIssuance || '',
              
              // Professional/Work-Related Info
              'Work Schedule': emp.workSchedule || '',
              'Shift Type': emp.shiftType || '',
              'Remote Work Eligible': emp.remoteWorkEligible ? 'Yes' : 'No',
              'Next Review Date': formatExcelDate(emp.nextReviewDate || ''),
              'Training Requirements': emp.trainingRequirements || '',
              
              // Family Information
              'Spouse Name': emp.spouseName || '',
              'Spouse Date Of Birth': formatExcelDate(emp.spouseDateOfBirth || ''),
              'Spouse Occupation': emp.spouseOccupation || '',
              'Spouse Employer': emp.spouseEmployer || '',
              'Spouse Contact Number': emp.spouseContactNumber || '',
              'Spouse CNIC': emp.spouseCNIC || '',
              
              // Additional metadata
              'Notes': emp.notes || '',
              'Special Instructions': emp.specialInstructions || '',
              'Updated At': formatExcelDate(emp.updatedAt || '')
            };
          } else if (exportFormat === 'import_template') {
            // Format data to match the import format exactly
            return {
              'firstName': emp.firstName || '',
              'lastName': emp.lastName || '',
              'middleName': emp.middleName || '',
              'gender': emp.gender || '',
              'dateOfBirth': formatExcelDate(emp.dateOfBirth || ''),
              'nationality': emp.nationality || '',
              'cnicNumber': emp.cnicNumber || '',
              'cnicExpiryDate': formatExcelDate(emp.cnicExpiryDate || ''),
              'employeeId': emp.employeeId || '',
              'department': emp.department || '',
              'designation': emp.designation || '',
              'employmentStatus': emp.employmentStatus || 'active',
              'employmentType': emp.employmentType || 'full-time',
              'joinDate': formatExcelDate(emp.joinDate || ''),
              'mobileNumber': emp.mobileNumber || '',
              'officialEmail': emp.officialEmail || '',
              // Add more import-relevant fields if needed
              'personalEmail': emp.personalEmail || '',
              'religion': emp.religion || '',
              'maritalStatus': emp.maritalStatus || '',
              'currentAddress': emp.currentAddress || '',
              'permanentAddress': emp.permanentAddress || '',
              'project': emp.project || '',
              'location': typeof emp.location === 'string' ? emp.location : (emp.location?.name || ''),
              'employeeLevel': emp.employeeLevel || '',
              'noticePeriod': emp.noticePeriod || '',
              'reportingTo': emp.reportingTo || '',
              'salary': emp.salary?.toString() || emp.totalSalary?.toString() || emp.benefit?.totalSalary || '',
              'paymentMode': emp.paymentMode || '',
              'bankName': emp.bankName || '',
              'bankBranch': emp.bankBranch || '',
              'accountNumber': emp.accountNumber || '',
              'accountTitle': emp.accountTitle || '',
              'iban': emp.iban || '',
              
              // Additional benefit fields
              'foodAllowanceInSalary': emp.foodAllowanceInSalary || false,
              'fuelAllowanceInSalary': emp.fuelAllowanceInSalary || false,
              'numberOfMeals': emp.numberOfMeals || '',
              'fuelInLiters': emp.fuelInLiters || '',
              'fuelAmount': emp.fuelAmount || '',
              'foodProvidedByCompany': emp.foodProvidedByCompany || false,
              
              // Education fields
              'educationLevel': emp.education?.level || '',
              'educationMajor': emp.education?.major || '',
              'educationDegree': Array.isArray(emp.educationEntries) && emp.educationEntries.length > 0
                ? emp.educationEntries[0].degree || ''
                : emp.education?.degree || '',
              'educationInstitution': Array.isArray(emp.educationEntries) && emp.educationEntries.length > 0
                ? emp.educationEntries[0].institution || ''
                : emp.education?.institution || '',
              'educationGraduationYear': Array.isArray(emp.educationEntries) && emp.educationEntries.length > 0
                ? (emp.educationEntries[0].endDate || emp.educationEntries[0].graduationYear || '')
                : (emp.education?.graduationYear || ''),
              'educationGPA': emp.education?.gpa || '',
              
              // Health & Insurance
              'healthInsuranceProvider': emp.healthInsuranceProvider || '',
              'healthInsurancePolicyNumber': emp.healthInsurancePolicyNumber || '',
              'healthInsuranceExpiryDate': emp.healthInsuranceExpiryDate || '',
              'lifeInsuranceProvider': emp.lifeInsuranceProvider || '',
              'lifeInsurancePolicyNumber': emp.lifeInsurancePolicyNumber || '',
              'lifeInsuranceExpiryDate': emp.lifeInsuranceExpiryDate || '',
              
              // Accommodation
              'accommodationProvidedByEmployer': emp.accommodationProvidedByEmployer || false,
              'accommodationType': emp.accommodationType || '',
              'accommodationAddress': emp.accommodationAddress || ''
            };
          } else {
            // Simplified data for CSV and PDF
            return {
              'ID': emp.employeeId,
              'First Name': emp.firstName,
              'Last Name': emp.lastName,
              'Email': emp.officialEmail || '',
              'Department': emp.department,
              'Position': emp.designation,
              'Status': emp.employmentStatus || 'Active',
              'Hire Date': formatDate(emp.joinDate),
              'Experience': calculateExperience(emp.joinDate),
              'Phone': emp.mobileNumber || '',
              'Education': Array.isArray(emp.educationEntries) && emp.educationEntries.length > 0
                ? `${emp.educationEntries[0].degree || ''} - ${emp.educationEntries[0].institution || ''}`
                : emp.education?.level || emp.educationLevel || '',
              'Salary': emp.benefit?.totalSalary || (emp.totalSalary ? String(emp.totalSalary) : '') || (emp.salary ? String(emp.salary) : '0')
            };
          }
        });

        // Now do the actual export
        if (exportFormat === 'excel') {
          exportToExcel(exportData);
        } else if (exportFormat === 'import_template') {
          exportToImportTemplate(exportData);
        } else if (exportFormat === 'csv') {
          exportToCSV(exportData);
        } else if (exportFormat === 'pdf') {
          exportToPDF(exportData);
        }

        // Hide loading toast and show success
        toast.dismiss();
        setIsLoading(false);
        setIsExportModalOpen(false);
        toast.success(`Employees exported successfully as ${exportFormat.toUpperCase()}`);
      }).catch(error => {
        console.error('Error during export:', error);
        toast.dismiss();
        setIsLoading(false);
        toast.error('Failed to export employees');
      });
    } catch (error) {
      console.error('Error exporting employees:', error);
      toast.dismiss();
      setIsLoading(false);
      toast.error('Failed to export employees');
    }
  };

  const exportToExcel = (data: any[]) => {
    try {
      // Create worksheet from data
      const worksheet = XLSX.utils.json_to_sheet(data);
      
      // Create workbook and add worksheet
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Employees");
      
      // Generate Excel file
      XLSX.writeFile(workbook, `employee-data-${new Date().toISOString().split('T')[0]}.xlsx`);
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('Failed to export as Excel');
    }
  };

  const exportToImportTemplate = (data: any[]) => {
    try {
      // Map the data to the format expected by the import system
      const importData = data.map(emp => {
        // Get consolidated blood type data
        const bloodTypeData = getBloodTypeData(emp);
        
        return {
          // Required fields according to import guide
          firstName: emp.firstName || '',
          lastName: emp.lastName || '',
          middleName: emp.middleName || '',
          gender: emp.gender || '',
          dateOfBirth: emp.dateOfBirth || '',
          nationality: emp.nationality || '',
          cnicNumber: emp.cnicNumber || '',
          cnicExpiryDate: emp.cnicExpiryDate || '',
          employeeId: emp.employeeId || '',
          department: emp.department || '',
          designation: emp.designation || '',
          employmentStatus: emp.employmentStatus || 'active',
          bloodType: bloodTypeData,
          employmentType: emp.employmentType || 'full-time',
          joinDate: emp.joinDate || '',
          mobileNumber: emp.mobileNumber || '',
          officialEmail: emp.officialEmail || '',
          personalEmail: emp.personalEmail || '',
          
          // Additional fields for employee entity
          religion: emp.religion || '',
          maritalStatus: emp.maritalStatus || '',
          fatherName: emp.fatherName || '',
          
          // Additional fields for contact entity
          officialNumber: emp.officialNumber || '',
          currentAddress: emp.currentAddress || '',
          permanentAddress: emp.permanentAddress || '',
          emergencyContactName: emp.emergencyContactName || '',
          emergencyContactPhone: emp.emergencyContactPhone || '',
          emergencyContactRelationship: emp.emergencyContactRelationship || '',
          linkedinProfile: emp.linkedinProfile || '',
          otherSocialProfiles: emp.otherSocialProfiles || '',
          
          // Education fields
          educationLevel: emp.education?.level || '',
          educationMajor: emp.education?.major || '',
          educationDegree: Array.isArray(emp.educationEntries) && emp.educationEntries.length > 0
            ? emp.educationEntries[0].degree || ''
            : emp.education?.degree || '',
          educationInstitution: Array.isArray(emp.educationEntries) && emp.educationEntries.length > 0
            ? emp.educationEntries[0].institution || ''
            : emp.education?.institution || '',
          educationGraduationYear: Array.isArray(emp.educationEntries) && emp.educationEntries.length > 0
            ? (emp.educationEntries[0].endDate || emp.educationEntries[0].graduationYear || '')
            : (emp.education?.graduationYear || ''),
          educationGPA: emp.education?.gpa || '',
          
          // Additional fields for job entity
          project: emp.project || '',
          location: typeof emp.location === 'string' ? emp.location : (emp.location?.name || ''),
          employeeLevel: emp.employeeLevel || '',
          probationEndDate: emp.probationEndDate || '',
          noticePeriod: emp.noticePeriod || '',
          reportingTo: emp.reportingTo || '',
          remoteWorkEligible: emp.remoteWorkEligible || false,
          nextReviewDate: emp.nextReviewDate || '',
          trainingRequirements: emp.trainingRequirements || '',
          workSchedule: emp.workSchedule || '',
          shiftType: emp.shiftType || '',
          
          // Benefit fields
          totalSalary: emp.totalSalary || emp.benefit?.totalSalary || emp.salary || '',
          salaryTier: emp.salaryTier || '',
          cashAmount: emp.cashAmount || '',
          bankAmount: emp.bankAmount || '',
          paymentMode: emp.paymentMode || '',
          bankName: emp.bankName || '',
          bankBranch: emp.bankBranch || '',
          accountNumber: emp.accountNumber || '',
          accountTitle: emp.accountTitle || '',
          iban: emp.iban || '',
          
          // Additional benefit fields
          foodAllowanceInSalary: emp.foodAllowanceInSalary || false,
          fuelAllowanceInSalary: emp.fuelAllowanceInSalary || false,
          numberOfMeals: emp.numberOfMeals || '',
          fuelInLiters: emp.fuelInLiters || '',
          fuelAmount: emp.fuelAmount || '',
          foodProvidedByCompany: emp.foodProvidedByCompany || false,
          
          // Health & Insurance
          healthInsuranceProvider: emp.healthInsuranceProvider || '',
          healthInsurancePolicyNumber: emp.healthInsurancePolicyNumber || '',
          healthInsuranceExpiryDate: emp.healthInsuranceExpiryDate || '',
          lifeInsuranceProvider: emp.lifeInsuranceProvider || '',
          lifeInsurancePolicyNumber: emp.lifeInsurancePolicyNumber || '',
          lifeInsuranceExpiryDate: emp.lifeInsuranceExpiryDate || '',
          
          // Accommodation
          accommodationProvidedByEmployer: emp.accommodationProvidedByEmployer || false,
          accommodationType: emp.accommodationType || '',
          accommodationAddress: emp.accommodationAddress || ''
        };
      });
      
      // Create worksheet from data
      const worksheet = XLSX.utils.json_to_sheet(importData);
      
      // Create workbook and add worksheet
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Employees");
      
      // Generate Excel file
      XLSX.writeFile(workbook, `employee-import-template-${new Date().toISOString().split('T')[0]}.xlsx`);
    } catch (error) {
      console.error('Error exporting to import template:', error);
      toast.error('Failed to export as import template');
    }
  };

  const exportToCSV = (data: any[]) => {
    try {
      // Convert data to CSV format
      const headers = Object.keys(data[0] || {}).join(',');
      const csvContent = data.map(row => 
        Object.values(row).map(value => 
          typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value
        ).join(',')
      );
      
      const csvString = [headers, ...csvContent].join('\n');
      const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      
      link.href = url;
      link.download = `employee-data-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      toast.error('Failed to export as CSV');
    }
  };

  const exportToPDF = (data: any[]) => {
    try {
      // Initialize jsPDF in landscape mode with larger page size
      const doc = new jsPDF('landscape', 'mm', 'a4');
      const pageWidth = doc.internal.pageSize.width;
      const pageHeight = doc.internal.pageSize.height;
      const margin = 15;

      // Helper function to create gradient-like header
      const drawPageHeader = (text: string = 'Employee Directory Report') => {
        // Header background
        doc.setFillColor(41, 128, 185);
        doc.rect(0, 0, pageWidth, 35, 'F');
        doc.setFillColor(52, 152, 219);
        doc.rect(0, 35, pageWidth, 2, 'F');

        // Company logo placeholder
        doc.setFillColor(255, 255, 255);
        doc.rect(margin, 10, 40, 15, 'F');
        doc.setTextColor(41, 128, 185);
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.text('COMPANY', margin + 5, 20);

        // Report title
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(24);
        doc.setFont('helvetica', 'bold');
        doc.text(text, pageWidth / 2, 22, { align: 'center' });

        // Report metadata
        doc.setFontSize(10);
        doc.setFont('helvetica', 'normal');
        const currentDate = new Date().toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
        doc.text(`Generated on: ${currentDate}`, pageWidth - margin, 20, { align: 'right' });
      };

      // Draw main header
      drawPageHeader();

      // Calculate and draw statistics
      const stats = {
        total: data.length,
        active: data.filter(emp => emp['Status'].toLowerCase() === 'active').length,
        departments: [...new Set(data.map(emp => emp['Department']))].length,
        totalSalary: data.reduce((sum, emp) => sum + parseFloat(emp['Salary'].replace(/[^0-9.-]+/g, '')), 0),
        locations: [...new Set(data.map(emp => emp['Location']))].length,
        avgSalary: data.reduce((sum, emp) => sum + parseFloat(emp['Salary'].replace(/[^0-9.-]+/g, '')), 0) / data.length
      };

      // Draw statistics boxes
      const boxWidth = (pageWidth - (margin * 2) - 25) / 3;
      const boxHeight = 30;
      const boxStartY = 45;
      const boxes = [
        [
          { label: 'Total Employees', value: stats.total.toString(), icon: '👥' },
          { label: 'Active Employees', value: `${stats.active} (${Math.round(stats.active/stats.total*100)}%)`, icon: '✓' },
        ],
        [
          { label: 'Departments', value: stats.departments.toString(), icon: '🏢' },
          { label: 'Locations', value: stats.locations.toString(), icon: '📍' },
        ],
        [
          { label: 'Total Monthly Payroll', value: `PKR ${stats.totalSalary.toLocaleString()}`, icon: '💰' },
          { label: 'Average Salary', value: `PKR ${Math.round(stats.avgSalary).toLocaleString()}`, icon: '📊' },
        ]
      ];

      boxes.forEach((group, groupIndex) => {
        group.forEach((box, boxIndex) => {
          const x = margin + (groupIndex * (boxWidth + 12.5));
          const y = boxStartY + (boxIndex * (boxHeight + 5));
          
          // Box background with gradient effect
          doc.setFillColor(248, 250, 252);
          doc.rect(x, y, boxWidth, boxHeight, 'F');
          doc.setFillColor(241, 245, 249);
          doc.rect(x, y + boxHeight - 5, boxWidth, 5, 'F');
          
          // Box border
          doc.setDrawColor(226, 232, 240);
          doc.rect(x, y, boxWidth, boxHeight);
          
          // Icon and content
          doc.setFontSize(10);
          doc.setTextColor(71, 85, 105);
          doc.text(box.icon, x + 10, y + 13);
          
          doc.setFontSize(9);
          doc.setTextColor(100, 116, 139);
          doc.text(box.label, x + 25, y + 13);
          
          doc.setFontSize(14);
          doc.setTextColor(15, 23, 42);
          doc.setFont('helvetica', 'bold');
          doc.text(box.value, x + 25, y + 25);
        });
      });

      // Department distribution
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(51, 65, 85);
      doc.text('Employee Distribution', margin, boxStartY + (boxHeight * 2) + 25);

      // Prepare table data
      const tableHeaders = [
        [
          { content: 'Employee ID', styles: { halign: 'center' as const, fillColor: [41, 128, 185] as [number, number, number], textColor: 255 }},
          { content: 'Name', styles: { halign: 'left' as const, fillColor: [41, 128, 185] as [number, number, number], textColor: 255 }},
          { content: 'Department', styles: { halign: 'left' as const, fillColor: [41, 128, 185] as [number, number, number], textColor: 255 }},
          { content: 'Position', styles: { halign: 'left' as const, fillColor: [41, 128, 185] as [number, number, number], textColor: 255 }},
          { content: 'Status', styles: { halign: 'center' as const, fillColor: [41, 128, 185] as [number, number, number], textColor: 255 }},
          { content: 'Joining Date', styles: { halign: 'center' as const, fillColor: [41, 128, 185] as [number, number, number], textColor: 255 }},
          { content: 'Experience', styles: { halign: 'center' as const, fillColor: [41, 128, 185] as [number, number, number], textColor: 255 }},
          { content: 'Email', styles: { halign: 'left' as const, fillColor: [41, 128, 185] as [number, number, number], textColor: 255 }},
          { content: 'Phone', styles: { halign: 'center' as const, fillColor: [41, 128, 185] as [number, number, number], textColor: 255 }},
          { content: 'Salary (PKR)', styles: { halign: 'right' as const, fillColor: [41, 128, 185] as [number, number, number], textColor: 255 }}
        ]
      ];

      const tableRows = data.map(item => [
        { content: item['ID'], styles: { halign: 'center' as const }},
        { content: `${item['First Name']} ${item['Last Name']}`, styles: { halign: 'left' as const }},
        { content: item['Department'], styles: { halign: 'left' as const }},
        { content: item['Position'], styles: { halign: 'left' as const }},
        { 
          content: item['Status'],
          styles: {
            halign: 'center' as const,
            fillColor: item['Status'].toLowerCase() === 'active' ? [220, 252, 231] as [number, number, number] :
                      item['Status'].toLowerCase() === 'inactive' ? [254, 226, 226] as [number, number, number] :
                      item['Status'].toLowerCase() === 'onleave' ? [254, 249, 195] as [number, number, number] :
                      undefined,
            textColor: item['Status'].toLowerCase() === 'active' ? [22, 101, 52] as [number, number, number] :
                      item['Status'].toLowerCase() === 'inactive' ? [153, 27, 27] as [number, number, number] :
                      item['Status'].toLowerCase() === 'onleave' ? [161, 98, 7] as [number, number, number] :
                      undefined
          }
        },
        { content: item['Hire Date'], styles: { halign: 'center' as const }},
        { content: calculateExperience(item['Hire Date']), styles: { halign: 'center' as const }},
        { content: item['Email'], styles: { halign: 'left' as const }},
        { content: item['Phone'], styles: { halign: 'center' as const }},
        { content: item['Salary'], styles: { halign: 'right' as const }}
      ]);

      // Add table
      autoTable(doc, {
        startY: boxStartY + (boxHeight * 2) + 35,
        head: tableHeaders,
        body: tableRows,
        theme: 'grid',
        headStyles: {
          fontSize: 9,
          fontStyle: 'bold',
          cellPadding: 3,
          minCellHeight: 12,
          valign: 'middle'
        },
        bodyStyles: {
          fontSize: 8.5,
          cellPadding: { top: 4, right: 3, bottom: 4, left: 3 },
          minCellHeight: 10,
          valign: 'middle',
          lineColor: [226, 232, 240]
        },
        columnStyles: {
          0: { cellWidth: 20 },  // ID
          1: { cellWidth: 35 },  // Name
          2: { cellWidth: 30 },  // Department
          3: { cellWidth: 30 },  // Position
          4: { cellWidth: 22 },  // Status
          5: { cellWidth: 25 },  // Joining Date
          6: { cellWidth: 38 },  // Experience
          7: { cellWidth: 38 },  // Email
          8: { cellWidth: 25 },  // Phone
          9: { cellWidth: 25 }   // Salary
        },
        margin: { top: 25, right: margin, bottom: 15, left: margin },
        didParseCell: function(data) {
          if (data.section === 'body') {
            data.cell.styles.cellWidth = 'wrap';
            if (data.column.index === 6) { // Email column
              data.cell.styles.fontSize = 7.5;
            }
          }
        },
        willDrawCell: function(data) {
          if (data.section === 'body' && data.column.index !== 4) { // Skip status column
            if (data.row.index % 2 === 0) {
              data.cell.styles.fillColor = [248, 250, 252] as [number, number, number];
            }
          }
        },
        didDrawPage: function(data) {
          // Add header to each page after first page
          if (data.pageNumber > 1) {
            drawPageHeader('Employee Directory Report - Continued');
          }

          // Add page number
          doc.setFontSize(8);
          doc.setTextColor(148, 163, 184);
          doc.text(
            `Page ${data.pageNumber} of ${(doc as any).internal.getNumberOfPages()}`,
            pageWidth / 2,
            pageHeight - 5,
            { align: 'center' }
          );

          // Add footer
          doc.setFontSize(7);
          doc.setTextColor(148, 163, 184);
          doc.text(
            'Confidential - For Internal Use Only',
            margin,
            pageHeight - 5
          );
          doc.text(
            `Generated by HR Management System • ${new Date().toLocaleDateString()}`,
            pageWidth - margin,
            pageHeight - 5,
            { align: 'right' }
          );
        }
      });

      // Save the PDF
      doc.save(`employee-directory-report-${new Date().toISOString().split('T')[0]}.pdf`);
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      toast.error('Failed to export as PDF');
    }
  };

  const openImportModal = () => {
    setIsImportModalOpen(true);
  };
  
  const closeImportModal = () => {
    setIsImportModalOpen(false);
  };
  
  const handleImportComplete = (importedEmployees: any[]) => {
    console.log('Imported employees:', importedEmployees);
    fetchEmployees(); // Refresh the employee list after import
    toast.success(`Successfully imported ${importedEmployees.length} employees`);
  };

  // After the formatDate function (around line 743), add this new function:

  const calculateExperience = (joinDateString: string): string => {
    try {
      if (!joinDateString) return 'N/A';
      
      const joinDate = new Date(joinDateString);
      if (isNaN(joinDate.getTime())) return 'N/A'; // Check if date is valid
      
      const currentDate = new Date();
      
      // Calculate difference in years, months, and days
      let years = currentDate.getFullYear() - joinDate.getFullYear();
      let months = currentDate.getMonth() - joinDate.getMonth();
      
      // Adjust years and months if needed
      if (months < 0) {
        years--;
        months += 12;
      }
      
      // Calculate days
      const joinDay = joinDate.getDate();
      const currentDay = currentDate.getDate();
      let days = currentDay - joinDay;
      
      if (days < 0) {
        // Borrow a month
        months--;
        if (months < 0) {
          years--;
          months += 12;
        }
        
        // Calculate days from previous month
        const lastMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0);
        days += lastMonth.getDate();
      }
      
      // Format the result
      const yearText = years > 0 ? `${years} ${years === 1 ? 'year' : 'years'}` : '';
      const monthText = months > 0 ? `${months} ${months === 1 ? 'month' : 'months'}` : '';
      const dayText = days > 0 ? `${days} ${days === 1 ? 'day' : 'days'}` : '';
      
      // Combine parts with commas and 'and'
      const parts = [yearText, monthText, dayText].filter(Boolean);
      
      if (parts.length === 0) return '0 days';
      if (parts.length === 1) return parts[0];
      if (parts.length === 2) return `${parts[0]} and ${parts[1]}`;
      return `${parts[0]}, ${parts[1]}, and ${parts[2]}`;
    } catch (e) {
      console.error('Error calculating experience:', e);
      return 'N/A';
    }
  };

  return (
    <div className="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-300">
      {/* Stats Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-7 gap-3 mb-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 p-3 relative overflow-hidden group hover:translate-y-[-2px] cursor-pointer">
          <div className="flex items-center mb-2">
            <div className="bg-blue-50 dark:bg-blue-900/30 p-1.5 rounded mr-2 group-hover:bg-blue-100 dark:group-hover:bg-blue-800/40 transition-colors duration-300">
              <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </div>
            <div className="flex flex-col flex-1 min-w-0">
              <h3 className="text-xs font-medium text-gray-600 dark:text-gray-300 group-hover:text-blue-700 dark:group-hover:text-blue-400 transition-colors duration-300 truncate">Total</h3>
              <span className="text-xs font-medium text-blue-700 dark:text-blue-300">100%</span>
          </div>
            </div>
          <p className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-blue-700 dark:group-hover:text-blue-400 transition-colors duration-300">{filteredEmployees.length}</p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">All employees</p>
          <div className="h-1 w-full bg-blue-500 dark:bg-blue-600 absolute bottom-0 left-0 group-hover:h-1.5 transition-all duration-300"></div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 p-3 relative overflow-hidden group hover:translate-y-[-2px] cursor-pointer">
          <div className="flex items-center mb-2">
            <div className="bg-green-50 dark:bg-green-900/30 p-1.5 rounded mr-2 group-hover:bg-green-100 dark:group-hover:bg-green-800/40 transition-colors duration-300">
              <svg className="h-4 w-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="flex flex-col flex-1 min-w-0">
              <h3 className="text-xs font-medium text-gray-600 dark:text-gray-300 group-hover:text-green-700 dark:group-hover:text-green-400 transition-colors duration-300 truncate">Active</h3>
              <span className="text-xs font-medium text-green-700 dark:text-green-300">
                  {filteredEmployees.length ? Math.round((filteredEmployees.filter(e => e.employmentStatus?.toLowerCase() === 'active').length / filteredEmployees.length) * 100) : 0}%
                </span>
              </div>
            </div>
          <p className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-green-700 dark:group-hover:text-green-400 transition-colors duration-300">{filteredEmployees.filter(e => e.employmentStatus?.toLowerCase() === 'active').length}</p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">Currently employed</p>
          <div className="h-1 w-full bg-green-500 dark:bg-green-600 absolute bottom-0 left-0 group-hover:h-1.5 transition-all duration-300"></div>
          </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 p-3 relative overflow-hidden group hover:translate-y-[-2px] cursor-pointer">
          <div className="flex items-center mb-2">
            <div className="bg-orange-50 dark:bg-orange-900/30 p-1.5 rounded mr-2 group-hover:bg-orange-100 dark:group-hover:bg-orange-800/40 transition-colors duration-300">
              <svg className="h-4 w-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
          </div>
            <div className="flex flex-col flex-1 min-w-0">
              <h3 className="text-xs font-medium text-gray-600 dark:text-gray-300 group-hover:text-orange-700 dark:group-hover:text-orange-400 transition-colors duration-300 truncate">Suspended</h3>
              <span className="text-xs font-medium text-orange-700 dark:text-orange-300">
                {filteredEmployees.length ? Math.round((filteredEmployees.filter(e => e.employmentStatus?.toLowerCase() === 'suspended').length / filteredEmployees.length) * 100) : 0}%
              </span>
            </div>
          </div>
          <p className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-orange-700 dark:group-hover:text-orange-400 transition-colors duration-300">{filteredEmployees.filter(e => e.employmentStatus?.toLowerCase() === 'suspended').length}</p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">Temporarily on hold</p>
          <div className="h-1 w-full bg-orange-500 dark:bg-orange-600 absolute bottom-0 left-0 group-hover:h-1.5 transition-all duration-300"></div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 p-3 relative overflow-hidden group hover:translate-y-[-2px] cursor-pointer">
          <div className="flex items-center mb-2">
            <div className="bg-gray-50 dark:bg-gray-700 p-1.5 rounded mr-2 group-hover:bg-gray-100 dark:group-hover:bg-gray-600 transition-colors duration-300">
              <svg className="h-4 w-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            </div>
            <div className="flex flex-col flex-1 min-w-0">
              <h3 className="text-xs font-medium text-gray-600 dark:text-gray-300 group-hover:text-gray-700 dark:group-hover:text-gray-200 transition-colors duration-300 truncate">Resigned</h3>
              <span className="text-xs font-medium text-gray-700 dark:text-gray-200">
                  {filteredEmployees.length ? Math.round((filteredEmployees.filter(e => e.employmentStatus?.toLowerCase() === 'resigned').length / filteredEmployees.length) * 100) : 0}%
                </span>
              </div>
            </div>
          <p className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-gray-700 dark:group-hover:text-gray-200 transition-colors duration-300">{filteredEmployees.filter(e => e.employmentStatus?.toLowerCase() === 'resigned').length}</p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">Voluntary departures</p>
          <div className="h-1 w-full bg-gray-400 dark:bg-gray-500 absolute bottom-0 left-0 group-hover:h-1.5 transition-all duration-300"></div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 p-3 relative overflow-hidden group hover:translate-y-[-2px] cursor-pointer">
          <div className="flex items-center mb-2">
            <div className="bg-pink-50 dark:bg-pink-900/30 p-1.5 rounded mr-2 group-hover:bg-pink-100 dark:group-hover:bg-pink-800/40 transition-colors duration-300">
              <svg className="h-4 w-4 text-pink-600 dark:text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </div>
            <div className="flex flex-col flex-1 min-w-0">
              <h3 className="text-xs font-medium text-gray-600 dark:text-gray-300 group-hover:text-pink-700 dark:group-hover:text-pink-400 transition-colors duration-300 truncate">Layoff</h3>
              <span className="text-xs font-medium text-pink-700 dark:text-pink-300">
                {filteredEmployees.length ? Math.round((filteredEmployees.filter(e => {
                  const status = e.employmentStatus || e.status;
                  return status?.toLowerCase() === 'layoff';
                }).length / filteredEmployees.length) * 100) : 0}%
                </span>
              </div>
            </div>
          <p className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-pink-700 dark:group-hover:text-pink-400 transition-colors duration-300">
            {filteredEmployees.filter(e => {
              const status = e.employmentStatus || e.status;
              return status?.toLowerCase() === 'layoff';
            }).length}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">Workforce reduction</p>
          <div className="h-1 w-full bg-pink-500 dark:bg-pink-600 absolute bottom-0 left-0 group-hover:h-1.5 transition-all duration-300"></div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 p-3 relative overflow-hidden group hover:translate-y-[-2px] cursor-pointer">
          <div className="flex items-center mb-2">
            <div className="bg-red-50 dark:bg-red-900/30 p-1.5 rounded mr-2 group-hover:bg-red-100 dark:group-hover:bg-red-800/40 transition-colors duration-300">
              <svg className="h-4 w-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <div className="flex flex-col flex-1 min-w-0">
              <h3 className="text-xs font-medium text-gray-600 dark:text-gray-300 group-hover:text-red-700 dark:group-hover:text-red-400 transition-colors duration-300 truncate">Terminated</h3>
              <span className="text-xs font-medium text-red-700 dark:text-red-300">
                {filteredEmployees.length ? Math.round((filteredEmployees.filter(e => e.employmentStatus?.toLowerCase() === 'terminated').length / filteredEmployees.length) * 100) : 0}%
                </span>
              </div>
            </div>
          <p className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-red-700 dark:group-hover:text-red-400 transition-colors duration-300">{filteredEmployees.filter(e => e.employmentStatus?.toLowerCase() === 'terminated').length}</p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">Ended by company</p>
          <div className="h-1 w-full bg-red-500 dark:bg-red-600 absolute bottom-0 left-0 group-hover:h-1.5 transition-all duration-300"></div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 p-3 relative overflow-hidden group hover:translate-y-[-2px] cursor-pointer">
          <div className="flex items-center mb-2">
            <div className="bg-indigo-50 dark:bg-indigo-900/30 p-1.5 rounded mr-2 group-hover:bg-indigo-100 dark:group-hover:bg-indigo-800/40 transition-colors duration-300">
              <svg className="h-4 w-4 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="flex flex-col flex-1 min-w-0">
              <h3 className="text-xs font-medium text-gray-600 dark:text-gray-300 group-hover:text-indigo-700 dark:group-hover:text-indigo-400 transition-colors duration-300 truncate">Payroll</h3>
              </div>
            </div>
          <div className="min-h-[2.5rem]">
            {(() => {
              const totalSalary = filteredEmployees
                .filter(emp => {
                  const status = emp.employmentStatus || emp.status;
                  return status?.toLowerCase() === 'active';
                })
                .reduce((total, emp) => {
                const salary = emp.salary ? emp.salary : 
                            emp.benefit?.totalSalary ? Number(emp.benefit.totalSalary) : 
                            emp.totalSalary ? (typeof emp.totalSalary === 'string' ? Number(emp.totalSalary) : emp.totalSalary) : 0;
                return total + (salary || 0);
              }, 0);
              
              const formattedSalary = totalSalary.toLocaleString();
              const textSizeClass = formattedSalary.length > 10 ? 'text-sm' : 'text-lg';
              
              return (
                <p className={`font-bold text-gray-900 dark:text-white group-hover:text-indigo-700 dark:group-hover:text-indigo-400 transition-colors duration-300 ${textSizeClass}`}>
                  <span className="text-xs font-medium mr-1 text-gray-700 dark:text-gray-300">PKR</span> 
                  {formattedSalary}
                </p>
              );
            })()}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">Monthly expenditure</p>
          <div className="h-1 w-full bg-indigo-500 dark:bg-indigo-600 absolute bottom-0 left-0 group-hover:h-1.5 transition-all duration-300"></div>
        </div>
      </div>

      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center mb-4 sm:mb-0">
          <Users className="h-8 w-8 text-blue-600 mr-3" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Employee Directory</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Manage your workforce efficiently
            </p>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row items-center gap-3">
          <div className="flex items-center order-2 sm:order-1 mt-3 sm:mt-0 w-full sm:w-auto">
            <div className="relative w-full sm:w-80 md:w-96">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="Search employees..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div className="ml-2 flex items-center border rounded overflow-hidden">
              <button 
                onClick={() => setViewMode('grid')}
                className={`p-2 ${viewMode === 'grid' ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' : 'bg-white dark:bg-gray-700 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'}`}
              >
                <LayoutGrid className="h-5 w-5" />
              </button>
              <button 
                onClick={() => setViewMode('list')}
                className={`p-2 ${viewMode === 'list' ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' : 'bg-white dark:bg-gray-700 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'}`}
              >
                <List className="h-5 w-5" />
              </button>
            </div>
          </div>
          
          <div className="flex flex-wrap order-1 sm:order-2 gap-2">
            <button 
              onClick={exportEmployees}
              className="btn-secondary flex items-center gap-1 px-3 py-2 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none"
            >
              <Download className="h-4 w-4" />
              <span>Export</span>
            </button>
            <button 
              onClick={openImportModal}
              className="btn-secondary flex items-center gap-1 px-3 py-2 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none"
              disabled={!canCreateEmployee}
              title={canCreateEmployee ? "Import employees" : "You don't have permission to import employees"}
            >
              <Upload className="h-4 w-4" />
              <span>Import</span>
            </button>
            <button
              onClick={navigateToAddEmployee}
              className={`btn-primary flex items-center gap-1 px-3 py-2 rounded text-sm font-medium text-white focus:outline-none ${
                canCreateEmployee 
                  ? 'bg-blue-600 hover:bg-blue-700' 
                  : 'bg-blue-400 cursor-not-allowed'
              }`}
              disabled={!canCreateEmployee}
              title={canCreateEmployee ? "Add new employee" : "You don't have permission to add employees"}
            >
              <Plus className="h-4 w-4" />
              <span>Add Employee</span>
            </button>
          </div>
        </div>
      </div>
      
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        {/* Filter Section */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
              <div className="relative">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm appearance-none bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  {statusOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
          </div>
          
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Position</label>
              <div className="relative">
                <select
                  value={positionFilter}
                  onChange={(e) => setPositionFilter(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm appearance-none bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="">All Positions</option>
                  {uniquePositions.map(position => (
                    <option key={position} value={position}>{position}</option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
          </div>
          
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Department</label>
            <div className="relative">
              <select
                value={departmentFilter}
                onChange={(e) => setDepartmentFilter(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm appearance-none bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="">All Departments</option>
                  {uniqueDepartments.map(dept => (
                    <option key={dept} value={dept}>
                      {departmentNames[dept] || dept}
                    </option>
                ))}
              </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Project</label>
            <div className="relative">
              <select
                  value={projectFilter}
                  onChange={(e) => setProjectFilter(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm appearance-none bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="">All Projects</option>
                  {projects.map(project => (
                    <option key={project} value={project}>{project}</option>
                ))}
              </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Location</label>
              <div className="relative">
                <select
                  value={locationFilter}
                  onChange={(e) => setLocationFilter(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm appearance-none bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="">All Locations</option>
                  {locations.map(location => (
                    <option key={location} value={location}>{location}</option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
          </div>
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex justify-center items-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : filteredEmployees.length === 0 ? (
        <div className="p-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">No employees found</p>
        </div>
      ) : viewMode === 'list' ? (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-900">
              <tr>
                <th scope="col" className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider dark:text-white">No.</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider dark:text-white">Staff ID</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider dark:text-white">Employee</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider dark:text-white">Department</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider dark:text-white">Position</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider dark:text-white">Status</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider dark:text-white">Hire Date</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider dark:text-white">Salary</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {paginatedEmployees.map((employee, index) => (
                <tr 
                  key={employee.id} 
                  className="hover:bg-blue-50 dark:hover:bg-blue-900/20 cursor-pointer transition-colors duration-150"
                  onClick={() => openViewModal(employee)}
                >
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    {(currentPage - 1) * rowsPerPage + index + 1}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">{employee.employeeId}</div>
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-12 w-12">
                        {employee.profileImagePath ? (
                          <img 
                            className="h-12 w-12 rounded-full object-cover border-2 border-blue-50 dark:border-gray-600 shadow-sm" 
                            src={`/uploads/${employee.profileImagePath?.replace(/^\/uploads\//,'').replace(/^uploads\//,'')}`}
                            alt={`${employee.firstName} ${employee.lastName}`}
                            onError={(e) => {
                              // If image fails to load, replace with fallback
                              (e.target as HTMLImageElement).style.display = 'none';
                              const parent = (e.target as HTMLImageElement).parentElement;
                              if (parent) {
                                parent.innerHTML = `<div class="h-12 w-12 rounded-full bg-blue-50 dark:bg-gray-600 flex items-center justify-center"><span class="text-sm font-medium text-blue-600 dark:text-gray-300">${employee.firstName?.[0] || ''}${employee.lastName?.[0] || ''}</span></div>`;
                              }
                            }}
                          />
                        ) : (
                          <div className="h-12 w-12 rounded-full bg-blue-50 dark:bg-gray-600 flex items-center justify-center">
                            <span className="text-sm font-medium text-blue-600 dark:text-gray-300">
                              {employee.firstName?.[0]}{employee.lastName?.[0]}
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="ml-5">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {employee.firstName} {employee.lastName}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {employee.officialEmail}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">{employee.department}</div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">{employee.designation}</div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    {(() => {
                      const status = employee.employmentStatus || employee.status;
                      return (
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(status)}`}>
                          {status === 'active' ? 'Active' 
                            : status === 'inactive' ? 'Inactive' 
                            : status === 'onleave' ? 'On Leave'
                            : status === 'resigned' ? 'Resigned'
                            : status === 'terminated' ? 'Terminated'
                            : status === 'suspended' ? 'Suspended'
                            : status === 'layoff' ? 'Layoff'
                            : status === 'retired' ? 'Retired'
                            : status}
                    </span>
                      );
                    })()}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {formatDate(employee.joinDate)}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {getSalaryDisplay(employee)}
                  </td>
                </tr>
              ))}
              {paginatedEmployees.length === 0 && (
                <tr>
                  <td colSpan={8} className="px-4 py-6 text-center text-gray-500 dark:text-gray-400">
                    No employees found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          
          {/* Pagination controls */}
          <div className="bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              {/* Mobile pagination */}
              <button
                onClick={() => goToPage(currentPage - 1)}
                disabled={currentPage === 1}
                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${
                  currentPage === 1 
                    ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500' 
                    : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                Previous
              </button>
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Page {currentPage} of {Math.ceil(filteredEmployees.length / rowsPerPage)}
              </span>
              <button
                onClick={() => goToPage(currentPage + 1)}
                disabled={currentPage === Math.ceil(filteredEmployees.length / rowsPerPage)}
                className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${
                  currentPage === Math.ceil(filteredEmployees.length / rowsPerPage) 
                    ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500' 
                    : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                Next
              </button>
            </div>
            
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Showing <span className="font-medium">{(currentPage - 1) * rowsPerPage + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(currentPage * rowsPerPage, filteredEmployees.length)}
                  </span>{' '}
                  of <span className="font-medium">{filteredEmployees.length}</span> results
                </p>
              </div>
              
              <div className="flex items-center gap-4">
                {/* Rows per page selector */}
                <div className="flex items-center">
                  <span className="text-sm text-gray-700 dark:text-gray-300 mr-2">Rows per page:</span>
                  <select
                    value={rowsPerPage}
                    onChange={(e) => setRowsPerPage(Number(e.target.value))}
                    className="rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:ring-2 focus:ring-blue-600 text-sm"
                  >
                    {[10, 20, 50, 100].map(pageSize => (
                      <option key={pageSize} value={pageSize}>
                        {pageSize}
                      </option>
                    ))}
                  </select>
                </div>
                
                {/* Page navigation */}
                <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                  <button
                    onClick={() => goToPage(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 dark:text-gray-500 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:z-20 focus:outline-offset-0 disabled:bg-gray-100 dark:disabled:bg-gray-800"
                  >
                    <span className="sr-only">Previous</span>
                    <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                  </button>
                  
                  {/* Page numbers */}
                  {[...Array(Math.ceil(filteredEmployees.length / rowsPerPage))].map((_, i) => {
                    const pageNum = i + 1;
                    const totalPages = Math.ceil(filteredEmployees.length / rowsPerPage);
                    
                    // Show current page and a few pages before and after
                    if (
                      pageNum === 1 || 
                      pageNum === totalPages || 
                      (pageNum >= currentPage - 1 && pageNum <= currentPage + 1) ||
                      (totalPages <= 5) // show all if only a few pages
                    ) {
                      return (
                        <button
                          key={i}
                          onClick={() => goToPage(pageNum)}
                          className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                            pageNum === currentPage
                              ? 'z-10 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 ring-1 ring-blue-500 dark:ring-blue-500'
                              : 'text-gray-900 dark:text-gray-100 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    } else if (
                      // Show ellipsis markers where needed
                      (pageNum === 2 && currentPage > 3) ||
                      (pageNum === totalPages - 1 && currentPage < totalPages - 2)
                    ) {
                      return (
                        <span
                          key={i}
                          className="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 dark:text-gray-300 ring-1 ring-inset ring-gray-300 dark:ring-gray-600"
                        >
                          ...
                        </span>
                      );
                    }
                    
                    return null; // Don't show this page number
                  })}
                  
                  <button
                    onClick={() => goToPage(currentPage + 1)}
                    disabled={currentPage === Math.ceil(filteredEmployees.length / rowsPerPage)}
                    className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 dark:text-gray-500 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:z-20 focus:outline-offset-0 disabled:bg-gray-100 dark:disabled:bg-gray-800"
                  >
                    <span className="sr-only">Next</span>
                    <ChevronRight className="h-5 w-5" aria-hidden="true" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6">
          {paginatedEmployees.map((employee) => (
            <div 
              key={employee.id} 
              className="bg-gradient-to-br from-white to-blue-50 dark:from-gray-800 dark:to-blue-900/20 overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 rounded-lg border border-blue-100 dark:border-blue-800/30 flex flex-col hover:translate-y-[-2px] relative group"
              onClick={() => openViewModal(employee)}
            >
              {/* Employee Header with department color */}
              <div className={`p-4 border-b ${
                employee.department?.toLowerCase() === 'it' ? 'bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/40 border-blue-100 dark:border-blue-800/30' :
                employee.department?.toLowerCase() === 'hr' ? 'bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/40 border-purple-100 dark:border-purple-800/30' :
                employee.department?.toLowerCase() === 'finance' ? 'bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/40 border-green-100 dark:border-green-800/30' :
                employee.department?.toLowerCase() === 'marketing' ? 'bg-gradient-to-r from-pink-50 to-pink-100 dark:from-pink-900/30 dark:to-pink-800/40 border-pink-100 dark:border-pink-800/30' :
                employee.department?.toLowerCase() === 'sales' ? 'bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/30 dark:to-yellow-800/40 border-yellow-100 dark:border-yellow-800/30' :
                employee.department?.toLowerCase() === 'digital sales' ? 'bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/30 dark:to-orange-800/40 border-orange-100 dark:border-orange-800/30' :
                employee.department?.toLowerCase() === 'operations' ? 'bg-gradient-to-r from-indigo-50 to-indigo-100 dark:from-indigo-900/30 dark:to-indigo-800/40 border-indigo-100 dark:border-indigo-800/30' :
                employee.department?.toLowerCase() === 'csd' ? 'bg-gradient-to-r from-teal-50 to-teal-100 dark:from-teal-900/30 dark:to-teal-800/40 border-teal-100 dark:border-teal-800/30' :
                employee.department?.toLowerCase() === 'land' ? 'bg-gradient-to-r from-amber-50 to-amber-100 dark:from-amber-900/30 dark:to-amber-800/40 border-amber-100 dark:border-amber-800/30' :
                employee.department?.toLowerCase() === 'legal' ? 'bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/30 dark:to-red-800/40 border-red-100 dark:border-red-800/30' :
                employee.department?.toLowerCase() === 'management' ? 'bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-600/50 border-gray-100 dark:border-gray-700' :
                employee.department?.toLowerCase() === 'pnd' ? 'bg-gradient-to-r from-cyan-50 to-cyan-100 dark:from-cyan-900/30 dark:to-cyan-800/40 border-cyan-100 dark:border-cyan-800/30' :
                'bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700/30 dark:to-gray-600/30 border-gray-100 dark:border-gray-700'
              }`}>
                <div className="flex items-center">
                  <div className="h-20 w-20 rounded-full bg-white dark:bg-gray-700 flex items-center justify-center text-blue-700 dark:text-blue-400 font-medium mr-4 overflow-hidden border-2 border-white dark:border-gray-600 shadow-sm">
                    <img 
                      src={employee.profileImagePath 
                        ? `/uploads/${employee.profileImagePath.replace(/^\/uploads\//,'')}`
                        : `/uploads/employees/${employee.id}/profile.jpg?v=${Date.now()}`}
                      className="h-full w-full object-cover" 
                      alt={`${employee.firstName} ${employee.lastName}`} 
                      onError={(e) => {
                        console.log(`Profile image error for ${employee.firstName} ${employee.lastName} (ID: ${employee.id}). Path attempted: ${(e.target as HTMLImageElement).src}`);
                        const target = e.target as HTMLImageElement;
                        target.onerror = null;
                        // Replace with initials
                        const parent = target.parentElement as HTMLElement;
                        parent.innerHTML = '';
                        parent.style.backgroundColor = '#EBF5FF'; // Light blue background
                        parent.style.color = '#1E40AF'; // Dark blue text
                        parent.style.display = 'flex';
                        parent.style.alignItems = 'center';
                        parent.style.justifyContent = 'center';
                        parent.style.fontWeight = 'bold';
                        parent.style.fontSize = '18px'; // Increased font size for initials
                        const initials = `${employee.firstName?.charAt(0)?.toUpperCase() || ''}${employee.lastName?.charAt(0)?.toUpperCase() || ''}`;
                        parent.textContent = initials;
                      }}
                    />
                  </div>
                  <div>
                    <h3 className="text-base font-semibold text-gray-900 dark:text-white">
                      {employee.firstName} {employee.lastName}
                    </h3>
                    <p className={`text-sm font-medium ${
                      employee.department?.toLowerCase() === 'it' ? 'text-blue-600 dark:text-blue-400' :
                      employee.department?.toLowerCase() === 'hr' ? 'text-purple-600 dark:text-purple-400' :
                      employee.department?.toLowerCase() === 'finance' ? 'text-green-600 dark:text-green-400' :
                      employee.department?.toLowerCase() === 'marketing' ? 'text-pink-600 dark:text-pink-400' :
                      employee.department?.toLowerCase() === 'sales' ? 'text-yellow-600 dark:text-yellow-400' :
                      employee.department?.toLowerCase() === 'digital sales' ? 'text-orange-600 dark:text-orange-400' :
                      employee.department?.toLowerCase() === 'operations' ? 'text-indigo-600 dark:text-indigo-400' :
                      employee.department?.toLowerCase() === 'csd' ? 'text-teal-600 dark:text-teal-400' :
                      employee.department?.toLowerCase() === 'land' ? 'text-amber-600 dark:text-amber-400' :
                      employee.department?.toLowerCase() === 'legal' ? 'text-red-600 dark:text-red-400' :
                      employee.department?.toLowerCase() === 'management' ? 'text-gray-600 dark:text-gray-400' :
                      employee.department?.toLowerCase() === 'pnd' ? 'text-cyan-600 dark:text-cyan-400' :
                      'text-gray-600 dark:text-gray-400'
                    }`}>
                      {employee.designation}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      ID: {employee.employeeId}
                    </p>
                  </div>
                </div>
              </div>
              
              {/* Employee Info */}
              <div className="px-4 py-3 flex-grow">
                <div className="grid grid-cols-1 gap-2">
                  <div className="flex items-center text-sm">
                    <div className="w-4 h-4 text-gray-400 mr-2 flex-shrink-0">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                        <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
                        <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
                      </svg>
                    </div>
                    <span className="text-xs text-gray-700 dark:text-gray-300 overflow-hidden text-ellipsis" title={employee.department}>{employee.department}</span>
                  </div>
                  
                  <div className="flex items-center text-sm">
                    <div className="w-4 h-4 text-gray-400 mr-2 flex-shrink-0">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                        <polyline points="22,6 12,13 2,6"></polyline>
                      </svg>
                    </div>
                    <span className="text-xs text-gray-700 dark:text-gray-300 overflow-hidden text-ellipsis" title={employee.officialEmail || 'No email'}>{employee.officialEmail || 'No email'}</span>
                  </div>
                  
                  <div className="flex items-center text-sm">
                    <div className="w-4 h-4 text-gray-400 mr-2 flex-shrink-0">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                      </svg>
                    </div>
                    <span className="text-xs text-gray-700 dark:text-gray-300 overflow-hidden text-ellipsis" title={employee.mobileNumber || 'No phone'}>{employee.mobileNumber || 'No phone'}</span>
                  </div>
                  
                  <div className="flex justify-between items-center mt-1">
                    <div className="flex items-center text-sm">
                      <div className="w-4 h-4 text-gray-400 mr-2 flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                          <line x1="12" y1="1" x2="12" y2="23"></line>
                          <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                        </svg>
                      </div>
                      <span className="text-xs text-gray-700 dark:text-gray-300 font-medium overflow-hidden text-ellipsis" title={getSalaryDisplay(employee)}>{getSalaryDisplay(employee)}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center text-sm mt-2">
                    <div className="w-4 h-4 text-gray-400 mr-2 flex-shrink-0">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                        <line x1="16" y1="2" x2="16" y2="6"></line>
                        <line x1="8" y1="2" x2="8" y2="6"></line>
                        <line x1="3" y1="10" x2="21" y2="10"></line>
                      </svg>
                    </div>
                    <span className="text-xs text-gray-700 dark:text-gray-300 overflow-hidden text-ellipsis" title={formatDate(employee.joinDate)}>
                      Joined: {formatDate(employee.joinDate)}
                    </span>
                  </div>
                  
                  <div className="flex items-center text-sm mt-2">
                    <div className="w-4 h-4 text-gray-400 mr-2 flex-shrink-0">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12 6 12 12 16 14"></polyline>
                      </svg>
                    </div>
                    <span className="text-xs text-gray-700 dark:text-gray-300 overflow-hidden text-ellipsis" title={calculateExperience(employee.joinDate)}>
                      Experience: {calculateExperience(employee.joinDate)}
                    </span>
                  </div>
                </div>
              </div>
              
              {/* Status Indicator */}
              <div className="border-t border-gray-100 dark:border-gray-700 py-2 px-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-700/50 flex justify-between items-center">
                {(() => {
                  const status = employee.employmentStatus || employee.status;
                  return (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(status)}`}>
                  <span className="mr-1 h-1.5 w-1.5 rounded-full bg-current"></span>
                      {status === 'active' ? 'Active' 
                        : status === 'inactive' ? 'Inactive' 
                        : status === 'onleave' ? 'On Leave'
                        : status === 'resigned' ? 'Resigned'
                        : status === 'terminated' ? 'Terminated'
                        : status === 'suspended' ? 'Suspended'
                        : status === 'layoff' ? 'Layoff'
                        : status === 'retired' ? 'Retired'
                        : status}
                </span>
                  );
                })()}
                
                {/* Action buttons moved here */}
                <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      openViewModal(employee);
                    }}
                    className="bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:text-blue-800 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-800/30 p-1.5 rounded-md text-xs font-medium flex items-center transition-all duration-200 shadow-sm"
                  >
                    <Eye className="h-3.5 w-3.5" />
                  </button>
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      navigateToEditForm(employee);
                    }}
                    className="bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-800/30 p-1.5 rounded-md text-xs font-medium flex items-center transition-all duration-200 shadow-sm"
                  >
                    <Edit className="h-3.5 w-3.5" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      openDeleteModal(employee);
                    }}
                    className="bg-white dark:bg-gray-700 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-800/30 p-1.5 rounded-md text-xs font-medium flex items-center transition-all duration-200 shadow-sm"
                  >
                    <Trash2 className="h-3.5 w-3.5" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination for Card View */}
        <div className="bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6 rounded-b-lg shadow-sm">
          <div className="flex-1 flex justify-between sm:hidden">
            {/* Mobile pagination */}
            <button
              onClick={() => goToPage(currentPage - 1)}
              disabled={currentPage === 1}
              className={`relative inline-flex items-center px-4 py-2 border rounded-md ${
                currentPage === 1 
                  ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 border-gray-200 dark:border-gray-600' 
                  : 'bg-white dark:bg-gray-800 text-indigo-600 dark:text-indigo-400 border-indigo-300 dark:border-indigo-700 hover:bg-indigo-50 dark:hover:bg-indigo-900/20'
              }`}
            >
              Previous
            </button>
            <span className="text-sm text-gray-700 dark:text-gray-300 flex items-center">
              <span className="font-medium mx-1">{currentPage}</span> of <span className="font-medium mx-1">{Math.ceil(filteredEmployees.length / rowsPerPage)}</span>
            </span>
            <button
              onClick={() => goToPage(currentPage + 1)}
              disabled={currentPage === Math.ceil(filteredEmployees.length / rowsPerPage)}
              className={`ml-3 relative inline-flex items-center px-4 py-2 border rounded-md ${
                currentPage === Math.ceil(filteredEmployees.length / rowsPerPage) 
                  ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 border-gray-200 dark:border-gray-600' 
                  : 'bg-white dark:bg-gray-800 text-indigo-600 dark:text-indigo-400 border-indigo-300 dark:border-indigo-700 hover:bg-indigo-50 dark:hover:bg-indigo-900/20'
              }`}
            >
              Next
            </button>
          </div>
          
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Showing <span className="font-medium">{(currentPage - 1) * rowsPerPage + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(currentPage * rowsPerPage, filteredEmployees.length)}
                </span>{' '}
                of <span className="font-medium">{filteredEmployees.length}</span> results
              </p>
            </div>
            
            <div className="flex items-center gap-4">
              {/* Rows per page selector */}
              <div className="flex items-center">
                <span className="text-sm text-gray-700 dark:text-gray-300 mr-2">Cards per page:</span>
                <select
                  value={rowsPerPage}
                  onChange={(e) => setRowsPerPage(Number(e.target.value))}
                  className="rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:ring-2 focus:ring-indigo-600 text-sm"
                >
                  {[8, 12, 16, 24, 32].map(pageSize => (
                    <option key={pageSize} value={pageSize}>
                      {pageSize}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Page navigation */}
              <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                <button
                  onClick={() => goToPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center rounded-l-md px-3 py-2 text-sm font-medium bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 disabled:opacity-50 disabled:pointer-events-none focus:z-20"
                >
                  <span className="sr-only">Previous</span>
                  <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                </button>
                
                {/* Page numbers */}
                {[...Array(Math.ceil(filteredEmployees.length / rowsPerPage))].map((_, i) => {
                  const pageNum = i + 1;
                  const totalPages = Math.ceil(filteredEmployees.length / rowsPerPage);
                  
                  // Show current page and a few pages before and after
                  if (
                    pageNum === 1 || 
                    pageNum === totalPages || 
                    (pageNum >= currentPage - 1 && pageNum <= currentPage + 1) ||
                    (totalPages <= 5) // show all if only a few pages
                  ) {
                    return (
                      <button
                        key={i}
                        onClick={() => goToPage(pageNum)}
                        className={`relative inline-flex items-center px-4 py-2 text-sm font-medium border ${
                          pageNum === currentPage
                            ? 'z-10 bg-blue-600 text-white border-blue-600 hover:bg-blue-700'
                            : 'text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                        } focus:z-20`}
                      >
                        {pageNum}
                      </button>
                    );
                  } else if (
                    // Show ellipsis markers where needed
                    (pageNum === 2 && currentPage > 3) ||
                    (pageNum === totalPages - 1 && currentPage < totalPages - 2)
                  ) {
                    return (
                      <span
                        key={i}
                        className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600"
                      >
                        ...
                      </span>
                    );
                  }
                  
                  return null; // Don't show this page number
                })}
                
                <button
                  onClick={() => goToPage(currentPage + 1)}
                  disabled={currentPage === Math.ceil(filteredEmployees.length / rowsPerPage)}
                  className="relative inline-flex items-center rounded-r-md px-3 py-2 text-sm font-medium bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 disabled:opacity-50 disabled:pointer-events-none focus:z-20"
                >
                  <span className="sr-only">Next</span>
                  <ChevronRight className="h-5 w-5" aria-hidden="true" />
                </button>
              </nav>
            </div>
          </div>
        </div>
        </>
      )}
      </div>
      
      {/* View Employee Modal */}
      {selectedEmployee && isViewModalOpen && (
        <EmployeeDetailsView
          employee={selectedEmployee}
          onClose={() => {
            setIsViewModalOpen(false);
            setSelectedEmployee(null);
          }}
          onEdit={navigateToEditForm}
          onDelete={openDeleteModal}
        />
      )}
      
      {/* Export Modal */}
      {isExportModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6 m-4">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Export Employees</h2>
            
            <div className="mb-5">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Export Scope</h3>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input 
                    type="radio" 
                    name="exportScope" 
                    value="filtered" 
                    checked={exportScope === 'filtered'} 
                    onChange={() => setExportScope('filtered')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Current View (Filtered)</span>
                </label>
                <label className="flex items-center">
                  <input 
                    type="radio" 
                    name="exportScope" 
                    value="all" 
                    checked={exportScope === 'all'} 
                    onChange={() => setExportScope('all')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">All Employees</span>
                </label>
              </div>
            </div>
            
            <div className="mb-5">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Export Format</h3>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input 
                    type="radio" 
                    name="exportFormat" 
                    value="excel" 
                    checked={exportFormat === 'excel'} 
                    onChange={() => setExportFormat('excel')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Excel (.xlsx)</span>
                </label>
                <label className="flex items-center">
                  <input 
                    type="radio" 
                    name="exportFormat" 
                    value="import_template" 
                    checked={exportFormat === 'import_template'} 
                    onChange={() => setExportFormat('import_template')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Export for Import (Template)</span>
                </label>
                <label className="flex items-center">
                  <input 
                    type="radio" 
                    name="exportFormat" 
                    value="csv" 
                    checked={exportFormat === 'csv'} 
                    onChange={() => setExportFormat('csv')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">CSV (.csv)</span>
                </label>
                <label className="flex items-center">
                  <input 
                    type="radio" 
                    name="exportFormat" 
                    value="pdf" 
                    checked={exportFormat === 'pdf'} 
                    onChange={() => setExportFormat('pdf')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">PDF (.pdf)</span>
                </label>
              </div>
            </div>
            
            <div className="flex justify-end mt-6 gap-3">
              <button 
                onClick={() => setIsExportModalOpen(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none"
              >
                Cancel
              </button>
              <button 
                onClick={handleExport}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none"
              >
                Export
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Delete Confirmation Modal */}
      <ConfirmationDialog
        isOpen={isDeleteModalOpen}
        title="Delete Employee"
        message={`Are you sure you want to delete ${selectedEmployee?.firstName} ${selectedEmployee?.lastName}? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={deleteEmployee}
        onCancel={() => setIsDeleteModalOpen(false)}
      />
      
      {/* Import Employee Modal */}
      {isImportModalOpen && (
        <EmployeeImport
          onImport={handleImportComplete}
          onClose={closeImportModal}
        />
      )}
    </div>
  );
};

export default EmployeeManagement; 