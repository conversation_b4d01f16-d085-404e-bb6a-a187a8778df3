import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { Employee } from './Employee';

@Entity('employee_family')
export class EmployeeFamily {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 100,  nullable: false })
  name: string;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  dateOfBirth: string;

  @Column({ type: 'varchar', length: 255,  nullable: false })
  relationship: string;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  gender: string;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  cnic: string;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  occupation: string;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  employer: string;

  @Column({ type: 'varchar', length: 20,  nullable: true })
  contactNumber: string;

  @Column({ type: 'varchar', length: 50,  nullable: false })
  type: string; // 'spouse', 'child', 'dependent'

  // Relation to Employee
  @ManyToOne(() => Employee, employee => employee.family, { onDelete: 'CASCADE' })
  @JoinColumn()
  employee: Employee;

  // System columns
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 