import React, { useState, useEffect } from 'react';
import { 
  Monitor, Laptop, Server, Smartphone, HardDrive, 
  Wifi, Database, AlertTriangle, CheckCircle, Clock,
  DollarSign, BarChart2, Calendar, Tag, Wrench,
  Package, Building2, Users, AlertCircle, TrendingUp, TrendingDown, Minus, Search, Activity,
  Network, Cloud, Shield, Battery, Printer, RefreshCw
} from 'lucide-react';
import { Pie<PERSON>hart } from './charts/PieChart';
import { Bar<PERSON><PERSON> } from './charts/BarChart';
import { LineChart } from './charts/LineChart';
import { motion, AnimatePresence } from 'framer-motion';
import { Radar<PERSON>hart } from './charts/RadarChart';
import { AnimatedStat } from './AnimatedStat';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';

interface AssetStats {
  total: number;
  active: number;
  maintenance: number;
  retired: number;
  byType: {
    desktop: number;
    laptop: number;
    server: number;
    mobile: number;
    network: number;
    storage: number;
  };
  byStatus: {
    operational: number;
    underMaintenance: number;
    needsAttention: number;
    endOfLife: number;
  };
  byDepartment: Record<string, number>;
  recentActivities: Array<{
    id: string;
    type: string;
    description: string;
    timestamp: Date;
    assetId: string;
  }>;
  performance: {
    utilizationRate: number;
    maintenanceCosts: string;
    averageLifespan: string;
    depreciationValue: string;
    roi: string;
    totalValue: string;
    monthlyDepreciation: string;
    maintenanceBudgetUsed: number;
    avgUtilization: number;
    turnoverRate: number;
    maintenanceEfficiency: number;
  };
  distribution: {
    type: {
      labels: string[];
      data: number[];
      trend: string[];
      details: Record<string, any>;
    };
    department: {
      labels: string[];
      data: number[];
      trend: string[];
      details: Record<string, any>;
    };
    status: {
      labels: string[];
      data: number[];
      trend: string[];
      details: Record<string, any>;
    };
  };
  insights: Array<{
    type: 'warning' | 'success' | 'info';
    message: string;
    action: string;
  }>;
}

interface ChartDatasets {
  label?: string;
  data: number[];
  backgroundColor: string | string[];
  borderColor?: string;
  borderWidth?: number;
  hoverOffset?: number;
  borderRadius?: number;
  hoverBackgroundColor?: string[];
  tension?: number;
  fill?: boolean;
}

interface PieChartDataset {
  data: number[];
  backgroundColor: string[];
  borderWidth: number;
  borderColor?: string;
  hoverBackgroundColor?: string[];
  hoverBorderColor?: string;
  hoverBorderWidth?: number;
}

interface BarChartDataset {
  label: string;
  data: number[];
  backgroundColor: string[];
  borderRadius?: number;
}

interface LineChartDataset {
  label: string;
  data: number[];
  borderColor: string;
  backgroundColor: string;
}

interface TooltipData {
  label: string;
  value: number;
  percentage: number;
  trend?: string;
}

const CHART_COLORS = {
  type: [
    { bg: '#4285F4', hover: '#2B5AD9' }, // Blue
    { bg: '#34A853', hover: '#2D8745' }, // Green
    { bg: '#FBBC05', hover: '#D69E00' }, // Yellow
    { bg: '#EA4335', hover: '#D03028' }, // Red
    { bg: '#9334E6', hover: '#7B2AC5' }, // Purple
    { bg: '#FF7A00', hover: '#E66D00' }  // Orange
  ],
  department: [
    { bg: '#4285F4', hover: '#2B5AD9' }, // Blue
    { bg: '#FF6B6B', hover: '#E65A5A' }, // Red
    { bg: '#845EF7', hover: '#6B4BD9' }, // Purple
    { bg: '#20C997', hover: '#1BA37C' }, // Teal
    { bg: '#FF922B', hover: '#E67D1A' }, // Orange
    { bg: '#51CF66', hover: '#40B853' }  // Green
  ],
  status: [
    { bg: '#34A853', hover: '#2D8745' }, // Green
    { bg: '#FBBC05', hover: '#D69E00' }, // Yellow
    { bg: '#4285F4', hover: '#2B5AD9' }, // Blue
    { bg: '#EA4335', hover: '#D03028' }, // Red
    { bg: '#9334E6', hover: '#7B2AC5' }, // Purple
    { bg: '#FF7A00', hover: '#E66D00' }  // Orange
  ]
};

// Update the chart options constants
const pieChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'right' as const,
      labels: {
        usePointStyle: true,
        padding: 20,
        font: { size: 12 },
        generateLabels: (chart: any) => {
          const datasets = chart.data.datasets[0];
          return chart.data.labels.map((label: string, i: number) => ({
            text: `${label} (${datasets.data[i]}%)`,
            fillStyle: datasets.backgroundColor[i],
            hidden: false,
            index: i
          }));
        }
      }
    },
    tooltip: {
      callbacks: {
        label: function(context: any) {
          const value = context.raw;
          const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
          const percentage = ((value / total) * 100).toFixed(1);
          return `${context.label}: ${value} (${percentage}%)`;
        }
      }
    }
  },
  animation: {
    animateScale: true,
    animateRotate: true,
    duration: 1000
  },
  hover: {
    mode: 'nearest',
    intersect: true,
    animationDuration: 200
  },
  onClick: (event: any, elements: any) => {
    if (elements && elements.length > 0) {
      const index = elements[0].index;
      // Handle click - you can add your logic here
      console.log('Selected:', event.chart.data.labels[index]);
    }
  }
};

const barChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      callbacks: {
        label: function(context: any) {
          const value = context.raw;
          const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
          const percentage = ((value / total) * 100).toFixed(1);
          return `${context.label}: ${value} (${percentage}%)`;
        }
      }
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      grid: {
        display: true,
        color: 'rgba(0,0,0,0.1)'
      }
    },
    x: {
      grid: {
        display: false
      }
    }
  },
  animation: {
    duration: 1000,
    easing: 'easeInOutQuart'
  },
  hover: {
    mode: 'index',
    intersect: false,
    animationDuration: 200
  }
};

const lineChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        callback: (value: number) => `${value}%`
      }
    }
  }
};

// Add this animation constant
const chartTransition = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
  transition: { duration: 0.3 }
};

// Add click handler type
type ChartClickHandler = (label: string, value: number, index: number) => void;

const AssetManagementDashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const redirectedRef = React.useRef(false);
  
  // Check if user has permission to access this page
  useEffect(() => {
    // Check if an access error has already been shown
    if (!window.hasOwnProperty('accessErrorShown')) {
      (window as any).accessErrorShown = false;
    }
    
    if (user && !['IT_ADMIN', 'IT_STAFF'].includes(user.role) && !redirectedRef.current && !(window as any).accessErrorShown) {
      redirectedRef.current = true;
      (window as any).accessErrorShown = true;
      toast.error("You don't have permission to access asset management");
      navigate('/dashboard');
    }
  }, [user, navigate]);
  
  const [stats, setStats] = useState<AssetStats>({
    total: 0,
    active: 0,
    maintenance: 0,
    retired: 0,
    byType: {
      desktop: 0,
      laptop: 0,
      server: 0,
      mobile: 0,
      network: 0,
      storage: 0
    },
    byStatus: {
      operational: 0,
      underMaintenance: 0,
      needsAttention: 0,
      endOfLife: 0
    },
    byDepartment: {},
    recentActivities: [],
    performance: {
      utilizationRate: 0,
      maintenanceCosts: '0',
      averageLifespan: '0',
      depreciationValue: '0',
      roi: '0',
      totalValue: '0',
      monthlyDepreciation: '0',
      maintenanceBudgetUsed: 0,
      avgUtilization: 0,
      turnoverRate: 0,
      maintenanceEfficiency: 0
    },
    distribution: {
      type: {
        labels: ['Laptops', 'Desktops', 'Servers', 'Network Devices', 'Mobile Devices', 'Peripherals'],
        data: [30, 25, 15, 10, 12, 8],
        trend: [],
        details: {}
      },
      department: {
        labels: ['IT', 'Sales', 'Marketing', 'HR', 'Finance', 'Operations'],
        data: [40, 10, 15, 10, 15, 10],
        trend: [],
        details: {}
      },
      status: {
        labels: ['Active', 'In Maintenance', 'Reserved', 'End of Life', 'Lost/Stolen', 'Disposed'],
        data: [60, 15, 10, 8, 2, 5],
        trend: [],
        details: {}
      }
    },
    insights: []
  });
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('current');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [distributionView, setDistributionView] = useState<'type' | 'department' | 'status'>('type');
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [assetType, setAssetType] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'value' | 'age' | 'utilization'>('value');
  const [chartView, setChartView] = useState<'assets' | 'departments'>('assets');
  const [tooltipData, setTooltipData] = useState<TooltipData | null>(null);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [selectedProject, setSelectedProject] = useState('all');
  const [viewType, setViewType] = useState('all');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [showTrends, setShowTrends] = useState(false);

  // First, let's format the asset distribution data correctly
  const assetDistributionData = {
    labels: chartView === 'assets' 
      ? ['Laptops', 'Desktops', 'Servers', 'Network Devices', 'Mobile Devices', 'Peripherals']
      : ['IT', 'Sales', 'Marketing', 'HR', 'Finance', 'Operations'],
    datasets: [{
      data: chartView === 'assets'
        ? [30, 25, 15, 10, 12, 8]
        : [40, 15, 15, 10, 10, 10],
      backgroundColor: chartView === 'assets'
        ? [
            '#4285F4', // Blue - Laptops
            '#34A853', // Green - Desktops
            '#FBBC05', // Yellow - Servers
            '#EA4335', // Red - Network Devices
            '#9334E6', // Purple - Mobile Devices
            '#FF7A00'  // Orange - Peripherals
          ]
        : [
            '#4285F4', // Blue - IT
            '#34A853', // Green - Sales
            '#FBBC05', // Yellow - Marketing
            '#EA4335', // Red - HR
            '#9334E6', // Purple - Finance
            '#FF7A00'  // Orange - Operations
          ],
      borderWidth: 0
    }]
  };

  useEffect(() => {
    const fetchAssetStats = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          // If no token, use default data
          setStats({
            ...stats,
            distribution: {
              type: { labels: assetDistributionData.labels, data: assetDistributionData.datasets[0].data, trend: [], details: {} },
              department: { labels: assetDistributionData.labels, data: assetDistributionData.datasets[0].data, trend: [], details: {} },
              status: { labels: assetDistributionData.labels, data: assetDistributionData.datasets[0].data, trend: [], details: {} }
            }
          } as AssetStats);
          return;
        }

        const response = await fetch(
          `/api/assets/stats?timeRange=${timeRange}&department=${selectedDepartment}&status=${selectedStatus}`,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );
        
        if (!response.ok) {
          throw new Error(`Failed to fetch asset stats: ${response.statusText}`);
        }
        
        const data = await response.json();
        setStats(data);
      } catch (error) {
        console.error('Error fetching asset stats:', error);
        // Use default data if API fails
        setStats({
          ...stats,
          distribution: {
            type: { labels: assetDistributionData.labels, data: assetDistributionData.datasets[0].data, trend: [], details: {} },
            department: { labels: assetDistributionData.labels, data: assetDistributionData.datasets[0].data, trend: [], details: {} },
            status: { labels: assetDistributionData.labels, data: assetDistributionData.datasets[0].data, trend: [], details: {} }
          }
        } as AssetStats);
      } finally {
        setLoading(false);
      }
    };

    fetchAssetStats();
  }, [timeRange, selectedDepartment, selectedStatus]);

  const handleChartClick: ChartClickHandler = (label, value, index) => {
    console.log(`Clicked: ${label} (${value})`);
    // Add your click logic here
  };

  const Tooltip: React.FC<{ data: TooltipData }> = ({ data }) => (
    <div className="absolute z-50 bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg text-sm">
      <div className="font-medium">{data.label}</div>
      <div>Count: {data.value}</div>
      <div>Percentage: {data.percentage}%</div>
      {data.trend && <div>Trend: {data.trend}</div>}
    </div>
  );

  return (
    <div className="space-y-4">
      {/* Asset Overview Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          icon={<Laptop className="w-6 h-6" />}
          title="Total Devices"
          value={245}
          trend="+12%"
          trendUp={true}
          status={{ type: 'success', text: 'Healthy' }}
        />
        <StatCard
          icon={<Server className="w-6 h-6" />}
          title="Servers"
          value={28}
          trend="+5%"
          trendUp={true}
          status={{ type: 'warning', text: '2 Updates Pending' }}
        />
        <StatCard
          icon={<Monitor className="w-6 h-6" />}
          title="Workstations"
          value={156}
          trend="-2%"
          trendUp={false}
          status={{ type: 'error', text: '3 Issues' }}
        />
        <StatCard
          icon={<Smartphone className="w-6 h-6" />}
          title="Mobile Devices"
          value={61}
          trend="+8%"
          trendUp={true}
          status={{ type: 'success', text: 'All Updated' }}
        />
      </div>

      {/* Asset Categories */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Hardware Section */}
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center gap-2 mb-4">
            <HardDrive className="w-6 h-6 text-purple-500" />
            <h3 className="text-lg font-semibold">Hardware</h3>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Laptop className="w-4 h-4 text-blue-500" />
                <span>Laptops</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">120</span>
                <div className="w-2 h-2 rounded-full bg-green-500" />
              </div>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Monitor className="w-4 h-4 text-green-500" />
                <span>Desktops</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">85</span>
                <div className="w-2 h-2 rounded-full bg-green-500" />
              </div>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Printer className="w-4 h-4 text-yellow-500" />
                <span>Printers</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">25</span>
                <div className="w-2 h-2 rounded-full bg-yellow-500" />
              </div>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Network className="w-4 h-4 text-blue-500" />
                <span>Networking Equipment</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">45</span>
                <div className="w-2 h-2 rounded-full bg-green-500" />
              </div>
            </div>
          </div>
      </div>

        {/* Software Section */}
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center gap-2 mb-4">
            <Database className="w-6 h-6 text-blue-500" />
            <h3 className="text-lg font-semibold">Software</h3>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Licensed</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">450</span>
                <div className="w-2 h-2 rounded-full bg-green-500" />
              </div>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-4 h-4 text-red-500" />
                <span>Expired</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">12</span>
                <div className="w-2 h-2 rounded-full bg-red-500" />
              </div>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <RefreshCw className="w-4 h-4 text-yellow-500" />
                <span>Updates Required</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">35</span>
                <div className="w-2 h-2 rounded-full bg-yellow-500" />
              </div>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Cloud className="w-4 h-4 text-blue-500" />
                <span>Cloud Services</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">28</span>
                <div className="w-2 h-2 rounded-full bg-green-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Cloud Resources Section */}
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center gap-2 mb-4">
            <Cloud className="w-6 h-6 text-green-500" />
            <h3 className="text-lg font-semibold">Cloud Resources</h3>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Server className="w-4 h-4 text-blue-500" />
                <span>Virtual Machines</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">42</span>
                <div className="w-2 h-2 rounded-full bg-green-500" />
              </div>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <HardDrive className="w-4 h-4 text-green-500" />
                <span>Storage (TB)</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">156</span>
                <div className="w-2 h-2 rounded-full bg-green-500" />
              </div>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Database className="w-4 h-4 text-yellow-500" />
                <span>Databases</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">18</span>
                <div className="w-2 h-2 rounded-full bg-yellow-500" />
              </div>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Package className="w-4 h-4 text-blue-500" />
                <span>Containers</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">94</span>
                <div className="w-2 h-2 rounded-full bg-green-500" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Asset Types Distribution */}
        <motion.div 
          className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-semibold">Asset Types Distribution</h3>
            <select 
              className="bg-white border border-gray-200 rounded-md px-3 py-1 text-sm hover:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-200"
              onChange={(e) => setViewType(e.target.value)}
            >
              <option value="all">All Types</option>
              <option value="hardware">Hardware</option>
              <option value="software">Software</option>
            </select>
          </div>
          <BarChart 
            data={{
              labels: ['Laptops', 'Desktops', 'Servers', 'Network', 'Mobile'],
              datasets: [{
                label: 'Asset Count',
                data: [45, 35, 25, 20, 15],
                backgroundColor: '#4285F4',
                borderRadius: 6
              }]
            }}
          />
        </motion.div>

        {/* Department Distribution */}
        <motion.div 
          className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-semibold">Department Distribution</h3>
            <button
              onClick={() => setShowTrends(!showTrends)}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                showTrends ? 'bg-blue-500 text-white' : 'bg-gray-100 hover:bg-gray-200'
              }`}
            >
              {showTrends ? 'Hide Trends' : 'Show Trends'}
            </button>
        </div>
          <PieChart 
            data={{
              labels: ['IT', 'Sales', 'Marketing', 'HR', 'Finance', 'Operations'],
              datasets: [{
                data: [45, 25, 20, 15, 25, 20],
                backgroundColor: [
                  '#4285F4', // Blue
                  '#34A853', // Green
                  '#FBBC05', // Yellow
                  '#EA4335', // Red
                  '#9334E6', // Purple
                  '#FF7A00'  // Orange
                ]
              }]
            }}
          />
        </motion.div>

        {/* Asset Status */}
        <motion.div 
          className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-semibold">Asset Status</h3>
            <div className="text-sm text-gray-500">Total: 150</div>
          </div>
          <PieChart 
          data={{
              labels: ['Active', 'In Maintenance', 'Reserved', 'End of Life'],
              datasets: [{
                data: [60, 15, 15, 10],
                backgroundColor: [
                  '#34A853', // Green
                  '#FBBC05', // Yellow
                  '#4285F4', // Blue
                  '#EA4335'  // Red
                ]
              }]
            }}
          />
        </motion.div>

        {/* Location Distribution */}
        <motion.div 
          className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-semibold">Location Distribution</h3>
            <div className="text-sm text-gray-500">5 Locations</div>
          </div>
          <BarChart 
            data={{
              labels: ['HQ', 'Remote', 'Branch', 'Data Center', 'In Transit'],
              datasets: [{
                label: 'Assets',
                data: [40, 25, 20, 10, 5],
                backgroundColor: [
                  '#673AB7', // Deep Purple
                  '#00BCD4', // Cyan
                  '#FF5722', // Deep Orange
                  '#795548', // Brown
                  '#607D8B'  // Blue Grey
                ],
                borderRadius: 6
              }]
            }}
          />
        </motion.div>

        {/* Summary Stats Row */}
        <motion.div 
          className="lg:col-span-2 grid grid-cols-2 md:grid-cols-4 gap-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
            <div className="text-gray-500 text-sm">Total Assets</div>
            <div className="text-xl font-semibold mt-1">458</div>
            <div className="text-sm text-green-500 mt-1">↑ 12.5%</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
            <div className="text-gray-500 text-sm">Active Assets</div>
            <div className="text-xl font-semibold mt-1">389</div>
            <div className="text-sm text-green-500 mt-1">↑ 8.3%</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
            <div className="text-gray-500 text-sm">Maintenance</div>
            <div className="text-xl font-semibold mt-1">45</div>
            <div className="text-sm text-red-500 mt-1">↓ 2.1%</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
            <div className="text-gray-500 text-sm">Utilization</div>
            <div className="text-xl font-semibold mt-1">87%</div>
            <div className="text-sm text-green-500 mt-1">↑ 5.2%</div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default AssetManagementDashboard;

interface StatCardProps {
  icon: React.ReactNode;
  title: string;
  value: number;
  trend: string;
  trendUp: boolean;
  status: {
    type: 'success' | 'warning' | 'error';
    text: string;
  };
}

const StatCard: React.FC<StatCardProps> = ({ icon, title, value, trend, trendUp, status }) => (
  <div className="bg-white p-4 rounded-lg shadow">
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-3">
        <div className="p-2 bg-gray-50 rounded-lg">
          {icon}
        </div>
        <div>
          <p className="text-sm text-gray-500">{title}</p>
          <p className="text-xl font-semibold">{value}</p>
          <div className={`text-sm mt-1 ${
            status.type === 'success' ? 'text-green-500' :
            status.type === 'warning' ? 'text-yellow-500' : 'text-red-500'
          }`}>
            {status.text}
          </div>
        </div>
      </div>
      <div className={`text-sm ${trendUp ? 'text-green-500' : 'text-red-500'}`}>
        {trend}
      </div>
    </div>
  </div>
);

interface CategoryCardProps {
  icon: React.ReactNode;
  title: string;
  items: Array<{ 
    label: string; 
    count: number; 
    status: 'success' | 'warning' | 'error';
  }>;
}

const CategoryCard: React.FC<CategoryCardProps> = ({ icon, title, items }) => (
  <div className="bg-white p-4 rounded-lg shadow">
    <div className="flex items-center gap-2 mb-3">
      {icon}
      <h3 className="text-lg font-semibold">{title}</h3>
    </div>
    <div className="space-y-2">
      {items.map((item, index) => (
        <div key={index} className="flex justify-between items-center">
          <span className="text-gray-600">{item.label}</span>
          <div className="flex items-center gap-2">
            <span className="font-medium">{item.count}</span>
            <div className={`w-2 h-2 rounded-full ${
              item.status === 'success' ? 'bg-green-500' :
              item.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
            }`} />
          </div>
        </div>
      ))}
    </div>
  </div>
);

interface MetricCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: 'blue' | 'green' | 'yellow' | 'red';
  trend: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon, color, trend }) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-500',
    green: 'bg-green-50 text-green-500',
    yellow: 'bg-yellow-50 text-yellow-500',
    red: 'bg-red-50 text-red-500',
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow">
      <div className="flex items-center gap-3">
        <div className={`p-3 rounded-full ${colorClasses[color]}`}>
          {icon}
        </div>
        <div className="flex-1">
          <p className="text-sm text-gray-500">{title}</p>
          <div className="flex items-center gap-2">
            <p className="text-2xl font-semibold">{value}</p>
            <span className={`text-sm ${
              trend.startsWith('+') ? 'text-green-500' : 'text-red-500'
            }`}>
              {trend}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

interface Insight {
  type: 'warning' | 'success' | 'info';
  message: string;
  action: string;
}

const InsightCard: React.FC<Insight> = ({ type, message, action }) => {
  const colors = {
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-700',
    success: 'bg-green-50 border-green-200 text-green-700',
    info: 'bg-blue-50 border-blue-200 text-blue-700'
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`${colors[type]} p-4 rounded-lg border flex items-start gap-3`}
    >
      <AlertCircle className="w-5 h-5 mt-0.5" />
      <div>
        <p className="font-medium">{message}</p>
        <button className="text-sm mt-1 hover:underline">{action}</button>
      </div>
    </motion.div>
  );
};

const TrendIndicator: React.FC<{ trend: string }> = ({ trend }) => {
  const value = parseFloat(trend);
  if (value > 0) {
    return <TrendingUp className="w-4 h-4 text-green-500" />;
  } else if (value < 0) {
    return <TrendingDown className="w-4 h-4 text-red-500" />;
  }
  return <Minus className="w-4 h-4 text-gray-500" />;
};

const DetailedStatsPanel: React.FC<{ stats: AssetStats }> = ({ stats }) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow mb-4">
      <h3 className="text-lg font-medium mb-4">Detailed Statistics</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="p-4 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-600 mb-3">Asset Health Overview</h4>
          <div className="space-y-2">
            {Object.entries(stats.byStatus || {}).map(([status, count]) => (
              <div key={status} className="flex items-center justify-between">
                <span className="text-sm text-gray-600 capitalize">
                  {status.replace(/([A-Z])/g, ' $1').toLowerCase()}
                </span>
                <div className="flex items-center gap-2">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        status === 'operational' ? 'bg-green-500' :
                        status === 'underMaintenance' ? 'bg-yellow-500' :
                        status === 'needsAttention' ? 'bg-red-500' :
                        'bg-gray-500'
                      }`}
                      style={{ width: `${stats.total ? (count / stats.total) * 100 : 0}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium">{count}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="p-4 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-600 mb-3">Financial Metrics</h4>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-gray-500">Total Asset Value</p>
              <p className="text-lg font-semibold">PKR {stats.performance.totalValue}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Monthly Depreciation</p>
              <p className="text-lg font-semibold">PKR {stats.performance.monthlyDepreciation}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Maintenance Budget Used</p>
              <p className="text-lg font-semibold">{stats.performance.maintenanceBudgetUsed}%</p>
            </div>
          </div>
        </div>

        <div className="p-4 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-600 mb-3">Efficiency Metrics</h4>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-gray-500">Average Utilization</p>
              <p className="text-lg font-semibold">{stats.performance.avgUtilization}%</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Asset Turnover Rate</p>
              <p className="text-lg font-semibold">{stats.performance.turnoverRate} per year</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Maintenance Efficiency</p>
              <p className="text-lg font-semibold">{stats.performance.maintenanceEfficiency}%</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 