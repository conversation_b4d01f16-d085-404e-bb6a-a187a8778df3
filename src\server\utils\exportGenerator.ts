import PDFDocument from 'pdfkit';
import xlsx from 'xlsx';
import { Parser } from 'json2csv';
import { PrinterMaintenance } from '../entities/PrinterMaintenance';

// Helper function to format currency
const formatCurrency = (amount: number | string | null | undefined): string => {
  if (!amount) return 'Rs 0';
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  return `Rs ${numericAmount.toLocaleString('en-PK')}`;
};

// Helper function to format date
const formatDate = (date: string | Date | null | undefined): string => {
  if (!date) return 'Not specified';
  return new Date(date).toLocaleDateString('en-PK', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// Helper function to sort dates
const sortByDate = (records: any[]): any[] => {
  return [...records].sort((a, b) => {
    const dateA = new Date(a.service_date || a.visit_date || 0);
    const dateB = new Date(b.service_date || b.visit_date || 0);
    return dateA.getTime() - dateB.getTime();
  });
};

export const generateMaintenanceExport = async (
  records: any[],
  format: 'xlsx' | 'csv' | 'pdf'
): Promise<{ data: Buffer; filename: string; contentType: string }> => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  
  // Sort records by date (oldest to newest)
  const sortedRecords = sortByDate(records);
  
  switch (format) {
    case 'xlsx': {
      // Create workbook and worksheet
      const wb = xlsx.utils.book_new();
      
      // Format data for Excel
      const excelData = sortedRecords.map((record, index) => ({
        'S.No': index + 1,
        'Printer Details': record.manufacturer 
          ? `${record.manufacturer} - ${record.model || record.printer_name || `Asset #${record.asset_id}`}`
          : record.model || record.printer_name || `Asset #${record.asset_id}`,
        'Department': record.department || 'IT',
        'Service Date': formatDate(record.service_date || record.visit_date),
        'Vendor': record.vendor_name || 'Not specified',
        'Service Type': record.service_type || record.action_taken || 'Maintenance',
        'Parts Replaced': Array.isArray(record.parts_replaced) 
          ? record.parts_replaced.map((p: any) => p.partName || p.name).join(', ')
          : 'None',
        'Amount': formatCurrency(record.invoice_amount || record.cost),
        'Remarks': record.remarks || 'None'
      }));

      // Add total row
      excelData.push({
        'S.No': '',
        'Printer Details': 'Total',
        'Department': '',
        'Service Date': '',
        'Vendor': '',
        'Service Type': '',
        'Parts Replaced': '',
        'Amount': formatCurrency(sortedRecords.reduce((sum: number, record) => {
          const amount = record.invoice_amount || record.cost || '0';
          return sum + (typeof amount === 'number' ? amount : parseFloat(amount));
        }, 0)),
        'Remarks': ''
      });

      const ws = xlsx.utils.json_to_sheet(excelData);
      
      // Set column widths
      const colWidths = [
        { wch: 8 },   // S.No
        { wch: 35 },  // Printer Details
        { wch: 15 },  // Department
        { wch: 12 },  // Service Date
        { wch: 25 },  // Vendor
        { wch: 20 },  // Service Type
        { wch: 30 },  // Parts Replaced
        { wch: 15 },  // Amount
        { wch: 30 }   // Remarks
      ];
      ws['!cols'] = colWidths;

      xlsx.utils.book_append_sheet(wb, ws, 'Maintenance Records');
      
      const buffer = xlsx.write(wb, { type: 'buffer', bookType: 'xlsx' });
      
      return {
        data: buffer,
        filename: `printer-maintenance-${timestamp}.xlsx`,
        contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      };
    }
    
    case 'csv': {
      const fields = [
        'id',
        'manufacturer',
        'printer_name',
        'service_date',
        'department',
        'vendor_name',
        'service_type',
        'issue_description',
        'parts_replaced',
        'technician_name',
        'invoice_number',
        'invoice_amount',
        'status',
        'remarks',
        'created_at'
      ];
      
      const csvData = sortedRecords.map((record, index) => ({
        id: index + 1,
        manufacturer: record.manufacturer || 'Not specified',
        printer_name: record.model || record.printer_name || `Asset #${record.asset_id}`,
        service_date: formatDate(record.service_date || record.visit_date),
        department: record.department || 'IT',
        vendor_name: record.vendor_name || 'Not specified',
        service_type: record.service_type || record.action_taken || 'Maintenance',
        issue_description: record.issue_description || 'Not specified',
        parts_replaced: Array.isArray(record.parts_replaced) 
          ? record.parts_replaced.map((p: any) => p.partName || p.name).join(', ')
          : 'None',
        technician_name: record.technician_name || 'Not specified',
        invoice_number: record.invoice_number || 'Not specified',
        invoice_amount: formatCurrency(record.invoice_amount || record.cost),
        status: record.status || 'Completed',
        remarks: record.remarks || 'None',
        created_at: formatDate(record.created_at)
      }));

      const parser = new Parser({ fields });
      const csv = parser.parse(csvData);
      
      return {
        data: Buffer.from(csv, 'utf-8'),
        filename: `printer-maintenance-${timestamp}.csv`,
        contentType: 'text/csv'
      };
    }
    
    case 'pdf': {
      const doc = new PDFDocument({ margin: 30, size: 'A4', bufferPages: true });
      const chunks: any[] = [];
      
      doc.on('data', chunk => chunks.push(chunk));
      
      // Add title and metadata
      doc.fontSize(20)
         .font('Helvetica-Bold')
         .text('Printer Maintenance Report', { align: 'center' });
      
      doc.moveDown();
      doc.fontSize(10)
         .font('Helvetica')
         .text(`Generated on: ${new Date().toLocaleString('en-PK')}`, { align: 'right' });
      
      // Add summary section
      doc.moveDown();
      doc.font('Helvetica-Bold')
         .fontSize(12)
         .text('Summary', { underline: true });
      
      const totalCost = sortedRecords.reduce((sum, record) => {
        const amount = parseFloat(String(record.invoice_amount || record.cost)) || 0;
        return sum + amount;
      }, 0);
      
      // Get date range from sorted records
      const startDate = formatDate(sortedRecords[0]?.service_date || sortedRecords[0]?.visit_date);
      const endDate = formatDate(sortedRecords[sortedRecords.length - 1]?.service_date || sortedRecords[sortedRecords.length - 1]?.visit_date);
      
      doc.moveDown()
         .fontSize(10)
         .font('Helvetica')
         .text(`Total Records: ${sortedRecords.length}`)
         .text(`Total Cost: ${formatCurrency(totalCost)}`)
         .text(`Date Range: ${startDate} to ${endDate}`);
      
      // Add table
      doc.moveDown();
      
      // Define table layout with optimized widths for A4 page
      const tableTop = doc.y + 20;
      const margins = { left: 40, right: 40 };
      const pageWidth = 595.28; // A4 width in points
      const usableWidth = pageWidth - margins.left - margins.right;

      // Calculate column widths as percentages of usable width
      const columns = {
        sno: { 
          x: margins.left, 
          width: Math.floor(usableWidth * 0.07), // Increased width for S.No
          align: 'center' as const 
        },
        printer: { 
          x: margins.left + Math.floor(usableWidth * 0.08), // Increased gap after S.No
          width: Math.floor(usableWidth * 0.22), // Adjusted width
          align: 'left' as const 
        },
        dept: { 
          x: margins.left + Math.floor(usableWidth * 0.31), // Adjusted position
          width: Math.floor(usableWidth * 0.09), // Slightly reduced
          align: 'left' as const 
        },
        date: { 
          x: margins.left + Math.floor(usableWidth * 0.41), // Adjusted position
          width: Math.floor(usableWidth * 0.11), // Slightly reduced
          align: 'left' as const 
        },
        vendor: { 
          x: margins.left + Math.floor(usableWidth * 0.53), // Adjusted position
          width: Math.floor(usableWidth * 0.17), // Slightly reduced
          align: 'left' as const 
        },
        service: { 
          x: margins.left + Math.floor(usableWidth * 0.71), // Adjusted position
          width: Math.floor(usableWidth * 0.15), // Slightly reduced
          align: 'left' as const 
        },
        amount: { 
          x: margins.left + Math.floor(usableWidth * 0.87), // Adjusted position
          width: Math.floor(usableWidth * 0.13), // Slightly reduced
          align: 'right' as const 
        }
      };

      // Helper function for smarter text truncation
      const truncateText = (text: string, maxLength: number) => {
        if (!text) return '';
        if (text.length <= maxLength) return text;
        
        // For very short text, use simple truncation
        if (maxLength < 10) {
          return text.substring(0, maxLength - 2) + '..';
        }
        
        // For longer text, try to break at word boundaries
        const truncated = text.substring(0, maxLength - 2);
        const lastSpace = truncated.lastIndexOf(' ');
        
        if (lastSpace > maxLength * 0.7) {
          return truncated.substring(0, lastSpace) + '..';
        }
        return truncated + '..';
      };

      // Add table headers with improved styling
      doc.rect(margins.left - 5, tableTop - 5, usableWidth + 10, 20)
         .fillColor('#f3f4f6')
         .fill();

      // Draw header cells with better spacing
      doc.font('Helvetica-Bold')
         .fontSize(9)
         .fillColor('#000000');

      // Draw headers with proper alignment
      Object.entries(columns).forEach(([key, col]) => {
        const headerText = 
          key === 'sno' ? 'S.No' :  // Removed extra padding as we increased column width
          key === 'printer' ? 'Printer Details' :
          key === 'dept' ? 'Dept.' :
          key === 'date' ? 'Date' :
          key === 'vendor' ? 'Vendor' :
          key === 'service' ? 'Service' :
          'Amount';
        
        // Add header cell background for better visual separation
        doc.rect(col.x - 2, tableTop - 5, col.width + 4, 20)
           .fillColor('#f3f4f6')
           .fill();
        
        // Draw header text
        doc.fillColor('#000000')
           .text(headerText, col.x, tableTop, { 
             width: col.width, 
             align: col.align,
             lineGap: 0 // Reduce line gap for headers
           });
      });

      // Add horizontal line with proper width
      doc.moveTo(margins.left - 5, tableTop + 15)
         .lineTo(margins.left + usableWidth + 5, tableTop + 15)
         .stroke();
      
      // Add table rows
      let rowTop = tableTop + 25;
      let isGray = false;
      
      sortedRecords.forEach((record, index) => {
        // Add new page if needed
        if (rowTop > 750) {
          doc.addPage();
          rowTop = 50;
          
          // Add headers on new page
          doc.rect(margins.left - 5, rowTop - 15, usableWidth + 10, 20)
             .fillColor('#f3f4f6')
             .fill();
          doc.font('Helvetica-Bold')
             .fontSize(9)
             .fillColor('#000000');
          
          // Redraw headers on new page
          Object.entries(columns).forEach(([key, col]) => {
            const headerText = 
              key === 'sno' ? 'S.No' :  // Removed extra padding as we increased column width
              key === 'printer' ? 'Printer Details' :
              key === 'dept' ? 'Dept.' :
              key === 'date' ? 'Date' :
              key === 'vendor' ? 'Vendor' :
              key === 'service' ? 'Service' :
              'Amount';
            
            doc.text(headerText, col.x, rowTop - 10, { width: col.width, align: col.align });
          });
          
          doc.moveTo(margins.left - 5, rowTop + 5)
             .lineTo(margins.left + usableWidth + 5, rowTop + 5)
             .stroke();
          
          rowTop += 15;
          isGray = false;
        }

        // Add alternating row background
        if (isGray) {
          doc.rect(margins.left - 5, rowTop - 5, usableWidth + 10, 20).fill('#f9fafb');
        }
        isGray = !isGray;
        
        // Draw data cells with optimized spacing
        doc.font('Helvetica')
           .fontSize(8.5) // Slightly increased font size
           .fillColor('#000000');

        // Calculate proper truncation lengths based on column widths
        const printerDetails = record.manufacturer 
          ? `${record.manufacturer} - ${record.model || record.printer_name || `Asset #${record.asset_id}`}`
          : record.model || record.printer_name || `Asset #${record.asset_id}`;

        // Draw each cell with proper spacing and truncation
        doc.text((index + 1).toString(), columns.sno.x, rowTop, { 
          width: columns.sno.width, 
          align: 'center' 
        });
        
        doc.text(truncateText(printerDetails, 30), columns.printer.x, rowTop, { 
          width: columns.printer.width, 
          align: 'left' 
        });
        
        doc.text(record.department || 'IT', columns.dept.x, rowTop, { 
          width: columns.dept.width, 
          align: 'left' 
        });
        
        doc.text(formatDate(record.service_date || record.visit_date), columns.date.x, rowTop, { 
          width: columns.date.width, 
          align: 'left' 
        });
        
        doc.text(truncateText(record.vendor_name || 'Not specified', 25), columns.vendor.x, rowTop, { 
          width: columns.vendor.width, 
          align: 'left' 
        });
        
        doc.text(truncateText(record.service_type || record.action_taken || 'Maintenance', 25), columns.service.x, rowTop, { 
          width: columns.service.width, 
          align: 'left' 
        });

        // Right align amount with proper spacing
        const amount = formatCurrency(record.invoice_amount || record.cost);
        doc.text(amount, columns.amount.x, rowTop, { 
          width: columns.amount.width, 
          align: 'right' 
        });
        
        rowTop += 22; // Increased row height for better readability
      });
      
      // Update total row styling
      doc.rect(margins.left - 5, rowTop - 5, usableWidth + 10, 22)
         .fillColor('#f3f4f6')
         .fill();

      doc.font('Helvetica-Bold')
         .fontSize(9)
         .fillColor('#000000');

      // Draw total row with proper alignment
      doc.text('', columns.sno.x, rowTop, { width: columns.sno.width, align: 'center' });
      doc.text('', columns.printer.x, rowTop, { width: columns.printer.width, align: 'left' });
      doc.text('', columns.dept.x, rowTop, { width: columns.dept.width, align: 'left' });
      doc.text('', columns.date.x, rowTop, { width: columns.date.width, align: 'left' });
      doc.text('', columns.vendor.x, rowTop, { width: columns.vendor.width, align: 'left' });
      doc.text('Total', columns.service.x, rowTop, { 
        width: columns.service.width, 
        align: 'right',
        continued: true 
      });
      doc.text(':', columns.service.x + columns.service.width - 5, rowTop, {
        width: 5,
        align: 'left'
      });
      doc.text(formatCurrency(totalCost), columns.amount.x, rowTop, { 
        width: columns.amount.width, 
        align: 'right' 
      });

      // Add final border with proper spacing
      doc.rect(margins.left - 5, tableTop - 5, usableWidth + 10, rowTop - tableTop + 22)
         .strokeColor('#e5e7eb')
         .stroke();
      
      // Add signature section
      doc.moveDown(4);
      
      // Calculate positions for 4 signature blocks
      const signatureWidth = Math.floor(usableWidth / 4);
      const signatureTop = doc.y;
      
      // Helper function to draw signature block
      const drawSignatureBlock = (x: number, title: string) => {
        // Draw signature line
        doc.moveTo(x + 10, signatureTop + 30)
           .lineTo(x + signatureWidth - 10, signatureTop + 30)
           .strokeColor('#000000')
           .stroke();
        
        // Add title centered under the line
        doc.font('Helvetica')
           .fontSize(10)
           .text(title, x + 10, signatureTop + 35, {
             width: signatureWidth - 20,
             align: 'center'
           });
      };
      
      // Draw signature blocks with proper spacing
      const titles = [
        'IT Department',
        'Chief Financial Officer',
        'Director Operations',
        'Chief Executive Officer'
      ];
      
      titles.forEach((title, index) => {
        drawSignatureBlock(
          margins.left + (signatureWidth * index),
          title
        );
      });

      // Finalize PDF
      doc.end();
      
      // Get PDF buffer
      const buffer = await new Promise<Buffer>((resolve) => {
        doc.on('end', () => resolve(Buffer.concat(chunks)));
      });
      
      return {
        data: buffer,
        filename: `printer-maintenance-${timestamp}.pdf`,
        contentType: 'application/pdf'
      };
    }
    
    default:
      throw new Error(`Unsupported export format: ${format}`);
  }
}; 