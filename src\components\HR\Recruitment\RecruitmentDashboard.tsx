import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Badge
} from '@mui/material';
import {
  Add as AddIcon,
  Work as WorkIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
  Schedule as ScheduleIcon,
  MoreVert as MoreVertIcon,
  TrendingUp as TrendingUpIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { recruitmentAPI } from '../../../services/recruitmentAPI';

interface RecruitmentStats {
  overview: {
    totalPostings: number;
    activePostings: number;
    draftPostings: number;
    closedPostings: number;
    totalApplications: number;
    pendingApplications: number;
  };
  applicationsByStatus: Array<{
    status: string;
    count: number;
  }>;
  postingsByDepartment: Array<{
    department: string;
    count: number;
  }>;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`recruitment-tabpanel-${index}`}
      aria-labelledby={`recruitment-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const RecruitmentDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<RecruitmentStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      // For now, use mock data until the backend is ready
      const mockStats = {
        overview: {
          totalPostings: 12,
          activePostings: 8,
          draftPostings: 3,
          closedPostings: 1,
          totalApplications: 45,
          pendingApplications: 18
        },
        applicationsByStatus: [
          { status: 'submitted', count: 15 },
          { status: 'under_review', count: 12 },
          { status: 'interviewing', count: 8 },
          { status: 'hired', count: 5 },
          { status: 'rejected', count: 5 }
        ],
        postingsByDepartment: [
          { department: 'Engineering', count: 5 },
          { department: 'Marketing', count: 3 },
          { department: 'Sales', count: 2 },
          { department: 'HR', count: 1 },
          { department: 'Finance', count: 1 }
        ]
      };

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      setStats(mockStats);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch recruitment statistics');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const getStatusColor = (status: string) => {
    const statusColors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      submitted: 'info',
      under_review: 'warning',
      interviewing: 'primary',
      hired: 'success',
      rejected: 'error',
      withdrawn: 'default'
    };
    return statusColors[status] || 'default';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button onClick={fetchStats} sx={{ ml: 2 }}>
          Retry
        </Button>
      </Alert>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" gutterBottom>
          Recruitment Management
        </Typography>
        <Box>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/hr/recruitment/job-postings/new')}
            sx={{ mr: 1 }}
          >
            New Job Posting
          </Button>
          <IconButton onClick={handleMenuClick}>
            <MoreVertIcon />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={() => navigate('/hr/recruitment/settings')}>
              Settings
            </MenuItem>
            <MenuItem onClick={() => navigate('/hr/recruitment/reports')}>
              Reports
            </MenuItem>
          </Menu>
        </Box>
      </Box>

      {/* Overview Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <WorkIcon color="primary" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Active Job Postings
                  </Typography>
                  <Typography variant="h4">
                    {stats?.overview.activePostings || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <PeopleIcon color="secondary" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Applications
                  </Typography>
                  <Typography variant="h4">
                    {stats?.overview.totalApplications || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <ScheduleIcon color="warning" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Pending Review
                  </Typography>
                  <Typography variant="h4">
                    {stats?.overview.pendingApplications || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUpIcon color="success" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Draft Postings
                  </Typography>
                  <Typography variant="h4">
                    {stats?.overview.draftPostings || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Applications by Status" />
            <Tab label="Postings by Department" />
            <Tab label="Quick Actions" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Typography variant="h6" gutterBottom>
            Application Status Overview
          </Typography>
          <Grid container spacing={2}>
            {stats?.applicationsByStatus.map((item) => (
              <Grid item xs={12} sm={6} md={4} key={item.status}>
                <Card variant="outlined">
                  <CardContent>
                    <Box display="flex" justifyContent="between" alignItems="center">
                      <Typography variant="body2" color="textSecondary">
                        {item.status.replace(/_/g, ' ').toUpperCase()}
                      </Typography>
                      <Chip
                        label={item.count}
                        color={getStatusColor(item.status)}
                        size="small"
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6" gutterBottom>
            Job Postings by Department
          </Typography>
          <Grid container spacing={2}>
            {stats?.postingsByDepartment.map((item) => (
              <Grid item xs={12} sm={6} md={4} key={item.department}>
                <Card variant="outlined">
                  <CardContent>
                    <Box display="flex" justifyContent="between" alignItems="center">
                      <Typography variant="body1">
                        {item.department}
                      </Typography>
                      <Badge badgeContent={item.count} color="primary">
                        <WorkIcon />
                      </Badge>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            Quick Actions
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={4}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<WorkIcon />}
                onClick={() => navigate('/hr/recruitment/job-postings')}
              >
                Manage Job Postings
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<PeopleIcon />}
                onClick={() => navigate('/hr/recruitment/applications')}
              >
                Review Applications
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<ScheduleIcon />}
                onClick={() => navigate('/hr/recruitment/interviews')}
              >
                Schedule Interviews
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<AssessmentIcon />}
                onClick={() => navigate('/hr/recruitment/reports')}
              >
                View Reports
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FilterIcon />}
                onClick={() => navigate('/hr/recruitment/pipeline')}
              >
                Hiring Pipeline
              </Button>
            </Grid>
          </Grid>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default RecruitmentDashboard;
