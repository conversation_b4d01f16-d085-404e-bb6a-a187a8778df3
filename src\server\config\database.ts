import { DataSource } from 'typeorm';
import { User } from '../../entities/User';
import { Ticket } from '../../entities/Ticket';
import { Comment } from '../../entities/Comment';
import { Attachment } from '../../entities/Attachment';
import { KnowledgeBase } from '../../entities/KnowledgeBase';
import { KnowledgeCategory } from '../../entities/KnowledgeCategory';
import { KnowledgeTag } from '../../entities/KnowledgeTag';
import { Vendor } from '../../entities/Vendor';
import { PrinterMaintenance } from '../entities/PrinterMaintenance';
import { Asset } from '../entities/Asset';
import { Role } from '../../entities/Role';
import { RoleTemplate } from '../../entities/RoleTemplate';
import { PermissionGroup } from '../../entities/PermissionGroup';
import { UserRoleAssignment } from '../../entities/UserRoleAssignment';
import logger from '../utils/logger';
import mysql from 'mysql2/promise';

const initializeDatabase = async () => {
  let connection: mysql.Connection | null = null;
  let dataSource: DataSource | null = null;

  try {
    // First, create a MySQL connection without database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root'
    });

    // Check if database exists, if not create it
    await connection.query('CREATE DATABASE IF NOT EXISTS ims_db');
    await connection.query('USE ims_db');

    // Close MySQL connection
    await connection.end();
    connection = null;

    // Create TypeORM connection
    dataSource = new DataSource({
      type: 'mysql',
      host: 'localhost',
      port: 3306,
      username: 'root',
      password: 'root',
      database: 'ims_db',
      entities: [
        User, 
        Ticket, 
        Comment, 
        Attachment, 
        KnowledgeBase, 
        KnowledgeCategory, 
        KnowledgeTag, 
        Vendor, 
        PrinterMaintenance, 
        Asset,
        Role,
        RoleTemplate,
        PermissionGroup,
        UserRoleAssignment
      ],
      synchronize: true,
      logging: true,
      logger: 'advanced-console'
    });

    await dataSource.initialize();
    logger.info('Data Source has been initialized');
    return dataSource;
  } catch (error) {
    logger.error('Error during database initialization:', error);

    // Clean up connections
    if (connection) {
      try {
        await connection.end();
      } catch (err) {
        logger.error('Error closing MySQL connection:', err);
      }
    }

    if (dataSource && dataSource.isInitialized) {
      try {
        await dataSource.destroy();
      } catch (err) {
        logger.error('Error closing TypeORM connection:', err);
      }
    }

    throw error;
  }
};

// Export both the initialization function and the DataSource
export const AppDataSource = new DataSource({
  type: 'mysql',
  host: 'localhost',
  port: 3306,
  username: 'root',
  password: 'root',
  database: 'ims_db',
  entities: [
    User, 
    Ticket, 
    Comment, 
    Attachment, 
    KnowledgeBase, 
    KnowledgeCategory, 
    KnowledgeTag, 
    Vendor, 
    PrinterMaintenance, 
    Asset,
    Role,
    RoleTemplate,
    PermissionGroup,
    UserRoleAssignment
  ],
  synchronize: true,
  logging: true,
  logger: 'advanced-console'
});

// Initialize the AppDataSource if it's not already initialized
export const initializeAppDataSource = async () => {
  try {
    if (!AppDataSource.isInitialized) {
      logger.info('Initializing AppDataSource...');
      await AppDataSource.initialize();
      logger.info('AppDataSource initialized successfully');
    }
    return AppDataSource;
  } catch (error) {
    logger.error('Failed to initialize AppDataSource:', error);
    throw error;
  }
};

export default initializeDatabase; 