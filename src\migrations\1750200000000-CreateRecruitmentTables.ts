import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateRecruitmentTables1750200000000 implements MigrationInterface {
  name = 'CreateRecruitmentTables1750200000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create job_postings table
    await queryRunner.query(`
      CREATE TABLE job_postings (
        id int NOT NULL AUTO_INCREMENT,
        title varchar(255) NOT NULL,
        description text NOT NULL,
        requirements text NOT NULL,
        responsibilities text,
        benefits text,
        department varchar(100) NOT NULL,
        location varchar(100) NOT NULL,
        jobType enum('full_time', 'part_time', 'contract', 'temporary', 'internship', 'freelance') NOT NULL DEFAULT 'full_time',
        experienceLevel enum('entry_level', 'junior', 'mid_level', 'senior', 'lead', 'manager', 'director', 'executive') NOT NULL DEFAULT 'mid_level',
        workLocation enum('onsite', 'remote', 'hybrid') NOT NULL DEFAULT 'onsite',
        status enum('draft', 'published', 'paused', 'closed', 'cancelled') NOT NULL DEFAULT 'draft',
        minSalary decimal(10,2),
        maxSalary decimal(10,2),
        salaryCurrency varchar(50),
        salaryPeriod varchar(50),
        numberOfPositions int,
        applicationDeadline timestamp NULL,
        requiredSkills json,
        preferredSkills json,
        qualifications json,
        applicationInstructions text,
        customFields json,
        isActive tinyint NOT NULL DEFAULT 1,
        isUrgent tinyint NOT NULL DEFAULT 0,
        isFeatured tinyint NOT NULL DEFAULT 0,
        allowExternalApplications tinyint NOT NULL DEFAULT 1,
        externalApplicationUrl varchar(500),
        internalNotes text,
        tags json,
        viewCount int NOT NULL DEFAULT 0,
        applicationCount int NOT NULL DEFAULT 0,
        publishedAt timestamp NULL,
        closedAt timestamp NULL,
        createdById varchar(36) NOT NULL,
        hiringManagerId varchar(36),
        createdAt timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        updatedAt timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
        PRIMARY KEY (id),
        INDEX idx_job_postings_status (status),
        INDEX idx_job_postings_department (department),
        INDEX idx_job_postings_location (location),
        INDEX idx_job_postings_job_type (jobType),
        INDEX idx_job_postings_experience_level (experienceLevel),
        INDEX idx_job_postings_work_location (workLocation),
        INDEX idx_job_postings_created_by (createdById),
        INDEX idx_job_postings_hiring_manager (hiringManagerId),
        INDEX idx_job_postings_created_at (createdAt),
        INDEX idx_job_postings_deadline (applicationDeadline),
        INDEX idx_job_postings_is_active (isActive),
        INDEX idx_job_postings_is_urgent (isUrgent),
        INDEX idx_job_postings_is_featured (isFeatured),
        CONSTRAINT FK_job_postings_created_by FOREIGN KEY (createdById) REFERENCES users(id) ON DELETE RESTRICT ON UPDATE CASCADE,
        CONSTRAINT FK_job_postings_hiring_manager FOREIGN KEY (hiringManagerId) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
      ) ENGINE=InnoDB
    `);

    // Create job_applications table
    await queryRunner.query(`
      CREATE TABLE job_applications (
        id int NOT NULL AUTO_INCREMENT,
        firstName varchar(100) NOT NULL,
        lastName varchar(100) NOT NULL,
        email varchar(255) NOT NULL,
        phone varchar(20),
        address text,
        city varchar(100),
        country varchar(100),
        postalCode varchar(20),
        coverLetter text,
        resumeUrl varchar(500),
        resumeFileName varchar(255),
        portfolioUrl varchar(500),
        linkedinUrl varchar(500),
        githubUrl varchar(500),
        websiteUrl varchar(500),
        status enum('submitted', 'under_review', 'screening', 'interview_scheduled', 'interviewing', 'technical_assessment', 'reference_check', 'offer_pending', 'offer_extended', 'offer_accepted', 'offer_declined', 'hired', 'rejected', 'withdrawn', 'on_hold') NOT NULL DEFAULT 'submitted',
        source enum('company_website', 'job_board', 'linkedin', 'referral', 'recruiter', 'social_media', 'career_fair', 'direct_application', 'other') NOT NULL DEFAULT 'company_website',
        referredBy varchar(255),
        rating int NOT NULL DEFAULT 0,
        isStarred tinyint NOT NULL DEFAULT 0,
        notes text,
        internalNotes text,
        customFields json,
        skills json,
        experience json,
        education json,
        certifications json,
        languages json,
        expectedSalary decimal(10,2),
        salaryCurrency varchar(50),
        salaryPeriod varchar(50),
        isAvailable tinyint NOT NULL DEFAULT 1,
        availableStartDate date,
        noticePeriodDays int,
        requiresVisa tinyint NOT NULL DEFAULT 0,
        willingToRelocate tinyint NOT NULL DEFAULT 0,
        willingToTravel tinyint NOT NULL DEFAULT 0,
        statusChangedAt timestamp NULL,
        lastViewedAt timestamp NULL,
        lastContactedAt timestamp NULL,
        jobPostingId int NOT NULL,
        assignedToId varchar(36),
        lastReviewedById varchar(36),
        createdAt timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        updatedAt timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
        PRIMARY KEY (id),
        INDEX idx_job_applications_email (email),
        INDEX idx_job_applications_status (status),
        INDEX idx_job_applications_job_posting (jobPostingId),
        INDEX idx_job_applications_assigned_to (assignedToId),
        INDEX idx_job_applications_source (source),
        INDEX idx_job_applications_created_at (createdAt),
        INDEX idx_job_applications_last_reviewed (lastReviewedById),
        INDEX idx_job_applications_rating (rating),
        INDEX idx_job_applications_is_starred (isStarred),
        INDEX idx_job_applications_status_changed (statusChangedAt),
        INDEX idx_job_applications_first_name (firstName),
        INDEX idx_job_applications_last_name (lastName),
        CONSTRAINT FK_job_applications_job_posting FOREIGN KEY (jobPostingId) REFERENCES job_postings(id) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT FK_job_applications_assigned_to FOREIGN KEY (assignedToId) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
        CONSTRAINT FK_job_applications_last_reviewed_by FOREIGN KEY (lastReviewedById) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
      ) ENGINE=InnoDB
    `);

    // Create interviews table
    await queryRunner.query(`
      CREATE TABLE interviews (
        id int NOT NULL AUTO_INCREMENT,
        title varchar(255) NOT NULL,
        description text,
        type enum('phone_screening', 'video_call', 'in_person', 'technical', 'behavioral', 'panel', 'group', 'presentation', 'case_study', 'final') NOT NULL DEFAULT 'video_call',
        status enum('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'rescheduled', 'no_show') NOT NULL DEFAULT 'scheduled',
        result enum('pending', 'strong_yes', 'yes', 'maybe', 'no', 'strong_no') NOT NULL DEFAULT 'pending',
        startTime timestamp NOT NULL,
        endTime timestamp NOT NULL,
        location varchar(255),
        meetingLink varchar(500),
        meetingId varchar(100),
        meetingPassword varchar(100),
        agenda text,
        questions json,
        preparationNotes text,
        candidateInstructions text,
        durationMinutes int,
        isRecorded tinyint NOT NULL DEFAULT 0,
        recordingUrl varchar(500),
        attachments json,
        internalNotes text,
        customFields json,
        reminderSentAt timestamp NULL,
        confirmationSentAt timestamp NULL,
        actualStartTime timestamp NULL,
        actualEndTime timestamp NULL,
        overallRating int NOT NULL DEFAULT 0,
        summary text,
        strengths text,
        weaknesses text,
        recommendations text,
        applicationId int NOT NULL,
        scheduledById varchar(36) NOT NULL,
        createdAt timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        updatedAt timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
        PRIMARY KEY (id),
        INDEX idx_interviews_application_id (applicationId),
        INDEX idx_interviews_status (status),
        INDEX idx_interviews_type (type),
        INDEX idx_interviews_result (result),
        INDEX idx_interviews_scheduled_by (scheduledById),
        INDEX idx_interviews_start_time (startTime),
        INDEX idx_interviews_end_time (endTime),
        INDEX idx_interviews_created_at (createdAt),
        INDEX idx_interviews_overall_rating (overallRating),
        CONSTRAINT FK_interviews_application FOREIGN KEY (applicationId) REFERENCES job_applications(id) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT FK_interviews_scheduled_by FOREIGN KEY (scheduledById) REFERENCES users(id) ON DELETE RESTRICT ON UPDATE CASCADE
      ) ENGINE=InnoDB
    `);

    // Create interview_interviewers junction table
    await queryRunner.query(`
      CREATE TABLE interview_interviewers (
        interviewId int NOT NULL,
        userId varchar(36) NOT NULL,
        PRIMARY KEY (interviewId, userId),
        INDEX idx_interview_interviewers_interview (interviewId),
        INDEX idx_interview_interviewers_user (userId),
        CONSTRAINT FK_interview_interviewers_interview FOREIGN KEY (interviewId) REFERENCES interviews(id) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT FK_interview_interviewers_user FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE
      ) ENGINE=InnoDB
    `);

    // Create application_evaluations table
    await queryRunner.query(`
      CREATE TABLE application_evaluations (
        id int NOT NULL AUTO_INCREMENT,
        overallRating int NOT NULL DEFAULT 0,
        technicalSkillsRating int,
        communicationRating int,
        experienceRating int,
        culturalFitRating int,
        educationRating int,
        strengths text,
        weaknesses text,
        comments text,
        recommendation text,
        recommendForHire tinyint NOT NULL DEFAULT 0,
        recommendForInterview tinyint NOT NULL DEFAULT 0,
        customRatings json,
        skillsAssessment json,
        internalNotes text,
        applicationId int NOT NULL,
        evaluatorId varchar(36) NOT NULL,
        createdAt timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        updatedAt timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
        PRIMARY KEY (id),
        INDEX idx_application_evaluations_application_id (applicationId),
        INDEX idx_application_evaluations_evaluator_id (evaluatorId),
        INDEX idx_application_evaluations_overall_rating (overallRating),
        INDEX idx_application_evaluations_created_at (createdAt),
        CONSTRAINT FK_application_evaluations_application FOREIGN KEY (applicationId) REFERENCES job_applications(id) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT FK_application_evaluations_evaluator FOREIGN KEY (evaluatorId) REFERENCES users(id) ON DELETE RESTRICT ON UPDATE CASCADE
      ) ENGINE=InnoDB
    `);

    // Create interview_feedback table
    await queryRunner.query(`
      CREATE TABLE interview_feedback (
        id int NOT NULL AUTO_INCREMENT,
        overallRating int NOT NULL DEFAULT 0,
        decision enum('strong_hire', 'hire', 'maybe', 'no_hire', 'strong_no_hire') NOT NULL DEFAULT 'maybe',
        technicalSkillsRating int,
        communicationRating int,
        problemSolvingRating int,
        culturalFitRating int,
        leadershipRating int,
        strengths text,
        areasForImprovement text,
        detailedFeedback text,
        questionResponses json,
        skillsAssessment json,
        nextSteps text,
        concerns text,
        wouldWorkWithAgain tinyint NOT NULL DEFAULT 0,
        recommendForNextRound tinyint NOT NULL DEFAULT 0,
        actualDurationMinutes int,
        internalNotes text,
        customFields json,
        interviewId int NOT NULL,
        interviewerId varchar(36) NOT NULL,
        createdAt timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        updatedAt timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
        PRIMARY KEY (id),
        INDEX idx_interview_feedback_interview_id (interviewId),
        INDEX idx_interview_feedback_interviewer_id (interviewerId),
        INDEX idx_interview_feedback_overall_rating (overallRating),
        INDEX idx_interview_feedback_created_at (createdAt),
        CONSTRAINT FK_interview_feedback_interview FOREIGN KEY (interviewId) REFERENCES interviews(id) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT FK_interview_feedback_interviewer FOREIGN KEY (interviewerId) REFERENCES users(id) ON DELETE RESTRICT ON UPDATE CASCADE
      ) ENGINE=InnoDB
    `);

    console.log('✅ Recruitment tables created successfully');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop tables in reverse order to handle foreign key constraints
    await queryRunner.query(`DROP TABLE IF EXISTS interview_feedback`);
    await queryRunner.query(`DROP TABLE IF EXISTS application_evaluations`);
    await queryRunner.query(`DROP TABLE IF EXISTS interview_interviewers`);
    await queryRunner.query(`DROP TABLE IF EXISTS interviews`);
    await queryRunner.query(`DROP TABLE IF EXISTS job_applications`);
    await queryRunner.query(`DROP TABLE IF EXISTS job_postings`);

    console.log('✅ Recruitment tables dropped successfully');
  }
}
