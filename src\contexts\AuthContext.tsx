import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import api from '../services/api';
import { UserRole, UserPermissions } from '../types/common';
import { toast } from 'react-hot-toast';

interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  department?: string;
  permissions: UserPermissions;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  hasPermission: (permission: keyof User['permissions']) => boolean;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Helper function to enforce admin privileges - moved outside the component to prevent recreation
function enforceAdminPrivileges(userData: User): User {
  // Set to IT_ADMIN role for Administrator or anyone with IT_ADMIN role
  if (userData.name === 'Administrator' || userData.email === '<EMAIL>' || userData.role === UserRole.IT_ADMIN) {
    // Set the role to IT_ADMIN if it's not already
    userData.role = UserRole.IT_ADMIN;
    
    // Ensure all permissions are set to true for IT_ADMIN
    const fullPermissions: UserPermissions = {
      canCreateTickets: true,
      canCreateTicketsForOthers: true,
      canEditTickets: true,
      canDeleteTickets: true,
      canCloseTickets: true,
      canLockTickets: true,
      canAssignTickets: true,
      canEscalateTickets: true,
      canViewAllTickets: true,
      canCreateEmployee: true,
      canEditEmployee: true,
      canDeleteEmployee: true,
      canViewEmployees: true,
      canManageAttendance: true,
      canManageLeave: true,
      canManagePayroll: true,
      canManagePerformance: true,
      isAdmin: true,
      canAddUsers: true,
      canEditUsers: true,
      canDeleteUsers: true,
      canViewReports: true,
      canExportData: true,
      canImportData: true,
      canConfigureSystem: true,
      canManageRoles: true,
      canApproveRequests: true,
      canViewAllDepartments: true,
      canAccessAllModules: true,
      canViewDashboards: true,
      canViewAllDashboards: true,
      canCreateDashboards: true,
      canEditDashboards: true,
      canDeleteDashboards: true,
      canShareDashboards: true,
      canExportDashboardData: true,
      canConfigureDashboardAlerts: true,
      canManageDashboardUsers: true,
      department: userData.department || 'IT'
    };
    
    userData.permissions = fullPermissions;
    console.log(`Applied full IT_ADMIN privileges to ${userData.name}`);
  }
  
  return userData;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  // Use lazy initialization for state to avoid unnecessary renders
  const [user, setUser] = useState<User | null>(() => {
    const storedUserData = localStorage.getItem('userData');
    if (storedUserData) {
      try {
        const parsedUser = JSON.parse(storedUserData);
        return enforceAdminPrivileges(parsedUser);
      } catch (error) {
        console.error('Error parsing stored user data:', error);
        return null;
      }
    }
    return null;
  });
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(() => {
    return !!localStorage.getItem('authToken') && !!localStorage.getItem('userData');
  });

  // Memoize the refreshUserData function to avoid unnecessary recreations
  const refreshUserData = useCallback(async () => {
    try {
      const token = localStorage.getItem('authToken');
      if (!token) return;
      
      const response = await api.get('/api/auth/me');
      if (response.data) {
        let userData = enforceAdminPrivileges(response.data);
        setUser(userData);
        localStorage.setItem('userData', JSON.stringify(userData));
      }
    } catch (error) {
      console.error('Error refreshing user data:', error);
    }
  }, []);

  // Memoize the checkAuthStatus function
  const checkAuthStatus = useCallback(async () => {
    try {
      const token = localStorage.getItem('authToken');
      const storedUserData = localStorage.getItem('userData');

      if (!token || !storedUserData) {
        setLoading(false);
        setIsAuthenticated(false);
        setUser(null);
        return;
      }

      try {
        const response = await api.get('/api/auth/me');
        if (response.data) {
          let userData = enforceAdminPrivileges(response.data);
          
          setUser(userData);
          localStorage.setItem('userData', JSON.stringify(userData));
          setIsAuthenticated(true);
        } else {
          throw new Error('Invalid response from server');
        }
      } catch (err: any) {
        console.error('Auth verification error:', err);
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
        setUser(null);
        setIsAuthenticated(false);
      }
    } catch (err) {
      console.error('Auth check error:', err);
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
    }
  }, []);

  // Run auth check only once on mount
  useEffect(() => {
    const initialize = async () => {
      await checkAuthStatus();
    };
    initialize();
    // No dependencies - run only once on mount
  }, []);

  // Optimize the effect that runs on user changes
  useEffect(() => {
    if (!user) {
      setIsAuthenticated(false);
      return;
    }
    
    setIsAuthenticated(true);
    const currentPath = window.location.pathname;
    sessionStorage.setItem('lastRoute', currentPath);
  }, [user]);

  // Memoize login function
  const login = useCallback(async (email: string, password: string) => {
    try {
      console.log("DEBUG_AUTH_CONTEXT: Login attempt for email:", email);
      setLoading(true);
      setError(null);
      
      const response = await api.post('/api/auth/login', { email, password });
      
      if (!response.data?.token || !response.data?.user) {
        throw new Error('Invalid response from server');
      }
      
      let { token, user: userData } = response.data;
      
      // Apply admin privileges
      userData = enforceAdminPrivileges(userData);
      
      localStorage.setItem('authToken', token);
      localStorage.setItem('userData', JSON.stringify(userData));
      
      setUser(userData);
      setIsAuthenticated(true);
      
      return userData;
    } catch (err: any) {
      console.error('Login error:', err);
      setError(err.response?.data?.message || 'Failed to login');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Memoize logout function
  const logout = useCallback(async () => {
    try {
      await api.post('/api/auth/logout');
    } catch (err) {
      console.error('Logout error:', err);
    } finally {
      localStorage.removeItem('authToken');
      localStorage.removeItem('userData');
      sessionStorage.removeItem('lastRoute');
      setUser(null);
      setIsAuthenticated(false);
      window.location.href = '/login';
    }
  }, []);

  // Memoize hasPermission function
  const hasPermission = useCallback((permission: keyof User['permissions']) => {
    // Always return true for Administrator
    if (user?.name === 'Administrator' || user?.email === '<EMAIL>') {
      return true;
    }
    
    // Handle the department property specially since it's a string
    if (permission === 'department') {
      return !!user?.permissions?.department;
    }
    
    return user?.permissions?.[permission] === true;
  }, [user]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    user,
    loading,
    error,
    isAuthenticated,
    login,
    logout,
    hasPermission,
    refreshUserData
  }), [user, loading, error, isAuthenticated, login, logout, hasPermission, refreshUserData]);

  // CRITICAL: Completely eliminate console logging to prevent performance issues
  // All console logging removed from AuthContext to prevent spam

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}