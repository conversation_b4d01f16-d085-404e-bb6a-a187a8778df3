import express from 'express';
import multer from 'multer';
import { SoftwareLicense } from '../../entities/SoftwareLicense';
import { AppDataSource } from '../../config/database';
import { CreateSoftwareLicenseDto } from '../../types/SoftwareLicense';
import { Vendor } from '../../entities/Vendor';
import path from 'path';
import fs from 'fs';
import { authMiddleware } from '../middleware/authMiddleware';
import { User } from '../../entities/User';
import { Asset } from '../../entities/Asset';
import { In } from 'typeorm';

const router = express.Router();
const softwareLicenseRepository = AppDataSource.getRepository(SoftwareLicense);
const vendorRepository = AppDataSource.getRepository(Vendor);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/invoices';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ storage });

// Apply auth middleware to all routes
router.use(authMiddleware.verify);

// Create a new software license
router.post('/', upload.single('invoiceFile'), async (req, res) => {
  try {
    console.log('Received software license creation request:', req.body);
    const data: CreateSoftwareLicenseDto = req.body;
    
    // Validate vendor
    const vendor = await vendorRepository.findOne({ where: { id: data.vendorId } });
    
    if (!vendor) {
      console.log(`Vendor with ID ${data.vendorId} not found`);
      return res.status(404).json({ message: 'Vendor not found' });
    }

    // Create software license object
    const softwareLicense = new SoftwareLicense();
    softwareLicense.name = data.softwareName;
    softwareLicense.vendor = vendor;
    softwareLicense.category = data.category || '';
    softwareLicense.type = data.type;
    softwareLicense.department = data.department;
    softwareLicense.status = data.status;
    softwareLicense.licenseKey = data.licenseKey || '';
    softwareLicense.totalSeats = data.totalSeats || 0;
    softwareLicense.usedSeats = data.usedSeats || 0;
    softwareLicense.purchaseDate = new Date(data.purchaseDate);
    softwareLicense.expiryDate = data.expiryDate ? new Date(data.expiryDate) : null;
    softwareLicense.paymentFrequency = data.paymentFrequency;
    softwareLicense.costPKR = data.costPKR || 0;
    softwareLicense.costUSD = data.costUSD || 0;
    softwareLicense.paidBy = data.paidBy || 'Company';
    softwareLicense.autoRenew = Boolean(data.autoRenew);
    softwareLicense.notes = data.notes || null;
    softwareLicense.currencyConverter = Boolean(data.currencyRate !== 1);
    softwareLicense.renewalReminder = Boolean(data.renewalReminder);
    softwareLicense.multiLocationUse = Boolean(data.multiLocation);
    softwareLicense.loginSharingInfo = data.loginInfo || null;
    softwareLicense.socialMediaLinks = data.socialMediaLinks || [];

    // Handle user assignments - FIXED VERSION USING QUERYBUILDER
    if (Array.isArray(data.assignedUserIds) && data.assignedUserIds.length > 0) {
      console.log('Processing user assignments with QueryBuilder:', data.assignedUserIds);
      
      // Ensure all IDs are normalized strings
      const userIds = data.assignedUserIds.map(id => String(id).trim());
      console.log('User IDs normalized:', userIds);
      
      try {
        // Use QueryBuilder with explicit parameter binding for reliable UUID matching
        const userRepository = AppDataSource.getRepository(User);
        
        // Build a query with proper parameter binding for each UUID
        const queryBuilder = userRepository.createQueryBuilder('user');
        
        // Add WHERE conditions for each ID with proper parameter binding
        const whereConditions = userIds.map((id, index) => {
          return `user.id = :id${index}`;
        }).join(' OR ');
        
        // Add parameters for each ID
        const parameters: Record<string, string> = {};
        userIds.forEach((id, index) => {
          parameters[`id${index}`] = id;
        });
        
        const users = await queryBuilder
          .where(whereConditions, parameters)
          .getMany();
        
        console.log(`QueryBuilder found ${users.length} users out of ${userIds.length} requested`);
        console.log('Users found:', users.map(u => ({ id: u.id, name: u.name })));
        
        // Assign the users to the software license
        softwareLicense.assignedUsers = users;
      } catch (error) {
        console.error('Error finding users with QueryBuilder:', error);
        softwareLicense.assignedUsers = [];
      }
    } else {
      console.log('No user assignments provided');
      softwareLicense.assignedUsers = [];
    }

    // Handle asset links similarly
    if (Array.isArray(data.linkedAssetIds) && data.linkedAssetIds.length > 0) {
      console.log('Processing asset links for new license:', data.linkedAssetIds);
      
      // Ensure all IDs are strings
      const assetIds = data.linkedAssetIds.map(id => String(id));
      console.log('Asset IDs normalized to strings:', assetIds);
      
      // Fetch assets from database
      const assetRepository = AppDataSource.getRepository(Asset);
      try {
        const assets = await assetRepository.findBy({
          id: In(assetIds)
        });
        
        console.log(`Found ${assets.length} assets out of ${assetIds.length} requested IDs`);
        console.log('Assets to link:', assets.map(a => ({ id: a.id, tag: a.assetTag || a.id })));
        
        softwareLicense.linkedAssets = assets;
      } catch (error) {
        console.error('Error fetching assets for linking:', error);
        softwareLicense.linkedAssets = [];
      }
    } else {
      console.log('No asset links provided');
      softwareLicense.linkedAssets = [];
    }

    // Handle file upload
    if (req.file) {
      softwareLicense.invoiceUrl = req.file.path;
    }

    // Save to database with relations
    console.log('Saving software license with assigned users:', 
      softwareLicense.assignedUsers?.map(u => ({ id: u.id, name: u.name })) || []);
    
    // Create a transaction to ensure atomicity
    await AppDataSource.transaction(async transactionalEntityManager => {
      // First, save the license to get an ID
      const savedLicense = await transactionalEntityManager.save(SoftwareLicense, softwareLicense);
      console.log('Software license saved with ID:', savedLicense.id);
      
      // If we have assigned users, explicitly save the relationships
      if (Array.isArray(softwareLicense.assignedUsers) && softwareLicense.assignedUsers.length > 0) {
        console.log(`Explicitly persisting ${softwareLicense.assignedUsers.length} user relationships`);
        
        // Clear any existing relationships
        await transactionalEntityManager.query(
          `DELETE FROM _assigned_users WHERE softwareLicenseId = ?`, 
          [savedLicense.id]
        );
        
        // Explicitly insert each relationship
        for (const user of softwareLicense.assignedUsers) {
          await transactionalEntityManager.query(
            `INSERT INTO _assigned_users(softwareLicenseId, userId) VALUES (?, ?)`,
            [savedLicense.id, user.id]
          );
          console.log(`Added relationship: License ${savedLicense.id} -> User ${user.id}`);
        }
      }
      
      // Return the saved license
      return savedLicense;
    })
    .then(async (savedLicense) => {
      // Continue with fetching the complete license...
      
      // Fetch the complete saved license with more detailed relational data
      try {
        // Get the complete license data with detailed user information
        const completeLicense = await AppDataSource
          .createQueryBuilder(SoftwareLicense, 'license')
          .leftJoinAndSelect('license.vendor', 'vendor')
          .leftJoinAndSelect('license.assignedUsers', 'user')
          .leftJoinAndSelect('license.linkedAssets', 'asset')
          .where('license.id = :id', { id: savedLicense.id })
          .select([
            'license',
            'vendor.id',
            'vendor.vendorName',
            'vendor.companyName',
            'user.id',
            'user.name',
            'user.email',
            'user.department',
            'asset.id',
            'asset.assetTag',
            'asset.assetType',
            'asset.manufacturer',
            'asset.model'
          ])
          .getOne();
        
        if (!completeLicense) {
          console.log('Warning: Could not fetch the complete license after saving');
          console.log('Returning the basic saved license data instead');
          res.status(201).json(savedLicense);
          return;
        }
        
        console.log('Complete saved license data:', {
          id: completeLicense.id,
          name: completeLicense.name,
          assignedUsers: completeLicense.assignedUsers?.map(u => ({ id: u.id, name: u.name, email: u.email })) || [],
          assignedUserCount: completeLicense.assignedUsers?.length || 0,
          linkedAssetCount: completeLicense.linkedAssets?.length || 0
        });
        
        res.status(201).json(completeLicense);
      } catch (error) {
        console.error('Error fetching complete license data after save:', error);
        // Fall back to returning just the saved license if we can't get the complete data
        res.status(201).json(savedLicense);
      }
    });
  } catch (error) {
    console.error('Error creating software license:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
    }
    res.status(500).json({ message: 'Failed to create software license' });
  }
});

// Get all software licenses
router.get('/', async (req, res) => {
  try {
    // Use query builder for consistent approach and more control over data
    const licenses = await AppDataSource
      .createQueryBuilder(SoftwareLicense, 'license')
      .leftJoinAndSelect('license.vendor', 'vendor')
      .leftJoinAndSelect('license.assignedUsers', 'user')
      .leftJoinAndSelect('license.linkedAssets', 'asset')
      .select([
        'license',
        'vendor.id',
        'vendor.vendorName',
        'vendor.companyName',
        'user.id',
        'user.name',
        'user.email',
        'user.department',
        'asset.id',
        'asset.assetTag',
        'asset.assetType',
        'asset.manufacturer',
        'asset.model'
      ])
      .getMany();
    
    console.log(`Found ${licenses.length} software licenses`);
    
    // Count assigned users for each license
    licenses.forEach(license => {
      if (license.assignedUsers && license.assignedUsers.length > 0) {
        console.log(`License ${license.id} has ${license.assignedUsers.length} assigned users`);
      }
    });
    
    res.json(licenses);
  } catch (error) {
    console.error('Error fetching software licenses:', error);
    res.status(500).json({ message: 'Failed to fetch software licenses' });
  }
});

// Get software license by ID
router.get('/:id', async (req, res) => {
  try {
    console.log(`Fetching software license with ID: ${req.params.id}`);
    
    // Use query builder for more control over the returned data
    const license = await AppDataSource
      .createQueryBuilder(SoftwareLicense, 'license')
      .leftJoinAndSelect('license.vendor', 'vendor')
      .leftJoinAndSelect('license.assignedUsers', 'user')
      .leftJoinAndSelect('license.linkedAssets', 'asset')
      .where('license.id = :id', { id: req.params.id })
      .select([
        'license',
        'vendor.id',
        'vendor.vendorName',
        'vendor.companyName',
        'user.id',
        'user.name',
        'user.email',
        'user.department',
        'asset.id',
        'asset.assetTag',
        'asset.assetType',
        'asset.manufacturer',
        'asset.model'
      ])
      .getOne();
    
    if (!license) {
      console.log(`Software license with ID ${req.params.id} not found`);
      return res.status(404).json({ message: 'Software license not found' });
    }
    
    console.log(`Found software license: ${license.name}`);
    
    // Check if assignedUsers exists and has data
    if (license.assignedUsers && license.assignedUsers.length > 0) {
      console.log(`Assigned users count: ${license.assignedUsers.length}`);
      console.log(`Assigned users details:`, 
        license.assignedUsers.map(u => ({ id: u.id, name: u.name, email: u.email })));
    } else {
      console.log('No assigned users found for this license');
    }
    
    // Check linked assets
    if (license.linkedAssets && license.linkedAssets.length > 0) {
      console.log(`Linked assets count: ${license.linkedAssets.length}`);
    } else {
      console.log('No linked assets found for this license');
    }
    
    res.json(license);
  } catch (error) {
    console.error('Error fetching software license:', error);
    res.status(500).json({ message: 'Failed to fetch software license' });
  }
});

// Update software license
router.put('/:id', upload.single('invoiceFile'), async (req, res) => {
  try {
    console.log(`Updating software license with ID: ${req.params.id}`);
    
    // Find the license with all its relations using query builder for consistency
    const license = await AppDataSource
      .createQueryBuilder(SoftwareLicense, 'license')
      .leftJoinAndSelect('license.vendor', 'vendor')
      .leftJoinAndSelect('license.assignedUsers', 'user')
      .leftJoinAndSelect('license.linkedAssets', 'asset')
      .where('license.id = :id', { id: req.params.id })
      .getOne();
    
    if (!license) {
      console.log(`Software license with ID ${req.params.id} not found`);
      return res.status(404).json({ message: 'Software license not found' });
    }

    const data: Partial<CreateSoftwareLicenseDto> = req.body;
    console.log('Update data received:', data);
    
    // Log existing assignments before update
    console.log('Current assignedUsers before update:', 
      license.assignedUsers?.map(u => ({ id: u.id, name: u.name, email: u.email })) || []);

    if (data.vendorId && data.vendorId !== license.vendor.id) {
      const vendor = await vendorRepository.findOne({ where: { id: data.vendorId } });
      if (!vendor) {
        return res.status(404).json({ message: 'Vendor not found' });
      }
      license.vendor = vendor;
    }

    // Update fields if they exist in the request
    if (data.softwareName) license.name = data.softwareName;
    if (data.category !== undefined) license.category = data.category || '';
    if (data.type) license.type = data.type;
    if (data.department) license.department = data.department;
    if (data.status) license.status = data.status;
    if (data.licenseKey !== undefined) license.licenseKey = data.licenseKey || '';
    if (data.totalSeats !== undefined) license.totalSeats = data.totalSeats || 0;
    if (data.usedSeats !== undefined) license.usedSeats = data.usedSeats || 0;
    if (data.purchaseDate) license.purchaseDate = new Date(data.purchaseDate);
    if (data.expiryDate !== undefined) {
      license.expiryDate = data.expiryDate ? new Date(data.expiryDate) : null;
    }
    if (data.paymentFrequency) license.paymentFrequency = data.paymentFrequency;
    if (data.costPKR !== undefined) license.costPKR = data.costPKR || 0;
    if (data.costUSD !== undefined) license.costUSD = data.costUSD || 0;
    if (data.paidBy) license.paidBy = data.paidBy;
    if (data.autoRenew !== undefined) license.autoRenew = Boolean(data.autoRenew);
    if (data.notes !== undefined) license.notes = data.notes || null;
    if (typeof data.currencyRate !== 'undefined') license.currencyConverter = Boolean(data.currencyRate !== 1);
    if (data.renewalReminder !== undefined) license.renewalReminder = Boolean(data.renewalReminder);
    if (data.multiLocation !== undefined) license.multiLocationUse = Boolean(data.multiLocation);
    if (data.loginInfo !== undefined) license.loginSharingInfo = data.loginInfo || null;
    if (data.socialMediaLinks) {
      license.socialMediaLinks = Array.isArray(data.socialMediaLinks) 
        ? data.socialMediaLinks 
        : [data.socialMediaLinks];
    }

    // Handle user assignments - FIXED VERSION USING QUERYBUILDER
    if (Array.isArray(data.assignedUserIds)) {
      console.log('Processing user assignments for update with QueryBuilder:', data.assignedUserIds);
      
      if (data.assignedUserIds.length > 0) {
        // Ensure all IDs are normalized strings
        const userIds = data.assignedUserIds.map(id => String(id).trim());
        console.log('User IDs normalized for update:', userIds);
        
        try {
          // Use QueryBuilder with explicit parameter binding for reliable UUID matching
          const userRepository = AppDataSource.getRepository(User);
          
          // Build a query with proper parameter binding for each UUID
          const queryBuilder = userRepository.createQueryBuilder('user');
          
          // Add WHERE conditions for each ID with proper parameter binding
          const whereConditions = userIds.map((id, index) => {
            return `user.id = :id${index}`;
          }).join(' OR ');
          
          // Add parameters for each ID
          const parameters: Record<string, string> = {};
          userIds.forEach((id, index) => {
            parameters[`id${index}`] = id;
          });
          
          const users = await queryBuilder
            .where(whereConditions, parameters)
            .getMany();
          
          console.log(`QueryBuilder found ${users.length} users out of ${userIds.length} requested for update`);
          console.log('Users found for update:', users.map(u => ({ id: u.id, name: u.name })));
          
          // Assign the users to the software license
          license.assignedUsers = users;
        } catch (error) {
          console.error('Error finding users with QueryBuilder for update:', error);
          // Keep existing assignments on error
          console.warn('Keeping existing user assignments due to error');
        }
      } else {
        // Clear all assignments if an empty array is provided
        console.log('Clearing user assignments (empty array provided)');
        license.assignedUsers = [];
      }
    } else {
      console.log('No user assignments data provided for update, keeping existing assignments');
    }

    // Handle asset links similarly
    if (Array.isArray(data.linkedAssetIds)) {
      console.log('Processing asset links:', data.linkedAssetIds);
      
      // Ensure all IDs are strings
      const assetIds = data.linkedAssetIds.map(id => String(id));
      console.log('Asset IDs normalized to strings:', assetIds);
      
      if (assetIds.length > 0) {
        // Fetch all assets with IDs in the linkedAssetIds array
        const assetRepository = AppDataSource.getRepository(Asset);
        const assets = await assetRepository.findBy({
          id: In(assetIds)
        });
        
        console.log(`Found ${assets.length} assets out of ${assetIds.length} requested IDs`);
        console.log('Assets found:', assets.map(a => ({ id: a.id, tag: a.assetTag })));
        
        // Completely replace the linked assets collection
        license.linkedAssets = assets;
      } else {
        // Clear the asset links if an empty array is provided
        console.log('Clearing asset links (empty array provided)');
        license.linkedAssets = [];
      }
    }

    if (req.file) {
      // Delete old invoice file if it exists
      if (license.invoiceUrl && fs.existsSync(license.invoiceUrl)) {
        fs.unlinkSync(license.invoiceUrl);
      }
      license.invoiceUrl = req.file.path;
    }

    console.log('Saving updated license with assigned users:', 
      license.assignedUsers?.map(u => ({ id: u.id, name: u.name })) || []);
      
    // Create a transaction to ensure atomicity
    await AppDataSource.transaction(async transactionalEntityManager => {
      // First, save the license
      const updatedLicense = await transactionalEntityManager.save(SoftwareLicense, license);
      console.log('License saved with ID:', updatedLicense.id);
      
      // If we have assigned users, explicitly save the relationships
      if (Array.isArray(license.assignedUsers) && license.assignedUsers.length > 0) {
        console.log(`Explicitly persisting ${license.assignedUsers.length} user relationships for update`);
        
        // Clear any existing relationships
        await transactionalEntityManager.query(
          `DELETE FROM _assigned_users WHERE softwareLicenseId = ?`, 
          [updatedLicense.id]
        );
        
        // Explicitly insert each relationship
        for (const user of license.assignedUsers) {
          await transactionalEntityManager.query(
            `INSERT INTO _assigned_users(softwareLicenseId, userId) VALUES (?, ?)`,
            [updatedLicense.id, user.id]
          );
          console.log(`Added relationship: License ${updatedLicense.id} -> User ${user.id}`);
        }
      } else if (Array.isArray(license.assignedUsers) && license.assignedUsers.length === 0) {
        // If empty array was provided, clear all relationships
        console.log('Clearing all user assignments for license:', updatedLicense.id);
        await transactionalEntityManager.query(
          `DELETE FROM _assigned_users WHERE softwareLicenseId = ?`, 
          [updatedLicense.id]
        );
      }
      
      // Return the updated license
      return updatedLicense;
    })
    .then(async (updatedLicense) => {
      // Continue with fetching the complete license...
      
      // Fetch the complete saved license with more detailed relational data
      try {
        // Get the updated license data with detailed user information
        const savedLicense = await AppDataSource
          .createQueryBuilder(SoftwareLicense, 'license')
          .leftJoinAndSelect('license.vendor', 'vendor')
          .leftJoinAndSelect('license.assignedUsers', 'user')
          .leftJoinAndSelect('license.linkedAssets', 'asset')
          .where('license.id = :id', { id: updatedLicense.id })
          .select([
            'license',
            'vendor.id',
            'vendor.vendorName',
            'vendor.companyName',
            'user.id',
            'user.name',
            'user.email',
            'user.department',
            'asset.id',
            'asset.assetTag',
            'asset.assetType',
            'asset.manufacturer',
            'asset.model'
          ])
          .getOne();
        
        if (!savedLicense) {
          console.log('Warning: Could not fetch the saved license after update');
          res.json(updatedLicense);
          return;
        }
        
        console.log('Final saved license data:', {
          id: savedLicense.id,
          name: savedLicense.name,
          assignedUsers: savedLicense.assignedUsers?.map(u => ({ id: u.id, name: u.name, email: u.email })) || [],
          assignedUserCount: savedLicense.assignedUsers?.length || 0,
          linkedAssetCount: savedLicense.linkedAssets?.length || 0
        });
        
        res.json(savedLicense);
      } catch (error) {
        console.error('Error fetching complete license data after update:', error);
        // Fall back to returning just the updated license if we can't get the complete data
        res.json(updatedLicense);
      }
    });
  } catch (error) {
    console.error('Error updating software license:', error);
    res.status(500).json({ message: 'Failed to update software license' });
  }
});

// Delete software license
router.delete('/:id', async (req, res) => {
  try {
    const license = await softwareLicenseRepository.findOne({
      where: { id: req.params.id }
    });
    
    if (!license) {
      return res.status(404).json({ message: 'Software license not found' });
    }

    // Delete invoice file if it exists
    if (license.invoiceUrl && fs.existsSync(license.invoiceUrl)) {
      fs.unlinkSync(license.invoiceUrl);
    }

    await softwareLicenseRepository.remove(license);
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting software license:', error);
    res.status(500).json({ message: 'Failed to delete software license' });
  }
});

// Get software licenses by vendor
router.get('/vendor/:vendorId', async (req, res) => {
  try {
    // Use query builder for consistent approach and more control over data
    const licenses = await AppDataSource
      .createQueryBuilder(SoftwareLicense, 'license')
      .leftJoinAndSelect('license.vendor', 'vendor')
      .leftJoinAndSelect('license.assignedUsers', 'user')
      .leftJoinAndSelect('license.linkedAssets', 'asset')
      .where('vendor.id = :vendorId', { vendorId: req.params.vendorId })
      .select([
        'license',
        'vendor.id',
        'vendor.vendorName',
        'vendor.companyName',
        'user.id',
        'user.name',
        'user.email',
        'user.department',
        'asset.id',
        'asset.assetTag',
        'asset.assetType',
        'asset.manufacturer',
        'asset.model'
      ])
      .getMany();
    
    console.log(`Found ${licenses.length} software licenses for vendor ${req.params.vendorId}`);
    
    res.json(licenses);
  } catch (error) {
    console.error('Error fetching vendor software licenses:', error);
    res.status(500).json({ message: 'Failed to fetch vendor software licenses' });
  }
});

export default router; 