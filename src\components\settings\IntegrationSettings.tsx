import React, { useState, useEffect } from 'react';
import { Globe, Save, AlertCircle, Link, CheckCircle, XCircle, RefreshCw } from 'lucide-react';

interface Setting {
  id: string;
  category: string;
  key: string;
  value: string;
  description: string;
  lastUpdated?: string;
  updatedBy?: string;
}

interface IntegrationSettingsProps {
  settings: Setting[];
  onSettingChange: (id: string, value: string) => void;
  onSave: () => void;
  isSaving?: boolean;
  saveSuccess?: boolean;
}

const IntegrationSettings: React.FC<IntegrationSettingsProps> = ({
  settings,
  onSettingChange,
  onSave,
  isSaving = false,
  saveSuccess = false
}) => {
  const [localSettings, setLocalSettings] = useState<Setting[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [testingConnections, setTestingConnections] = useState<{[key: string]: boolean}>({});
  const [connectionResults, setConnectionResults] = useState<{[key: string]: {success: boolean, message: string}}>({});

  useEffect(() => {
    const integrationSettings = settings.filter(setting => setting.category === 'integrations');
    setLocalSettings(integrationSettings);
  }, [settings]);

  const handleChange = (id: string, value: string) => {
    setLocalSettings(prev => prev.map(setting => 
      setting.id === id ? { ...setting, value } : setting
    ));
    setHasChanges(true);
    onSettingChange(id, value);
  };

  const handleSave = () => {
    onSave();
    setHasChanges(false);
  };

  const handleTestConnection = async (integrationType: string) => {
    setTestingConnections(prev => ({ ...prev, [integrationType]: true }));
    setConnectionResults(prev => ({ ...prev, [integrationType]: { success: false, message: '' } }));
    
    // Simulate connection test
    setTimeout(() => {
      const isEnabled = localSettings.find(s => s.key === `enable${integrationType}Integration`)?.value === 'true';
      const hasUrl = localSettings.find(s => s.key.includes(integrationType.toLowerCase()) && s.key.includes('Url'))?.value;
      
      if (isEnabled && hasUrl && hasUrl !== '') {
        setConnectionResults(prev => ({
          ...prev,
          [integrationType]: {
            success: true,
            message: `${integrationType} connection successful! Integration is working properly.`
          }
        }));
      } else {
        setConnectionResults(prev => ({
          ...prev,
          [integrationType]: {
            success: false,
            message: `${integrationType} connection failed. Please check your configuration and credentials.`
          }
        }));
      }
      setTestingConnections(prev => ({ ...prev, [integrationType]: false }));
    }, 2000);
  };

  const isPasswordField = (key: string) => {
    return key.toLowerCase().includes('password') || key.toLowerCase().includes('secret') || key.toLowerCase().includes('token');
  };

  const isUrlField = (key: string) => {
    return key.toLowerCase().includes('url') || key.toLowerCase().includes('webhook');
  };

  const getIntegrationStatus = (integrationType: string) => {
    const isEnabled = localSettings.find(s => s.key === `enable${integrationType}Integration`)?.value === 'true';
    const hasUrl = localSettings.find(s => s.key.includes(integrationType.toLowerCase()) && s.key.includes('Url'))?.value;
    
    if (isEnabled && hasUrl && hasUrl !== '') {
      return { status: 'connected', color: 'text-green-600', icon: CheckCircle };
    } else if (isEnabled) {
      return { status: 'configured', color: 'text-yellow-600', icon: RefreshCw };
    } else {
      return { status: 'disconnected', color: 'text-gray-400', icon: XCircle };
    }
  };

  const integrationTypes = [
    { name: 'ActiveDirectory', label: 'Active Directory', description: 'LDAP authentication and user management' },
    { name: 'Slack', label: 'Slack', description: 'Team communication and notifications' },
    { name: 'Teams', label: 'Microsoft Teams', description: 'Collaboration and meeting integration' }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Globe className="h-6 w-6 text-indigo-600" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Integration Settings</h2>
            <p className="text-sm text-gray-600">Configure third-party integrations and external services</p>
          </div>
        </div>
        {hasChanges && (
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSaving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        )}
      </div>

      {saveSuccess && (
        <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700">
          <AlertCircle className="h-4 w-4" />
          Integration settings saved successfully!
        </div>
      )}

      {/* Integration Status Overview */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center gap-3 mb-4">
          <Link className="h-5 w-5 text-indigo-600" />
          <h3 className="text-lg font-medium text-gray-900">Integration Status</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {integrationTypes.map((integration) => {
            const status = getIntegrationStatus(integration.name);
            const StatusIcon = status.icon;
            return (
              <div key={integration.name} className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <StatusIcon className={`h-4 w-4 ${status.color}`} />
                    <div className="text-sm font-medium text-gray-900">{integration.label}</div>
                  </div>
                  <button
                    onClick={() => handleTestConnection(integration.name)}
                    disabled={testingConnections[integration.name]}
                    className="text-xs px-2 py-1 bg-indigo-100 text-indigo-700 rounded hover:bg-indigo-200 disabled:opacity-50"
                  >
                    {testingConnections[integration.name] ? 'Testing...' : 'Test'}
                  </button>
                </div>
                <div className="text-xs text-gray-600 mb-2">{integration.description}</div>
                <div className={`text-xs font-medium ${status.color}`}>
                  {status.status === 'connected' ? 'Connected' : 
                   status.status === 'configured' ? 'Configured' : 'Disconnected'}
                </div>
                {connectionResults[integration.name] && (
                  <div className={`mt-2 text-xs p-2 rounded ${
                    connectionResults[integration.name].success 
                      ? 'bg-green-50 text-green-700 border border-green-200'
                      : 'bg-red-50 text-red-700 border border-red-200'
                  }`}>
                    {connectionResults[integration.name].message}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 divide-y divide-gray-200">
        {localSettings.map((setting) => (
          <div key={setting.id} className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-900 mb-1">
                  {setting.key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </label>
                <p className="text-sm text-gray-600 mb-3">{setting.description}</p>
                
                {setting.key.includes('enable') || setting.key.includes('Enable') ? (
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={setting.id}
                      checked={setting.value === 'true'}
                      onChange={(e) => handleChange(setting.id, e.target.checked ? 'true' : 'false')}
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    />
                    <label htmlFor={setting.id} className="ml-2 text-sm text-gray-700">
                      {setting.value === 'true' ? 'Enabled' : 'Disabled'}
                    </label>
                  </div>
                ) : isPasswordField(setting.key) ? (
                  <input
                    type="password"
                    value={setting.value}
                    onChange={(e) => handleChange(setting.id, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    placeholder={`Enter ${setting.key}`}
                  />
                ) : isUrlField(setting.key) ? (
                  <div className="flex items-center gap-2">
                    <input
                      type="url"
                      value={setting.value}
                      onChange={(e) => handleChange(setting.id, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                      placeholder={`Enter ${setting.key}`}
                    />
                    {setting.value && (
                      <a
                        href={setting.value}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 text-indigo-600 hover:text-indigo-800"
                        title="Open URL"
                      >
                        <Link className="h-4 w-4" />
                      </a>
                    )}
                  </div>
                ) : (
                  <input
                    type="text"
                    value={setting.value}
                    onChange={(e) => handleChange(setting.id, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    placeholder={`Enter ${setting.key}`}
                  />
                )}
              </div>
            </div>
            
            {setting.lastUpdated && (
              <div className="mt-3 text-xs text-gray-500">
                Last updated: {new Date(setting.lastUpdated).toLocaleString()} by {setting.updatedBy}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default IntegrationSettings;