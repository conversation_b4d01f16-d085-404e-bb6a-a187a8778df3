import { Router, Request, Response } from 'express';
import { PrinterMaintenanceController } from '../controllers/PrinterMaintenanceController';
import { auth, authorize } from '../middleware/auth';
import { UserRole } from '../../types/common';
import multer from 'multer';
import { UploadedFile } from 'express-fileupload';
import { AppDataSource } from '../../config/database';
import { PrinterMaintenance } from '../../entities/PrinterMaintenance';
import { User } from '../../entities/User';
const path = require('path');
const fs = require('fs');

const router = Router();
const printerMaintenanceController = new PrinterMaintenanceController();

// Configure multer with DISK storage instead of memory storage for better handling of multipart/form-data
const upload = multer({
  storage: multer.diskStorage({
    destination: function (_req, _file, cb) {
      cb(null, './uploads/invoices'); // Save files to the uploads/invoices directory
    },
    filename: function (_req, file, cb) {
      // Create a unique filename
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      cb(null, file.fieldname + '-' + uniqueSuffix + '-' + file.originalname.replace(/\s+/g, '_'));
    }
  }),
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Helper function to ensure the uploads directory exists
const uploadsDir = './uploads';

// Create uploads directory if it doesn't exist
if (!fs.existsSync(uploadsDir)) {
  console.log('Creating uploads directory');
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Log all incoming requests to this router for debugging
router.use((req, res, next) => {
  console.log('--------------------------------------');
  console.log('Printer Maintenance API Request:');
  console.log('- Method:', req.method);
  console.log('- URL:', req.originalUrl);
  console.log('- Content-Type:', req.headers['content-type']);
  
  // For POST/PUT requests, log more details
  if (req.method === 'POST' || req.method === 'PUT') {
    console.log('- Body Keys:', Object.keys(req.body || {}));
    
    // Log specific important fields for debugging
    const fieldsToPrint = ['asset_id', 'printer_id', 'service_date', 'visit_date', 'vendor_id', 'vendor_name', 'vendor'];
    const debugValues: Record<string, any> = {};
    
    fieldsToPrint.forEach(field => {
      if (req.body && field in req.body) {
        debugValues[field] = req.body[field];
      }
    });
    
    console.log('- Important Fields:', debugValues);
    
    // Log all received fields for thorough debugging
    console.log('- ALL RECEIVED FIELDS:');
    if (req.body) {
      for (const [key, value] of Object.entries(req.body)) {
        console.log(`   ${key} => ${typeof value === 'object' ? JSON.stringify(value) : value}`);
      }
    }
  }
  
  if (req.file) {
    console.log('- File:', {
      fieldname: req.file.fieldname,
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size
    });
  }
  
  console.log('--------------------------------------');
  next();
});

// Create a new printer maintenance record with robust error handling
router.post(
  '/',
  auth,
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF]),
  (req, res, next) => {
    console.log('🔍 INITIAL REQUEST: Content-Type:', req.headers['content-type']);
    
    // Attempt to process the multipart form with file - use a simpler approach
    upload.single('file')(req, res, (err) => {
      if (err) {
        console.error('❌ MULTER ERROR:', err);
        return res.status(500).json({
          error: 'File upload error',
          message: err.message,
          stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
        });
      }
      
      console.log('✅ Form processed successfully');
      console.log('📦 File received:', req.file ? 
        `${req.file.originalname} (${req.file.size} bytes)` : 'No file uploaded');
      console.log('📋 Form data keys:', Object.keys(req.body));
      
      // Log all form fields
      for (const key in req.body) {
        console.log(`📝 ${key} = ${req.body[key]}`);
      }
      
      // Check required fields
      const { asset_id, service_date, vendor } = req.body;
      if (!asset_id || !service_date || !vendor) {
        console.log("❌ Missing required fields!");
        const missingFields = [];
        if (!asset_id) missingFields.push('asset_id');
        if (!service_date) missingFields.push('service_date');
        if (!vendor) missingFields.push('vendor');
        
        return res.status(400).json({
          message: 'Missing required fields',
          missing: missingFields,
          received: Object.keys(req.body)
        });
      }
      
      console.log("✅ All required fields present");
      next();
    });
  },
  printerMaintenanceController.recordMaintenance.bind(printerMaintenanceController)
);

// Upload invoice route with similar approach
router.post(
  '/:id/upload-invoice',
  auth,
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF]),
  (req, res, next) => {
    console.log('🔍 INVOICE UPLOAD: Content-Type:', req.headers['content-type']);
    
    // Process the multipart form with file
    upload.single('file')(req, res, (err) => {
      if (err) {
        console.error('❌ MULTER ERROR:', err);
        return res.status(500).json({
          error: 'File upload error',
          message: err.message,
          stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
        });
      }
      
      console.log('✅ Invoice form processed');
      console.log('📦 File received:', req.file ? 
        `${req.file.originalname} (${req.file.size} bytes)` : 'No file uploaded');
      
      // Check if file was uploaded
      if (!req.file) {
        return res.status(400).json({
          error: 'Missing file',
          message: 'An invoice file is required for this operation'
        });
      }

      // Store ONLY the filename, not the path
      const filename = req.file.filename;
      const id = req.params.id;
      
      // Update the maintenance record with just the filename
      const maintenanceRepo = AppDataSource.getRepository(PrinterMaintenance);
      
      maintenanceRepo.findOne({ where: { id } })
        .then((record: PrinterMaintenance | null) => {
          if (record) {
            record.invoiceFilePath = filename; // Store only filename
            return maintenanceRepo.save(record);
          }
          throw new Error('Record not found');
        })
        .then(() => {
          // Return success response with just the filename
          return res.status(200).json({
            success: true,
            message: 'File uploaded successfully',
            file: {
              filename: filename,
              originalname: req.file?.originalname,
              size: req.file?.size
            }
          });
        })
        .catch((error) => {
          console.error('❌ Error updating record:', error);
          return res.status(500).json({
            error: 'Database error',
            message: 'Failed to update record with file information'
          });
        });
    });
  }
);

// Approve a maintenance invoice
router.patch(
  '/:id/approve',
  auth,
  authorize([UserRole.IT_ADMIN]),
  printerMaintenanceController.approveInvoice.bind(printerMaintenanceController)
);

// Get maintenance records pending approval
router.get(
  '/pending-approvals',
  auth,
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF]),
  printerMaintenanceController.getPendingApprovals.bind(printerMaintenanceController)
);

// Get maintenance history for an asset
router.get(
  '/asset/:assetId',
  auth,
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF, UserRole.IT_SUPPORT]),
  printerMaintenanceController.getMaintenanceHistory.bind(printerMaintenanceController)
);

// Generate finance report
router.get(
  '/finance-report',
  auth,
  authorize([UserRole.IT_ADMIN]),
  printerMaintenanceController.generateFinanceReport.bind(printerMaintenanceController)
);

// Mark records as submitted to finance
router.post(
  '/submit-to-finance',
  auth,
  authorize([UserRole.IT_ADMIN]),
  printerMaintenanceController.markAsSubmittedToFinance.bind(printerMaintenanceController)
);

// Get all maintenance records (with filtering and pagination)
router.get(
  '/',
  auth,
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF, UserRole.IT_SUPPORT]),
  printerMaintenanceController.getMaintenanceRecords.bind(printerMaintenanceController)
);

// Get a single maintenance record by ID
router.get(
  '/:id',
  auth,
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF, UserRole.IT_SUPPORT]),
  printerMaintenanceController.getMaintenanceRecord.bind(printerMaintenanceController)
);

// Update a maintenance record
router.put(
  '/:id',
  auth,
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF]),
  printerMaintenanceController.updateMaintenanceRecord.bind(printerMaintenanceController)
);

// Delete a maintenance record
router.delete(
  '/:id',
  auth,
  authorize([UserRole.IT_ADMIN]),
  printerMaintenanceController.deleteMaintenanceRecord.bind(printerMaintenanceController)
);

// Add export route
router.post(
  '/export',
  auth,
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF, UserRole.IT_SUPPORT]),
  printerMaintenanceController.exportMaintenance.bind(printerMaintenanceController)
);

// Add a simplified file upload endpoint
router.post(
  '/upload/file',
  auth,
  (req, res) => {
    console.log('🔄 Simple file upload route called');
    console.log('📋 Query params:', req.query);
    
    // Simple uploads handler with disk storage
    const simpleUpload = multer({
      storage: multer.diskStorage({
        destination: (_req, _file, cb) => {
          // Store in uploads directory
          cb(null, './uploads');
        },
        filename: (_req, file, cb) => {
          // Generate a unique filename
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
          const safeName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
          cb(null, `${uniqueSuffix}-${safeName}`);
        }
      }),
      limits: { fileSize: 10 * 1024 * 1024 } // 10MB limit
    }).single('file');
    
    // Process the upload with robust error handling
    simpleUpload(req, res, (err) => {
      if (err) {
        console.error('❌ Simple upload error:', err);
        return res.status(500).json({
          success: false,
          message: 'File upload failed',
          error: err.message
        });
      }
      
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'No file was provided'
        });
      }
      
      const recordId = req.query.recordId || 'unknown';
      console.log('✅ File uploaded successfully:', req.file.originalname);
      console.log('📊 File details:', {
        filename: req.file.filename,
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        path: req.file.path
      });
      
      // Update the record in the database with the file path
      if (recordId && recordId !== 'unknown') {
        const maintenanceRepo = AppDataSource.getRepository(PrinterMaintenance);
        
        // Find the maintenance record and update it with the file path
        maintenanceRepo.findOne({ where: { id: recordId.toString() } })
          .then((record: PrinterMaintenance | null) => {
            if (record) {
              record.invoiceFilePath = req.file?.path || '';
              // Store the original filename in the notes field if it's available
              if (req.file && req.file.originalname && !record.notes) {
                record.notes = `Original filename: ${req.file.originalname}`;
              } else if (req.file && req.file.originalname) {
                record.notes = `${record.notes || ''}\nOriginal filename: ${req.file.originalname}`;
              }
              // Mark as update to prevent new ID generation in BeforeInsert hook
              record.markAsUpdate();
              return maintenanceRepo.save(record);
            }
            console.error('⚠️ Record not found for ID:', recordId);
            return null;
          })
          .then(() => {
            console.log('✅ Database updated with file path');
          })
          .catch((dbError: Error) => {
            console.error('❌ Database update error:', dbError);
            // We still return success since the file was uploaded,
            // even if the DB update failed
          });
      }
      
      // Return success response
      return res.status(200).json({
        success: true,
        message: 'File uploaded successfully',
        file: {
          filename: req.file.filename,
          originalname: req.file.originalname,
          size: req.file.size,
          path: req.file.path
        }
      });
    });
  }
);

// Ultra-basic file upload endpoint - no auth, no complex handling
router.post('/test-upload', (req, res) => {
  console.log('================ ULTRA BASIC FILE UPLOAD =================');
  console.log('Content-Type:', req.headers['content-type']);
  console.log('All Headers:', JSON.stringify(req.headers, null, 2));
  console.log('Files received:', req.files ? Object.keys(req.files) : 'none');
  
  // Create uploads/invoices directory if it doesn't exist
  const uploadDir = path.resolve('./uploads/invoices');
  if (!fs.existsSync(uploadDir)) {
    console.log(`Creating directory: ${uploadDir}`);
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  try {
    // Check if files were uploaded
    if (!req.files || Object.keys(req.files).length === 0) {
      console.error('⚠️ NO FILE RECEIVED');
      console.log('Received body:', req.body);
      console.log('Received query:', req.query);
      console.log('Received files:', req.files);
      return res.status(400).json({
        success: false,
        error: 'Missing file',
        message: 'No file was uploaded'
      });
    }

    // Type assertion to handle express-fileupload's types
    const fileUpload = req.files as { [fieldname: string]: any };
    
    // Log what we received
    console.log('Files structure:', {
      fileKeys: Object.keys(fileUpload),
      hasFileField: 'file' in fileUpload,
      fileFieldType: fileUpload.file ? (Array.isArray(fileUpload.file) ? 'array' : 'object') : 'none'
    });
    
    if (!fileUpload.file) {
      return res.status(400).json({
        success: false,
        error: 'Missing file',
        message: 'No file field found. Please use "file" as the form field name'
      });
    }

    const uploadedFile = fileUpload.file;
    if (Array.isArray(uploadedFile)) {
      console.log('File is an array, using first file');
      if (uploadedFile.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'Missing file',
          message: 'No file was uploaded (empty array)'
        });
      }
      
      // Process the first file if it's an array
      const file = uploadedFile[0];
      const timestamp = Date.now();
      const safeName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
      const filename = `upload_${timestamp}_${safeName}`;
      const filePath = path.join(uploadDir, filename);

      // Move the file to the destination folder
      file.mv(filePath, function(error: Error | null) {
        if (error) {
          console.error('❌ FILE MOVE ERROR:', error);
          return res.status(500).json({
            success: false,
            error: 'File upload error',
            message: error.message
          });
        }

        console.log('✅ BASIC UPLOAD SUCCESS!');
        console.log('File details:', {
          originalname: file.name,
          mimetype: file.mimetype,
          size: file.size,
          path: filePath
        });

        // Return success with file details
        return res.status(200).json({
          success: true,
          message: 'File uploaded successfully',
          file: {
            fieldname: 'file',
            originalname: file.name,
            mimetype: file.mimetype,
            size: file.size,
            path: filePath,
            filename: filename
          }
        });
      });
    } else {
      // Handle single file object
      const timestamp = Date.now();
      const safeName = uploadedFile.name.replace(/[^a-zA-Z0-9.-]/g, '_');
      const filename = `upload_${timestamp}_${safeName}`;
      const filePath = path.join(uploadDir, filename);

      // Move the file to the destination folder
      uploadedFile.mv(filePath, function(error: Error | null) {
        if (error) {
          console.error('❌ FILE MOVE ERROR:', error);
          return res.status(500).json({
            success: false, 
            error: 'File upload error',
            message: error.message
          });
        }

        console.log('✅ BASIC UPLOAD SUCCESS!');
        console.log('File details:', {
          originalname: uploadedFile.name,
          mimetype: uploadedFile.mimetype,
          size: uploadedFile.size,
          path: filePath
        });

        // Return success with file details
        return res.status(200).json({
          success: true,
          message: 'File uploaded successfully',
          file: {
            fieldname: 'file',
            originalname: uploadedFile.name,
            mimetype: uploadedFile.mimetype,
            size: uploadedFile.size,
            path: filePath,
            filename: filename
          }
        });
      });
    }
  } catch (error: any) {
    console.error('❌ UPLOAD ERROR CAUGHT:', error);
    return res.status(500).json({
      success: false,
      error: 'File upload error',
      message: error.message || 'Unknown error',
      details: {
        name: error.name,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }
    });
  }
});

// Associate an uploaded file with a maintenance record
router.post(
  '/:id/associate-file',
  auth,
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF]),
  async (req, res) => {
    try {
      const { id } = req.params;
      const { filePath, fileName } = req.body;
      
      console.log(`🔗 Associating file with record ${id}:`, { filePath, fileName });
      
      // Try a direct database query to confirm if the record exists
      const directQuery = await AppDataSource.query(
        `SELECT * FROM printer_maintenance WHERE id = ?`, 
        [id]
      );
      
      console.log('Direct SQL query result:', directQuery.length ? 'Record found' : 'No record found', 
                 directQuery.length ? directQuery[0].id : 'N/A');
      
      // If direct query found the record, use that
      if (directQuery.length > 0) {
        const record = directQuery[0];
        
        console.log('Found record via direct query, updating file information:', { 
          recordId: record.id,
          currentNotes: record.notes,
          currentFilePath: record.invoiceFilePath
        });
        
        // Update the record with file information
        record.invoiceFilePath = filePath;
        
        // Store the original filename in the notes field if it's available
        if (fileName && !record.notes) {
          record.notes = `Original filename: ${fileName}`;
        } else if (fileName) {
          record.notes = `${record.notes || ''}\nOriginal filename: ${fileName}`;
        }
        
        // Use a direct update query to ensure it works
        await AppDataSource.query(
          `UPDATE printer_maintenance SET invoiceFilePath = ?, notes = ? WHERE id = ?`,
          [filePath, record.notes, id]
        );
        
        console.log('✅ File associated with record successfully via direct query');
        
        return res.status(200).json({
          success: true,
          message: 'File associated with record successfully',
          record: record
        });
      }
      
      // Continue with the existing code as fallback...
      return res.status(404).json({
        error: 'Record not found',
        message: `Maintenance record with ID ${id} not found`
      });
    } catch (error: any) {
      console.error('❌ Error associating file with record:', error);
      return res.status(500).json({
        error: 'Server error',
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
    }
  }
);

// Download invoice file for a maintenance record
router.get(
  '/:id/download',
  async (req: Request, res: Response) => {
    try {
      // Set CORS headers for the download route
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      res.header('Access-Control-Expose-Headers', 'Content-Disposition, Content-Type, Content-Length');
      
      const { id } = req.params;
      const { token } = req.query; // Get token from query parameter if provided
      
      console.log(`Public download request for maintenance record ID: ${id}`);
      console.log('Request headers:', {
        ...req.headers,
        authorization: req.headers.authorization ? 'Bearer [redacted]' : 'None provided'
      });
      console.log('Query parameters:', {
        ...req.query,
        token: token ? '[TOKEN_PROVIDED]' : 'None'
      });
      
      // If token was provided in query, add it to authorization header for internal systems
      if (token && typeof token === 'string') {
        req.headers.authorization = `Bearer ${token}`;
        console.log('Added token from query parameter to authorization header');
      }
      
      if (!id) {
        return res.status(400).json({ message: 'Maintenance record ID is required' });
      }
      
      // Get the maintenance record from database
      const maintenanceRepo = AppDataSource.getRepository(PrinterMaintenance);
      
      // Try direct SQL query to ensure we can find the record
      let record;
      try {
        const directQuery = await AppDataSource.query(
          `SELECT * FROM printer_maintenance WHERE id = ?`, 
          [id]
        );
        
        if (directQuery.length > 0) {
          record = directQuery[0];
          console.log(`Found record via direct SQL query: ${record.id}`);
        } else {
          // Fall back to repository if direct query fails
          record = await maintenanceRepo.findOne({ where: { id } });
          console.log(`Found record via repository: ${record?.id || 'Not found'}`);
        }
      } catch (dbError) {
        console.error('Database query error:', dbError);
        record = await maintenanceRepo.findOne({ where: { id } });
      }
      
      if (!record) {
        console.error(`Maintenance record not found for ID: ${id}`);
        return res.status(404).json({ message: 'Maintenance record not found' });
      }
      
      console.log('Maintenance record found:', {
        id: record.id,
        invoiceFilePath: record.invoiceFilePath
      });
      
      // Check if invoice file exists
      if (!record.invoiceFilePath) {
        console.error(`No invoice file path for maintenance record ID: ${id}`);
        return res.status(404).json({ message: 'No invoice file found for this record' });
      }
      
      // Get the filename and sanitize it
      const fileName = path.basename(record.invoiceFilePath);
      console.log('Sanitized filename:', fileName);
      
      // Try all possible file locations
      const possiblePaths = [
        // Primary location
        path.join(process.cwd(), 'uploads', 'invoices', fileName),
        
        // Alternative locations
        path.join(process.cwd(), 'uploads', fileName),
        path.join(__dirname, '..', '..', 'uploads', 'invoices', fileName),
        path.join(__dirname, '..', '..', 'uploads', fileName),
        path.join(process.cwd(), 'public', 'uploads', 'invoices', fileName),
        path.join(process.cwd(), 'public', 'uploads', fileName),
        
        // Original path if it's absolute
        record.invoiceFilePath.startsWith('/') || record.invoiceFilePath.includes(':') 
          ? record.invoiceFilePath 
          : null,
          
        // Stored path directly
        record.invoiceFilePath
      ].filter(Boolean);
      
      console.log('Checking all possible file paths:', possiblePaths);
      
      // Find the first existing file path
      let foundPath = null;
      for (const testPath of possiblePaths) {
        try {
          if (fs.existsSync(testPath)) {
            foundPath = testPath;
            console.log(`✅ Found file at: ${testPath}`);
            break;
          } else {
            console.log(`❌ File not found at: ${testPath}`);
          }
        } catch (fsError) {
          console.error(`Error checking path ${testPath}:`, fsError);
        }
      }
      
      if (!foundPath) {
        console.error('❌ File not found in any location');
        return res.status(404).json({ 
          message: 'File not found on server',
          details: {
            checkedPaths: possiblePaths
          }
        });
      }
      
      // File exists, prepare for download
      try {
        // Get file stats
        const stats = fs.statSync(foundPath);
        console.log('File stats:', {
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime
        });
        
        // Determine content type based on file extension
        const ext = path.extname(fileName).toLowerCase();
        let contentType = 'application/octet-stream'; // Default binary
        
        if (ext === '.pdf') contentType = 'application/pdf';
        else if (ext === '.jpg' || ext === '.jpeg') contentType = 'image/jpeg';
        else if (ext === '.png') contentType = 'image/png';
        else if (ext === '.txt') contentType = 'text/plain';
        
        // Set download headers
        res.setHeader('Content-Type', contentType);
        res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
        res.setHeader('Content-Length', stats.size);
        
        console.log('Sending file with headers:', {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${fileName}"`,
          'Content-Length': stats.size
        });
        
        // Use the basic res.sendFile for better compatibility
        return res.sendFile(foundPath, (err) => {
          if (err) {
            console.error('❌ Error sending file:', err);
            // Only respond if headers haven't been sent
            if (!res.headersSent) {
              res.status(500).json({
                message: 'Error sending file', 
                error: err.message
              });
            }
          } else {
            console.log('✅ File sent successfully');
          }
        });
      } catch (fileError: any) {
        console.error('Error preparing file for download:', fileError);
        return res.status(500).json({
          message: 'Error preparing file for download',
          error: fileError.message
        });
      }
    } catch (error: any) {
      console.error('❌ Unhandled error in download route:', error);
      return res.status(500).json({ 
        message: 'Error downloading invoice', 
        error: error.message 
      });
    }
  }
);

// Download route - handle auth via Authorization header
router.get('/download', async (req, res) => {
  try {
    console.log('Download request received:', {
      query: req.query,
      headers: {
        ...req.headers,
        authorization: req.headers.authorization ? 'Bearer [redacted]' : undefined
      }
    });

    const { file } = req.query;
    
    // Check if file parameter exists
    if (!file) {
      console.log('No file parameter provided');
      return res.status(400).json({ message: 'Filename is required' });
    }

    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('Missing or invalid authorization header');
      return res.status(401).json({ message: 'Authorization token required' });
    }

    // Clean the filename to prevent directory traversal
    const cleanFilename = path.basename(file as string);
    console.log('Cleaned filename:', cleanFilename);
    
    // Define possible file paths
    const possiblePaths = [
      path.join(process.cwd(), 'uploads', 'invoices', cleanFilename),
      path.join(process.cwd(), 'uploads', cleanFilename),
      // Add more paths if needed
    ];

    console.log('Checking file paths:', possiblePaths);

    // Try to find the file
    let filePath = null;
    for (const path of possiblePaths) {
      console.log('Checking path:', path);
      if (fs.existsSync(path)) {
        filePath = path;
        console.log('File found at:', path);
        break;
      }
    }

    if (!filePath) {
      console.log('File not found in any of the possible locations');
      return res.status(404).json({ 
        message: 'File not found',
        details: { 
          requestedFile: file,
          cleanedFilename: cleanFilename,
          checkedPaths: possiblePaths
        }
      });
    }

    // Get file stats for logging
    const stats = fs.statSync(filePath);
    console.log('File stats:', {
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime
    });

    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${cleanFilename}"`);
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Length', stats.size);

    console.log('Streaming file to client');

    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

    // Handle streaming errors
    fileStream.on('error', (error) => {
      console.error('Error streaming file:', error);
      if (!res.headersSent) {
        res.status(500).json({ 
          message: 'Error streaming file',
          error: error.message
        });
      }
    });

    // Log when streaming is complete
    fileStream.on('end', () => {
      console.log('File streaming completed');
    });

  } catch (error) {
    console.error('Download error:', error);
    if (!res.headersSent) {
      res.status(500).json({ 
        message: 'Error downloading file',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
});

export default router;