import React, { useState, useEffect } from 'react';
import { Cog, Save, AlertCircle, Globe, Clock, Building, Users, Settings } from 'lucide-react';

interface Setting {
  id: string;
  category: string;
  key: string;
  value: string;
  description: string;
  lastUpdated?: string;
  updatedBy?: string;
}

interface GeneralSettingsProps {
  settings: Setting[];
  onSettingChange: (id: string, value: string) => void;
  onSave: () => void;
  isSaving?: boolean;
  saveSuccess?: boolean;
}

const GeneralSettings: React.FC<GeneralSettingsProps> = ({
  settings,
  onSettingChange,
  onSave,
  isSaving = false,
  saveSuccess = false
}) => {
  const [localSettings, setLocalSettings] = useState<Setting[]>([]);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    console.log('GeneralSettings - All settings received:', settings);
    const generalSettings = settings.filter(setting => setting.category === 'general');
    console.log('GeneralSettings - Filtered general settings:', generalSettings);
    setLocalSettings(generalSettings);
  }, [settings]);

  const handleChange = (id: string, value: string) => {
    setLocalSettings(prev => prev.map(setting => 
      setting.id === id ? { ...setting, value } : setting
    ));
    setHasChanges(true);
    onSettingChange(id, value);
  };

  const handleSave = () => {
    onSave();
    setHasChanges(false);
  };

  const renderSettingInput = (setting: Setting) => {
    const { key, value, id } = setting;
    
    // Boolean settings
    if (key.includes('enable') || key.includes('Enable') || value === 'true' || value === 'false') {
      return (
        <div className="flex items-center">
          <input
            type="checkbox"
            id={id}
            checked={value === 'true'}
            onChange={(e) => handleChange(id, e.target.checked ? 'true' : 'false')}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor={id} className="ml-2 text-sm text-gray-700">
            {value === 'true' ? 'Enabled' : 'Disabled'}
          </label>
        </div>
      );
    }
    
    // Select dropdowns for specific settings
    if (key === 'defaultLanguage') {
      return (
        <select
          value={value}
          onChange={(e) => handleChange(id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="en">English</option>
          <option value="es">Spanish</option>
          <option value="fr">French</option>
          <option value="de">German</option>
          <option value="it">Italian</option>
          <option value="pt">Portuguese</option>
          <option value="zh">Chinese</option>
          <option value="ja">Japanese</option>
          <option value="ko">Korean</option>
          <option value="ar">Arabic</option>
        </select>
      );
    }
    
    if (key === 'dateFormat') {
      return (
        <select
          value={value}
          onChange={(e) => handleChange(id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="MM/DD/YYYY">MM/DD/YYYY (US Format)</option>
          <option value="DD/MM/YYYY">DD/MM/YYYY (European Format)</option>
          <option value="YYYY-MM-DD">YYYY-MM-DD (ISO Format)</option>
          <option value="DD-MM-YYYY">DD-MM-YYYY</option>
          <option value="MM-DD-YYYY">MM-DD-YYYY</option>
        </select>
      );
    }
    
    if (key === 'timeFormat') {
      return (
        <select
          value={value}
          onChange={(e) => handleChange(id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="12">12 Hour (AM/PM)</option>
          <option value="24">24 Hour</option>
        </select>
      );
    }
    
    if (key === 'currency') {
      return (
        <select
          value={value}
          onChange={(e) => handleChange(id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="USD">USD - US Dollar</option>
          <option value="EUR">EUR - Euro</option>
          <option value="GBP">GBP - British Pound</option>
          <option value="JPY">JPY - Japanese Yen</option>
          <option value="CAD">CAD - Canadian Dollar</option>
          <option value="AUD">AUD - Australian Dollar</option>
          <option value="CHF">CHF - Swiss Franc</option>
          <option value="CNY">CNY - Chinese Yuan</option>
          <option value="INR">INR - Indian Rupee</option>
        </select>
      );
    }
    
    if (key === 'timeZone') {
      return (
        <select
          value={value}
          onChange={(e) => handleChange(id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="UTC">UTC (Coordinated Universal Time)</option>
          <option value="America/New_York">Eastern Time (ET)</option>
          <option value="America/Chicago">Central Time (CT)</option>
          <option value="America/Denver">Mountain Time (MT)</option>
          <option value="America/Los_Angeles">Pacific Time (PT)</option>
          <option value="Europe/London">London (GMT)</option>
          <option value="Europe/Paris">Paris (CET)</option>
          <option value="Europe/Berlin">Berlin (CET)</option>
          <option value="Asia/Tokyo">Tokyo (JST)</option>
          <option value="Asia/Shanghai">Shanghai (CST)</option>
          <option value="Asia/Dubai">Dubai (GST)</option>
          <option value="Australia/Sydney">Sydney (AEDT)</option>
        </select>
      );
    }
    
    if (key === 'dateFormat') {
      return (
        <select
          value={value}
          onChange={(e) => handleChange(id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="MM/DD/YYYY">MM/DD/YYYY (US Format)</option>
          <option value="DD/MM/YYYY">DD/MM/YYYY (European Format)</option>
          <option value="YYYY-MM-DD">YYYY-MM-DD (ISO Format)</option>
          <option value="DD-MM-YYYY">DD-MM-YYYY</option>
          <option value="MM-DD-YYYY">MM-DD-YYYY</option>
        </select>
      );
    }
    
    if (key === 'timeFormat') {
      return (
        <select
          value={value}
          onChange={(e) => handleChange(id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="12">12-hour (AM/PM)</option>
          <option value="24">24-hour</option>
        </select>
      );
    }
    
    // Number inputs
    if (key.includes('timeout') || key.includes('limit') || key.includes('max') || key.includes('Min')) {
      return (
        <input
          type="number"
          value={value}
          onChange={(e) => handleChange(id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder={`Enter ${key}`}
          min="0"
        />
      );
    }
    
    // Textarea for longer text
    if (key.includes('description') || key.includes('message') || key.includes('terms')) {
      return (
        <textarea
          value={value}
          onChange={(e) => handleChange(id, e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder={`Enter ${key}`}
        />
      );
    }
    
    // Number inputs for specific settings
    if (key === 'maxFileUploadSize' || key === 'defaultItemsPerPage') {
      return (
        <input
          type="number"
          value={value}
          onChange={(e) => handleChange(id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder={`Enter ${key.replace(/([A-Z])/g, ' $1').toLowerCase()}`}
          min="1"
        />
      );
    }
    
    // Textarea for longer text
    if (key === 'companyAddress') {
      return (
        <textarea
          value={value}
          onChange={(e) => handleChange(id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder={`Enter ${key.replace(/([A-Z])/g, ' $1').toLowerCase()}`}
          rows={3}
        />
      );
    }
    
    // Read-only for system version
    if (key === 'systemVersion') {
      return (
        <input
          type="text"
          value={value}
          readOnly
          className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 cursor-not-allowed"
        />
      );
    }
    
    // Default text input
    return (
      <input
        type="text"
        value={value}
        onChange={(e) => handleChange(id, e.target.value)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        placeholder={`Enter ${key.replace(/([A-Z])/g, ' $1').toLowerCase()}`}
      />
    );
  };

  const getSettingIcon = (key: string) => {
    if (key.includes('language') || key.includes('Language')) return <Globe className="h-4 w-4" />;
    if (key.includes('time') || key.includes('Time') || key.includes('date') || key.includes('Date')) return <Clock className="h-4 w-4" />;
    if (key.includes('company') || key.includes('Company') || key.includes('organization')) return <Building className="h-4 w-4" />;
    if (key.includes('user') || key.includes('User') || key.includes('session')) return <Users className="h-4 w-4" />;
    return <Settings className="h-4 w-4" />;
  };

  const groupedSettings = {
    system: localSettings.filter(s => 
      s.key.includes('system') || s.key.includes('System') || s.key.includes('application') ||
      s.key === 'systemName' || s.key === 'systemVersion' || s.key === 'maintenanceMode' ||
      s.key === 'debugMode' || s.key === 'maxFileUploadSize' || s.key === 'defaultItemsPerPage'
    ),
    localization: localSettings.filter(s => 
      s.key.includes('language') || s.key.includes('time') || s.key.includes('date') || s.key.includes('format') ||
      s.key === 'defaultLanguage' || s.key === 'timeZone' || s.key === 'dateFormat' ||
      s.key === 'timeFormat' || s.key === 'currency'
    ),
    company: localSettings.filter(s => 
      s.key.includes('company') || s.key.includes('organization') || s.key.includes('contact') ||
      s.key === 'companyName' || s.key === 'companyAddress' || s.key === 'companyPhone' ||
      s.key === 'companyEmail'
    ),
    user: localSettings.filter(s => 
      s.key.includes('user') || s.key.includes('session') || s.key.includes('login')
    ),
    other: localSettings.filter(s => {
      const systemKeys = ['system', 'System', 'application', 'systemName', 'systemVersion', 'maintenanceMode', 'debugMode', 'maxFileUploadSize', 'defaultItemsPerPage'];
      const localizationKeys = ['language', 'time', 'date', 'format', 'defaultLanguage', 'timeZone', 'dateFormat', 'timeFormat', 'currency'];
      const companyKeys = ['company', 'organization', 'contact', 'companyName', 'companyAddress', 'companyPhone', 'companyEmail'];
      const userKeys = ['user', 'session', 'login'];
      
      return !systemKeys.some(key => s.key.includes(key) || s.key === key) &&
             !localizationKeys.some(key => s.key.includes(key) || s.key === key) &&
             !companyKeys.some(key => s.key.includes(key) || s.key === key) &&
             !userKeys.some(key => s.key.includes(key) || s.key === key);
    })
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Cog className="h-6 w-6 text-blue-600" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">General Settings</h2>
            <p className="text-sm text-gray-600">Configure basic system preferences and behavior</p>
          </div>
        </div>
        {hasChanges && (
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSaving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        )}
      </div>

      {saveSuccess && (
        <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700">
          <AlertCircle className="h-4 w-4" />
          Settings saved successfully!
        </div>
      )}

      <div className="space-y-6">
        {/* System Settings */}
        {groupedSettings.system.length > 0 && (
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-gray-600" />
                <h3 className="text-lg font-medium text-gray-900">System Configuration</h3>
              </div>
              <p className="text-sm text-gray-600 mt-1">Basic system and application settings</p>
            </div>
            <div className="divide-y divide-gray-200">
              {groupedSettings.system.map((setting) => (
                <div key={setting.id} className="p-6">
                  <div className="flex items-start gap-3">
                    <div className="text-gray-400 mt-1">
                      {getSettingIcon(setting.key)}
                    </div>
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-gray-900 mb-1">
                        {setting.key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </label>
                      <p className="text-sm text-gray-600 mb-3">{setting.description}</p>
                      {renderSettingInput(setting)}
                      {setting.lastUpdated && (
                        <div className="mt-3 text-xs text-gray-500">
                          Last updated: {new Date(setting.lastUpdated).toLocaleString()} by {setting.updatedBy}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Localization Settings */}
        {groupedSettings.localization.length > 0 && (
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <Globe className="h-5 w-5 text-gray-600" />
                <h3 className="text-lg font-medium text-gray-900">Localization & Formatting</h3>
              </div>
              <p className="text-sm text-gray-600 mt-1">Language, timezone, and date/time formatting preferences</p>
            </div>
            <div className="divide-y divide-gray-200">
              {groupedSettings.localization.map((setting) => (
                <div key={setting.id} className="p-6">
                  <div className="flex items-start gap-3">
                    <div className="text-gray-400 mt-1">
                      {getSettingIcon(setting.key)}
                    </div>
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-gray-900 mb-1">
                        {setting.key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </label>
                      <p className="text-sm text-gray-600 mb-3">{setting.description}</p>
                      {renderSettingInput(setting)}
                      {setting.lastUpdated && (
                        <div className="mt-3 text-xs text-gray-500">
                          Last updated: {new Date(setting.lastUpdated).toLocaleString()} by {setting.updatedBy}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Company Settings */}
        {groupedSettings.company.length > 0 && (
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <Building className="h-5 w-5 text-gray-600" />
                <h3 className="text-lg font-medium text-gray-900">Company Information</h3>
              </div>
              <p className="text-sm text-gray-600 mt-1">Organization details and contact information</p>
            </div>
            <div className="divide-y divide-gray-200">
              {groupedSettings.company.map((setting) => (
                <div key={setting.id} className="p-6">
                  <div className="flex items-start gap-3">
                    <div className="text-gray-400 mt-1">
                      {getSettingIcon(setting.key)}
                    </div>
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-gray-900 mb-1">
                        {setting.key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </label>
                      <p className="text-sm text-gray-600 mb-3">{setting.description}</p>
                      {renderSettingInput(setting)}
                      {setting.lastUpdated && (
                        <div className="mt-3 text-xs text-gray-500">
                          Last updated: {new Date(setting.lastUpdated).toLocaleString()} by {setting.updatedBy}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* User Settings */}
        {groupedSettings.user.length > 0 && (
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-gray-600" />
                <h3 className="text-lg font-medium text-gray-900">User & Session Management</h3>
              </div>
              <p className="text-sm text-gray-600 mt-1">User authentication and session preferences</p>
            </div>
            <div className="divide-y divide-gray-200">
              {groupedSettings.user.map((setting) => (
                <div key={setting.id} className="p-6">
                  <div className="flex items-start gap-3">
                    <div className="text-gray-400 mt-1">
                      {getSettingIcon(setting.key)}
                    </div>
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-gray-900 mb-1">
                        {setting.key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </label>
                      <p className="text-sm text-gray-600 mb-3">{setting.description}</p>
                      {renderSettingInput(setting)}
                      {setting.lastUpdated && (
                        <div className="mt-3 text-xs text-gray-500">
                          Last updated: {new Date(setting.lastUpdated).toLocaleString()} by {setting.updatedBy}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Other Settings */}
        {groupedSettings.other.length > 0 && (
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <Cog className="h-5 w-5 text-gray-600" />
                <h3 className="text-lg font-medium text-gray-900">Additional Settings</h3>
              </div>
              <p className="text-sm text-gray-600 mt-1">Other system configuration options</p>
            </div>
            <div className="divide-y divide-gray-200">
              {groupedSettings.other.map((setting) => (
                <div key={setting.id} className="p-6">
                  <div className="flex items-start gap-3">
                    <div className="text-gray-400 mt-1">
                      {getSettingIcon(setting.key)}
                    </div>
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-gray-900 mb-1">
                        {setting.key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </label>
                      <p className="text-sm text-gray-600 mb-3">{setting.description}</p>
                      {renderSettingInput(setting)}
                      {setting.lastUpdated && (
                        <div className="mt-3 text-xs text-gray-500">
                          Last updated: {new Date(setting.lastUpdated).toLocaleString()} by {setting.updatedBy}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {localSettings.length === 0 && (
          <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
            <Cog className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No General Settings Found</h3>
            <p className="text-gray-600">General settings will appear here once they are configured.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default GeneralSettings;