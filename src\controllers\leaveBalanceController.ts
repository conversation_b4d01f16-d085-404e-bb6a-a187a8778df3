import { Request, Response } from 'express';
import { AppDataSource } from '../config/database';
import { LeaveBalance } from '../entities/LeaveBalance';
import { LeaveAllocation } from '../entities/LeaveAllocation';
import { User } from '../entities/User';
import { Employee } from '../server/entities/Employee';
import { LeaveTypePolicy } from '../entities/LeaveTypePolicy';
import { LeavePolicyConfiguration } from '../entities/LeavePolicyConfiguration';
import { proratedLeaveCalculationService } from '../services/ProratedLeaveCalculationService';
import { leaveBalanceCalculationService } from '../services/LeaveBalanceCalculationService';

export class LeaveBalanceController {

  /**
   * Get all leave balances for a specific employee
   */
  async getByEmployeeId(req: Request, res: Response): Promise<void> {
    try {
      const { employeeId } = req.params;
      const year = req.query.year ? parseInt(req.query.year as string) : new Date().getFullYear();

      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
      
      const balances = await leaveBalanceRepository.find({
        where: {
          employeeId: parseInt(employeeId),
          year,
          isActive: true
        }
      });

      // Transform the data to match the frontend expectation
      const transformedBalances = balances.map(balance => ({
        employeeId: balance.employeeId,
        leaveType: balance.leaveType,
        total: balance.totalAllocated + balance.carriedForward,
        used: balance.used,
        pending: balance.pending,
        remaining: balance.remaining,
        carryForward: balance.carriedForward,
        expiryDate: balance.expiryDate
      }));

      res.json({
        success: true,
        data: transformedBalances
      });
    } catch (error: any) {
      console.error('Error fetching leave balances:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch leave balances',
        error: error.message
      });
    }
  }

  /**
   * Get leave balance for a specific employee and leave type
   */
  async getByEmployeeAndType(req: Request, res: Response): Promise<void> {
    try {
      const { employeeId, leaveType } = req.params;
      const year = req.query.year ? parseInt(req.query.year as string) : new Date().getFullYear();

      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
      
      const balance = await leaveBalanceRepository.findOne({
        where: {
          employeeId: parseInt(employeeId),
          leaveType,
          year,
          isActive: true
        }
      });

      if (!balance) {
        res.status(404).json({
          success: false,
          message: 'Leave balance not found'
        });
        return;
      }

      const transformedBalance = {
        employeeId: balance.employeeId,
        leaveType: balance.leaveType,
        total: balance.totalAllocated + balance.carriedForward,
        used: balance.used,
        pending: balance.pending,
        remaining: balance.remaining,
        carryForward: balance.carriedForward,
        expiryDate: balance.expiryDate
      };

      res.json({
        success: true,
        data: transformedBalance
      });
    } catch (error: any) {
      console.error('Error fetching leave balance:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch leave balance',
        error: error.message
      });
    }
  }

  /**
   * Create or update leave allocation for an employee
   * Leave balance is calculated from allocation automatically
   */
  async upsertLeaveBalance(req: Request, res: Response): Promise<void> {
    try {
      const { employeeId, leaveType, year, totalAllocated, carriedForward, notes } = req.body;

      if (!employeeId || !leaveType || !year || totalAllocated === undefined) {
        res.status(400).json({
          success: false,
          message: 'Missing required fields: employeeId, leaveType, year, totalAllocated'
        });
        return;
      }

      const employeeRepository = AppDataSource.getRepository('Employee');
      const leaveAllocationRepository = AppDataSource.getRepository(LeaveAllocation);

      // Verify employee exists
      const employee = await employeeRepository.findOne({ where: { id: employeeId } });
      
      if (!employee) {
        res.status(404).json({
          success: false,
          message: 'Employee not found'
        });
        return;
      }

      // Create or update allocation (source of truth)
      const existingAllocation = await leaveAllocationRepository.findOne({
        where: { employeeId, leaveType, year, isActive: true }
      });

      let allocation: LeaveAllocation;
      let wasCreated = false;

      if (existingAllocation) {
        // Update existing allocation - calculate manual adjustment properly
        const currentPolicyAllocation = existingAllocation.policyAllocation || 0;
        const currentCarriedForward = carriedForward !== undefined ? carriedForward : existingAllocation.carriedForward;
        
        // Calculate what the manual adjustment should be to reach the desired total
        const requiredManualAdjustment = totalAllocated - currentPolicyAllocation - currentCarriedForward;
        
        existingAllocation.manualAdjustment = requiredManualAdjustment;
        existingAllocation.carriedForward = currentCarriedForward;
        existingAllocation.notes = notes || `Manual adjustment: ${requiredManualAdjustment > 0 ? '+' : ''}${requiredManualAdjustment} days (Total: ${totalAllocated})`;
        existingAllocation.source = 'MANUAL';
        existingAllocation.updatedAt = new Date();
        
        allocation = await leaveAllocationRepository.save(existingAllocation);
        console.log(`✅ Updated allocation for employee ${employeeId}, ${leaveType}: Policy=${currentPolicyAllocation}, Manual=${requiredManualAdjustment}, Total=${totalAllocated} days`);
      } else {
        // Create new allocation - check if there's a policy allocation for this leave type
        const leaveTypePolicyRepository = AppDataSource.getRepository(LeaveTypePolicy);
        const leaveTypePolicy = await leaveTypePolicyRepository.findOne({
          where: { leaveType, isActive: true }
        });
        
        const defaultPolicyAllocation = leaveTypePolicy?.maxDaysPerYear || 0;
        const currentCarriedForward = carriedForward || 0;
        const requiredManualAdjustment = totalAllocated - defaultPolicyAllocation - currentCarriedForward;
        
        allocation = leaveAllocationRepository.create({
          employeeId,
          leaveType,
          year,
          policyAllocation: defaultPolicyAllocation,
          manualAdjustment: requiredManualAdjustment,
          carriedForward: currentCarriedForward,
          source: 'MANUAL',
          notes: notes || `Manual allocation: Policy=${defaultPolicyAllocation}, Manual=${requiredManualAdjustment > 0 ? '+' : ''}${requiredManualAdjustment} (Total: ${totalAllocated})`,
          isActive: true
        });
        
        allocation = await leaveAllocationRepository.save(allocation);
        wasCreated = true;
        console.log(`✅ Created allocation for employee ${employeeId}, ${leaveType}: Policy=${defaultPolicyAllocation}, Manual=${requiredManualAdjustment}, Total=${totalAllocated} days`);
      }

      // Calculate current balance to return
      const calculatedBalance = await leaveBalanceCalculationService.calculateEmployeeLeaveBalance(
        employeeId,
        leaveType,
        year
      );

      res.json({
        success: true,
        data: {
          allocation: {
            id: allocation.id,
            employeeId: allocation.employeeId,
            leaveType: allocation.leaveType,
            year: allocation.year,
            totalAllocated: allocation.totalAllocated,
            carriedForward: allocation.carriedForward,
            source: allocation.source,
            notes: allocation.notes
          },
          balance: calculatedBalance,
          wasCreated
        },
        message: `Leave allocation ${wasCreated ? 'created' : 'updated'} successfully. Balance calculated automatically.`
      });
    } catch (error: any) {
      console.error('❌ Error upserting leave allocation:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to save leave allocation',
        error: error.message
      });
    }
  }

  /**
   * Get all employees with their leave balances
   */
  async getAllEmployeeBalances(req: Request, res: Response): Promise<void> {
    try {
      const year = req.query.year ? parseInt(req.query.year as string) : new Date().getFullYear();
      const department = req.query.department as string;

      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
      
      let query = leaveBalanceRepository.createQueryBuilder('balance')
        .where('balance.year = :year', { year })
        .andWhere('balance.isActive = :isActive', { isActive: true });

      // Note: Department filtering is not available without employee relation
      // Would need to join with Employee entity if department filtering is required

      const balances = await query.getMany();

      // Group balances by employee
      const employeeBalances: { [key: number]: any } = {};
      
      balances.forEach((balance: LeaveBalance) => {
        if (!employeeBalances[balance.employeeId]) {
          employeeBalances[balance.employeeId] = {
            employeeId: balance.employeeId,
            employeeName: 'Unknown', // We'll need to fetch this separately if needed
            department: 'Unknown', // We'll need to fetch this separately if needed
            balances: []
          };
        }
        
        employeeBalances[balance.employeeId].balances.push({
          leaveType: balance.leaveType,
          total: balance.totalAllocated + balance.carriedForward,
          used: balance.used,
          pending: balance.pending,
          remaining: balance.remaining,
          carryForward: balance.carriedForward,
          expiryDate: balance.expiryDate
        });
      });

      const result = Object.values(employeeBalances);

      res.json({
        success: true,
        data: result
      });
    } catch (error: any) {
      console.error('Error fetching all employee balances:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch employee balances',
        error: error.message
      });
    }
  }

  /**
   * Get all active employees with their calculated leave balances
   * Balance = Allocation - Used - Pending (calculated on-the-fly)
   */
  async getAllActiveEmployeesWithBalances(req: Request, res: Response): Promise<void> {
    try {
      const year = req.query.year ? parseInt(req.query.year as string) : new Date().getFullYear();
      const department = req.query.department as string;
      const page = req.query.page ? parseInt(req.query.page as string) : 1;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
      const search = req.query.search as string;

      console.log(`📊 Fetching employees with calculated balances for year ${year}`);

      // Use the calculation service to get employees with calculated balances
      const result = await leaveBalanceCalculationService.getAllEmployeesWithCalculatedBalances(
        year,
        department === 'all' ? undefined : department,
        page,
        limit,
        search
      );

      // Transform the data to match the expected format
      const transformedData = result.employees.map(employee => ({
        employeeId: employee.employeeId,
        employeeName: employee.employeeName,
        employeeCode: employee.employeeCode,
        department: employee.department,
        position: employee.position,
        balances: employee.balances.map(balance => ({
          leaveType: balance.leaveType,
          totalAllocated: balance.totalAllocated,
          total: balance.totalAllocated + balance.carriedForward,
          used: balance.used,
          pending: balance.pending,
          remaining: balance.remaining,
          carryForward: balance.carriedForward,
          expiryDate: null, // Not relevant for calculated balances
          isProrated: balance.isProrated || false,
          proratedInfo: balance.proratedInfo || null
        }))
      }));

      console.log(`✅ Fetched ${transformedData.length} employees with calculated balances`);

       res.json({
         success: true,
        data: transformedData,
         pagination: {
          page: result.pagination.page,
          limit: result.pagination.limit,
          total: result.pagination.total,
          totalPages: result.pagination.totalPages,
          hasNextPage: result.pagination.page < result.pagination.totalPages,
          hasPreviousPage: result.pagination.page > 1
         }
       });
    } catch (error: any) {
      console.error('❌ Error fetching employees with calculated balances:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch employees with calculated balances',
        error: error.message
      });
    }
  }

  /**
   * Auto-allocate leaves to employees based on leave policies
   */
  async autoAllocateLeaves(req: Request, res: Response): Promise<void> {
    try {
      const { year, employeeIds, leaveTypes, departmentFilter } = req.body;
      const currentYear = year || new Date().getFullYear();
      const userId = req.user?.id || 1;

      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
      const userRepository = AppDataSource.getRepository(User);
      const leaveTypePolicyRepository = AppDataSource.getRepository(LeaveTypePolicy);
      const policyConfigRepository = AppDataSource.getRepository(LeavePolicyConfiguration);

      // Get active policy configuration
      const activePolicyConfig = await policyConfigRepository.findOne({
        where: { isActive: true },
        relations: ['leaveTypes', 'accrualRules']
      });

      if (!activePolicyConfig) {
        res.status(404).json({
          success: false,
          message: 'No active leave policy configuration found'
        });
        return;
      }

      // Get employees to allocate leaves for
      let employeeQuery = userRepository.createQueryBuilder('user')
        .where('user.isActive = :isActive', { isActive: true });

      if (employeeIds && employeeIds.length > 0) {
        employeeQuery = employeeQuery.andWhere('user.id IN (:...employeeIds)', { employeeIds });
      }

      if (departmentFilter) {
        employeeQuery = employeeQuery.andWhere('user.department = :department', { department: departmentFilter });
      }

      const employees = await employeeQuery.getMany();

      // Get leave types to allocate
      let leaveTypesToAllocate = activePolicyConfig.leaveTypes.filter(lt => lt.enabled && lt.isActive);
      
      if (leaveTypes && leaveTypes.length > 0) {
        leaveTypesToAllocate = leaveTypesToAllocate.filter(lt => leaveTypes.includes(lt.leaveType));
      }

      const allocationResults = [];
      const errors = [];

      for (const employee of employees) {
        for (const leaveTypePolicy of leaveTypesToAllocate) {
          try {
            // Check if employee is eligible for this leave type
            if (!this.isEmployeeEligibleForLeaveType(employee, leaveTypePolicy)) {
              continue;
            }

            // Calculate allocation based on policy
            const allocation = await this.calculateEmployeeAllocation(employee, leaveTypePolicy, currentYear);

                         // Check if balance already exists
             let existingBalance = await leaveBalanceRepository.findOne({
               where: {
                 employeeId: Number(employee.id),
                 leaveType: leaveTypePolicy.leaveType,
                 year: currentYear
               }
             });

            if (existingBalance) {
              // Update existing balance if auto-allocation is allowed
              existingBalance.totalAllocated = allocation.totalDays;
              existingBalance.notes = `Auto-allocated based on ${leaveTypePolicy.displayName} policy`;
              existingBalance.isActive = true;
              await leaveBalanceRepository.save(existingBalance);
              
              allocationResults.push({
                employeeId: employee.id,
                employeeName: employee.name,
                leaveType: leaveTypePolicy.leaveType,
                leaveTypeName: leaveTypePolicy.displayName,
                allocatedDays: allocation.totalDays,
                status: 'updated'
              });
            } else {
                             // Create new balance
               const newBalance = leaveBalanceRepository.create({
                 employeeId: Number(employee.id),
                 leaveType: leaveTypePolicy.leaveType,
                 year: currentYear,
                 totalAllocated: allocation.totalDays,
                 used: 0,
                 pending: 0,
                 carriedForward: 0,
                 lapsed: 0,
                 notes: `Auto-allocated based on ${leaveTypePolicy.displayName} policy`,
                 isActive: true
               });

              await leaveBalanceRepository.save(newBalance);
              
              allocationResults.push({
                employeeId: employee.id,
                employeeName: employee.name,
                leaveType: leaveTypePolicy.leaveType,
                leaveTypeName: leaveTypePolicy.displayName,
                allocatedDays: allocation.totalDays,
                status: 'created'
              });
            }
          } catch (error: any) {
            errors.push({
              employeeId: employee.id,
              employeeName: employee.name,
              leaveType: leaveTypePolicy.leaveType,
              error: error.message
            });
          }
        }
      }

      res.json({
        success: true,
        data: {
          allocations: allocationResults,
          errors,
          summary: {
            totalEmployees: employees.length,
            totalAllocations: allocationResults.length,
            totalErrors: errors.length,
            leaveTypesProcessed: leaveTypesToAllocate.length
          }
        },
        message: `Successfully processed leave allocations for ${allocationResults.length} employee-leave type combinations`
      });

    } catch (error: any) {
      console.error('Error auto-allocating leaves:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to auto-allocate leaves',
        error: error.message
      });
    }
  }

  /**
   * Bulk allocate specific leave type to multiple employees
   */
  async bulkAllocateLeave(req: Request, res: Response): Promise<void> {
    try {
      const { employeeIds, leaveType, allocatedDays, year, notes, effectiveDate } = req.body;
      const currentYear = year || new Date().getFullYear();

      if (!employeeIds || !leaveType || allocatedDays === undefined) {
        res.status(400).json({
          success: false,
          message: 'Missing required fields: employeeIds, leaveType, allocatedDays'
        });
        return;
      }

      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
      const userRepository = AppDataSource.getRepository(User);

      // Verify employees exist
      const employees = await userRepository.findByIds(employeeIds);
      if (employees.length !== employeeIds.length) {
        res.status(404).json({
          success: false,
          message: 'Some employees not found'
        });
        return;
      }

      const results = [];
      const errors = [];

      for (const employee of employees) {
        try {
                     // Check if balance already exists
           let balance = await leaveBalanceRepository.findOne({
             where: { employeeId: Number(employee.id), leaveType, year: currentYear }
           });

          if (balance) {
            // Update existing balance
            balance.totalAllocated = allocatedDays;
            balance.notes = notes || `Bulk allocation - ${allocatedDays} days`;
            balance.isActive = true;
            await leaveBalanceRepository.save(balance);
            
            results.push({
              employeeId: employee.id,
              employeeName: employee.name,
              status: 'updated',
              allocatedDays
            });
                     } else {
             // Create new balance
             balance = leaveBalanceRepository.create({
               employeeId: Number(employee.id),
               leaveType,
               year: currentYear,
               totalAllocated: allocatedDays,
               used: 0,
               pending: 0,
               carriedForward: 0,
               lapsed: 0,
               notes: notes || `Bulk allocation - ${allocatedDays} days`,
               isActive: true
             });

            await leaveBalanceRepository.save(balance);
            
            results.push({
              employeeId: employee.id,
              employeeName: employee.name,
              status: 'created',
              allocatedDays
            });
          }
        } catch (error: any) {
          errors.push({
            employeeId: employee.id,
            employeeName: employee.name,
            error: error.message
          });
        }
      }

      res.json({
        success: true,
        data: {
          results,
          errors,
          summary: {
            totalEmployees: employees.length,
            successful: results.length,
            failed: errors.length,
            leaveType,
            allocatedDays
          }
        },
        message: `Bulk allocation completed for ${results.length} employees`
      });

    } catch (error: any) {
      console.error('Error bulk allocating leave:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to bulk allocate leave',
        error: error.message
      });
    }
  }

  /**
   * Check if employee is eligible for a specific leave type
   */
  private isEmployeeEligibleForLeaveType(employee: User, leaveTypePolicy: LeaveTypePolicy): boolean {
    // Check applicable departments
    if (leaveTypePolicy.applicableDepartments && !leaveTypePolicy.applicableDepartments.includes('all')) {
      if (!leaveTypePolicy.applicableDepartments.includes(employee.department || '')) {
        return false;
      }
    }

    // Check minimum service period
    if (leaveTypePolicy.minServicePeriod > 0) {
      const serviceDays = Math.floor((new Date().getTime() - new Date(employee.createdAt).getTime()) / (1000 * 60 * 60 * 24));
      const minServiceDays = leaveTypePolicy.minServicePeriod * 30; // Convert months to days
      if (serviceDays < minServiceDays) {
        return false;
      }
    }

    return true;
  }

  /**
   * Calculate employee allocation based on policy using dynamic allocation service
   */
  private async calculateEmployeeAllocation(employee: User, leaveTypePolicy: LeaveTypePolicy, year: number): Promise<{ totalDays: number; calculation: string }> {
    try {
      // Use the dynamic leave allocation service
      const { leaveAllocationService } = await import('../services/LeaveAllocationService');
      const employeeId = parseInt(employee.id.toString());
      const allocation = await leaveAllocationService.calculateEmployeeAllocation(employeeId, leaveTypePolicy.leaveType, year);
      
      return {
        totalDays: allocation.totalDays,
        calculation: allocation.calculation
      };
    } catch (error) {
      console.error('Error using dynamic allocation service, falling back to basic calculation:', error);
      
      // Fallback to basic calculation if dynamic service fails
      let totalDays = leaveTypePolicy.maxDaysPerYear || 0;
      let calculation = `Fallback allocation: ${totalDays} days`;

      // Apply proration for new employees if they joined this year
      const joiningYear = new Date(employee.createdAt).getFullYear();
      if (joiningYear === year) {
        const joiningMonth = new Date(employee.createdAt).getMonth() + 1;
        const remainingMonths = 12 - joiningMonth + 1;
        const proratedDays = Math.round((totalDays / 12) * remainingMonths);
        
        if (proratedDays < totalDays) {
          totalDays = proratedDays;
          calculation += ` (Prorated for ${remainingMonths} months: ${totalDays} days)`;
        }
      }

      return { totalDays, calculation };
    }
  }

  /**
   * Bulk adjust leave quotas for multiple employees
   */
  async bulkAdjustLeaveQuotas(req: Request, res: Response): Promise<void> {
    try {
      const { employeeIds, adjustments, reason } = req.body;
      const currentYear = new Date().getFullYear();

      if (!employeeIds || !adjustments || !Array.isArray(adjustments)) {
        res.status(400).json({
          success: false,
          message: 'Missing required fields: employeeIds, adjustments'
        });
        return;
      }

      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
      const userRepository = AppDataSource.getRepository(User);

      // Verify employees exist
      const employees = await userRepository.findByIds(employeeIds);
      if (employees.length === 0) {
        res.status(404).json({
          success: false,
          message: 'No valid employees found'
        });
        return;
      }

      const results = [];
      const errors = [];

      for (const employee of employees) {
        for (const adjustment of adjustments) {
          const { leaveType, adjustmentType, value } = adjustment;
          
          try {
            // Find existing balance or create new one
            let balance = await leaveBalanceRepository.findOne({
              where: { 
                employeeId: Number(employee.id), 
                leaveType, 
                year: currentYear 
              }
            });

            let newTotal = 0;
            let operation = '';

            if (balance) {
              // Apply adjustment based on type
              switch (adjustmentType) {
                case 'set':
                  newTotal = value;
                  operation = `Set to ${value}`;
                  break;
                case 'add':
                  newTotal = balance.totalAllocated + value;
                  operation = `Added ${value} (was ${balance.totalAllocated})`;
                  break;
                case 'subtract':
                  newTotal = Math.max(0, balance.totalAllocated - value);
                  operation = `Subtracted ${value} (was ${balance.totalAllocated})`;
                  break;
                default:
                  throw new Error(`Invalid adjustment type: ${adjustmentType}`);
              }

              balance.totalAllocated = newTotal;
              balance.notes = `${balance.notes || ''}\n${new Date().toISOString()}: ${operation} - ${reason || 'Bulk adjustment'}`.trim();
              await leaveBalanceRepository.save(balance);
            } else {
              // Create new balance only for 'set' operations
              if (adjustmentType === 'set') {
                newTotal = value;
                operation = `Created with ${value}`;
                
                balance = leaveBalanceRepository.create({
                  employeeId: Number(employee.id),
                  leaveType,
                  year: currentYear,
                  totalAllocated: newTotal,
                  used: 0,
                  pending: 0,
                  carriedForward: 0,
                  lapsed: 0,
                  notes: `${new Date().toISOString()}: ${operation} - ${reason || 'Bulk adjustment'}`,
                  isActive: true
                });

                await leaveBalanceRepository.save(balance);
              } else {
                throw new Error(`Cannot ${adjustmentType} on non-existent balance. Use 'set' to create new balance.`);
              }
            }

            results.push({
              employeeId: employee.id,
              employeeName: employee.name,
              leaveType,
              operation,
              newTotal,
              status: 'success'
            });

          } catch (error: any) {
            errors.push({
              employeeId: employee.id,
              employeeName: employee.name,
              leaveType,
              error: error.message
            });
          }
        }
      }

      res.json({
        success: true,
        data: {
          results,
          errors,
          summary: {
            totalEmployees: employees.length,
            totalAdjustments: adjustments.length,
            successful: results.length,
            failed: errors.length,
            reason: reason || 'Bulk adjustment'
          }
        },
        message: `Bulk adjustment completed: ${results.length} successful, ${errors.length} failed`
      });

    } catch (error: any) {
      console.error('Error bulk adjusting leave quotas:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to bulk adjust leave quotas',
        error: error.message
      });
    }
  }

  /**
   * Bulk save all employee leave allocations (allocations are source of truth)
   */
  async bulkSaveAllocations(req: Request, res: Response): Promise<void> {
    try {
      const { allocations } = req.body;

      if (!allocations || !Array.isArray(allocations)) {
        res.status(400).json({
          success: false,
          message: 'Invalid allocations data. Expected array of allocation objects.'
        });
        return;
      }

      console.log(`📥 Received bulk allocation save request for ${allocations.length} records`);

      const employeeRepository = AppDataSource.getRepository('Employee');
      const leaveAllocationRepository = AppDataSource.getRepository(LeaveAllocation);
      const errors: string[] = [];

      let savedCount = 0;
      let updatedCount = 0;

      // Process each allocation
      for (const allocationData of allocations) {
        try {
          const { employeeId, leaveType, year, totalAllocated, notes } = allocationData;

          console.log(`🔍 Processing allocation:`, {
            employeeId,
            leaveType,
            year,
            totalAllocated,
            notes
          });

          if (!employeeId || !leaveType || !year || totalAllocated === undefined) {
            errors.push(`Missing required fields for allocation: ${JSON.stringify(allocationData)}`);
            continue;
          }

          // Verify employee exists
          const employee = await employeeRepository.findOne({ where: { id: employeeId } });
          if (!employee) {
            console.log(`❌ Employee not found: ${employeeId}`);
            errors.push(`Employee not found: ${employeeId}`);
            continue;
          }

          const employeeName = `${employee.firstName || ''} ${employee.lastName || ''}`.trim();
          console.log(`✅ Employee found: ${employeeName} (ID: ${employeeId})`);

          // Check if allocation exists
          const existingAllocation = await leaveAllocationRepository.findOne({
            where: { employeeId, leaveType, year: year || new Date().getFullYear(), isActive: true }
          });

          if (existingAllocation) {
            // Update existing allocation
            existingAllocation.policyAllocation = totalAllocated;
            existingAllocation.manualAdjustment = 0;
            existingAllocation.notes = notes || `Bulk allocation update - ${new Date().toISOString()}`;
            existingAllocation.source = 'BULK_ADJUSTMENT';
            existingAllocation.updatedAt = new Date();
            
            await leaveAllocationRepository.save(existingAllocation);
            updatedCount++;
            console.log(`✅ Updated allocation for ${employeeName}, ${leaveType}: ${totalAllocated} days`);
          } else {
            // Create new allocation
            const newAllocation = leaveAllocationRepository.create({
              employeeId,
              leaveType,
              year: year || new Date().getFullYear(),
              policyAllocation: totalAllocated,
              manualAdjustment: 0,
              carriedForward: 0,
              source: 'BULK_ADJUSTMENT',
              notes: notes || `Bulk allocation creation - ${new Date().toISOString()}`,
              isActive: true
            });
            
            await leaveAllocationRepository.save(newAllocation);
            savedCount++;
            console.log(`✅ Created allocation for ${employeeName}, ${leaveType}: ${totalAllocated} days`);
          }

        } catch (error: any) {
          console.error('Error processing allocation:', allocationData, error);
          errors.push(`Error processing allocation for employee ${allocationData.employeeId}: ${error.message}`);
        }
      }

      console.log(`✅ Bulk allocation save completed: ${savedCount + updatedCount} processed, ${savedCount} created, ${updatedCount} updated, ${errors.length} errors`);

      res.json({
        success: true,
        data: {
          totalProcessed: savedCount + updatedCount,
          savedCount,
          updatedCount,
          errorCount: errors.length,
          errors: errors.length > 0 ? errors : undefined
        },
        message: `Successfully processed ${savedCount + updatedCount} leave allocations (${savedCount} created, ${updatedCount} updated)${errors.length > 0 ? ` with ${errors.length} errors` : ''}. Balances will be calculated automatically.`
      });

    } catch (error: any) {
      console.error('❌ Error in bulk save allocations:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to save leave allocations',
        error: error.message
      });
    }
  }
} 
