import axios from 'axios';
import { Vendor } from '../components/VendorContacts';

const API_URL = '/api/vendors';

// Export as a named export
export const vendorService = {
  // Get all vendors
  async getAllVendors(): Promise<Vendor[]> {
    try {
      const response = await axios.get(API_URL);
      return response.data;
    } catch (error) {
      console.error('Error fetching vendors:', error);
      throw error;
    }
  },

  // Get vendor by ID
  async getVendorById(id: string): Promise<Vendor> {
    try {
      const response = await axios.get(`${API_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching vendor with ID ${id}:`, error);
      throw error;
    }
  },

  // Create a new vendor
  async createVendor(vendorData: Partial<Vendor>): Promise<Vendor> {
    try {
      console.log('Creating vendor with data:', vendorData);
      const response = await axios.post(API_URL, vendorData);
      return response.data;
    } catch (error) {
      console.error('Error creating vendor:', error);
      throw error;
    }
  },

  // Update a vendor
  async updateVendor(id: string, vendorData: Partial<Vendor>): Promise<Vendor> {
    try {
      const response = await axios.put(`${API_URL}/${id}`, vendorData);
      return response.data;
    } catch (error) {
      console.error(`Error updating vendor with ID ${id}:`, error);
      throw error;
    }
  },

  // Delete a vendor
  async deleteVendor(id: string): Promise<boolean> {
    try {
      await axios.delete(`${API_URL}/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting vendor with ID ${id}:`, error);
      throw error;
    }
  }
}; 