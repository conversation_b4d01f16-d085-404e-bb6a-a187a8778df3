import { MigrationInterface, QueryRunner } from "typeorm";

export class AddHRRoles1747685483671 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // First update any records with roles that don't match our enum
        // This prevents the "Data truncated" error during schema alteration
        
        // Check if any incompatible roles exist and update them to EMPLOYEE
        await queryRunner.query(`
            UPDATE users 
            SET role = 'EMPLOYEE' 
            WHERE role NOT IN ('EMPLOYEE', 'DEPT_HEAD', 'IT_STAFF', 'IT_ADMIN', 'IT_SUPPORT', 'CEO', 'FINANCE_MANAGER', 'VIEW')
        `);

        // Now we can safely alter the column to include the new roles
        if (await queryRunner.hasTable('users')) {
            // For MySQL/MariaDB
            try {
                // Alter the enum to include the new HR roles
                await queryRunner.query(`
                    ALTER TABLE users 
                    MODIFY role ENUM('<PERSON><PERSON><PERSON><PERSON>Y<PERSON>', 'DEPT_HEAD', 'IT_STAFF', 'IT_ADMIN', 'IT_SUPPORT', 'CEO', 'FINANCE_MANAGER', 'VIEW', 'HR_ADMIN', 'HR_STAFF', 'ADMIN', 'SYSTEM_ADMIN') 
                    NOT NULL DEFAULT 'EMPLOYEE'
                `);
            } catch (error) {
                console.error('Failed to update role enum using MODIFY. Attempting alternative approach.', error);
                // Alternative approach for different database types
                await queryRunner.query(`
                    ALTER TABLE users 
                    DROP COLUMN role
                `);
                
                await queryRunner.query(`
                    ALTER TABLE users 
                    ADD COLUMN role VARCHAR(20) NOT NULL DEFAULT 'EMPLOYEE'
                `);
            }
        }

        // Update user permissions table schema if it exists
        if (await queryRunner.hasTable('user_permissions')) {
            // Add the new HR-specific permission columns
            try {
                await queryRunner.query(`
                    ALTER TABLE user_permissions 
                    ADD COLUMN IF NOT EXISTS canCreateEmployee BOOLEAN DEFAULT FALSE,
                    ADD COLUMN IF NOT EXISTS canEditEmployee BOOLEAN DEFAULT FALSE,
                    ADD COLUMN IF NOT EXISTS canDeleteEmployee BOOLEAN DEFAULT FALSE,
                    ADD COLUMN IF NOT EXISTS canViewEmployees BOOLEAN DEFAULT FALSE,
                    ADD COLUMN IF NOT EXISTS canManageAttendance BOOLEAN DEFAULT FALSE,
                    ADD COLUMN IF NOT EXISTS canManageLeave BOOLEAN DEFAULT FALSE,
                    ADD COLUMN IF NOT EXISTS canManagePayroll BOOLEAN DEFAULT FALSE,
                    ADD COLUMN IF NOT EXISTS canManagePerformance BOOLEAN DEFAULT FALSE
                `);
            } catch (error) {
                console.error('Failed to add HR permission columns', error);
            }
        }

        // Update IT_ADMIN users to have all HR permissions
        try {
            // First check if the permissions column is JSON
            const tableInfo = await queryRunner.query(`
                SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'users' 
                AND COLUMN_NAME = 'permissions'
            `);
            
            if (tableInfo && tableInfo.length > 0) {
                const dataType = tableInfo[0].DATA_TYPE.toLowerCase();
                
                if (dataType === 'json') {
                    // For MySQL with JSON type support
                    await queryRunner.query(`
                        UPDATE users 
                        SET permissions = JSON_MERGE_PATCH(
                            COALESCE(permissions, '{}'),
                            '{"canCreateEmployee":true,"canEditEmployee":true,"canDeleteEmployee":true,"canViewEmployees":true,"canManageAttendance":true,"canManageLeave":true,"canManagePayroll":true,"canManagePerformance":true}'
                        )
                        WHERE role IN ('IT_ADMIN', 'ADMIN', 'SYSTEM_ADMIN', 'CEO')
                    `);
                } else {
                    // For other DB types or older MySQL without JSON functions
                    console.log('JSON column type not detected, skipping automatic permission update');
                }
            }
        } catch (error) {
            console.error('Failed to update IT_ADMIN permissions:', error);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert changes if needed
        if (await queryRunner.hasTable('users')) {
            try {
                // Revert the enum to original values
                await queryRunner.query(`
                    ALTER TABLE users 
                    MODIFY role ENUM('EMPLOYEE', 'DEPT_HEAD', 'IT_STAFF', 'IT_ADMIN', 'IT_SUPPORT', 'CEO', 'FINANCE_MANAGER', 'VIEW') 
                    NOT NULL DEFAULT 'EMPLOYEE'
                `);
            } catch (error) {
                console.error('Failed to revert role enum', error);
            }
        }

        // Remove the HR permission columns from user_permissions if it exists
        if (await queryRunner.hasTable('user_permissions')) {
            try {
                await queryRunner.query(`
                    ALTER TABLE user_permissions 
                    DROP COLUMN IF EXISTS canCreateEmployee,
                    DROP COLUMN IF EXISTS canEditEmployee,
                    DROP COLUMN IF EXISTS canDeleteEmployee,
                    DROP COLUMN IF EXISTS canViewEmployees,
                    DROP COLUMN IF EXISTS canManageAttendance,
                    DROP COLUMN IF EXISTS canManageLeave,
                    DROP COLUMN IF EXISTS canManagePayroll,
                    DROP COLUMN IF EXISTS canManagePerformance
                `);
            } catch (error) {
                console.error('Failed to remove HR permission columns', error);
            }
        }
    }

}
