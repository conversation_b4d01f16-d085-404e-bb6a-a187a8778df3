import { AppDataSource } from '../server/database/database';
import { AddSalaryTrenchField1700000000019 } from '../server/migrations/1700000000019-AddSalaryTrenchField';

async function runSalaryTrenchMigration() {
  console.log('Starting salary trench field migration...');
  
  try {
    // Initialize the data source
    if (!AppDataSource.isInitialized) {
      console.log('Initializing database connection...');
      await AppDataSource.initialize();
      console.log('Database connection initialized');
    }
    
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    
    // Start transaction
    await queryRunner.startTransaction();
    
    try {
      console.log('Running migration to add salaryTrench field to employee_benefits...');
      const migration = new AddSalaryTrenchField1700000000019();
      await migration.up(queryRunner);
      
      // Commit transaction
      await queryRunner.commitTransaction();
      console.log('Migration completed successfully');
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();
      console.error('Error during migration:', error);
      throw error;
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
    
  } catch (error) {
    console.error('Migration process failed:', error);
  } finally {
    // Close the database connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('Database connection closed');
    }
  }
}

// Run the migration
runSalaryTrenchMigration()
  .then(() => {
    console.log('Salary trench migration process completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Unhandled error in migration process:', error);
    process.exit(1);
  }); 