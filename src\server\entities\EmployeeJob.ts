import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn
} from 'typeorm';
import { Employee } from './Employee';

@Entity('employee_jobs')
export class EmployeeJob {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  designation: string;

  @Column({ nullable: false })
  department: string;

  @Column({ type: 'text', nullable: true })
  project: string;

  @Column({ type: 'text', nullable: true })
  location: string;

  @Column({ nullable: true, default: 'Full-time' })
  employmentType: string;

  @Column({ nullable: true, default: 'Active' })
  employmentStatus: string;

  @Column({ type: 'text', nullable: true })
  employeeLevel: string;

  @Column({ nullable: false })
  joinDate: string;

  @Column({ nullable: true })
  probationEndDate: string;

  @Column({ nullable: true })
  noticePeriod: string;

  @Column({ type: 'text', nullable: true })
  reportingTo: string;
  
  @Column({ nullable: true, default: false })
  remoteWorkEligible: boolean;
  
  @Column({ nullable: true })
  nextReviewDate: string;
  
  @Column({ type: 'text', nullable: true })
  trainingRequirements: string;
  
  @Column({ nullable: true })
  workSchedule: string;
  
  @Column({ nullable: true })
  shiftType: string;
  
  // Relation to Employee
  @OneToOne(() => Employee, employee => employee.job, { onDelete: 'CASCADE' })
  @JoinColumn()
  employee: Employee;

  // System columns
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 