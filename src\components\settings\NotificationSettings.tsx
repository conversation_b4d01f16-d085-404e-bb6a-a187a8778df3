import React, { useState, useEffect } from 'react';
import { Bell, Save, AlertCircle, Volume2, VolumeX, Mail, Settings, Send, Users, Clock, Shield, Eye, FileText, Download } from 'lucide-react';

interface Setting {
  id: string;
  category: string;
  key: string;
  value: string;
  description: string;
  lastUpdated?: string;
  updatedBy?: string;
}

interface NotificationSettingsProps {
  settings: Setting[];
  onSettingChange: (id: string, value: string) => void;
  onSave: () => void;
  isSaving?: boolean;
  saveSuccess?: boolean;
}

const NotificationSettings: React.FC<NotificationSettingsProps> = ({
  settings,
  onSettingChange,
  onSave,
  isSaving = false,
  saveSuccess = false
}) => {
  const [localSettings, setLocalSettings] = useState<Setting[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [activeTab, setActiveTab] = useState('channels');
  const [templatePreview, setTemplatePreview] = useState('');
  const [notificationQueue, setNotificationQueue] = useState({
    pending: 0,
    sent: 0,
    failed: 0,
    lastProcessed: new Date().toISOString()
  });

  useEffect(() => {
    const notificationSettings = settings.filter(setting => setting.category === 'notifications');
    setLocalSettings(notificationSettings);
  }, [settings]);

  // Simulate real-time notification queue updates
  useEffect(() => {
    const interval = setInterval(() => {
      setNotificationQueue(prev => ({
        pending: Math.max(0, prev.pending + Math.floor(Math.random() * 3) - 1),
        sent: prev.sent + Math.floor(Math.random() * 5),
        failed: prev.failed + (Math.random() > 0.9 ? 1 : 0),
        lastProcessed: new Date().toISOString()
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const handleChange = (id: string, value: string) => {
    setLocalSettings(prev => prev.map(setting => 
      setting.id === id ? { ...setting, value } : setting
    ));
    setHasChanges(true);
    onSettingChange(id, value);
  };

  const handleSave = () => {
    onSave();
    setHasChanges(false);
  };



  const handleTemplatePreview = (templateType: string) => {
    const templates = getNotificationTemplates();
    const template = templates.find(t => t.type === templateType);
    if (template) {
      setTemplatePreview(template.content);
    }
  };

  const getNotificationTemplates = () => [
    {
      type: 'ticket_created',
      name: 'Ticket Created',
      subject: 'New Ticket Created - {{ticket_id}}',
      content: 'Hello {{user_name}},\n\nA new ticket has been created:\n\nTicket ID: {{ticket_id}}\nTitle: {{ticket_title}}\nPriority: {{priority}}\nAssigned to: {{assigned_to}}\n\nPlease log in to the system to view details.\n\nBest regards,\nIT Support Team'
    },
    {
      type: 'ticket_updated',
      name: 'Ticket Updated',
      subject: 'Ticket Updated - {{ticket_id}}',
      content: 'Hello {{user_name}},\n\nTicket {{ticket_id}} has been updated:\n\nStatus: {{status}}\nLast Comment: {{last_comment}}\nUpdated by: {{updated_by}}\n\nPlease check the system for full details.\n\nBest regards,\nIT Support Team'
    },
    {
      type: 'system_alert',
      name: 'System Alert',
      subject: 'System Alert - {{alert_type}}',
      content: 'Hello {{user_name}},\n\nA system alert has been triggered:\n\nAlert Type: {{alert_type}}\nSeverity: {{severity}}\nDescription: {{description}}\nTime: {{timestamp}}\n\nImmediate attention may be required.\n\nBest regards,\nIT Support Team'
    }
  ];

  const isSelectField = (key: string) => {
    return key === 'notificationFrequency' || key === 'notificationSound' || key === 'smtpSecurity' || key === 'notificationPriority';
  };

  const isPasswordField = (key: string) => {
    return key.toLowerCase().includes('password') || key.toLowerCase().includes('secret');
  };

  const isNumberField = (key: string) => {
    return key.includes('port') || key.includes('timeout') || key.includes('retry') || key.includes('limit');
  };

  const isTextareaField = (key: string) => {
    return key.includes('template') || key.includes('signature') || key.includes('footer');
  };

  const getNotificationFrequencyOptions = () => [
    { value: 'immediate', label: 'Immediate' },
    { value: 'hourly', label: 'Hourly' },
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' }
  ];

  const getNotificationSoundOptions = () => [
    { value: 'default', label: 'Default' },
    { value: 'chime', label: 'Chime' },
    { value: 'bell', label: 'Bell' },
    { value: 'none', label: 'None' }
  ];

  const getSmtpSecurityOptions = () => [
    { value: 'none', label: 'None' },
    { value: 'tls', label: 'TLS' },
    { value: 'ssl', label: 'SSL' }
  ];

  const getNotificationPriorityOptions = () => [
    { value: 'low', label: 'Low' },
    { value: 'normal', label: 'Normal' },
    { value: 'high', label: 'High' },
    { value: 'urgent', label: 'Urgent' }
  ];

  const getSettingsByTab = (tab: string) => {
    switch (tab) {
      case 'channels':
        return localSettings.filter(s => 
          s.key.includes('enable') || s.key.includes('Enable') || 
          s.key === 'notificationFrequency' || s.key === 'notificationSound'
        );
      case 'smtp':
        return localSettings.filter(s => 
          s.key.includes('smtp') || s.key.includes('email') || s.key.includes('mail')
        );
      case 'templates':
        return localSettings.filter(s => 
          s.key.includes('template') || s.key.includes('signature') || s.key.includes('footer')
        );
      case 'delivery':
        return localSettings.filter(s => 
          s.key.includes('retry') || s.key.includes('timeout') || s.key.includes('limit') || s.key.includes('priority')
        );
      default:
        return localSettings;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Bell className="h-6 w-6 text-yellow-600" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Notification Settings</h2>
            <p className="text-sm text-gray-600">Configure email, push, and in-app notification preferences</p>
          </div>
        </div>
        {hasChanges && (
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSaving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        )}
      </div>

      {saveSuccess && (
        <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700">
          <AlertCircle className="h-4 w-4" />
          Notification settings saved successfully!
        </div>
      )}



      {/* Notification Queue Status */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center gap-3 mb-4">
          <Clock className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-medium text-gray-900">Notification Queue Status</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-yellow-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-yellow-600" />
              <div className="text-sm font-medium text-gray-900">Pending</div>
            </div>
            <div className="text-2xl font-bold text-yellow-600">{notificationQueue.pending}</div>
          </div>
          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Send className="h-4 w-4 text-green-600" />
              <div className="text-sm font-medium text-gray-900">Sent</div>
            </div>
            <div className="text-2xl font-bold text-green-600">{notificationQueue.sent}</div>
          </div>
          <div className="bg-red-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <div className="text-sm font-medium text-gray-900">Failed</div>
            </div>
            <div className="text-2xl font-bold text-red-600">{notificationQueue.failed}</div>
          </div>
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Users className="h-4 w-4 text-blue-600" />
              <div className="text-sm font-medium text-gray-900">Last Processed</div>
            </div>
            <div className="text-xs text-blue-600">
              {new Date(notificationQueue.lastProcessed).toLocaleTimeString()}
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {[
              { id: 'channels', name: 'Notification Channels', icon: Bell },
              { id: 'smtp', name: 'SMTP Configuration', icon: Mail },
              { id: 'templates', name: 'Email Templates', icon: FileText },
              { id: 'delivery', name: 'Delivery Settings', icon: Settings }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-yellow-500 text-yellow-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'templates' && (
            <div className="mb-6">
              <div className="flex items-center gap-3 mb-4">
                <FileText className="h-5 w-5 text-blue-600" />
                <h3 className="text-lg font-medium text-gray-900">Email Templates</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                {getNotificationTemplates().map((template) => (
                  <div key={template.type} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{template.name}</h4>
                      <button
                        onClick={() => handleTemplatePreview(template.type)}
                        className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-700"
                      >
                        <Eye className="h-3 w-3" />
                        Preview
                      </button>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">Subject: {template.subject}</p>
                    <p className="text-xs text-gray-500">{template.content.substring(0, 100)}...</p>
                  </div>
                ))}
              </div>
              {templatePreview && (
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">Template Preview</h4>
                  <pre className="text-sm text-gray-600 whitespace-pre-wrap bg-gray-50 p-3 rounded">
                    {templatePreview}
                  </pre>
                </div>
              )}
            </div>
          )}

          <div className="space-y-6">
            {getSettingsByTab(activeTab).map((setting) => (
              <div key={setting.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      {setting.key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </label>
                    <p className="text-sm text-gray-600 mb-3">{setting.description}</p>
                    
                    {setting.key.includes('enable') || setting.key.includes('Enable') ? (
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id={setting.id}
                          checked={setting.value === 'true'}
                          onChange={(e) => handleChange(setting.id, e.target.checked ? 'true' : 'false')}
                          className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                        />
                        <label htmlFor={setting.id} className="ml-2 text-sm text-gray-700">
                          {setting.value === 'true' ? 'Enabled' : 'Disabled'}
                        </label>
                      </div>
                    ) : isSelectField(setting.key) ? (
                      <select
                        value={setting.value}
                        onChange={(e) => handleChange(setting.id, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                      >
                        {setting.key === 'notificationFrequency' 
                          ? getNotificationFrequencyOptions().map(option => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))
                          : setting.key === 'notificationSound'
                          ? getNotificationSoundOptions().map(option => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))
                          : setting.key === 'smtpSecurity'
                          ? getSmtpSecurityOptions().map(option => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))
                          : getNotificationPriorityOptions().map(option => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))
                        }
                      </select>
                    ) : isPasswordField(setting.key) ? (
                      <input
                        type="password"
                        value={setting.value}
                        onChange={(e) => handleChange(setting.id, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                        placeholder={`Enter ${setting.key}`}
                      />
                    ) : isNumberField(setting.key) ? (
                      <input
                        type="number"
                        value={setting.value}
                        onChange={(e) => handleChange(setting.id, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                        placeholder={`Enter ${setting.key}`}
                      />
                    ) : isTextareaField(setting.key) ? (
                      <textarea
                        value={setting.value}
                        onChange={(e) => handleChange(setting.id, e.target.value)}
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                        placeholder={`Enter ${setting.key}`}
                      />
                    ) : (
                      <input
                        type="text"
                        value={setting.value}
                        onChange={(e) => handleChange(setting.id, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                        placeholder={`Enter ${setting.key}`}
                      />
                    )}
                  </div>
                </div>
                
                {setting.lastUpdated && (
                  <div className="mt-3 text-xs text-gray-500">
                    Last updated: {new Date(setting.lastUpdated).toLocaleString()} by {setting.updatedBy}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationSettings;