import React from 'react';
import { RefreshCw, FileText, RotateCw, List, Grid, Shield, Users } from 'lucide-react';

interface LogViewerHeaderProps {
  onRefresh?: () => void;
  onExport?: () => void;
  onClear?: () => void;
  onSecurityDashboard?: () => void;
  onToggleUserCategories?: () => void;
  showUserCategorySelector?: boolean;
  viewMode: 'table' | 'cards';
  setViewMode: (mode: 'table' | 'cards') => void;
}

export const LogViewerHeader: React.FC<LogViewerHeaderProps> = ({
  onRefresh,
  onExport,
  onClear,
  onSecurityDashboard,
  onToggleUserCategories,
  showUserCategorySelector,
  viewMode,
  setViewMode
}) => {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 w-full">
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center flex-shrink-0">
        <span className="bg-gradient-to-r from-blue-600 to-indigo-600 h-5 w-1 rounded mr-2"></span>
        System Logs
      </h2>
      <div className="flex items-center flex-wrap gap-2">
        <div className="bg-gray-100 dark:bg-gray-700 rounded-md p-1 flex mr-1">
          <button
            onClick={() => setViewMode('table')}
            className={`p-1 rounded-md transition-all ${
              viewMode === 'table' 
                ? 'bg-white dark:bg-gray-600 shadow-sm text-blue-600 dark:text-blue-400' 
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
            title="Table view"
          >
            <List className="w-4 h-4" />
          </button>
          <button
            onClick={() => setViewMode('cards')}
            className={`p-1 rounded-md transition-all ${
              viewMode === 'cards'
                ? 'bg-white dark:bg-gray-600 shadow-sm text-blue-600 dark:text-blue-400' 
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
            title="Card view"
          >
            <Grid className="w-4 h-4" />
          </button>
        </div>

        {onToggleUserCategories && (
          <button
            onClick={onToggleUserCategories}
            className="flex items-center justify-center gap-1 px-2 h-7 text-sm bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 rounded-md hover:bg-gray-50 dark:hover:bg-gray-650 border border-gray-200 dark:border-gray-600 transition-colors shadow-sm"
            title={showUserCategorySelector ? "Show Activity Chart" : "Show User Categories"}
          >
            <Users className="h-4 w-4" />
            <span className="hidden sm:inline">{showUserCategorySelector ? "Activity Chart" : "Categories"}</span>
          </button>
        )}

        {onSecurityDashboard && (
          <button
            onClick={onSecurityDashboard}
            className="flex items-center justify-center gap-1 px-2 h-7 text-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-650 border border-gray-200 dark:border-gray-600 transition-colors shadow-sm"
            title="Security insights"
          >
            <Shield className="h-4 w-4" />
            <span className="hidden sm:inline">Security</span>
          </button>
        )}

        {onRefresh && (
          <button
            onClick={onRefresh}
            className="flex items-center justify-center gap-1 px-2 h-7 text-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-650 border border-gray-200 dark:border-gray-600 transition-colors shadow-sm"
            title="Refresh logs"
          >
            <RefreshCw className="h-4 w-4" />
            <span className="hidden sm:inline">Refresh</span>
          </button>
        )}

        {onExport && (
          <button
            onClick={onExport}
            className="flex items-center justify-center gap-1 px-2 h-7 text-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-650 border border-gray-200 dark:border-gray-600 transition-colors shadow-sm"
            title="Export logs to CSV"
          >
            <FileText className="h-4 w-4" />
            <span className="hidden sm:inline">Export</span>
          </button>
        )}

        {onClear && (
          <button
            onClick={onClear}
            className="flex items-center justify-center gap-1 px-2 h-7 text-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-650 border border-gray-200 dark:border-gray-600 transition-colors shadow-sm"
            title="Clear logs"
          >
            <RotateCw className="h-4 w-4" />
            <span className="hidden sm:inline">Clear</span>
          </button>
        )}
      </div>
    </div>
  );
}; 