import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { UserRole } from '../../types/common';
import { User } from '../../entities/User';
import { AppDataSource } from '../../config/database';

const userRepository = AppDataSource.getRepository(User);
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export interface AuthRequest extends Request {
  user?: User & { role?: UserRole };
}

export const auth = async (req: AuthRequest, res: Response, next: NextFunction) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      res.status(401).json({ error: 'Authorization token required' });
      return;
    }

    try {
      const decoded = jwt.verify(token, JWT_SECRET) as any;
      const userId = decoded.userId || decoded.id;
      
      if (!userId) {
        return res.status(401).json({ error: 'Invalid token: missing user ID' });
      }
      
      const user = await userRepository.findOne({ where: { id: userId } });
      
      if (!user) {
        return res.status(401).json({ error: 'User not found' });
      }
      
      if (!user.isActive) {
        return res.status(403).json({ error: 'Account is inactive' });
      }
      
      req.user = user;
      next();
    } catch (jwtError) {
      console.error('JWT verification failed:', jwtError);
      return res.status(401).json({ error: 'Invalid token' });
    }
  } catch (error) {
    console.error('Authentication middleware error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

// Optional: Add role-based authorization middleware
export const authorize = (roles: UserRole[]) => 
  async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      // IT_ADMIN always has access to everything
      if (req.user.role && (req.user.role === UserRole.IT_ADMIN || req.user.role === UserRole.ADMIN || req.user.role === UserRole.SYSTEM_ADMIN)) {
        next();
        return;
      }

      if (!req.user.role || !roles.includes(req.user.role as UserRole)) {
        // Check for fallback routes - bypass auth for fallback routes if database is down
        if (req.path.includes('/fallback')) {
          console.log('Bypassing authorization for fallback route:', req.path);
          next();
          return;
        }
        
        res.status(403).json({ error: 'Not authorized' });
        return;
      }

      next();
    } catch (error) {
      console.error('Authorization error:', error);
      // If there's any error during authorization, bypass for fallback routes
      if (req.path.includes('/fallback')) {
        console.log('Error during authorization, bypassing for fallback route:', req.path);
        next();
        return;
      }
      res.status(500).json({ error: 'Authorization error' });
      return;
    }
  };