import React, { useState, useEffect } from 'react';
import { 
  X, 
  Upload, 
  Calendar, 
  DollarSign, 
  FileText, 
  Globe, 
  Shield,
  Save,
  AlertCircle
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface Trademark {
  id?: string;
  trademarkName: string;
  description?: string;
  trademarkType: 'word_mark' | 'design_mark' | 'combined_mark' | 'service_mark';
  status: 'pending' | 'registered' | 'opposed' | 'abandoned' | 'expired' | 'renewed';
  registrationNumber?: string;
  applicationNumber?: string;
  applicationDate?: string;
  registrationDate?: string;
  renewalDate?: string;
  expiryDate?: string;
  jurisdiction: string;
  registryOffice?: string;
  trademarkClasses: string[];
  goodsAndServices?: string;
  trademarkImageUrl?: string;
  attorney?: string;
  registrationFee?: number;
  renewalFee?: number;
  currency?: string;
  notes?: string;
  isActive: boolean;
  isPrimary: boolean;
  priority: number;
}

interface TrademarkFormProps {
  trademark?: Trademark | null;
  onSave: (trademark: Trademark) => void;
  onCancel: () => void;
}

const TrademarkForm: React.FC<TrademarkFormProps> = ({ trademark, onSave, onCancel }) => {
  const [formData, setFormData] = useState<Trademark>({
    trademarkName: '',
    description: '',
    trademarkType: 'word_mark',
    status: 'pending',
    registrationNumber: '',
    applicationNumber: '',
    applicationDate: '',
    registrationDate: '',
    renewalDate: '',
    expiryDate: '',
    jurisdiction: '',
    registryOffice: '',
    trademarkClasses: [],
    goodsAndServices: '',
    trademarkImageUrl: '',
    attorney: '',
    registrationFee: 0,
    renewalFee: 0,
    currency: 'USD',
    notes: '',
    isActive: true,
    isPrimary: false,
    priority: 0
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [saving, setSaving] = useState(false);
  const [imagePreview, setImagePreview] = useState<string>('');

  useEffect(() => {
    if (trademark) {
      setFormData(trademark);
      if (trademark.trademarkImageUrl) {
        setImagePreview(trademark.trademarkImageUrl);
      }
    }
  }, [trademark]);

  const trademarkTypes = [
    { value: 'word_mark', label: 'Word Mark' },
    { value: 'design_mark', label: 'Design Mark' },
    { value: 'combined_mark', label: 'Combined Mark' },
    { value: 'service_mark', label: 'Service Mark' }
  ];

  const statusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'registered', label: 'Registered' },
    { value: 'opposed', label: 'Opposed' },
    { value: 'abandoned', label: 'Abandoned' },
    { value: 'expired', label: 'Expired' },
    { value: 'renewed', label: 'Renewed' }
  ];

  const trademarkClassOptions = [
    'Class 01 - Chemicals',
    'Class 09 - Scientific instruments, software',
    'Class 35 - Advertising, business management',
    'Class 42 - Scientific and technological services',
    'Class 45 - Legal services'
  ];

  const jurisdictions = [
    'United States',
    'European Union',
    'United Kingdom',
    'Canada',
    'Australia',
    'Pakistan',
    'India',
    'China',
    'Japan',
    'Other'
  ];

  const currencies = ['USD', 'EUR', 'GBP', 'PKR', 'INR', 'CNY', 'JPY'];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseFloat(value) || 0 }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleClassChange = (className: string) => {
    setFormData(prev => ({
      ...prev,
      trademarkClasses: prev.trademarkClasses.includes(className)
        ? prev.trademarkClasses.filter(c => c !== className)
        : [...prev.trademarkClasses, className]
    }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error('Image size should be less than 5MB');
        return;
      }

      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        setImagePreview(result);
        setFormData(prev => ({ ...prev, trademarkImageUrl: result }));
      };
      reader.readAsDataURL(file);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.trademarkName.trim()) {
      newErrors.trademarkName = 'Trademark name is required';
    }

    if (!formData.jurisdiction.trim()) {
      newErrors.jurisdiction = 'Jurisdiction is required';
    }

    if (formData.trademarkClasses.length === 0) {
      newErrors.trademarkClasses = 'At least one trademark class is required';
    }

    if (formData.applicationDate && formData.registrationDate) {
      const appDate = new Date(formData.applicationDate);
      const regDate = new Date(formData.registrationDate);
      if (regDate < appDate) {
        newErrors.registrationDate = 'Registration date cannot be before application date';
      }
    }

    if (formData.registrationDate && formData.expiryDate) {
      const regDate = new Date(formData.registrationDate);
      const expDate = new Date(formData.expiryDate);
      if (expDate < regDate) {
        newErrors.expiryDate = 'Expiry date cannot be before registration date';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the errors before submitting');
      return;
    }

    setSaving(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const trademarkToSave = {
        ...formData,
        id: trademark?.id || `tm-${Date.now()}`,
        createdAt: trademark?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      onSave(trademarkToSave);
      toast.success(trademark ? 'Trademark updated successfully' : 'Trademark added successfully');
    } catch (error) {
      toast.error('Failed to save trademark');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-600" />
            {trademark ? 'Edit Trademark' : 'Add New Trademark'}
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trademark Name *
              </label>
              <input
                type="text"
                name="trademarkName"
                value={formData.trademarkName}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.trademarkName ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter trademark name"
              />
              {errors.trademarkName && (
                <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.trademarkName}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trademark Type
              </label>
              <select
                name="trademarkType"
                value={formData.trademarkType}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {trademarkTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Brief description of the trademark"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {statusOptions.map(status => (
                  <option key={status.value} value={status.value}>
                    {status.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Jurisdiction *
              </label>
              <select
                name="jurisdiction"
                value={formData.jurisdiction}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.jurisdiction ? 'border-red-300' : 'border-gray-300'
                }`}
              >
                <option value="">Select jurisdiction</option>
                {jurisdictions.map(jurisdiction => (
                  <option key={jurisdiction} value={jurisdiction}>
                    {jurisdiction}
                  </option>
                ))}
              </select>
              {errors.jurisdiction && (
                <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.jurisdiction}
                </p>
              )}
            </div>
          </div>

          {/* Registration Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Registration Details</h3>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Application Number
              </label>
              <input
                type="text"
                name="applicationNumber"
                value={formData.applicationNumber}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Application number"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Registration Number
              </label>
              <input
                type="text"
                name="registrationNumber"
                value={formData.registrationNumber}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Registration number"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Application Date
              </label>
              <input
                type="date"
                name="applicationDate"
                value={formData.applicationDate}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Registration Date
              </label>
              <input
                type="date"
                name="registrationDate"
                value={formData.registrationDate}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.registrationDate ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.registrationDate && (
                <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.registrationDate}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Renewal Date
              </label>
              <input
                type="date"
                name="renewalDate"
                value={formData.renewalDate}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Expiry Date
              </label>
              <input
                type="date"
                name="expiryDate"
                value={formData.expiryDate}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.expiryDate ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.expiryDate && (
                <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.expiryDate}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Registry Office
              </label>
              <input
                type="text"
                name="registryOffice"
                value={formData.registryOffice}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., USPTO, EUIPO, IPO Pakistan"
              />
            </div>
          </div>

          {/* Trademark Classes */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Trademark Classes *</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {trademarkClassOptions.map(className => (
                <label key={className} className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.trademarkClasses.includes(className)}
                    onChange={() => handleClassChange(className)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">{className}</span>
                </label>
              ))}
            </div>
            {errors.trademarkClasses && (
              <p className="mt-2 text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.trademarkClasses}
              </p>
            )}
          </div>

          {/* Goods and Services */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Goods and Services
            </label>
            <textarea
              name="goodsAndServices"
              value={formData.goodsAndServices}
              onChange={handleInputChange}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe the goods and services covered by this trademark"
            />
          </div>

          {/* Trademark Image */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trademark Image
            </label>
            <div className="flex items-center space-x-4">
              {imagePreview && (
                <div className="flex-shrink-0">
                  <img
                    src={imagePreview}
                    alt="Trademark preview"
                    className="h-20 w-20 object-contain bg-gray-50 rounded-lg border border-gray-200 p-2"
                  />
                </div>
              )}
              <div className="flex-1">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="trademark-image"
                />
                <label
                  htmlFor="trademark-image"
                  className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Image
                </label>
                <p className="mt-1 text-xs text-gray-500">
                  PNG, JPG, GIF up to 5MB
                </p>
              </div>
            </div>
          </div>

          {/* Financial Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Financial Information</h3>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Currency
              </label>
              <select
                name="currency"
                value={formData.currency}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {currencies.map(currency => (
                  <option key={currency} value={currency}>
                    {currency}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Registration Fee
              </label>
              <input
                type="number"
                name="registrationFee"
                value={formData.registrationFee}
                onChange={handleInputChange}
                min="0"
                step="0.01"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0.00"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Renewal Fee
              </label>
              <input
                type="number"
                name="renewalFee"
                value={formData.renewalFee}
                onChange={handleInputChange}
                min="0"
                step="0.01"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0.00"
              />
            </div>
          </div>

          {/* Legal Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Legal Information</h3>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Attorney/Law Firm
              </label>
              <input
                type="text"
                name="attorney"
                value={formData.attorney}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Attorney or law firm name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority
              </label>
              <input
                type="number"
                name="priority"
                value={formData.priority}
                onChange={handleInputChange}
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Display order priority"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notes
              </label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Additional notes or comments"
              />
            </div>
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Settings</h3>
            <div className="space-y-3">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">Active trademark</span>
              </label>
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  name="isPrimary"
                  checked={formData.isPrimary}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">Primary company trademark</span>
              </label>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving}
              className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  {trademark ? 'Update Trademark' : 'Add Trademark'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TrademarkForm;
