import { Router } from 'express';
import { requireAuth } from '../middleware/auth';
import { requirePermission } from '../middleware/dynamicPermission';
import {
  // Job Posting Controllers
  createJobPosting,
  getJobPostings,
  getJobPostingById,
  updateJobPosting,
  deleteJobPosting,
  publishJobPosting,
  closeJobPosting,
  getJobPostingStats,
  
  // Job Application Controllers
  createJobApplication,
  getJobApplications,
} from '../controllers/recruitmentController';

const router = Router();

// Job Posting Routes
router.post('/job-postings', 
  requireAuth, 
  requirePermission('recruitment:create'), 
  createJobPosting
);

router.get('/job-postings', 
  requireAuth, 
  requirePermission('recruitment:read'), 
  getJobPostings
);

router.get('/job-postings/stats', 
  requireAuth, 
  requirePermission('recruitment:read'), 
  getJobPostingStats
);

router.get('/job-postings/:id', 
  requireAuth, 
  requirePermission('recruitment:read'), 
  getJobPostingById
);

router.put('/job-postings/:id', 
  requireAuth, 
  requirePermission('recruitment:update'), 
  updateJobPosting
);

router.delete('/job-postings/:id', 
  requireAuth, 
  requirePermission('recruitment:delete'), 
  deleteJobPosting
);

router.post('/job-postings/:id/publish', 
  requireAuth, 
  requirePermission('recruitment:update'), 
  publishJobPosting
);

router.post('/job-postings/:id/close', 
  requireAuth, 
  requirePermission('recruitment:update'), 
  closeJobPosting
);

// Job Application Routes
router.post('/applications', 
  createJobApplication // Public endpoint for external applications
);

router.get('/applications', 
  requireAuth, 
  requirePermission('recruitment:read'), 
  getJobApplications
);

// Public routes for job seekers (no authentication required)
router.get('/public/job-postings', getJobPostings);
router.get('/public/job-postings/:id', getJobPostingById);

export default router;
