import React, { useState, useRef, useEffect, RefObject } from 'react';
import { Search, Calendar, ChevronDown, X, Sliders, XCircle } from 'lucide-react';
import { SystemLog, LogFilterState } from './types';

interface LogFiltersProps {
  filters: LogFilterState;
  onFilterChange: (filters: Partial<LogFilterState>) => void;
  logs: SystemLog[];
  searchInputRef?: RefObject<HTMLInputElement>;
}

export const LogFilters: React.FC<LogFiltersProps> = ({ 
  filters, 
  onFilterChange, 
  logs,
  searchInputRef
}) => {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const datePickerRef = useRef<HTMLDivElement>(null);
  const internalSearchInputRef = useRef<HTMLInputElement>(null);

  // Get unique users from logs for the filter dropdown
  const uniqueUsers = Array.from(new Set(logs.map(log => log.user)));

  // Check if any filters are active
  const hasActiveFilters = 
    filters.search !== '' || 
    filters.type !== 'all' || 
    filters.user !== 'all' || 
    filters.dateRange.start !== null || 
    filters.dateRange.end !== null;

  // Function to format date for display
  const formatDate = (date: Date | null) => {
    if (!date) return '';
    return date.toLocaleDateString();
  };

  // Get date range display text
  const getDateRangeText = () => {
    if (filters.dateRange.start && filters.dateRange.end) {
      return `${formatDate(filters.dateRange.start)} - ${formatDate(filters.dateRange.end)}`;
    } else if (filters.dateRange.start) {
      return `From ${formatDate(filters.dateRange.start)}`;
    } else if (filters.dateRange.end) {
      return `Until ${formatDate(filters.dateRange.end)}`;
    }
    return 'Select Range';
  };

  // Handle clicks outside of date picker
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target as Node)) {
        setShowDatePicker(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle date range selection
  const handleDateChange = (type: 'start' | 'end', date: Date | null) => {
    onFilterChange({
      dateRange: {
        ...filters.dateRange,
        [type]: date
      }
    });
  };

  // Clear specific filter
  const clearFilter = (filterName: keyof LogFilterState) => {
    if (filterName === 'dateRange') {
      onFilterChange({
        dateRange: {
          start: null,
          end: null
        }
      });
    } else {
      onFilterChange({
        [filterName]: filterName === 'type' || filterName === 'user' ? 'all' : ''
      });
    }
  };

  // Clear all filters
  const clearFilters = () => {
    onFilterChange({
      search: '',
      type: 'all',
      user: 'all',
      dateRange: {
        start: null,
        end: null
      }
    });
  };

  // Add preset date ranges
  const applyPresetRange = (days: number) => {
    const end = new Date();
    const start = new Date();
    start.setDate(start.getDate() - days);
    
    onFilterChange({
      dateRange: {
        start,
        end
      }
    });
  };

  return (
    <div className="flex flex-col gap-4 w-full">
      <div className="flex flex-col md:flex-row items-stretch gap-3">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search in logs..."
            value={filters.search}
            onChange={(e) => onFilterChange({ search: e.target.value })}
            className="w-full pl-10 pr-4 py-2.5 text-md border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
          />
        </div>
        
        <div className="flex flex-wrap items-center gap-3">
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-600 dark:text-gray-400 whitespace-nowrap">Type:</label>
            <select
              value={filters.type}
              onChange={(e) => onFilterChange({ type: e.target.value as 'all' | 'info' | 'warning' | 'error' | 'success' })}
              className="text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 pl-3 pr-8 py-2 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
            >
              <option value="all">All</option>
              <option value="info">Info</option>
              <option value="success">Success</option>
              <option value="warning">Warning</option>
              <option value="error">Error</option>
            </select>
          </div>
          
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-600 dark:text-gray-400 whitespace-nowrap">User:</label>
            <select
              value={filters.user}
              onChange={(e) => onFilterChange({ user: e.target.value })}
              className="text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 pl-3 pr-8 py-2 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
            >
              <option value="all">All Users</option>
              {uniqueUsers.map(user => (
                <option key={user} value={user}>{user}</option>
              ))}
            </select>
          </div>
          
          <div className="flex items-center relative">
            <button
              onClick={() => setShowDatePicker(!showDatePicker)}
              className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-650 shadow-sm"
            >
              <Calendar className="h-4 w-4" />
              <span>{filters.dateRange.start || filters.dateRange.end ? getDateRangeText() : "Date Range"}</span>
              <ChevronDown className={`h-4 w-4 transition-transform ${showDatePicker ? 'rotate-180' : ''}`} />
            </button>
            
            {(filters.dateRange.start || filters.dateRange.end) && (
              <button
                onClick={() => clearFilter('dateRange')}
                className="ml-1 p-1 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Clear date filter"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
          
          <button
            onClick={clearFilters}
            className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-650 shadow-sm"
            title="Clear all filters"
          >
            <XCircle className="h-4 w-4" />
            <span>Clear All</span>
          </button>
        </div>
      </div>
      
      {/* Date Picker */}
      {showDatePicker && (
        <div className="absolute z-50 mt-2 right-4 w-96 max-w-full" ref={datePickerRef}>
          <div className="p-5 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 shadow-xl">
            <h3 className="font-medium text-gray-800 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">Select Date Range</h3>
            
            {/* Quick presets */}
            <div className="mb-4 flex flex-wrap gap-2">
              <button 
                onClick={() => applyPresetRange(1)}
                className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
              >
                Today
              </button>
              <button 
                onClick={() => applyPresetRange(7)}
                className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
              >
                Last 7 days
              </button>
              <button 
                onClick={() => applyPresetRange(30)}
                className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
              >
                Last 30 days
              </button>
              <button 
                onClick={() => applyPresetRange(90)}
                className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
              >
                Last 90 days
              </button>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-5">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Start Date</label>
                <input
                  type="datetime-local"
                  value={filters.dateRange.start ? filters.dateRange.start.toISOString().substring(0, 16) : ''}
                  onChange={(e) => {
                    const date = e.target.value ? new Date(e.target.value) : null;
                    handleDateChange('start', date);
                  }}
                  placeholder="yyyy-mm-dd hh:mm"
                  className="w-full text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 py-2 px-3 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">End Date</label>
                <input
                  type="datetime-local"
                  value={filters.dateRange.end ? filters.dateRange.end.toISOString().substring(0, 16) : ''}
                  onChange={(e) => {
                    const date = e.target.value ? new Date(e.target.value) : null;
                    handleDateChange('end', date);
                  }}
                  placeholder="yyyy-mm-dd hh:mm"
                  className="w-full text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 py-2 px-3 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
                />
              </div>
            </div>
            <div className="flex justify-between gap-3 border-t border-gray-200 dark:border-gray-700 pt-4">
              <button
                onClick={() => {
                  handleDateChange('start', null);
                  handleDateChange('end', null);
                }}
                className="text-sm px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 font-medium shadow-sm"
              >
                Clear Range
              </button>
              <button
                onClick={() => setShowDatePicker(false)}
                className="text-sm bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 font-medium shadow-sm"
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 