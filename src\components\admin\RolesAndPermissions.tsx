import React, { useState, useEffect } from 'react';
import { Shield, Plus, Edit, Trash2, Lock, User, CheckSquare, AlertCircle, Users, Copy, Search, ChevronDown, CheckCircle, Save, Download, UploadCloud, FileText, Star, X, Monitor, Clock, BarChart2, SearchX } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import api from '../../services/api';
import { toast } from 'react-hot-toast';
import { Toaster } from 'react-hot-toast';

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  critical?: boolean;
  impacts?: string[];
}

interface PermissionGroup {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  createdBy?: string;
  createdAt: string;
  isCustom?: boolean;
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  createdAt: string;
  updatedBy?: string;
  userCount?: number;
  parentId?: string;
  category?: 'system' | 'dashboard' | 'custom'; // Add category to identify dashboard roles
  dashboardAccess?: string[]; // Add dashboard access list
}

interface UserRoleAssignment {
  userId: string;
  roleId: string;
  assignedAt: string;
  assignedBy: string;
  expiresAt?: string;
  notes?: string;
  // Hierarchical scoping fields
  projectId: string; // required
  locationId?: string; // optional, within project
  departmentId?: string; // optional, within location
}



interface UserData {  
  id: string;  
  name: string;  
  email: string;
  roles: string[];
  department?: string;
  status: 'active' | 'inactive' | 'locked';
  lastLogin?: string;
  temporaryRoles?: UserRoleAssignment[];
  dashboardAccess?: DashboardAccess[]; // Add this field
}

interface RoleTemplate {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  category: 'predefined' | 'custom';
  iconName?: string;
  iconColor?: string;
  createdAt?: string;
}

interface JITAccessRequest {
  id: string;
  userId: string;
  userName: string;
  requestedRoleId: string;
  requestedRoleName: string;
  reason: string;
  status: 'pending' | 'approved' | 'denied' | 'expired';
  requestedAt: string;
  approvedAt?: string;
  deniedAt?: string;
  expiresAt?: string;
  approverId?: string;
  approverName?: string;
}

interface PermissionUsage {
  permissionId: string;
  count: number;
  lastUsed: string | null;
}

interface RoleUsage {
  roleId: string;
  count: number;
  lastUsed: string | null;
}

// Add DashboardAccess interface
interface DashboardAccess {
  dashboardId: string;
  accessLevel: 'view' | 'edit' | 'admin';
  grantedAt: string;
  grantedBy: string;
}

export const RolesAndPermissions = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'roles' | 'users' | 'groups' | 'jit' | 'analytics'>('roles');
  const [activeView, setActiveView] = useState<'list' | 'hierarchy' | 'impact'>('list');
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [users, setUsers] = useState<UserData[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [userError, setUserError] = useState<string | null>(null);
  const [isLoadingRoles, setIsLoadingRoles] = useState(true); // Add loading state for roles
    const [permissionGroups, setPermissionGroups] = useState<PermissionGroup[]>([]);
  const [roleTemplates, setRoleTemplates] = useState<RoleTemplate[]>([]);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [selectedPermissionGroup, setSelectedPermissionGroup] = useState<PermissionGroup | null>(null);
  const [showRoleForm, setShowRoleForm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showRoleUsers, setShowRoleUsers] = useState(false);
  const [showBulkAssign, setShowBulkAssign] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  const [showSaveTemplate, setShowSaveTemplate] = useState(false);
  const [showTempRoleForm, setShowTempRoleForm] = useState(false);
    const [showPermissionGroupForm, setShowPermissionGroupForm] = useState(false);  const [selectedUser, setSelectedUser] = useState<UserData | null>(null);  const [templateName, setTemplateName] = useState('');  const [templateDescription, setTemplateDescription] = useState('');  const [roleSearch, setRoleSearch] = useState('');  const [userSearch, setUserSearch] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
    const [bulkRoleId, setBulkRoleId] = useState('');
  const [selectedPermission, setSelectedPermission] = useState<Permission | null>(null);
  const [showImpactAnalysis, setShowImpactAnalysis] = useState(false);
  const [showHierarchyEdit, setShowHierarchyEdit] = useState(false);
  const [tempRoleData, setTempRoleData] = useState({
    userId: '',
    roleId: '',
    expiresAt: '',
    notes: '',
    projectId: '',
    locationId: '',
    departmentId: ''
  });
  const [jitRequests, setJitRequests] = useState<JITAccessRequest[]>([]);
  const [permissionUsage, setPermissionUsage] = useState<PermissionUsage[]>([]);
  const [roleUsage, setRoleUsage] = useState<RoleUsage[]>([]);
  const [showJITRequestForm, setShowJITRequestForm] = useState(false);
  const [showJITApproval, setShowJITApproval] = useState(false);
  const [selectedJITRequest, setSelectedJITRequest] = useState<JITAccessRequest | null>(null);
  // Add state for editing user roles
  const [editingUser, setEditingUser] = useState<UserData | null>(null);
  const [editingRoles, setEditingRoles] = useState<string[]>([]);
  
  // Add state for the new assign roles modal
  const [showAssignRolesModal, setShowAssignRolesModal] = useState(false);
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);
  const [assignmentRoles, setAssignmentRoles] = useState<string[]>([]);
  const [userSearchInModal, setUserSearchInModal] = useState('');
  
  // Add loading states to prevent race conditions
  const [isUpdatingRoles, setIsUpdatingRoles] = useState(false);
  const [updatingUserIds, setUpdatingUserIds] = useState<Set<string>>(new Set());
  
  // Initial data load
  useEffect(() => {
    // Create permissions based on AuthContext capabilities FIRST
    const systemPermissions: Permission[] = [
      // Ticket management permissions
      {
        id: 'canCreateTickets',
        name: 'Create Tickets',
        description: 'Can create service desk tickets',
        category: 'Service Desk'
      },
      {
        id: 'canCreateTicketsForOthers',
        name: 'Create Tickets for Others',
        description: 'Can create tickets on behalf of other users',
        category: 'Service Desk'
      },
      {
        id: 'canEditTickets',
        name: 'Edit Tickets',
        description: 'Can modify existing tickets',
        category: 'Service Desk'
      },
                  {        id: 'canDeleteTickets',        name: 'Delete Tickets',        description: 'Can delete tickets from the system',        category: 'Service Desk',        critical: true,        impacts: ['Ticket Deletion Controls', 'System Records', 'Audit Trail']      },
      {
        id: 'canCloseTickets',
        name: 'Close Tickets',
        description: 'Can mark tickets as resolved or closed',
        category: 'Service Desk'
      },
      {
        id: 'canLockTickets',
        name: 'Lock Tickets',
        description: 'Can lock tickets to prevent further changes',
        category: 'Service Desk'
      },
      {
        id: 'canAssignTickets',
        name: 'Assign Tickets',
        description: 'Can assign tickets to other staff members',
        category: 'Service Desk'
      },
      {
        id: 'canEscalateTickets',
        name: 'Escalate Tickets',
        description: 'Can escalate tickets to higher support levels',
        category: 'Service Desk'
      },
      {
        id: 'canViewAllTickets',
        name: 'View All Tickets',
        description: 'Can see tickets from all users',
        category: 'Service Desk'
      },
      
      // HR management permissions
      {
        id: 'canCreateEmployee',
        name: 'Create Employees',
        description: 'Can add new employees to the system',
        category: 'HR Management'
      },
      {
        id: 'canEditEmployee',
        name: 'Edit Employees',
        description: 'Can modify employee information',
        category: 'HR Management'
      },
      {
        id: 'canDeleteEmployee',
        name: 'Delete Employees',
        description: 'Can remove employees from the system',
        category: 'HR Management'
      },
      {
        id: 'canViewEmployees',
        name: 'View Employees',
        description: 'Can view employee information',
        category: 'HR Management'
      },
      {
        id: 'canManageAttendance',
        name: 'Manage Attendance',
        description: 'Can manage employee attendance records',
        category: 'HR Management'
      },
      {
        id: 'canManageLeave',
        name: 'Manage Leave',
        description: 'Can manage employee leave requests',
        category: 'HR Management'
      },
      {
        id: 'canManagePayroll',
        name: 'Manage Payroll',
        description: 'Can manage employee payroll',
        category: 'HR Management'
      },
      {
        id: 'canManagePerformance',
        name: 'Manage Performance',
        description: 'Can manage employee performance reviews',
        category: 'HR Management'
      },
      
      // System admin permissions
      {
        id: 'canAddUsers',
        name: 'Add Users',
        description: 'Can create new user accounts',
        category: 'System Administration'
      },
      {
        id: 'canEditUsers',
        name: 'Edit Users',
        description: 'Can modify existing user accounts',
        category: 'System Administration'
      },
      {
        id: 'canDeleteUsers',
        name: 'Delete Users',
        description: 'Can remove user accounts',
        category: 'System Administration'
      },
      {
        id: 'canViewReports',
        name: 'View Reports',
        description: 'Can access system reports and analytics',
        category: 'System Administration'
      },
      {
        id: 'canExportData',
        name: 'Export Data',
        description: 'Can export data from the system',
        category: 'System Administration'
      },
      {
        id: 'canImportData',
        name: 'Import Data',
        description: 'Can import data into the system',
        category: 'System Administration'
      },
      {
        id: 'canConfigureSystem',
        name: 'Configure System',
        description: 'Can modify system settings and configuration',
        category: 'System Administration'
      },
      {
        id: 'canManageRoles',
        name: 'Manage Roles',
        description: 'Can create, edit, and delete roles',
        category: 'System Administration'
      },
      {
        id: 'canApproveRequests',
        name: 'Approve Requests',
        description: 'Can approve various system requests',
        category: 'System Administration'
      },
      {
        id: 'canViewAllDepartments',
        name: 'View All Departments',
        description: 'Can access information across all departments',
        category: 'System Administration'
      },
      {
        id: 'canAccessAllModules',
        name: 'Access All Modules',
        description: 'Can access all system modules and features',
        category: 'System Administration'
      },
      {
        id: 'isAdmin',
        name: 'Administrator',
        description: 'Has administrative privileges (use with caution)',
        category: 'System Administration'
        },
        // Add Dashboard permissions to the main permissions list
        {
          id: 'canViewDashboards',
          name: 'View Dashboards',
          description: 'Can view dashboards they have access to',
          category: 'Dashboards'
        },
        {
          id: 'canViewAllDashboards',
          name: 'View All Dashboards',
          description: 'Can view all dashboards in the system',
          category: 'Dashboards'
        },
        {
          id: 'canCreateDashboards',
          name: 'Create Dashboards',
          description: 'Can create new dashboards',
          category: 'Dashboards',
          critical: true,
          impacts: ['Dashboard Management', 'Data Visualization']
        },
        {
          id: 'canEditDashboards',
          name: 'Edit Dashboards',
          description: 'Can modify existing dashboards',
          category: 'Dashboards'
        },
        {
          id: 'canDeleteDashboards',
          name: 'Delete Dashboards',
          description: 'Can delete dashboards from the system',
          category: 'Dashboards',
          critical: true,
          impacts: ['Dashboard Management', 'User Access']
        },
        {
          id: 'canShareDashboards',
          name: 'Share Dashboards',
          description: 'Can share dashboards with other users',
          category: 'Dashboards'
        },
        {
          id: 'canExportDashboardData',
          name: 'Export Dashboard Data',
          description: 'Can export data from dashboards',
          category: 'Dashboards'
        },
        {
          id: 'canConfigureDashboardAlerts',
          name: 'Configure Dashboard Alerts',
          description: 'Can set up alerts and notifications for dashboards',
          category: 'Dashboards'
        },
        {
          id: 'canManageDashboardUsers',
          name: 'Manage Dashboard Users',
          description: 'Can manage user access to dashboards',
          category: 'Dashboards',
          critical: true,
          impacts: ['User Access', 'Data Security']
      }
    ];
    
    setPermissions(systemPermissions);
    
    // Don't load default roles immediately - let the database be the source of truth
    console.log('🚀 Starting role system initialization...');
    
    // Fetch data from APIs - prioritize database over defaults
    fetchRoles();
    fetchPermissionGroups();
    fetchUsers();
    fetchRoleTemplatesData();
  }, []);
  
  // Define fetch functions for the APIs
  const fetchRoles = async () => {
    setIsLoadingRoles(true);
    try {
      console.log('🔄 Attempting to fetch roles from database...');
      const response = await api.get('/roles');
      
      if (response.status === 200 && response.data) {
        const apiRoles = response.data.roles || response.data || [];
        
        if (Array.isArray(apiRoles) && apiRoles.length > 0) {
          // Successfully got roles from database
        const typedRoles: Role[] = apiRoles.map((role: any) => ({
          ...role,
            id: String(role.id), // Ensure ID is string for consistency
            category: role.category as 'system' | 'dashboard' | 'custom' || 'system',
            dashboardAccess: role.dashboardAccess || [],
            permissions: Array.isArray(role.permissions) ? role.permissions : []
        }));
          
                  setRoles(typedRoles);
          setIsLoadingRoles(false);
          console.log('✅ Successfully loaded', typedRoles.length, 'roles from database');
          console.log('Database roles:', typedRoles.map(r => ({ id: r.id, name: r.name })));
          return;
      } else {
          console.log('📝 Database is connected but no roles found - starting with empty role list');
          setRoles([]);
          setIsLoadingRoles(false);
          return;
        }
      } else {
        console.warn('⚠️ Invalid response format from roles API:', response.status);
        throw new Error(`Invalid response: ${response.status}`);
      }
    } catch (error: any) {
      console.error('❌ Failed to fetch roles from database:', error.message);
      
      // No fallback - just show empty state and let user create roles manually
      setRoles([]);
      setIsLoadingRoles(false);
        
      if (error.response?.status === 500) {
        toast.error('Database server error. Please try again.');
      } else if (!navigator.onLine) {
        toast.error('No internet connection. Please check your network.');
      }
      
      console.log('🚀 Starting with empty role list - you can create roles manually');
    }
  };
  
  const fetchPermissionGroups = async () => {
    try {
      const response = await api.get('/permission-groups');
      if (response.status === 200 && response.data) {
        const apiPermissionGroups = response.data.permissionGroups || [];
        setPermissionGroups(apiPermissionGroups);
      } else {
        throw new Error('Invalid response format from permission groups API');
      }
    } catch (error: any) {
      console.error('Failed to fetch permission groups:', error);
      
      // Try fallback endpoint with direct fetch (no auth needed)
      try {
        console.log('Attempting to use fallback permission groups endpoint');
        const fallbackResponse = await fetch('/api/permission-groups/fallback');
        
        if (fallbackResponse.ok) {
          const fallbackData = await fallbackResponse.json();
          const fallbackGroups = fallbackData.permissionGroups || [];
          setPermissionGroups(fallbackGroups);
          console.log('Permission groups loaded from fallback API:', fallbackGroups.length);
          return;
        } else {
          throw new Error(`Fallback endpoint returned ${fallbackResponse.status}`);
        }
      } catch (fallbackError) {
        console.error('Fallback permission groups endpoint also failed:', fallbackError);
      }
      
      // Use default permission groups as last resort
    }
  };
  
  const fetchRoleTemplatesData = async () => {
    try {
      const response = await api.get('/role-templates');
      if (response.status === 200 && response.data) {
        const apiTemplates = response.data.templates || [];
        setRoleTemplates(apiTemplates);
      } else {
        throw new Error('Invalid response format from role templates API');
    }
    } catch (error: any) {
      console.error('Failed to fetch role templates:', error);
      
      // Try fallback endpoint with direct fetch (no auth needed)
      try {
        console.log('Attempting to use fallback role templates endpoint');
        const fallbackResponse = await fetch('/api/role-templates/fallback');
        
        if (fallbackResponse.ok) {
          const fallbackData = await fallbackResponse.json();
          const fallbackTemplates = fallbackData.templates || [];
          setRoleTemplates(fallbackTemplates);
          console.log('Role templates loaded from fallback API:', fallbackTemplates.length);
          return;
        } else {
          throw new Error(`Fallback endpoint returned ${fallbackResponse.status}`);
        }
      } catch (fallbackError) {
        console.error('Fallback role templates endpoint also failed:', fallbackError);
      }
      
      // Use default templates as last resort
    }
  };
  
  
  
  const handleAddRole = async (role: Omit<Role, 'id' | 'createdAt'>) => {
    try {
      const response = await api.post('/roles', {
      ...role,
        category: role.category || 'system',
        dashboardAccess: role.dashboardAccess || []
      });
      
      if (response.status === 201 && response.data) {
        // Add the newly created role to the state
        const newRole = response.data;
    setRoles(prev => [...prev, newRole]);
    setShowRoleForm(false);
        toast.success('Role created successfully');
      } else {
        throw new Error('Failed to create role');
      }
    } catch (error) {
      console.error('Error creating role:', error);
      toast.error('Failed to create role. Please try again.');
    }
  };
  
  const handleUpdateRole = async (updatedRole: Role) => {
    try {
      const response = await api.put(`/roles/${updatedRole.id}`, updatedRole);
      
      if (response.status === 200 || response.status === 204) {
    setRoles(prev => prev.map(role => 
      role.id === updatedRole.id ? updatedRole : role
    ));
    setSelectedRole(null);
    setShowRoleForm(false);
        toast.success('Role updated successfully');
      } else {
        throw new Error('Failed to update role');
      }
    } catch (error) {
      console.error('Error updating role:', error);
      toast.error('Failed to update role. Please try again.');
    }
  };
  
  const handleDeleteRole = async () => {
    if (selectedRole) {
      try {
        const response = await api.delete(`/roles/${selectedRole.id}`);
        
        if (response.status === 200 || response.status === 204) {
      setRoles(prev => prev.filter(role => role.id !== selectedRole.id));
      setSelectedRole(null);
      setShowDeleteConfirm(false);
          toast.success('Role deleted successfully');
        } else {
          throw new Error('Failed to delete role');
        }
      } catch (error) {
        console.error('Error deleting role:', error);
        toast.error('Failed to delete role. Please try again.');
      }
    }
  };
  
  const handleUpdateUserRole = (userId: string, roleId: string) => {
    setUsers(prev => prev.map(user => 
      user.id === userId ? { ...user, role: roleId } : user
    ));
  };
  
  // Group permissions by category for the form
  const permissionsByCategory = permissions.reduce<Record<string, Permission[]>>((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {});
  
  // Clone an existing role
  const handleCloneRole = (sourceRole: Role) => {
    const newRole: Role = {
      ...sourceRole,
      id: `role-${Date.now()}`,
      name: `${sourceRole.name} (Copy)`,
      createdAt: new Date().toISOString(),
      userCount: 0
    };
    
    setRoles(prev => [...prev, newRole]);
    setSelectedRole(newRole);
    setShowRoleForm(true);
  };
  
  // Bulk assign roles to users
  const handleBulkAssign = async () => {
    if (bulkRoleId && selectedUsers.length > 0) {
      try {
        // Replace with your actual API endpoint
        const response = await fetch('/api/users/bulk-assign-roles', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userIds: selectedUsers,
            roleId: bulkRoleId
          }),
        });
        
        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }
        
        // Update local state after successful API call
        setUsers(prev => prev.map(user => 
        selectedUsers.includes(user.id) 
            ? { ...user, roles: [...(user.roles || []), bulkRoleId] } 
          : user
        ));
        
      setSelectedUsers([]);
      setShowBulkAssign(false);
        
        // Show success notification
        alert('Roles assigned successfully to selected users');
      } catch (error) {
        console.error('Failed to bulk assign roles:', error);
        alert('Failed to assign roles. Please try again.');
      }
    }
  };
  
  // Toggle user selection for bulk operations
  const toggleUserSelection = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };
  
  // Filter roles based on search
  const filteredRoles = roles.filter(role => 
    role.name.toLowerCase().includes(roleSearch.toLowerCase()) ||
    role.description.toLowerCase().includes(roleSearch.toLowerCase())
  );
  
  // Filter users based on search
  const filteredUsers = users.filter(user => 
    user.name.toLowerCase().includes(userSearch.toLowerCase()) ||
    user.email.toLowerCase().includes(userSearch.toLowerCase()) ||
    (user.department && user.department.toLowerCase().includes(userSearch.toLowerCase()))
  );
  
  // Get users for a specific role
  const getUsersForRole = (roleId: string) => {
    return users.filter(user => user.roles && user.roles.includes(roleId));
  };
  
  // Create a new role from a template
  const handleCreateFromTemplate = async (template: RoleTemplate) => {
    try {
      const newRole: Omit<Role, 'id' | 'createdAt' | 'userCount'> = {
      name: template.name,
      description: template.description,
      permissions: template.permissions,
        category: template.category === 'predefined' ? 'system' : 'custom'
      };
      
      const response = await api.post('/roles', newRole);
      
      if (response.status === 201 && response.data) {
        setRoles(prev => [...prev, response.data]);
        setSelectedRole(response.data);
    setShowTemplates(false);
        toast.success('Role created from template');
      } else {
        throw new Error('Failed to create role from template');
      }
    } catch (error) {
      console.error('Error creating role from template:', error);
      toast.error('Failed to create role. Please try again.');
    }
  };
  
  // Helper function to get icon component from name
  const getIconComponent = (iconName: string, className: string) => {
    switch (iconName) {
      case 'Shield': return <Shield className={className} />;
      case 'Users': return <Users className={className} />;
      case 'Monitor': return <Monitor className={className} />;
      case 'User': return <User className={className} />;
      case 'Star': return <Star className={className} />;
      case 'FileText': return <FileText className={className} />;
      default: return <FileText className={className} />;
    }
  };
  
  // Save current role as a template
  const handleSaveAsTemplate = async () => {
    if (selectedRole && templateName) {
      try {
        const newTemplate: Omit<RoleTemplate, 'id'> = {
        name: templateName,
        description: templateDescription || selectedRole.description,
        permissions: selectedRole.permissions,
        category: 'custom',
        iconName: 'Star',
        iconColor: 'text-yellow-500'
      };
      
        const response = await api.post('/role-templates', newTemplate);
        
        if (response.status === 201 && response.data) {
          setRoleTemplates(prev => [...prev, response.data]);
      setShowSaveTemplate(false);
      setTemplateName('');
      setTemplateDescription('');
          toast.success('Template saved successfully');
        } else {
          throw new Error('Failed to save template');
        }
      } catch (error) {
        console.error('Error saving template:', error);
        toast.error('Failed to save template. Please try again.');
      }
    }
  };
  
  // Delete a template
  const handleDeleteTemplate = async (templateId: string) => {
    try {
      const response = await api.delete(`/role-templates/${templateId}`);
      
      if (response.status === 200 || response.status === 204) {
    setRoleTemplates(prev => prev.filter(template => template.id !== templateId));
        toast.success('Template deleted successfully');
      } else {
        throw new Error('Failed to delete template');
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      toast.error('Failed to delete template. Please try again.');
    }
  };
  
  // Get all permissions for a role, including inherited permissions
  const getAllPermissionsForRole = (roleId: string): string[] => {
    const role = roles.find(r => r.id === roleId);
    if (!role) return [];
    
    // Get direct permissions
    const directPermissions = [...role.permissions];
    
    // Get parent permissions if there's a parent role
    if (role.parentId) {
      const parentPermissions = getAllPermissionsForRole(role.parentId);
      return [...new Set([...directPermissions, ...parentPermissions])];
    }
    
    return directPermissions;
  };
  
  // Check if a role has a specific permission, including through inheritance
  const roleHasPermission = (roleId: string, permissionId: string): boolean => {
    return getAllPermissionsForRole(roleId).includes(permissionId);
  };
  
  // Get all child roles for a given role
  const getChildRoles = (parentId: string): Role[] => {
    return roles.filter(role => role.parentId === parentId);
  };
  
  // Check if removing a permission would affect child roles
  const wouldAffectChildRoles = (roleId: string, permissionId: string): Role[] => {
    const childRoles = getChildRoles(roleId);
    return childRoles.filter(child => 
      !child.permissions.includes(permissionId) && roleHasPermission(child.id, permissionId)
    );
  };
  
  // Get roles by permission
  const getRolesByPermission = (permissionId: string): Role[] => {
    return roles.filter(role => role.permissions.includes(permissionId));
  };
  
  // Check if a permission is critical
  const isPermissionCritical = (permissionId: string): boolean => {
    const permission = permissions.find(p => p.id === permissionId);
    return permission?.critical || false;
  };
  
  // Get impact areas for a permission
  const getPermissionImpact = (permissionId: string): string[] => {
    const permission = permissions.find(p => p.id === permissionId);
    return permission?.impacts || [];
  };
  
  // Handle showing permission impact analysis
  const handleShowImpactAnalysis = (permission: Permission) => {
    setSelectedPermission(permission);
    setShowImpactAnalysis(true);
  };
  
  // Handle parent role selection during role editing
  const handleParentRoleChange = (parentId: string, roleId?: string) => {
    // Prevent circular references
    if (parentId && roleId) {
      let currentParent = parentId;
      while (currentParent) {
        const parent = roles.find(r => r.id === currentParent);
        if (parent?.parentId === roleId) {
          alert("Cannot create circular inheritance. Please select a different parent role.");
          return false;
        }
        currentParent = parent?.parentId || '';
      }
    }
    return true;
  };
  
  // Handle adding a temporary role assignment
  const handleAddTemporaryRole = () => {
    if (!tempRoleData.userId || !tempRoleData.roleId || !tempRoleData.expiresAt) {
      alert('Please select a user, role, and expiration date');
      return;
    }
    
    const newAssignment: UserRoleAssignment = {
      userId: tempRoleData.userId,
      roleId: tempRoleData.roleId,
      assignedAt: new Date().toISOString(),
      assignedBy: user?.id || 'system',
      expiresAt: new Date(tempRoleData.expiresAt).toISOString(),
      notes: tempRoleData.notes,
      projectId: tempRoleData.projectId,
      locationId: tempRoleData.locationId,
      departmentId: tempRoleData.departmentId
    };
    
    // Update the user's temporary roles
    setUsers(prev => prev.map(u => {
      if (u.id === tempRoleData.userId) {
        const updatedUser = { 
          ...u, 
          temporaryRoles: [...(u.temporaryRoles || []), newAssignment] 
        };
        return updatedUser;
      }
      return u;
    }));
    
    // Reset form
    setTempRoleData({
      userId: '',
      roleId: '',
      expiresAt: '',
      notes: '',
      projectId: '',
      locationId: '',
      departmentId: ''
    });
    
    setShowTempRoleForm(false);
  };
  
  // Remove a temporary role assignment
  const handleRemoveTemporaryRole = (userId: string, assignmentIndex: number) => {
    setUsers(prev => prev.map(u => {
      if (u.id === userId && u.temporaryRoles) {
        const tempRoles = [...u.temporaryRoles];
        tempRoles.splice(assignmentIndex, 1);
        return { ...u, temporaryRoles: tempRoles };
      }
      return u;
    }));
  };
  
  // Check if a temporary role is expired
  const isRoleExpired = (expiresAt: string): boolean => {
    return new Date(expiresAt) < new Date();
  };
  
  // Get active temporary roles for a user
  const getActiveTemporaryRoles = (userId: string): UserRoleAssignment[] => {
    const user = users.find(u => u.id === userId);
    if (!user || !user.temporaryRoles) return [];
    
    return user.temporaryRoles.filter(role => !isRoleExpired(role.expiresAt!));
  };
  
  // Handle creating a permission group
  const handleCreatePermissionGroup = (group: Omit<PermissionGroup, 'id' | 'createdAt' | 'createdBy'>) => {
    const newGroup: PermissionGroup = {
      ...group,
      id: `group-${Date.now()}`,
      createdAt: new Date().toISOString(),
      createdBy: user?.id || 'system',
      isCustom: true
    };
    
    setPermissionGroups(prev => [...prev, newGroup]);
    setShowPermissionGroupForm(false);
  };
  
  // Update permission group
  const handleUpdatePermissionGroup = (updatedGroup: PermissionGroup) => {
    setPermissionGroups(prev => prev.map(group => 
      group.id === updatedGroup.id ? updatedGroup : group
    ));
    
    setSelectedPermissionGroup(null);
    setShowPermissionGroupForm(false);
  };
  
  // Delete permission group
  const handleDeletePermissionGroup = (groupId: string) => {
    setPermissionGroups(prev => prev.filter(g => g.id !== groupId));
  };
  
  // Move this function just after the setUsers declaration inside the RolesAndPermissions component:
  const handleUpdateUserRoles = async (userId: string, roleIds: string[]) => {
    // Prevent multiple simultaneous requests for the same user
    if (updatingUserIds.has(userId)) {
      console.log('⚠️ Update already in progress for user:', userId);
      return;
    }
    
    try {
      console.log('=== ROLE ASSIGNMENT DEBUG ===');
      console.log('Updating user roles for:', userId, 'with roles:', roleIds);
      console.log('Number of roles being assigned:', roleIds.length);
      console.log('Is this an unselection operation?', roleIds.length === 0);
      console.log('Current user data before update:', users.find(u => u.id === userId));
      
      // Set loading state
      setUpdatingUserIds(prev => new Set([...prev, userId]));
      setIsUpdatingRoles(true);
      
      // Check if we have authentication
      const token = localStorage.getItem('authToken');
      if (!token) {
        console.error('❌ No authentication token found');
        return;
      }
      console.log('✅ Authentication token found:', token.substring(0, 20) + '...');
      
      // Validate input data
      if (!userId || !Array.isArray(roleIds)) {
        throw new Error('Invalid input: userId and roleIds are required');
      }
      
      // Remove duplicates from roleIds
      const uniqueRoleIds = [...new Set(roleIds)];
      console.log('Deduplicated role IDs:', uniqueRoleIds);
      
      // Log the exact payload being sent
      const roleString = uniqueRoleIds.length > 0 ? uniqueRoleIds.join(',') + ',' : ',';
      console.log('Role string being sent to API:', JSON.stringify(roleString));
      console.log('Will this trigger role assignment path?', roleString.includes(','));
      
      // Add delay to prevent rapid-fire requests
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Get the current user data to include existing fields
      const currentUser = users.find(u => u.id === userId);
      if (!currentUser) {
        throw new Error('User not found in current user list');
      }
      
      // Use the API service with proper authentication instead of direct fetch
      const response = await api.put(`/users/${userId}`, {
        // Include existing user data to satisfy backend validation
        name: currentUser.name,
        email: currentUser.email,
        department: currentUser.department || 'IT',
        // CRITICAL FIX: Always include comma to trigger role assignment path
        // Empty roles: "," -> splits to [] after filter(Boolean) 
        // With roles: "role1,role2," -> normal processing
        role: roleString
      });
      
      console.log('✅ Role assignment response:', response.status);
      console.log('Response data:', response.data);
      
      if (response.status === 200 || response.status === 204) {
        toast.success('User roles updated successfully');
        
        // Add a small delay to ensure database write has completed
        await new Promise(resolve => setTimeout(resolve, 500));
        await fetchUsers();
        await fetchRoles();
        
      } else {
        console.error('❌ API error response:', response.status, response.statusText);
        throw new Error(`Failed to update user roles: HTTP ${response.status}`);
      }
    } catch (error: any) {
      console.error('❌ Failed to update user roles:', error);
      
      if (error.response?.status === 401) {
        toast.error('Authentication failed. Please log in again.');
      } else if (error.response?.status === 403) {
        toast.error('Access denied. You do not have permission to assign roles.');
      } else if (error.response?.status === 500) {
        toast.error('Server error. Please try again.');
      } else if (!navigator.onLine) {
        toast.error('No internet connection. Please check your network.');
      } else {
        toast.error('Failed to update user roles. Please try again.');
      }
      
      // Log full error for debugging
      console.error('Full error object:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        config: error.config
      });
    } finally {
      // Always clean up loading states
      setUpdatingUserIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
      setIsUpdatingRoles(false);
    }
  };
  
  // Add this function to fetch users from your API
  const fetchUsers = async () => {
    setIsLoadingUsers(true);
    setUserError(null);
    
    try {
      // Call the same API endpoint that UserManagement.tsx uses
      const response = await api.get('/users');
      
      if (response.status === 200 && response.data) {
        // Map the API user data to the format expected by this component
        const apiUsers = response.data.users || [];
        
        const mappedUsers = apiUsers.map((apiUser: any) => ({
          id: apiUser.id,
          name: apiUser.name,
          email: apiUser.email,
          department: apiUser.department,
          status: apiUser.isActive ? 'active' : 'inactive',
          // Use the roles array from the backend (already mapped to frontend IDs)
          roles: Array.isArray(apiUser.roles) ? apiUser.roles : (apiUser.role ? [apiUser.role] : []),
          project: apiUser.project || '',
          location: apiUser.location || '',
          // Store role assignments for reference if needed
          roleAssignments: apiUser.roleAssignments || []
        }));
        
        setUsers(mappedUsers);
        console.log(`Loaded ${mappedUsers.length} users from API with roles:`, mappedUsers);
      } else {
        throw new Error('Invalid response format from users API');
      }
    } catch (error: any) {
      console.error('Failed to fetch users:', error);
      setUserError('Failed to load users. Please try again.');
      
      // Don't add mock data fallback as requested by the user
    } finally {
      setIsLoadingUsers(false);
    }
  };
  
  // Add after the existing user-related functions
  // Handle adding a new user manually with role assignments
  const handleManualRoleAssignment = async () => {
    if (selectedUserIds.length === 0) {
      toast.error('Please select at least one user');
      return;
    }

    if (assignmentRoles.length === 0) {
      toast.error('Please select at least one role to assign');
      return;
    }

    // Prevent multiple simultaneous bulk assignments
    if (isUpdatingRoles) {
      return;
    }

    try {
      setIsUpdatingRoles(true);
      console.log('=== BULK ROLE ASSIGNMENT STARTED ===');
      console.log('Selected users:', selectedUserIds);
      console.log('Roles to assign:', assignmentRoles);
      
      // Add all users to updating set
      setUpdatingUserIds(prev => new Set([...prev, ...selectedUserIds]));
      
      // Add delay to prevent rapid-fire requests
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Create an array of promises for each user update using the API service with authentication
      const updatePromises = selectedUserIds.map(async (userId, index) => {
        try {
          // Stagger requests to prevent overwhelming the server
          await new Promise(resolve => setTimeout(resolve, index * 100));
          
          const user = users.find(u => u.id === userId);
          if (!user) {
            console.warn(`User not found: ${userId}`);
            return { userId, success: false, error: 'User not found' };
          }
          
          // Merge existing roles with newly assigned roles
          const currentRoles = user.roles || [];
          const updatedRoles = [...new Set([...currentRoles, ...assignmentRoles])];
          
          console.log(`Updating user ${user.name} (${userId}) with roles:`, updatedRoles);
          
          // Get dashboard roles
          const dashboardRoles = roles.filter(r => 
            assignmentRoles.includes(r.id) && 
            r.category === 'dashboard' && 
            r.dashboardAccess && 
            r.dashboardAccess.length > 0
          );
          
          // Process dashboard access separately if needed
          if (dashboardRoles.length > 0) {
            console.log(`Assigning dashboard roles to user ${userId}:`, dashboardRoles);
          }
          
          // Use the API service with proper authentication
          const response = await api.put(`/users/${userId}`, {
            // Include existing user data to satisfy backend validation
            name: user.name,
            email: user.email,
            department: user.department || 'IT',
            // CRITICAL FIX: Always include comma to trigger role assignment path
            role: updatedRoles.length > 0 ? updatedRoles.join(',') + ',' : ','
          });
          
          return { 
            userId, 
            success: true, 
            data: response.data,
            userName: user.name 
          };
        } catch (error: any) {
          console.error(`Failed to update user ${userId}:`, error);
          return { 
            userId, 
            success: false, 
            error: error.message || 'Unknown error',
            status: error.response?.status 
          };
        }
      });
      
      // Wait for all updates to complete
      console.log('⏳ Waiting for all role assignments to complete...');
      const results = await Promise.all(updatePromises);
      
      // Analyze results
      const successful = results.filter(r => r.success);
      const failed = results.filter(r => !r.success);
      
      console.log('✅ Successful assignments:', successful.length);
      console.log('❌ Failed assignments:', failed.length);
      
      // Check if any roles were saved to database
      const savedToDatabase = successful.some(result => result.data?.savedToDatabase);
      const fallbackMode = successful.some(result => result.data?.fallback);
      
      // Reset form state
      setSelectedUserIds([]);
      setAssignmentRoles([]);
      setShowAssignRolesModal(false);
      
      // Show appropriate success/error messages
      if (successful.length > 0) {
        toast.success(`Roles assigned to ${successful.length} user(s)`);
      }
      
      if (failed.length > 0) {
        toast.error(`Failed to assign roles to ${failed.length} user(s)`);
      }
      
      await new Promise(resolve => setTimeout(resolve, 500));
      await fetchUsers();
      await fetchRoles();
      
    } catch (error: any) {
      console.error('❌ Bulk role assignment failed:', error);
      toast.error('Failed to assign roles. Please try again.');
    } finally {
      // Always clean up loading states
      setIsUpdatingRoles(false);
      setUpdatingUserIds(new Set());
    }
  };
  
  return (
    <div className="space-y-6">
      <Toaster position="top-right" />
      
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4 w-full">
          <div className="flex bg-gray-100 rounded-lg overflow-hidden shadow-sm">
            <button
              onClick={() => setActiveTab('roles')}
              className={`flex items-center gap-1 px-4 py-2 font-medium text-sm transition-all duration-150 h-10 ${
                activeTab === 'roles' ? 'bg-blue-500 text-white shadow' : 'text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Shield className="h-4 w-4" />
              Roles
            </button>
            <button
              onClick={() => setActiveTab('users')}
              className={`flex items-center gap-1 px-4 py-2 font-medium text-sm transition-all duration-150 h-10 ${
                activeTab === 'users' ? 'bg-blue-500 text-white shadow' : 'text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Users className="h-4 w-4" />
              User Assignments
            </button>
            <button
              onClick={() => setActiveTab('groups')}
              className={`flex items-center gap-1 px-4 py-2 font-medium text-sm transition-all duration-150 h-10 ${
                activeTab === 'groups' ? 'bg-blue-500 text-white shadow' : 'text-gray-700 hover:bg-gray-200'
              }`}
            >
              <CheckSquare className="h-4 w-4" />
              Permission Groups
            </button>
            <button
              onClick={() => setActiveTab('jit')}
              className={`flex items-center gap-1 px-4 py-2 font-medium text-sm transition-all duration-150 h-10 ${
                activeTab === 'jit' ? 'bg-blue-500 text-white shadow' : 'text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Lock className="h-4 w-4" />
              JIT Access
            </button>
            <button
              onClick={() => setActiveTab('analytics')}
              className={`flex items-center gap-1 px-4 py-2 font-medium text-sm transition-all duration-150 h-10 ${
                activeTab === 'analytics' ? 'bg-blue-500 text-white shadow' : 'text-gray-700 hover:bg-gray-200'
              }`}
            >
              <BarChart2 className="h-4 w-4" />
              Analytics
            </button>
          </div>
          <div className="flex gap-2 ml-4">
            {activeTab === 'roles' && (
              <>
                <button
                  onClick={() => setShowTemplates(true)}
                  className="flex items-center gap-2 px-4 py-2 h-10 bg-violet-500 text-white font-semibold rounded-lg shadow hover:bg-violet-600 transition-all duration-150"
                  title="Use role templates"
                >
                  <FileText className="h-4 w-4" />
                  Templates
                </button>
                <button
                  onClick={() => {
                    setSelectedRole(null);
                    setShowRoleForm(true);
                  }}
                  className="flex items-center gap-2 px-4 py-2 h-10 bg-blue-500 text-white font-semibold rounded-lg shadow hover:bg-blue-600 transition-all duration-150"
                >
                  <Plus className="h-4 w-4" />
                  Add Role
                </button>
              </>
            )}
          </div>
        </div>
      </div>
      
      {activeTab === 'roles' && (
        <div className="mb-4">
          <div className="flex border-b">
            <button
              onClick={() => setActiveView('list')}
              className={`py-2 px-4 font-medium text-sm ${
                activeView === 'list' 
                  ? 'text-blue-600 border-b-2 border-blue-600' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              List View
            </button>
            <button
              onClick={() => setActiveView('hierarchy')}
              className={`py-2 px-4 font-medium text-sm ${
                activeView === 'hierarchy' 
                  ? 'text-blue-600 border-b-2 border-blue-600' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Hierarchy View
            </button>
            <button
              onClick={() => setActiveView('impact')}
              className={`py-2 px-4 font-medium text-sm ${
                activeView === 'impact' 
                  ? 'text-blue-600 border-b-2 border-blue-600' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Impact Analysis
            </button>
          </div>
        </div>
      )}
      
      {activeTab === 'roles' ? (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {activeView === 'list' ? (
            <>
              {/* Roles List */}
              <div className="lg:col-span-1 bg-white p-4 rounded-lg shadow-sm space-y-4">
                <div className="flex justify-between items-center border-b pb-2">
                  <h3 className="font-semibold text-gray-800">Defined Roles</h3>
                  <div className="relative">
                    <Search className="h-4 w-4 absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search roles..."
                      value={roleSearch}
                      onChange={(e) => setRoleSearch(e.target.value)}
                      className="pl-8 pr-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                </div>
                <div className="space-y-2 max-h-[calc(100vh-320px)] overflow-y-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
                  {isLoadingRoles ? (
                    <div className="flex flex-col items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-3"></div>
                      <p className="text-gray-500 text-sm">Loading roles from database...</p>
                    </div>
                  ) : filteredRoles.length === 0 ? (
                    <div className="text-center py-8">
                      <Shield className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-gray-500">No roles found</p>
                      <p className="text-sm text-gray-500 mt-1">Create your first role to get started</p>
                    </div>
                  ) : (
                    filteredRoles.map(role => (
                    <div 
                      key={role.id}
                      className={`p-3 rounded-lg cursor-pointer flex justify-between items-center ${
                        selectedRole?.id === role.id ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50 hover:bg-gray-100'
                      }`}
                      onClick={() => setSelectedRole(role)}
                    >
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-gray-200 rounded-full">
                          <Shield className="h-5 w-5 text-gray-700" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{role.name}</h4>
                          <p className="text-xs text-gray-500">{role.userCount} users</p>
                        </div>
                      </div>
                      <div className="flex gap-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            const roleUsers = getUsersForRole(role.id);
                            setSelectedRole(role);
                            if (roleUsers.length > 0) {
                              setShowRoleUsers(true);
                            }
                          }}
                          className="p-1 text-gray-600 hover:bg-gray-200 rounded"
                          title="View users with this role"
                        >
                          <Users className="h-4 w-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCloneRole(role);
                          }}
                          className="p-1 text-indigo-600 hover:bg-indigo-50 rounded"
                          title="Clone role"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedRole(role);
                            setShowDeleteConfirm(true);
                          }}
                          className="p-1 text-red-600 hover:bg-red-50 rounded"
                          title="Delete role"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                    ))
                  )}
                </div>
              </div>
              
              {/* Role Details */}
              <div className="lg:col-span-2 bg-white p-4 rounded-lg shadow-sm">
                {selectedRole ? (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-blue-100 rounded-full">
                          <Shield className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-bold text-gray-900">{selectedRole.name}</h3>
                          <p className="text-sm text-gray-600">{selectedRole.description}</p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <button
                          onClick={() => setShowSaveTemplate(true)}
                          className="px-3 py-1 bg-green-100 text-green-700 font-medium rounded hover:bg-green-200 flex items-center gap-1"
                          title="Save as template"
                        >
                          <Save className="h-3 w-3" />
                          Template
                        </button>
                        <button
                          onClick={() => setShowRoleForm(true)}
                          className="px-3 py-1 bg-blue-100 text-blue-700 font-medium rounded hover:bg-blue-200"
                        >
                          Edit
                        </button>
                      </div>
                    </div>
                    
                    <div className="border-t pt-3">
                      <h4 className="font-medium text-gray-900 mb-3">Permissions</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => (
                          <div key={category} className="bg-gray-50 p-3 rounded-lg">
                            <h5 className="font-medium text-gray-900 mb-2">{category}</h5>
                            <div className="space-y-2">
                              {categoryPermissions.map(permission => (
                                <div key={permission.id} className="flex items-center gap-2">
                                  <div className={`p-1 rounded ${
                                    selectedRole.permissions.includes(permission.id)
                                      ? 'text-green-600'
                                      : 'text-gray-400'
                                  }`}>
                                    {selectedRole.permissions.includes(permission.id) ? (
                                      <CheckSquare className="h-5 w-5" />
                                    ) : (
                                      <Lock className="h-5 w-5" />
                                    )}
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium">{permission.name}</p>
                                    <p className="text-xs text-gray-500">{permission.description}</p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div className="border-t pt-3">
                      <h4 className="font-medium text-gray-900 mb-2">Role Details</h4>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <p className="text-sm text-gray-500">Created</p>
                          <p className="text-sm">{new Date(selectedRole.createdAt).toLocaleDateString()}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Last Updated</p>
                          <p className="text-sm">{selectedRole.updatedBy || 'Never'}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Active Users</p>
                          <p className="text-sm">{selectedRole.userCount || 0}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-64 text-center">
                    <Shield className="h-16 w-16 text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-1">No Role Selected</h3>
                    <div className="space-y-2 text-gray-500 max-w-md">
                      <p>
                        Select a role from the list to view its details and permissions, or create a new role.
                      </p>
                      <div className="flex justify-center gap-3 pt-4">
                        <button
                          onClick={() => setShowTemplates(true)}
                          className="px-3 py-2 bg-indigo-100 text-indigo-700 font-medium rounded hover:bg-indigo-200 flex items-center gap-2"
                        >
                          <FileText className="h-4 w-4" />
                          Use Template
                        </button>
                        <button
                          onClick={() => setShowRoleForm(true)}
                          className="px-3 py-2 bg-blue-100 text-blue-700 font-medium rounded hover:bg-blue-200 flex items-center gap-2"
                        >
                          <Plus className="h-4 w-4" />
                          Create New Role
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </>
          ) : activeView === 'hierarchy' ? (
            <div className="lg:col-span-3 bg-white p-4 rounded-lg shadow-sm">
              <div className="mb-4 flex justify-between items-center">
                <h3 className="font-semibold text-gray-800">Role Hierarchy</h3>
                <button
                  onClick={() => setShowHierarchyEdit(true)}
                  className="px-3 py-1 bg-blue-100 text-blue-700 font-medium rounded hover:bg-blue-200 flex items-center gap-1"
                >
                  <Edit className="h-3 w-3" />
                  Edit Hierarchy
                </button>
              </div>
              
              <div className="relative overflow-x-auto border rounded-lg my-4 min-h-[400px]">
                {/* Hierarchy Visualization */}
                <div className="p-6 flex flex-col items-center">
                  {/* Root-level roles */}
                  <div className="flex flex-wrap justify-center gap-8 mb-8">
                    {roles.filter(role => !role.parentId).map(role => (
                      <div key={role.id} className="flex flex-col items-center">
                        <div 
                          className="p-3 rounded-lg border-2 border-blue-500 bg-blue-50 flex flex-col items-center cursor-pointer"
                          onClick={() => setSelectedRole(role)}
                        >
                          <Shield className="h-6 w-6 text-blue-600 mb-1" />
                          <h4 className="font-medium text-gray-900">{role.name}</h4>
                          <p className="text-xs text-gray-500 mt-1">{role.permissions.length} direct permissions</p>
                        </div>
                        
                        {/* Child roles */}
                        {getChildRoles(role.id).length > 0 && (
                          <>
                            <div className="w-px h-8 bg-gray-300"></div>
                            <div className="flex flex-wrap justify-center gap-4">
                              {getChildRoles(role.id).map(childRole => (
                                <div key={childRole.id} className="flex flex-col items-center">
                                  <div 
                                    className="p-3 rounded-lg border border-indigo-400 bg-indigo-50 flex flex-col items-center cursor-pointer w-36"
                                    onClick={() => setSelectedRole(childRole)}
                                  >
                                    <User className="h-5 w-5 text-indigo-600 mb-1" />
                                    <h5 className="font-medium text-gray-900 text-sm text-center">{childRole.name}</h5>
                                    <div className="flex items-center text-xs text-gray-500 mt-1">
                                      <span>{childRole.permissions.length} direct</span>
                                      <span className="mx-1">+</span>
                                      <span>{getAllPermissionsForRole(childRole.id).length - childRole.permissions.length} inherited</span>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </>
                        )}
                      </div>
                    ))}
                  </div>
                  
                  {roles.filter(role => !role.parentId).length === 0 && (
                    <div className="text-center p-8">
                      <p className="text-gray-500">No role hierarchy defined yet.</p>
                      <p className="text-sm text-gray-500 mt-1">
                        Edit role hierarchy to establish parent-child relationships between roles.
                      </p>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">About Role Inheritance</h4>
                <ul className="text-sm text-gray-600 space-y-2 list-disc pl-4">
                  <li>Child roles automatically inherit all permissions from their parent roles.</li>
                  <li>A role can have only one parent but multiple children.</li>
                  <li>Inheritance allows you to create role hierarchies that reflect your organization's structure.</li>
                  <li>When a parent role's permissions change, all child roles immediately inherit those changes.</li>
                </ul>
              </div>
            </div>
          ) : (
            <div className="lg:col-span-3 bg-white p-4 rounded-lg shadow-sm">
              <div className="mb-4">
                <h3 className="font-semibold text-gray-800">Permission Impact Analysis</h3>
                <p className="text-sm text-gray-600">
                  Understand which areas of the application are affected by each permission
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="col-span-1 border rounded-lg p-3">
                  <div className="flex justify-between items-center border-b pb-2 mb-2">
                    <h4 className="font-medium text-gray-900">Permissions</h4>
                    <div className="relative">
                      <Search className="h-4 w-4 absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search permissions..."
                        className="pl-8 pr-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  
                  <div className="max-h-[500px] overflow-y-auto space-y-2">
                    {Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => (
                      <div key={category} className="mb-3">
                        <h5 className="text-sm font-semibold text-gray-700 mb-1">{category}</h5>
                        {categoryPermissions.map(permission => (
                          <div 
                            key={permission.id}
                            className={`p-2 rounded-md text-sm cursor-pointer flex items-start ${
                              selectedPermission?.id === permission.id 
                                ? 'bg-blue-50 border border-blue-200' 
                                : 'hover:bg-gray-50'
                            }`}
                            onClick={() => setSelectedPermission(permission)}
                          >
                            <div className="flex-1">
                              <div className="flex items-center">
                                <span className="font-medium">{permission.name}</span>
                                {permission.critical && (
                                  <span className="ml-2 px-1.5 py-0.5 text-xs bg-red-100 text-red-800 rounded-full">
                                    Critical
                                  </span>
                                )}
                              </div>
                              <p className="text-xs text-gray-500 mt-0.5">{permission.description}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="col-span-2 border rounded-lg p-3">
                  {selectedPermission ? (
                    <div>
                      <div className="flex justify-between items-center border-b pb-3 mb-3">
                        <div>
                          <h4 className="font-medium text-gray-900 flex items-center">
                            {selectedPermission.name}
                            {selectedPermission.critical && (
                              <span className="ml-2 px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full flex items-center">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                Critical
                              </span>
                            )}
                          </h4>
                          <p className="text-sm text-gray-600">{selectedPermission.description}</p>
                        </div>
                        <div className="text-sm text-gray-500">
                          {getRolesByPermission(selectedPermission.id).length} roles use this permission
                        </div>
                      </div>
                      
                      <div className="mb-4">
                        <h5 className="text-sm font-semibold text-gray-700 mb-2">Impact Areas</h5>
                        
                        {selectedPermission.impacts && selectedPermission.impacts.length > 0 ? (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            {selectedPermission.impacts.map((impact, index) => (
                              <div 
                                key={index}
                                className="bg-gray-50 border border-gray-200 rounded-lg p-3 flex items-center"
                              >
                                <div className="p-2 bg-blue-100 rounded-full mr-3">
                                  <Monitor className="h-4 w-4 text-blue-600" />
                                </div>
                                <div className="text-sm">{impact}</div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-500">No specific impact areas defined for this permission.</p>
                        )}
                      </div>
                      
                      <div className="mb-4">
                        <h5 className="text-sm font-semibold text-gray-700 mb-2">Roles with this Permission</h5>
                        
                        {getRolesByPermission(selectedPermission.id).length > 0 ? (
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                            {getRolesByPermission(selectedPermission.id).map(role => (
                              <div 
                                key={role.id}
                                className="bg-gray-50 border border-gray-200 rounded-lg p-2 text-sm cursor-pointer hover:bg-gray-100"
                                onClick={() => {
                                  setSelectedRole(role);
                                  setActiveView('list');
                                }}
                              >
                                <div className="font-medium">{role.name}</div>
                                <div className="text-xs text-gray-500">{role.userCount || 0} users</div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-500">No roles are currently using this permission.</p>
                        )}
                      </div>
                      
                      {selectedPermission.critical && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                          <h5 className="text-sm font-semibold text-yellow-800 flex items-center mb-1">
                            <AlertCircle className="h-4 w-4 mr-1" />
                            Critical Permission Warning
                          </h5>
                          <p className="text-sm text-yellow-700">
                            This is a critical permission that provides significant system access or capabilities.
                            Assigning this permission should be limited to trusted roles and users only.
                          </p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-64 text-center">
                      <Shield className="h-12 w-12 text-gray-300 mb-3" />
                      <h3 className="text-lg font-medium text-gray-900 mb-1">Select a Permission</h3>
                      <p className="text-gray-500 max-w-md">
                        Choose a permission from the list to see its impact analysis, including affected areas
                        of the application and which roles use it.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <div className="flex justify-between items-center border-b pb-2 mb-4">
            <h3 className="font-semibold text-gray-800">User Role Assignments</h3>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="h-4 w-4 absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search users..."
                  value={userSearch}
                  onChange={(e) => setUserSearch(e.target.value)}
                  className="pl-8 pr-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
              <button
                onClick={() => setShowBulkAssign(true)}
                className="px-3 py-1 bg-blue-500 text-white text-sm font-medium rounded hover:bg-blue-600 flex items-center gap-1"
                disabled={users.length === 0}
              >
                <Users className="h-3 w-3" />
                Bulk Assign
              </button>
              <button
                onClick={() => setShowAssignRolesModal(true)}
                className="px-3 py-1 bg-purple-500 text-white text-sm font-medium rounded hover:bg-purple-600 flex items-center gap-1"
              >
                <Shield className="h-3 w-3 mr-1" />
                <User className="h-3 w-3" />
                Assign Roles
              </button>
            </div>
          </div>
          
          {isLoadingUsers ? (
            <div className="flex justify-center items-center py-10">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-2 text-gray-500">Loading users...</span>
            </div>
          ) : userError ? (
            <div className="text-center py-10">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-3" />
              <p className="text-red-500">{userError}</p>
              <div className="flex justify-center gap-4 mt-6">
                <button
                  onClick={() => setShowAssignRolesModal(true)}
                  className="px-4 py-2 bg-purple-500 text-white font-semibold rounded-lg shadow hover:bg-purple-600 transition-all duration-150 flex items-center"
                >
                  <User className="h-4 w-4 mr-2" />
                  <Plus className="h-4 w-4 mr-2" />
                  Manually Assign Roles
                </button>
              </div>
            </div>
          ) : users.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-2 py-3 text-left">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={selectedUsers.length > 0 && selectedUsers.length === users.length}
                          onChange={() => {
                            if (selectedUsers.length === users.length) {
                              setSelectedUsers([]);
                            } else {
                              setSelectedUsers(users.map(u => u.id));
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </div>
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Department
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Assigned Roles
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredUsers.map(userData => (
                    <tr key={userData.id} className={selectedUsers.includes(userData.id) ? 'bg-blue-50' : ''}>
                      <td className="px-2 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedUsers.includes(userData.id)}
                          onChange={() => toggleUserSelection(userData.id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center">
                            <User className="h-4 w-4 text-gray-600" />
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900">{userData.name}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{userData.email}</div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{userData.department || '-'}</div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          userData.status === 'active' ? 'bg-green-100 text-green-800' :
                          userData.status === 'inactive' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {userData.status}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex flex-wrap gap-1">
                          {userData.roles && userData.roles.length > 0 ? (
                            userData.roles.map(roleId => {
                              // Handle both string and numeric role ID matching
                              let role = roles.find(r => r.id === roleId);
                              
                              // If not found with direct match, try mapping string IDs to numeric IDs
                              if (!role && typeof roleId === 'string') {
                                // Create mapping from frontend string IDs to backend numeric IDs
                                const stringToNumericIdMap: Record<string, number> = {
                                  'role-1': 1,   // System Administrator
                                  'role-2': 2,   // HR-Users  
                                  'role-3': 4,   // IT-Staff
                                  'role-4': 6,   // IT Administrator
                                  'role-5': 8,   // Department Head
                                  'role-6': 9,   // Employee
                                  'role-7': 30   // Dashboard Manager
                                };
                                
                                const numericId = stringToNumericIdMap[roleId];
                                if (numericId) {
                                  role = roles.find(r => r.id === String(numericId)); // Convert number to string for comparison
                                }
                              }
                              
                              // If still not found, try parsing roleId as number and convert to string
                              if (!role && !isNaN(parseInt(roleId.toString()))) {
                                const numericRoleId = parseInt(roleId.toString());
                                role = roles.find(r => r.id === String(numericRoleId)); // Convert number to string for comparison
                              }
                              
                              return role ? (
                                <span key={roleId} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                                  {role.name}
                                </span>
                              ) : (
                                <span key={roleId} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                                  Unknown Role ({roleId})
                                </span>
                              );
                            })
                          ) : (
                            <span className="text-gray-400 text-xs">No roles assigned</span>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button
                          className={`px-2 py-1 rounded text-xs font-medium transition-all duration-150 ${
                            updatingUserIds.has(userData.id)
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                          }`}
                          disabled={updatingUserIds.has(userData.id)}
                          onClick={() => {
                            if (updatingUserIds.has(userData.id)) return;
                            
                            console.log('=== EDIT ROLES CLICKED ===');
                            console.log('User data:', userData);
                            console.log('User roles (raw):', userData.roles);
                            console.log('User roles type:', typeof userData.roles);
                            console.log('Is array:', Array.isArray(userData.roles));
                            console.log('Available roles:', roles.map(r => ({ id: r.id, name: r.name, type: typeof r.id })));
                            
                            setEditingUser(userData);
                            const userRoles = Array.isArray(userData.roles) ? userData.roles : [];
                            console.log('Setting editing roles to:', userRoles);
                            setEditingRoles(userRoles);
                          }}
                        >
                          {updatingUserIds.has(userData.id) ? (
                            <div className="flex items-center gap-1">
                              <div className="animate-spin rounded-full h-3 w-3 border-b border-gray-400"></div>
                              <span>Updating...</span>
                            </div>
                          ) : (
                            'Edit Roles'
                          )}
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-10">
              <User className="h-12 w-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">No users found in the system.</p>
              <p className="text-sm text-gray-500 mt-1">Connect to your user management system to assign roles or add them manually.</p>
              <div className="flex justify-center gap-4 mt-6">
                <button
                  onClick={() => setShowAssignRolesModal(true)}
                  className="px-4 py-2 bg-purple-500 text-white font-semibold rounded-lg shadow hover:bg-purple-600 transition-all duration-150 flex items-center"
                >
                  <User className="h-4 w-4 mr-2" />
                  <Plus className="h-4 w-4 mr-2" />
                  Manually Assign Roles
                </button>
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Role Form Modal */}
      {showRoleForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl m-4 p-6 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900">
                {selectedRole ? 'Edit Role' : 'Create New Role'}
              </h3>
              <button 
                onClick={() => {
                  setShowRoleForm(false);
                }}
                className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <RoleForm 
              role={selectedRole} 
              permissions={permissions} 
              permissionsByCategory={permissionsByCategory}
              roles={roles}
              onSave={selectedRole ? handleUpdateRole : handleAddRole} 
              onCancel={() => setShowRoleForm(false)} 
            />
          </div>
        </div>
      )}
      
      {/* Delete Confirmation */}
      {showDeleteConfirm && selectedRole && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md m-4 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-red-100 rounded-lg">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900">Delete Role</h3>
            </div>
            <p className="text-gray-600 mb-2">
              Are you sure you want to delete the role "{selectedRole.name}"?
            </p>
            {selectedRole.userCount && selectedRole.userCount > 0 ? (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-6">
                <p className="text-sm text-yellow-800 font-medium">
                  Warning: This role is currently assigned to {selectedRole.userCount} users.
                  Deleting it will remove these permissions from those users.
                </p>
              </div>
            ) : (
              <p className="text-gray-600 mb-6">This action cannot be undone.</p>
            )}
            
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 font-medium rounded-lg hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteRole}
                className="flex items-center gap-2 px-4 py-2 bg-red-500 text-white font-medium rounded-lg hover:bg-red-600"
              >
                <Trash2 className="h-5 w-5" />
                Delete Role
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Add users with role modal */}
      {showRoleUsers && selectedRole && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl m-4 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900">
                Users with {selectedRole.name} Role
              </h3>
              <button 
                onClick={() => setShowRoleUsers(false)}
                className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            {getUsersForRole(selectedRole.id).length > 0 ? (
              <div className="overflow-y-auto max-h-[60vh]">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Email
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Department
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {getUsersForRole(selectedRole.id).map(userData => (
                      <tr key={userData.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                              <User className="h-5 w-5 text-gray-600" />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{userData.name}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{userData.email}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{userData.department || '-'}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            userData.status === 'active' ? 'bg-green-100 text-green-800' :
                            userData.status === 'inactive' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {userData.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-10">
                <Users className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500">No users have been assigned this role yet.</p>
              </div>
            )}
            
            <div className="flex justify-end mt-6">
              <button
                onClick={() => setShowRoleUsers(false)}
                className="px-4 py-2 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Bulk assign role modal */}
      {showBulkAssign && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md m-4 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900">
                Bulk Assign Role
              </h3>
              <button 
                onClick={() => setShowBulkAssign(false)}
                className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-4">
                Select a role to assign to {selectedUsers.length} user{selectedUsers.length !== 1 ? 's' : ''}
              </p>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Role
                </label>
                <select
                  value={bulkRoleId}
                  onChange={(e) => setBulkRoleId(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-2"
                >
                  <option value="">Select a role</option>
                  {roles.map(role => (
                    <option key={role.id} value={role.id}>{role.name}</option>
                  ))}
                </select>
              </div>
              
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-sm text-blue-800">
                  <strong>Note:</strong> This will override any existing role assignments for the selected users.
                </p>
              </div>
            </div>
            
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowBulkAssign(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 font-medium rounded-lg hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={handleBulkAssign}
                disabled={!bulkRoleId || selectedUsers.length === 0}
                className={`px-4 py-2 font-medium rounded-lg flex items-center gap-2 
                  ${!bulkRoleId || selectedUsers.length === 0 
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                    : 'bg-blue-500 text-white hover:bg-blue-600'}`}
              >
                <CheckCircle className="h-4 w-4" />
                Assign Role
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Templates Modal */}
      {showTemplates && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl m-4 p-6 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900">
                Role Templates
              </h3>
              <button 
                onClick={() => setShowTemplates(false)}
                className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <p className="text-sm text-gray-600 mb-4">
              Create a new role using one of these templates as a starting point. You can customize it afterward.
            </p>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                  <Shield className="h-4 w-4 text-purple-600" />
                  Predefined Templates
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {roleTemplates
                    .filter(template => template.category === 'predefined')
                    .map(template => (
                      <div
                        key={template.id}
                        className="border border-gray-200 rounded-lg p-3 hover:border-blue-300 hover:bg-blue-50 transition-colors cursor-pointer"
                        onClick={() => handleCreateFromTemplate(template)}
                      >
                        <div className="flex items-start gap-3">
                          <div className="p-2 bg-gray-100 rounded-full">
                            {getIconComponent(template.iconName || 'FileText', `h-5 w-5 ${template.iconColor || 'text-gray-600'}`)}
                          </div>
                          <div>
                            <h5 className="font-medium text-gray-900">{template.name}</h5>
                            <p className="text-xs text-gray-500">{template.description}</p>
                            <div className="mt-1 text-xs text-gray-500">
                              {template.permissions.length} permission{template.permissions.length !== 1 ? 's' : ''}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
              
              {roleTemplates.some(template => template.category === 'custom') && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    Custom Templates
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {roleTemplates
                      .filter(template => template.category === 'custom')
                      .map(template => (
                        <div
                          key={template.id}
                          className="border border-gray-200 rounded-lg p-3 hover:border-blue-300 hover:bg-blue-50 transition-colors relative group"
                        >
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteTemplate(template.id);
                            }}
                            className="absolute top-2 right-2 p-1 text-gray-400 hover:text-red-500 rounded opacity-0 group-hover:opacity-100 transition-opacity"
                            title="Delete template"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                          <div 
                            className="flex items-start gap-3 cursor-pointer"
                            onClick={() => handleCreateFromTemplate(template)}
                          >
                            <div className="p-2 bg-gray-100 rounded-full">
                              {getIconComponent(template.iconName || 'Star', `h-5 w-5 ${template.iconColor || 'text-yellow-500'}`)}
                            </div>
                            <div>
                              <h5 className="font-medium text-gray-900">{template.name}</h5>
                              <p className="text-xs text-gray-500">{template.description}</p>
                              <div className="mt-1 text-xs text-gray-500">
                                {template.permissions.length} permission{template.permissions.length !== 1 ? 's' : ''}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex justify-end gap-3 mt-6 border-t pt-4">
              <button
                onClick={() => setShowTemplates(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 font-medium rounded-lg hover:bg-gray-200 flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                Close
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Save as Template Modal */}
      {showSaveTemplate && selectedRole && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md m-4 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900">
                Save as Template
              </h3>
              <button 
                onClick={() => setShowSaveTemplate(false)}
                className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <p className="text-sm text-gray-600 mb-4">
              Save the current role configuration as a template that can be reused later to create new roles.
            </p>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Template Name*
                </label>
                <input
                  type="text"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  placeholder="E.g., Senior Developer Role"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={templateDescription}
                  onChange={(e) => setTemplateDescription(e.target.value)}
                  placeholder="Describe what this role template is for..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div className="bg-gray-50 p-3 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 mb-1">Summary</h4>
                <p className="text-xs text-gray-600">Template based on: <span className="font-medium">{selectedRole.name}</span></p>
                <p className="text-xs text-gray-600">{selectedRole.permissions.length} permissions included</p>
              </div>
            </div>
            
            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => setShowSaveTemplate(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 font-medium rounded-lg hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveAsTemplate}
                disabled={!templateName}
                className={`px-4 py-2 font-medium rounded-lg flex items-center gap-2 ${
                  !templateName 
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                    : 'bg-blue-500 text-white hover:bg-blue-600'
                }`}
              >
                <Save className="h-4 w-4" />
                Save Template
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Hierarchy Edit Modal */}
      {showHierarchyEdit && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl m-4 p-6 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900">
                Edit Role Hierarchy
              </h3>
              <button 
                onClick={() => setShowHierarchyEdit(false)}
                className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <p className="text-sm text-gray-600 mb-4">
              Configure parent-child relationships between roles. Child roles inherit all permissions from their parent roles.
            </p>
            
            <div className="border rounded-lg overflow-hidden mb-6">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Role
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Parent Role
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Inherited Permissions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {roles.map(role => (
                    <tr key={role.id}>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="p-1 bg-gray-100 rounded-full mr-2">
                            <Shield className="h-4 w-4 text-gray-600" />
                          </div>
                          <div className="font-medium text-gray-900">{role.name}</div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <select
                          name="parentId"
                          value={role.parentId || ''}
                          onChange={(e) => {
                            const { name, value } = e.target;
                            setRoles(prev => prev.map(r => 
                              r.id === role.id ? {...r, parentId: value} : r
                            ));
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">No Parent (Root Level)</option>
                          {roles
                            .filter(r => r.id !== role.id) // Cannot be its own parent
                            .map(parentRole => (
                              <option key={parentRole.id} value={parentRole.id}>
                                {parentRole.name}
                              </option>
                            ))}
                        </select>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        {role.parentId ? (
                          <div className="text-sm">
                            <span className="font-medium text-green-600">
                              +{getAllPermissionsForRole(role.id).length - role.permissions.length}
                            </span>
                            <span className="text-gray-500 ml-1">inherited permissions</span>
                          </div>
                        ) : (
                          <span className="text-gray-500 text-sm">No inherited permissions</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
              <h5 className="text-sm font-semibold text-blue-800 flex items-center mb-1">
                <AlertCircle className="h-4 w-4 mr-1" />
                About Role Inheritance
              </h5>
              <p className="text-sm text-blue-700">
                Changes to role inheritance take effect immediately. Users with child roles will automatically receive
                all permissions from parent roles in addition to their directly assigned permissions.
              </p>
            </div>
            
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowHierarchyEdit(false)}
                className="px-4 py-2 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
      {editingUser && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md m-4 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900">Edit Roles for {editingUser.name}</h3>
              <button onClick={() => setEditingUser(null)} className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"><X className="w-6 h-6" /></button>
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Roles</label>
              <div className="space-y-2 max-h-[400px] overflow-y-auto">
                {roles.map(role => {
                  // Create mapping from frontend string IDs to backend numeric IDs for comparison
                  const stringToNumericIdMap: Record<string, number> = {
                    'role-1': 1,   // System Administrator
                    'role-2': 2,   // HR-Users  
                    'role-3': 4,   // IT-Staff
                    'role-4': 6,   // IT Administrator
                    'role-5': 8,   // Department Head
                    'role-6': 9,   // Employee
                    'role-7': 30   // Dashboard Manager
                  };
                  
                  // Check if this role is currently assigned to the user
                  const isRoleAssigned = Array.isArray(editingRoles) && editingRoles.some(roleId => {
                    // Direct match (convert both to string for comparison)
                    if (String(roleId) === String(role.id)) return true;
                    
                    // String to numeric mapping
                    if (typeof roleId === 'string' && stringToNumericIdMap[roleId] === Number(role.id)) return true;
                    
                    // Numeric string to number comparison
                    if (typeof roleId === 'string' && !isNaN(parseInt(roleId)) && parseInt(roleId) === Number(role.id)) return true;
                    
                    return false;
                  });
                  
                  return (
                    <label key={role.id} className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded">
                      <input
                        type="checkbox"
                        checked={isRoleAssigned}
                        onChange={e => {
                          if (e.target.checked) {
                            // Add role ID (convert numeric ID back to frontend string format if needed)
                            const frontendRoleId = Object.entries(stringToNumericIdMap).find(([strId, numId]) => numId === Number(role.id))?.[0] || String(role.id);
                            setEditingRoles(prev => [...prev, frontendRoleId]);
                          } else {
                            // SIMPLIFIED: Remove role ID - just use the actual role.id
                            setEditingRoles(prev => {
                              console.log(`🔄 Unselecting role ${role.name} (ID: ${role.id})`);
                              console.log('Current roles before removal:', prev);
                              
                              const newRoles = prev.filter(rid => {
                                // Convert both to string and compare
                                const roleIdStr = String(role.id);
                                const ridStr = String(rid);
                                
                                // Direct string comparison
                                if (ridStr === roleIdStr) {
                                  console.log(`✅ Removing role ${rid} (direct match)`);
                                  return false;
                                }
                                
                                // Check if rid is the frontend mapped version
                                const mappedNumericId = stringToNumericIdMap[ridStr];
                                if (mappedNumericId && String(mappedNumericId) === roleIdStr) {
                                  console.log(`✅ Removing role ${rid} (mapped match: ${mappedNumericId})`);
                                  return false;
                                }
                                
                                // Keep this role in the list
                                return true;
                              });
                              
                              console.log('Roles after removal:', newRoles);
                              return newRoles;
                            });
                          }
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <div>
                        <div className="font-medium">{role.name}</div>
                        <div className="text-xs text-gray-500">{role.description}</div>
                      </div>
                    </label>
                  );
                })}
              </div>
            </div>
            <div className="flex justify-end gap-3 pt-4 border-t">
              <button type="button" onClick={() => setEditingUser(null)} className="px-4 py-2 text-gray-700 bg-gray-100 font-medium rounded-lg hover:bg-gray-200">Cancel</button>
              <button
                type="button"
                onClick={() => {
                  if (!editingUser || updatingUserIds.has(editingUser.id)) return;
                  handleUpdateUserRoles(editingUser.id, editingRoles);
                  setEditingUser(null);
                }}
                disabled={!editingUser || updatingUserIds.has(editingUser?.id || '')}
                className={`px-4 py-2 font-medium rounded-lg transition-all duration-150 ${
                  !editingUser || updatingUserIds.has(editingUser?.id || '')
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-500 text-white hover:bg-blue-600'
                }`}
              >
                {editingUser && updatingUserIds.has(editingUser.id) ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Saving...</span>
                  </div>
                ) : (
                  'Save'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
      {showAssignRolesModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl m-4 p-6 max-h-[90vh] overflow-hidden flex flex-col">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900">Assign Roles to Users</h3>
              <button 
                onClick={() => setShowAssignRolesModal(false)}
                className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="flex flex-col md:flex-row gap-6 flex-1 overflow-hidden">
              {/* Left side - User selection */}
              <div className="md:w-1/2 flex flex-col overflow-hidden">
                <div className="mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Select Users
                  </label>
                  <div className="relative">
                    <Search className="h-4 w-4 absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search users..."
                      value={userSearchInModal}
                      onChange={(e) => setUserSearchInModal(e.target.value)}
                      className="pl-8 pr-2 py-2 w-full text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                </div>
                
                <div className="border border-gray-300 rounded-lg overflow-y-auto flex-1">
                  <div className="p-2 border-b bg-gray-50 flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedUserIds.length === users.length && users.length > 0}
                      onChange={() => {
                        if (selectedUserIds.length === users.length) {
                          setSelectedUserIds([]);
                        } else {
                          setSelectedUserIds(users.map(u => u.id));
                        }
                      }}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
                    />
                    <span className="text-sm font-medium">Select All ({users.length})</span>
                    {selectedUserIds.length > 0 && (
                      <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                        {selectedUserIds.length} selected
                      </span>
                    )}
                  </div>
                  
                  <div className="max-h-[calc(60vh-200px)] overflow-y-auto">
                    {users
                      .filter(u => 
                        u.name.toLowerCase().includes(userSearchInModal.toLowerCase()) || 
                        u.email.toLowerCase().includes(userSearchInModal.toLowerCase()) ||
                        (u.department && u.department.toLowerCase().includes(userSearchInModal.toLowerCase()))
                      )
                      .map(user => (
                        <label key={user.id} className="flex items-center p-3 hover:bg-gray-50 border-b cursor-pointer">
                          <input
                            type="checkbox"
                            checked={selectedUserIds.includes(user.id)}
                            onChange={() => {
                              setSelectedUserIds(prev => 
                                prev.includes(user.id)
                                  ? prev.filter(id => id !== user.id)
                                  : [...prev, user.id]
                              );
                            }}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3"
                          />
                          <div className="flex items-center flex-1">
                            <div className="flex-shrink-0 h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                              <User className="h-4 w-4 text-gray-600" />
                            </div>
                            <div>
                              <div className="font-medium text-sm">{user.name}</div>
                              <div className="text-xs text-gray-500">{user.email}</div>
                              {user.department && (
                                <div className="text-xs text-gray-500">{user.department}</div>
                              )}
                            </div>
                          </div>
                          <div className="ml-auto">
                            <span className={`px-2 py-0.5 text-xs rounded-full ${
                              user.status === 'active' ? 'bg-green-100 text-green-800' :
                              user.status === 'inactive' ? 'bg-red-100 text-red-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {user.status}
                            </span>
                          </div>
                        </label>
                      ))}
                    
                    {users.filter(u => 
                      u.name.toLowerCase().includes(userSearchInModal.toLowerCase()) || 
                      u.email.toLowerCase().includes(userSearchInModal.toLowerCase()) ||
                      (u.department && u.department.toLowerCase().includes(userSearchInModal.toLowerCase()))
                    ).length === 0 && (
                      <div className="py-6 text-center">
                        <SearchX className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                        <p className="text-gray-500">No users found</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Right side - Role assignment with categories */}
              <div className="md:w-1/2 flex flex-col overflow-hidden">
                <div className="mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Assign Roles {selectedUserIds.length > 0 && `(for ${selectedUserIds.length} selected users)`}
                  </label>
                </div>
                
                <div className="border border-gray-300 rounded-lg flex-1 overflow-hidden">
                  <div className="p-2 border-b bg-gray-50 flex items-center justify-between">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={assignmentRoles.length === roles.length && roles.length > 0}
                        onChange={() => {
                          if (assignmentRoles.length === roles.length) {
                            setAssignmentRoles([]);
                          } else {
                            setAssignmentRoles(roles.map(r => r.id));
                          }
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
                      />
                      <span className="text-sm font-medium">Select All Roles</span>
                      {assignmentRoles.length > 0 && (
                        <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                          {assignmentRoles.length} selected
                        </span>
                      )}
                    </div>
                    
                    {/* Role category filters */}
                    <div className="flex gap-2">
                      <button
                        type="button"
                        className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 hover:bg-blue-200"
                      >
                        System
                      </button>
                      <button
                        type="button"
                        className="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800 hover:bg-purple-200"
                      >
                        Dashboard
                      </button>
                    </div>
                  </div>
                  
                  <div className="max-h-[calc(60vh-200px)] overflow-y-auto">
                    {/* System Roles Section */}
                    <div className="p-2 bg-blue-50 sticky top-0 z-10">
                      <h4 className="text-sm font-medium text-blue-800 flex items-center">
                        <Shield className="h-4 w-4 mr-1" />
                        System Roles
                      </h4>
                    </div>
                    {roles
                      .filter(role => role.category === 'system' || !role.category)
                      .map(role => (
                        <label key={role.id} className="flex items-start p-3 hover:bg-gray-50 border-b cursor-pointer">
                          <input
                            type="checkbox"
                            checked={assignmentRoles.includes(role.id)}
                            onChange={() => {
                              setAssignmentRoles(prev => 
                                prev.includes(role.id)
                                  ? prev.filter(id => id !== role.id)
                                  : [...prev, role.id]
                              );
                            }}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mt-1 mr-3"
                          />
                          <div>
                            <div className="font-medium">{role.name}</div>
                            <div className="text-xs text-gray-500 mt-1">{role.description}</div>
                            <div className="text-xs text-gray-500 mt-1">
                              {role.permissions.length} permission{role.permissions.length !== 1 ? 's' : ''}
                            </div>
                          </div>
                        </label>
                      ))
                    }

                    {/* Dashboard Roles Section */}
                    <div className="p-2 bg-purple-50 sticky top-0 z-10">
                      <h4 className="text-sm font-medium text-purple-800 flex items-center">
                        <BarChart2 className="h-4 w-4 mr-1" />
                        Dashboard Roles
                      </h4>
                    </div>
                    {roles
                      .filter(role => role.category === 'dashboard')
                      .map(role => (
                        <label key={role.id} className="flex items-start p-3 hover:bg-gray-50 border-b cursor-pointer">
                          <input
                            type="checkbox"
                            checked={assignmentRoles.includes(role.id)}
                            onChange={() => {
                              setAssignmentRoles(prev => 
                                prev.includes(role.id)
                                  ? prev.filter(id => id !== role.id)
                                  : [...prev, role.id]
                              );
                            }}
                            className="rounded border-gray-300 text-purple-600 focus:ring-purple-500 mt-1 mr-3"
                          />
                          <div>
                            <div className="font-medium">{role.name}</div>
                            <div className="text-xs text-gray-500 mt-1">{role.description}</div>
                            {role.dashboardAccess && (
                              <div className="flex flex-wrap gap-1 mt-1">
                                {role.dashboardAccess.map(dashboard => (
                                  <span key={dashboard} className="px-1.5 py-0.5 text-xs bg-purple-100 text-purple-800 rounded">
                                    {dashboard}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>
                        </label>
                      ))
                    }
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-end gap-3 pt-4 mt-4 border-t">
              <button
                type="button"
                onClick={() => setShowAssignRolesModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 font-medium rounded-lg hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleManualRoleAssignment}
                disabled={selectedUserIds.length === 0 || assignmentRoles.length === 0}
                className={`px-4 py-2 font-medium rounded-lg flex items-center gap-2 ${
                  selectedUserIds.length === 0 || assignmentRoles.length === 0
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-purple-500 text-white hover:bg-purple-600'
                }`}
              >
                <CheckCircle className="h-4 w-4" />
                Assign Roles to {selectedUserIds.length} User{selectedUserIds.length !== 1 ? 's' : ''}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

interface RoleFormProps {
  role: Role | null;
  permissions: Permission[];
  permissionsByCategory: Record<string, Permission[]>;
  roles: Role[];
  onSave: (role: any) => void;
  onCancel: () => void;
}

const RoleForm = ({ role, permissions, permissionsByCategory, roles, onSave, onCancel }: RoleFormProps) => {
  const [formData, setFormData] = useState({
    id: role?.id || '',
    name: role?.name || '',
    description: role?.description || '',
    permissions: role?.permissions || [],
    parentId: role?.parentId || '',
    category: role?.category || 'system',
    dashboardAccess: role?.dashboardAccess || []
  });
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handlePermissionToggle = (permissionId: string) => {
    setFormData(prev => {
      if (prev.permissions.includes(permissionId)) {
        return {
          ...prev,
          permissions: prev.permissions.filter(id => id !== permissionId)
        };
      } else {
        return {
          ...prev,
          permissions: [...prev.permissions, permissionId]
        };
      }
    });
  };
  
  // Helper to select all permissions in a category
  const handleSelectAllInCategory = (category: string) => {
    const categoryPermissionIds = permissionsByCategory[category].map(p => p.id);
    
    // Check if all permissions in this category are already selected
    const allSelected = categoryPermissionIds.every(id => formData.permissions.includes(id));
    
    if (allSelected) {
      // Unselect all in this category
      setFormData(prev => ({
        ...prev,
        permissions: prev.permissions.filter(id => !categoryPermissionIds.includes(id))
      }));
    } else {
      // Select all in this category
      const currentPermissions = [...formData.permissions];
      categoryPermissionIds.forEach(id => {
        if (!currentPermissions.includes(id)) {
          currentPermissions.push(id);
        }
      });
      
      setFormData(prev => ({
        ...prev,
        permissions: currentPermissions
      }));
    }
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.name.trim()) {
      alert('Role name is required');
      return;
    }
    
    onSave(formData);
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Role Name*
        </label>
        <input
          type="text"
          name="name"
          value={formData.name}
          onChange={handleChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          name="description"
          value={formData.description}
          onChange={handleChange}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Parent Role
        </label>
        <select
          name="parentId"
          value={formData.parentId}
          onChange={(e) => {
            const { name, value } = e.target;
            setFormData(prev => ({
              ...prev,
              [name]: value
            }));
          }}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">No Parent (Root Level)</option>
          {roles.filter((r) => r.id !== formData.id).map((parentRole) => (
            <option key={parentRole.id} value={parentRole.id}>
              {parentRole.name}
            </option>
          ))}
        </select>
        {formData.parentId && (
          <p className="mt-1 text-sm text-blue-600">
            This role will inherit all permissions from its parent role.
          </p>
        )}
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Permissions
        </label>
        
        <div className="space-y-4">
          {Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => (
            <div key={category} className="border border-gray-200 rounded-lg p-3">
              <div className="flex justify-between items-center border-b pb-2 mb-2">
                <h5 className="font-medium text-gray-900">{category}</h5>
                <button 
                  type="button"
                  onClick={() => handleSelectAllInCategory(category)}
                  className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded"
                >
                  {categoryPermissions.every(p => formData.permissions.includes(p.id)) 
                    ? 'Unselect All' 
                    : 'Select All'}
                </button>
              </div>
              <div className="space-y-2">
                {categoryPermissions.map(permission => (
                  <div key={permission.id} className="flex items-start">
                    <input
                      type="checkbox"
                      id={`perm-${permission.id}`}
                      checked={formData.permissions.includes(permission.id)}
                      onChange={() => handlePermissionToggle(permission.id)}
                      className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor={`perm-${permission.id}`} className="ml-2 block">
                      <span className="text-sm font-medium text-gray-900">{permission.name}</span>
                      <p className="text-xs text-gray-500 mt-0.5">{permission.description}</p>
                    </label>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Role Category */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Role Category
        </label>
        <select
          name="category"
          value={formData.category}
          onChange={(e) => {
            const { name, value } = e.target;
            setFormData(prev => ({
              ...prev,
              [name]: value
            }));
          }}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="system">System Role</option>
          <option value="dashboard">Dashboard Role</option>
          <option value="custom">Custom Role</option>
        </select>
      </div>
      
      {/* Dashboard Access (Only shown for dashboard roles) */}
      {formData.category === 'dashboard' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Dashboard Access
          </label>
          <div className="space-y-2 border border-gray-200 rounded-lg p-3">
            {['sales', 'finance', 'operations', 'hr', 'it', 'executive'].map(dashboard => (
              <label key={dashboard} className="flex items-center p-2 border border-gray-200 rounded-lg hover:bg-gray-50">
                <input
                  type="checkbox"
                  checked={formData.dashboardAccess.includes(dashboard)}
                  onChange={() => {
                    setFormData(prev => {
                      const dashboardAccess = [...prev.dashboardAccess];
                      if (dashboardAccess.includes(dashboard)) {
                        return {
                          ...prev,
                          dashboardAccess: dashboardAccess.filter(d => d !== dashboard)
                        };
                      } else {
                        return {
                          ...prev,
                          dashboardAccess: [...dashboardAccess, dashboard]
                        };
                      }
                    });
                  }}
                  className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 h-4 w-4"
                />
                <span className="ml-2 text-sm capitalize">{dashboard}</span>
              </label>
            ))}
          </div>
        </div>
      )}
      
      <div className="flex justify-end gap-3 pt-4 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-gray-700 bg-gray-100 font-medium rounded-lg hover:bg-gray-200"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-blue-500 text-white font-medium rounded-lg hover:bg-blue-600"
        >
          {role ? 'Update Role' : 'Create Role'}
        </button>
      </div>
    </form>
  );
}; 