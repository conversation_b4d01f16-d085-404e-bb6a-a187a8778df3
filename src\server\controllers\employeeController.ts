import { Request, Response } from 'express';
import { AppDataSource } from '../../config/database';
import { Employee } from '../entities/Employee';
import fs from 'fs';
import path from 'path';
import fileUpload from 'express-fileupload';

// Get the repository
const employeeRepository = AppDataSource.getRepository(Employee);

// Generate an employee ID if not provided
const generateEmployeeId = async (firstName: string, lastName: string): Promise<string> => {
  try {
    // Basic pattern: First letter of first name + first letter of last name + number
    const prefix = (firstName?.[0] || 'E') + (lastName?.[0] || 'E');
    
    // Find employees with the same prefix using LIKE operator
    const employees = await employeeRepository.createQueryBuilder('employee')
      .where('employee.employeeId LIKE :prefix', { prefix: `${prefix}%` })
      .getMany();
    
    let maxNumber = 0;
    employees.forEach(emp => {
      // Extract the number part
      const match = emp.employeeId.match(/\d+$/);
      if (match) {
        const num = parseInt(match[0], 10);
        if (!isNaN(num) && num > maxNumber) {
          maxNumber = num;
        }
      }
    });
    
    // Generate a new ID by incrementing the max number
    return `${prefix}${maxNumber + 1}`;
  } catch (error) {
    console.error('Error generating employee ID:', error);
    // Fallback to timestamp-based ID
    return `EMP${Date.now().toString().slice(-5)}`;
  }
};

// Create a new employee
export const createEmployee = async (req: Request, res: Response) => {
  try {
    const employeeData = req.body;
    console.log('Received employee data', { firstName: employeeData.firstName, lastName: employeeData.lastName });

    // List of fields that exist in the Employee entity
    const employeeFields = [
      // Personal Info
      'firstName', 'middleName', 'lastName', 'gender', 'dateOfBirth', 'religion',
      'cnicNumber', 'cnicExpiryDate', 'nationality', 'maritalStatus', 'bloodType',
      'profileImagePath', 'employeeId', 'status', 'fatherName', 'statusDate'
    ];
    
    // Fields for EmployeeContact entity
    const contactFields = [
      'mobileNumber', 'officialNumber', 'officialEmail', 'personalEmail',
      'permanentAddress', 'currentAddress', 'emergencyContactName',
      'emergencyContactPhone', 'emergencyContactRelationship', 'linkedinProfile', 
      'otherSocialProfiles'
    ];
    
    // Fields for EmployeeJob entity
    const jobFields = [
      'designation', 'department', 'project', 'location', 'employmentType',
      'employmentStatus', 'employeeLevel', 'joinDate', 'probationEndDate',
      'noticePeriod', 'reportingTo', 'remoteWorkEligible',
      'nextReviewDate', 'trainingRequirements', 'workSchedule', 'shiftType'
    ];
    
    // Fields for EmployeeBenefit entity
    const benefitFields = [
      'totalSalary', 'salaryTier', 'salaryTrench', 'cashAmount', 'bankAmount', 'paymentMode',
      'foodAllowanceInSalary', 'fuelAllowanceInSalary', 'numberOfMeals',
      'fuelInLiters', 'fuelAmount', 'foodProvidedByCompany',
      'bankName', 'bankBranch', 'accountNumber', 'accountTitle', 'iban',
      'healthInsuranceProvider', 'healthInsurancePolicyNumber', 'healthInsuranceExpiryDate',
      'lifeInsuranceProvider', 'lifeInsurancePolicyNumber', 'lifeInsuranceExpiryDate',
      'accommodationProvidedByEmployer', 'accommodationType', 'accommodationAddress'
    ];
    
    // Fields that should be ignored as they're now separate entities
    const fieldsToIgnore = [
      'educationEntries', 'experienceEntries', 'deviceEntries',
      'children', 'dependents', 'documents', 'requiredDocuments', 'projectEntries'
    ];

    // Filter the data for the main employee entity
    const employeeEntityData: Record<string, any> = {};
    employeeFields.forEach(field => {
      if (field in employeeData) {
          employeeEntityData[field] = employeeData[field];
      }
    });

    // Filter data for the contact entity
    const contactEntityData: Record<string, any> = {};
    contactFields.forEach(field => {
      if (field in employeeData) {
        contactEntityData[field] = employeeData[field];
      }
    });
    
    // Filter data for the job entity
    const jobEntityData: Record<string, any> = {};
    jobFields.forEach(field => {
      if (field in employeeData) {
        jobEntityData[field] = employeeData[field];
      }
    });
    
    // Filter data for the benefit entity
    const benefitEntityData: Record<string, any> = {};
    benefitFields.forEach(field => {
      if (field in employeeData) {
        benefitEntityData[field] = employeeData[field];
      }
    });

    // Generate employee ID if not provided
    if (!employeeEntityData.employeeId || employeeEntityData.employeeId.trim() === '') {
      employeeEntityData.employeeId = await generateEmployeeId(
        employeeEntityData.firstName, 
        employeeEntityData.lastName
      );
      console.log('Generated employee ID:', employeeEntityData.employeeId);
    }

    console.log('Creating employee with data:', { 
      employee: Object.keys(employeeEntityData),
      contact: Object.keys(contactEntityData),
      job: Object.keys(jobEntityData),
      benefit: Object.keys(benefitEntityData)
    });
    
    // Start a transaction to ensure all entities are created or none
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      // Create new employee instance
      const employee = employeeRepository.create(employeeEntityData);
      await queryRunner.manager.save(employee);
      
      // Create contact entity if we have contact data
      if (Object.keys(contactEntityData).length > 0) {
        const contactRepository = AppDataSource.getRepository('EmployeeContact');
        const contact = contactRepository.create({
          ...contactEntityData,
          employee
        });
        await queryRunner.manager.save(contact);
      }
      
      // Create job entity if we have job data
      if (Object.keys(jobEntityData).length > 0) {
        const jobRepository = AppDataSource.getRepository('EmployeeJob');
        const job = jobRepository.create({
          ...jobEntityData,
          employee
        });
        await queryRunner.manager.save(job);
      }
      
      // Create benefit entity if we have benefit data
      if (Object.keys(benefitEntityData).length > 0) {
        const benefitRepository = AppDataSource.getRepository('EmployeeBenefit');
        const benefit = benefitRepository.create({
          ...benefitEntityData,
          employee
        });
        await queryRunner.manager.save(benefit);
      }
      
      // Handle education entries 
      if (employeeData.educationEntries && Array.isArray(employeeData.educationEntries) && employeeData.educationEntries.length > 0) {
        console.log(`Creating ${employeeData.educationEntries.length} education entries`);
        const educationRepository = AppDataSource.getRepository('EmployeeEducation');
        
        for (const educationEntry of employeeData.educationEntries) {
          // Skip empty entries
          if (!educationEntry.educationLevel && !educationEntry.degree && !educationEntry.institution) {
            console.log('Skipping empty education entry');
            continue;
          }
          
          const education = educationRepository.create({
            educationLevel: educationEntry.educationLevel || '',
            degree: educationEntry.degree || '',
            major: educationEntry.major || '',
            institution: educationEntry.institution || '',
            graduationYear: educationEntry.graduationYear || '',
            grade: educationEntry.grade || '',
            employee
          });
          
          await queryRunner.manager.save(education);
          console.log(`Created education entry: ${education.degree} at ${education.institution}`);
        }
      } else {
        console.log('No education entries to create');
      }
      
      // Handle experience entries
      if (employeeData.experienceEntries && Array.isArray(employeeData.experienceEntries) && employeeData.experienceEntries.length > 0) {
        console.log(`Creating ${employeeData.experienceEntries.length} experience entries`);
        const experienceRepository = AppDataSource.getRepository('EmployeeExperience');
        
        for (const experienceEntry of employeeData.experienceEntries) {
          // Skip empty entries
          if (!experienceEntry.companyName && !experienceEntry.jobTitle) {
            console.log('Skipping empty experience entry');
            continue;
          }
          
          const experience = experienceRepository.create({
            companyName: experienceEntry.companyName || '',
            jobTitle: experienceEntry.jobTitle || '',
            startDate: experienceEntry.startDate || '',
            endDate: experienceEntry.endDate || '',
            currentlyWorking: experienceEntry.currentlyWorking || false,
            jobDescription: experienceEntry.jobDescription || '',
            employee
          });
          
          await queryRunner.manager.save(experience);
          console.log(`Created experience entry: ${experience.jobTitle} at ${experience.companyName}`);
        }
      } else {
        console.log('No experience entries to create');
      }
      
      // Handle device entries
      if (employeeData.deviceEntries && Array.isArray(employeeData.deviceEntries) && employeeData.deviceEntries.length > 0) {
        console.log(`Creating ${employeeData.deviceEntries.length} device entries`);
        const deviceRepository = AppDataSource.getRepository('EmployeeDevice');
        
        for (const deviceEntry of employeeData.deviceEntries) {
          // Skip empty entries
          if (!deviceEntry.deviceName && !deviceEntry.makeModel && !deviceEntry.serialNumber) {
            console.log('Skipping empty device entry');
            continue;
          }
          
          const device = deviceRepository.create({
            deviceName: deviceEntry.deviceName || '',
            makeModel: deviceEntry.makeModel || '',
            serialNumber: deviceEntry.serialNumber || '',
            handoverDate: deviceEntry.handoverDate || '',
            returnDate: deviceEntry.returnDate || '',
            condition: deviceEntry.condition || '',
            employee
          });
          
          await queryRunner.manager.save(device);
          console.log(`Created device entry: ${device.deviceName} (${device.makeModel})`);
        }
      } else {
        console.log('No device entries to create');
      }
      
      // Handle family members (children and dependents)
      const allFamilyMembers = [];
      
      // Process spouse data if provided
      if (employeeData.spouseName) {
        console.log('Adding spouse data for:', employeeData.spouseName);
        allFamilyMembers.push({
          name: employeeData.spouseName || '',
          dateOfBirth: employeeData.spouseDateOfBirth || '',
          gender: '',
          relationship: 'spouse',
          cnic: employeeData.spouseCNIC || '',
          occupation: employeeData.spouseOccupation || '',
          employer: employeeData.spouseEmployer || '',
          contactNumber: employeeData.spouseContactNumber || '',
          type: 'spouse',
          employee
        });
      }
      
      // Process children
      if (employeeData.children && Array.isArray(employeeData.children) && employeeData.children.length > 0) {
        console.log(`Creating ${employeeData.children.length} child entries`);
        
        for (const child of employeeData.children) {
          if (!child.name) {
            console.log('Skipping empty child entry');
            continue;
          }
          
          allFamilyMembers.push({
            name: child.name || '',
            dateOfBirth: child.dateOfBirth || '',
            gender: child.gender || '',
            relationship: 'child',
            cnic: child.cnic || '',
            type: 'child',
            employee
          });
        }
      }
      
      // Process dependents
      if (employeeData.dependents && Array.isArray(employeeData.dependents) && employeeData.dependents.length > 0) {
        console.log(`Creating ${employeeData.dependents.length} dependent entries`);
        
        for (const dependent of employeeData.dependents) {
          if (!dependent.name) {
            console.log('Skipping empty dependent entry');
            continue;
          }
          
          allFamilyMembers.push({
            name: dependent.name || '',
            dateOfBirth: dependent.dateOfBirth || '',
            gender: dependent.gender || '',
            relationship: dependent.relationship || 'other',
            cnic: dependent.cnic || '',
            type: 'dependent',
            employee
          });
        }
      }
      
      // Save all family members
      if (allFamilyMembers.length > 0) {
        const familyRepository = AppDataSource.getRepository('EmployeeFamily');
        
        for (const familyMember of allFamilyMembers) {
          const family = familyRepository.create(familyMember);
          await queryRunner.manager.save(family);
          console.log(`Created family member: ${family.name} (${family.relationship})`);
        }
      } else {
        console.log('No family members to create');
      }
      
      // Handle documents
      if (employeeData.documents && Array.isArray(employeeData.documents) && employeeData.documents.length > 0) {
        console.log(`Creating ${employeeData.documents.length} document entries`);
        const documentRepository = AppDataSource.getRepository('EmployeeDocument');
        
        for (const docEntry of employeeData.documents) {
          if (!docEntry.documentType) {
            console.log('Skipping empty document entry');
            continue;
          }
          
          const document = documentRepository.create({
            documentType: docEntry.documentType || '',
            verificationStatus: docEntry.verificationStatus || 'verified',
            employee
          });
          
          await queryRunner.manager.save(document);
          console.log(`Created document entry: ${document.documentType}`);
        }
      } else {
        console.log('No document entries to create');
      }
      
      // Handle project entries
      if (employeeData.projectEntries && Array.isArray(employeeData.projectEntries) && employeeData.projectEntries.length > 0) {
        console.log(`Creating ${employeeData.projectEntries.length} project entries`);
        const projectRepository = AppDataSource.getRepository('EmployeeProject');
        
        for (const projectEntry of employeeData.projectEntries) {
          // Skip empty entries
          if (!projectEntry.projectName && !projectEntry.role) {
            console.log('Skipping empty project entry');
            continue;
          }
          
          const project = projectRepository.create({
            projectName: projectEntry.projectName || '',
            role: projectEntry.role || '',
            startDate: projectEntry.startDate || '',
            endDate: projectEntry.endDate || '',
            currentProject: projectEntry.currentProject || false,
            technologies: projectEntry.technologies || '',
            description: projectEntry.description || '',
            achievements: projectEntry.achievements || '',
            teamSize: projectEntry.teamSize || '',
            clientName: projectEntry.clientName || '',
            employee
          });
          
          await queryRunner.manager.save(project);
          console.log(`Created project entry: ${project.projectName} (${project.role})`);
        }
      } else {
        console.log('No project entries to create');
      }
      
      // Save skills and certifications directly to employee record
      const skillsFields = [
        'professionalSkills', 'technicalSkills', 'certifications', 'languages'
      ];
      
      const skillsData: Record<string, any> = {};
      skillsFields.forEach(field => {
        if (field in employeeData && employeeData[field]) {
          skillsData[field] = employeeData[field];
        }
      });
      
      if (Object.keys(skillsData).length > 0) {
        console.log('Saving skills and certifications data');
        const skillRepository = AppDataSource.getRepository('EmployeeSkill');
        const skill = skillRepository.create({
          ...skillsData,
          employee
        });
        await queryRunner.manager.save(skill);
      }
      
      // Save vehicle details directly to employee record
      const vehicleFields = [
        'vehicleType', 'registrationNumber', 'providedByCompany', 
        'handingOverDate', 'returnDate', 'vehicleMakeModel',
        'vehicleColor', 'mileageAtIssuance'
      ];
      
      const vehicleData: Record<string, any> = {};
      vehicleFields.forEach(field => {
        if (field in employeeData && employeeData[field] !== undefined) {
          vehicleData[field] = employeeData[field];
        }
      });
      
      if (Object.keys(vehicleData).length > 0) {
        console.log('Saving vehicle details data');
        const vehicleRepository = AppDataSource.getRepository('EmployeeVehicle');
        const vehicle = vehicleRepository.create({
          ...vehicleData,
          employee
        });
        await queryRunner.manager.save(vehicle);
      }
      
      // Save medical records directly to employee record
      const medicalFields = [
        'vaccinationRecords', 'medicalHistory', 'bloodGroup',
        'allergies', 'chronicConditions', 'regularMedications'
      ];
      
      const medicalData: Record<string, any> = {};
      medicalFields.forEach(field => {
        if (field in employeeData && employeeData[field]) {
          medicalData[field] = employeeData[field];
        }
      });
      
      if (Object.keys(medicalData).length > 0) {
        console.log('Saving medical records data');
        const healthRepository = AppDataSource.getRepository('EmployeeHealth');
        const health = healthRepository.create({
          ...medicalData,
          employee
        });
        await queryRunner.manager.save(health);
      }
      
      // Process notes and special instructions
      if ('notes' in employeeData && employeeData.notes !== undefined) {
        employee.notes = employeeData.notes;
      }
      
      if ('specialInstructions' in employeeData && employeeData.specialInstructions !== undefined) {
        employee.specialInstructions = employeeData.specialInstructions;
      }
      
      // Save any updates to the employee entity
      await queryRunner.manager.save(employee);
      
      // Commit the transaction
      await queryRunner.commitTransaction();
      
      // Fetch the complete employee with relations to return
      const savedEmployee = await employeeRepository.findOne({
        where: { id: employee.id },
        relations: ['contact', 'job', 'benefit', 'education', 'experience', 'devices', 'family']
      });
      
      // Fetch documents separately to avoid "Cannot query across one-to-many" error
      const documentRepository = AppDataSource.getRepository('EmployeeDocument');
      const employeeDocuments = await documentRepository.find({
        where: { employee: { id: Number(employee.id) } }
      });
      
      // Process data for frontend
      const processedEmployee = { ...savedEmployee } as Record<string, any>;
      
      // Add documents that were fetched separately
      processedEmployee.documents = employeeDocuments;
      
      // Convert related entities to the expected format for the frontend
      if (processedEmployee.education) {
        processedEmployee.educationEntries = processedEmployee.education;
        delete processedEmployee.education;
      }
      
      if (processedEmployee.experience) {
        processedEmployee.experienceEntries = processedEmployee.experience;
        delete processedEmployee.experience;
      }
      
      if (processedEmployee.family) {
        // Split family members into children and dependents
        console.log('Processing family members:', processedEmployee.family.length);
        processedEmployee.children = processedEmployee.family.filter((f: any) => f.relationship === 'child' || f.type === 'child');
        processedEmployee.dependents = processedEmployee.family.filter((f: any) => 
          f.relationship !== 'child' && f.type !== 'child' && f.relationship !== 'spouse' && f.type !== 'spouse');
        
        console.log(`Split into ${processedEmployee.children.length} children and ${processedEmployee.dependents.length} dependents`);
        
        // Look for spouse in family members
        const spouse = processedEmployee.family.find((f: any) => f.relationship === 'spouse' || f.type === 'spouse' || f.relationship === 'Spouse' || f.type === 'Spouse');
        if (spouse) {
          console.log('Found spouse in family members:', JSON.stringify(spouse, null, 2));
          processedEmployee.spouseName = spouse.name || '';
          processedEmployee.spouseDateOfBirth = spouse.dateOfBirth || '';
          processedEmployee.spouseOccupation = spouse.occupation || '';
          processedEmployee.spouseEmployer = spouse.employer || '';
          processedEmployee.spouseContactNumber = spouse.contactNumber || '';
          processedEmployee.spouseCNIC = spouse.cnic || '';
        } else {
          console.log('No spouse found in family members. Family data:', JSON.stringify(processedEmployee.family, null, 2));
        }
        
        delete processedEmployee.family;
      }
      
      if (processedEmployee.devices) {
        processedEmployee.deviceEntries = processedEmployee.devices;
        delete processedEmployee.devices;
      }
      
      if (processedEmployee.projects) {
        processedEmployee.projectEntries = processedEmployee.projects;
        delete processedEmployee.projects;
      }
      
      // Process existing skills data to put fields at the top level
      if (processedEmployee.skills && processedEmployee.skills.length > 0) {
        // Take the first entry's skills (there should only be one)
        const skill = processedEmployee.skills[0];
        processedEmployee.professionalSkills = skill.professionalSkills || '';
        processedEmployee.technicalSkills = skill.technicalSkills || '';
        processedEmployee.certifications = skill.certifications || '';
        processedEmployee.languages = skill.languages || '';
        delete processedEmployee.skills;
      }
      
      // Include document entries
      if (processedEmployee.documents) {
        console.log('Processing documents:', processedEmployee.documents.length);
        processedEmployee.documentEntries = processedEmployee.documents.map((doc: any) => ({
          ...doc,
          // Add any missing required fields
          files: doc.filePath ? [{
            id: Date.now().toString(),
            file: null,
            preview: `/uploads/${doc.filePath}`
          }] : []
        }));
        console.log('Processed document entries:', processedEmployee.documentEntries.length);
        delete processedEmployee.documents;
      }
      
      // Include requiredDocuments if it exists in the database
      if (processedEmployee.requiredDocuments) {
        console.log('Processing requiredDocuments');
        // Keep as is if it's an object, or parse if it's a string
        if (typeof processedEmployee.requiredDocuments === 'string') {
          try {
            processedEmployee.requiredDocuments = JSON.parse(processedEmployee.requiredDocuments);
            console.log('Parsed requiredDocuments from string:', processedEmployee.requiredDocuments);
          } catch (e) {
            console.error('Error parsing requiredDocuments:', e);
            // Set default empty object if parsing fails
            processedEmployee.requiredDocuments = {
              cnic: false,
              passport: false,
              drivingLicense: false,
              educationalCertificates: false,
              experienceCertificates: false,
              bankAccountDetails: false,
              taxDocuments: false,
              medicalCertificate: false,
              policeClearanceCertificate: false,
              cv: false,
              affidavit: false,
              otherDocuments: false
            };
          }
        }
      } else {
        console.log('No requiredDocuments found, setting default values');
        processedEmployee.requiredDocuments = {
          cnic: false,
          passport: false,
          drivingLicense: false,
          educationalCertificates: false,
          experienceCertificates: false,
          bankAccountDetails: false,
          taxDocuments: false,
          medicalCertificate: false,
          policeClearanceCertificate: false,
          cv: false,
          affidavit: false,
          otherDocuments: false
        };
      }
      
      // Include vehicle data
      if (processedEmployee.vehicles && processedEmployee.vehicles.length > 0) {
        const vehicle = processedEmployee.vehicles[0];
        processedEmployee.vehicleType = vehicle.vehicleType || '';
        processedEmployee.registrationNumber = vehicle.registrationNumber || '';
        processedEmployee.providedByCompany = vehicle.providedByCompany || false;
        processedEmployee.handingOverDate = vehicle.handingOverDate || '';
        processedEmployee.returnDate = vehicle.returnDate || '';
        processedEmployee.vehicleMakeModel = vehicle.vehicleMakeModel || '';
        processedEmployee.vehicleColor = vehicle.vehicleColor || '';
        processedEmployee.mileageAtIssuance = vehicle.mileageAtIssuance || '';
        delete processedEmployee.vehicles;
      }
      
      // Include health data
      if (processedEmployee.healthRecords && processedEmployee.healthRecords.length > 0) {
        const health = processedEmployee.healthRecords[0];
        processedEmployee.vaccinationRecords = health.vaccinationRecords || '';
        processedEmployee.medicalHistory = health.medicalHistory || '';
        processedEmployee.bloodGroup = health.bloodGroup || '';
        processedEmployee.allergies = health.allergies || '';
        processedEmployee.chronicConditions = health.chronicConditions || '';
        processedEmployee.regularMedications = health.regularMedications || '';
        delete processedEmployee.healthRecords;
      }

      console.log(`Returning employee data with fields: ${Object.keys(processedEmployee).join(', ')}`);
      
      return res.status(201).json({
        success: true,
        message: 'Employee created successfully',
        id: employee.id,
        employee: processedEmployee
      });
    } catch (error) {
      // Rollback transaction in case of error
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }
  } catch (error) {
    console.error('Error creating employee:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create employee',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all employees
export const getAllEmployees = async (_req: Request, res: Response) => {
  try {
    console.log('getAllEmployees: Fetching all employees');
    const employees = await employeeRepository.find();
    console.log(`getAllEmployees: Found ${employees.length} employees`);
    
    if (employees.length === 0) {
      console.log('getAllEmployees: No employees found in database');
      return res.json({
        success: true,
        count: 0,
        employees: []
      });
    }
    
    // No need to parse JSON fields as they're now separate entities
    console.log(`getAllEmployees: Returning ${employees.length} employees`);
    return res.json({
      success: true,
      count: employees.length,
      employees: employees
    });
  } catch (error) {
    console.error('Error fetching employees:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch employees',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all employees for listing (with joins to related tables)
export const getEmployeesForListing = async (req: Request, res: Response) => {
  try {
    const timestamp = req.query.timestamp || Date.now();
    console.log(`getEmployeesForListing: Fetching employees with joined data (timestamp: ${timestamp})`);
    
    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');
    
    // Instead of clearing the entire EntityManager cache, use a fresh query
    // AppDataSource.manager.clear(); - This caused a linter error
    
    // Using query builder to join all required tables with cache disabled
    const employeesData = await employeeRepository.createQueryBuilder('employee')
      .leftJoinAndSelect('employee.contact', 'contact')
      .leftJoinAndSelect('employee.job', 'job')
      .leftJoinAndSelect('employee.benefit', 'benefit')
      .cache(false) // Explicitly disable query caching
      .getMany();
      
    console.log(`getEmployeesForListing: Found ${employeesData.length} employees with joined data`);
    
    // More detailed logging to debug the issue
    employeesData.forEach((employee, index) => {
      console.log(`Employee #${index + 1} - ID: ${employee.id}, Name: ${employee.firstName} ${employee.lastName}`);
      console.log(`  Contact Data Present: ${employee.contact ? 'Yes' : 'No'}`);
      console.log(`  Job Data Present: ${employee.job ? 'Yes' : 'No'}`);
      console.log(`  Benefit Data Present: ${employee.benefit ? 'Yes' : 'No'}`);
      
      if (employee.contact) {
        console.log(`  Email: ${employee.contact.officialEmail || 'Not set'}`);
      }
      
      if (employee.job) {
        console.log(`  Department: ${employee.job.department || 'Not set'}`);
        console.log(`  Designation: ${employee.job.designation || 'Not set'}`);
      }
    });
    
    if (employeesData.length === 0) {
      console.log('getEmployeesForListing: No employees found in database');
      return res.json({
        success: true,
        count: 0,
        employees: []
      });
    }
    
    // Transform data into the format expected by the frontend
    const normalizedEmployees = employeesData.map(employee => {
      return {
        id: employee.id,
        employeeId: employee.employeeId,
        firstName: employee.firstName,
        lastName: employee.lastName,
        officialEmail: employee.contact?.officialEmail || '',
        personalEmail: employee.contact?.personalEmail || '',
        department: employee.job?.department || '',
        designation: employee.job?.designation || '',
        joinDate: employee.job?.joinDate || '',
        employmentStatus: employee.status || 'active',
        mobileNumber: employee.contact?.mobileNumber || '',
        totalSalary: employee.benefit?.totalSalary || '0',
        profileImagePath: employee.profileImagePath || '',
        gender: employee.gender || '', // Ensure gender is included
        dateOfBirth: employee.dateOfBirth || '', // Add dateOfBirth field
        // Add missing job fields
        location: employee.job?.location || '',
        reportingTo: employee.job?.reportingTo || '',
        employmentType: employee.job?.employmentType || '',
        // Add missing contact fields
        officialNumber: employee.contact?.officialNumber || '',
        // Add status field
        status: employee.status || 'active'
      };
    });
    
    console.log(`getEmployeesForListing: Returning ${normalizedEmployees.length} normalized employees`);
    
    return res.json({
      success: true,
      count: normalizedEmployees.length,
      employees: normalizedEmployees,
      timestamp: Date.now() // Return current timestamp for validation
    });
  } catch (error) {
    console.error('Error fetching employees for listing:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch employees',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get employee by ID
export const getEmployeeById = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { t } = req.query; // Use timestamp if provided for cache busting
  
  try {
    console.log(`getEmployeeById: Fetching employee with ID ${id}, timestamp: ${t || 'none'}`);
    
    // Validate ID is a number
    const employeeId = parseInt(id);
    if (isNaN(employeeId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid employee ID. ID must be a number.'
      });
    }
    
    const employeeRepository = AppDataSource.getRepository('Employee');
    const employee = await employeeRepository.findOne({
      where: { id: employeeId },
      relations: [
        'contact', 
        'job', 
        'benefit',
        'education',
        'experience',
        'devices',
        'projects',
        'family',
        'skills',
        'vehicles',
        'healthRecords'
      ]
    });
    
    if (!employee) {
      console.log(`getEmployeeById: Employee with ID ${id} not found`);
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }
    
    // Fetch documents separately to avoid "Cannot query across one-to-many" error
    const documentRepository = AppDataSource.getRepository('EmployeeDocument');
    const employeeDocuments = await documentRepository.find({
      where: { employee: { id: Number(id) } }
    });
    
    console.log(`Found ${employeeDocuments.length} documents for employee ${id}`);
    
    // Format documents for the frontend with additional validation
    const processedDocuments = employeeDocuments.map(doc => {
      // Construct the full file path to check existence
      const fullPath = doc.filePath ? path.join(process.cwd(), 'public', doc.filePath) : null;
      const fileExists = fullPath ? fs.existsSync(fullPath) : false;
      
      // Extract file extension for better handling in the frontend
      const fileExtension = doc.filePath ? path.extname(doc.filePath).toLowerCase() : '';
      
      return {
        ...doc,
        preview: doc.filePath ? `/${doc.filePath}` : null,
        fileExists,
        fileExtension
      };
    });
    
    console.log(`Processed ${processedDocuments.length} documents for frontend`);
    
    // Also log family information
    if (employee.family) {
      console.log(`Found family members for employee: ${employee.family.length}`);
    } else {
      console.log('No family members found for employee');
    }

    console.log(`Found employee: ${employee.firstName} ${employee.lastName}`);
    
    // Process data for frontend
    const processedEmployee = { ...employee } as Record<string, any>;
    
    // Add documents that were fetched separately
    processedEmployee.documents = processedDocuments;
    
    // Convert related entities to the expected format for the frontend
    if (processedEmployee.education) {
      processedEmployee.educationEntries = processedEmployee.education;
      delete processedEmployee.education;
    }
    
    if (processedEmployee.experience) {
      processedEmployee.experienceEntries = processedEmployee.experience;
      delete processedEmployee.experience;
    }
    
    if (processedEmployee.family) {
      // Split family members into children and dependents
      console.log('Processing family members:', processedEmployee.family.length);
      processedEmployee.children = processedEmployee.family.filter((f: any) => f.relationship === 'child' || f.type === 'child');
      processedEmployee.dependents = processedEmployee.family.filter((f: any) => 
        f.relationship !== 'child' && f.type !== 'child' && f.relationship !== 'spouse' && f.type !== 'spouse');
      
      console.log(`Split into ${processedEmployee.children.length} children and ${processedEmployee.dependents.length} dependents`);
      
      // Look for spouse in family members
      const spouse = processedEmployee.family.find((f: any) => f.relationship === 'spouse' || f.type === 'spouse' || f.relationship === 'Spouse' || f.type === 'Spouse');
      if (spouse) {
        console.log('Found spouse in family members:', JSON.stringify(spouse, null, 2));
        processedEmployee.spouseName = spouse.name || '';
        processedEmployee.spouseDateOfBirth = spouse.dateOfBirth || '';
        processedEmployee.spouseOccupation = spouse.occupation || '';
        processedEmployee.spouseEmployer = spouse.employer || '';
        processedEmployee.spouseContactNumber = spouse.contactNumber || '';
        processedEmployee.spouseCNIC = spouse.cnic || '';
      } else {
        console.log('No spouse found in family members. Family data:', JSON.stringify(processedEmployee.family, null, 2));
      }
      
      delete processedEmployee.family;
    }
    
    if (processedEmployee.devices) {
      processedEmployee.deviceEntries = processedEmployee.devices;
      delete processedEmployee.devices;
    }
    
    if (processedEmployee.projects) {
      processedEmployee.projectEntries = processedEmployee.projects;
      delete processedEmployee.projects;
    }
    
    // Process existing skills data to put fields at the top level
    if (processedEmployee.skills && processedEmployee.skills.length > 0) {
      // Take the first entry's skills (there should only be one)
      const skill = processedEmployee.skills[0];
      processedEmployee.professionalSkills = skill.professionalSkills || '';
      processedEmployee.technicalSkills = skill.technicalSkills || '';
      processedEmployee.certifications = skill.certifications || '';
      processedEmployee.languages = skill.languages || '';
      delete processedEmployee.skills;
    }
    
    // Include document entries
    if (processedEmployee.documents) {
      console.log('Processing documents:', processedEmployee.documents.length);
      processedEmployee.documentEntries = processedEmployee.documents.map((doc: any) => ({
        ...doc,
        // Add any missing required fields
        files: doc.filePath ? [{
          id: Date.now().toString(),
          file: null,
          preview: `/uploads/${doc.filePath}`
        }] : []
      }));
      console.log('Processed document entries:', processedEmployee.documentEntries.length);
      delete processedEmployee.documents;
    }
    
    // Include requiredDocuments if it exists in the database
    // Extract from notes field using <REQUIRED_DOCS> tags
    try {
      if (processedEmployee.notes) {
        const match = processedEmployee.notes.match(/<REQUIRED_DOCS>(.*?)<\/REQUIRED_DOCS>/s);
        if (match && match[1]) {
          console.log('Found requiredDocuments data in notes field');
          processedEmployee.requiredDocuments = JSON.parse(match[1]);
          
          // Clean up notes field by removing the tag - don't show this in the frontend
          processedEmployee.notes = processedEmployee.notes.replace(/<REQUIRED_DOCS>.*?<\/REQUIRED_DOCS>/s, '').trim();
          console.log('Cleaned up notes field for frontend display');
        } else {
          console.log('No requiredDocuments found in notes field, setting default values');
          processedEmployee.requiredDocuments = {
            cnic: false,
            passport: false,
            drivingLicense: false,
            educationalCertificates: false,
            experienceCertificates: false,
            bankAccountDetails: false,
            taxDocuments: false,
            medicalCertificate: false,
            policeClearanceCertificate: false,
            cv: false,
            affidavit: false,
            otherDocuments: false
          };
        }
      } else {
        console.log('No notes field found, setting default values for requiredDocuments');
        processedEmployee.requiredDocuments = {
          cnic: false,
          passport: false,
          drivingLicense: false,
          educationalCertificates: false,
          experienceCertificates: false,
          bankAccountDetails: false,
          taxDocuments: false,
          medicalCertificate: false,
          policeClearanceCertificate: false,
          cv: false,
          affidavit: false,
          otherDocuments: false
        };
      }
    } catch (error) {
      console.error('Error processing requiredDocuments:', error);
      processedEmployee.requiredDocuments = {
        cnic: false,
        passport: false,
        drivingLicense: false,
        educationalCertificates: false,
        experienceCertificates: false,
        bankAccountDetails: false,
        taxDocuments: false,
        medicalCertificate: false,
        policeClearanceCertificate: false,
        cv: false,
        affidavit: false,
        otherDocuments: false
      };
    }
    
    // Include vehicle data
    if (processedEmployee.vehicles && processedEmployee.vehicles.length > 0) {
      const vehicle = processedEmployee.vehicles[0];
      processedEmployee.vehicleType = vehicle.vehicleType || '';
      processedEmployee.registrationNumber = vehicle.registrationNumber || '';
      processedEmployee.providedByCompany = vehicle.providedByCompany || false;
      processedEmployee.handingOverDate = vehicle.handingOverDate || '';
      processedEmployee.returnDate = vehicle.returnDate || '';
      processedEmployee.vehicleMakeModel = vehicle.vehicleMakeModel || '';
      processedEmployee.vehicleColor = vehicle.vehicleColor || '';
      processedEmployee.mileageAtIssuance = vehicle.mileageAtIssuance || '';
      delete processedEmployee.vehicles;
    }
    
    // Include health data
    if (processedEmployee.healthRecords && processedEmployee.healthRecords.length > 0) {
      const health = processedEmployee.healthRecords[0];
      processedEmployee.vaccinationRecords = health.vaccinationRecords || '';
      processedEmployee.medicalHistory = health.medicalHistory || '';
      processedEmployee.bloodGroup = health.bloodGroup || '';
      processedEmployee.allergies = health.allergies || '';
      processedEmployee.chronicConditions = health.chronicConditions || '';
      processedEmployee.regularMedications = health.regularMedications || '';
      delete processedEmployee.healthRecords;
    }

    console.log(`Returning employee data with fields: ${Object.keys(processedEmployee).join(', ')}`);
    
    return res.json({
      success: true,
      employee: processedEmployee
    });
  } catch (error) {
    console.error('Error fetching employee:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return res.status(500).json({
      success: false,
      message: 'Error fetching employee data', 
      error: errorMessage 
    });
  }
};

// Update employee
export const updateEmployee = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const employeeData = req.body;
    
    // Validate ID is a number
    const employeeId = parseInt(id);
    if (isNaN(employeeId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid employee ID. ID must be a number.'
      });
    }
    
    console.log(`updateEmployee: Updating employee with ID ${id}`, { dataKeys: Object.keys(employeeData) });
    
    // Check if employee exists
    const employee = await employeeRepository.findOne({ 
      where: { id: employeeId },
      relations: [
        'contact', 
        'job', 
        'benefit',
        'education',
        'experience',
        'devices',
        'projects',
        'family',
        'skills',
        'vehicles',
        'healthRecords'
      ]
    });
    
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }
    
    // Fetch documents separately to avoid "Cannot query across one-to-many" error
    const documentRepository = AppDataSource.getRepository('EmployeeDocument');
    const employeeDocuments = await documentRepository.find({
      where: { employee: { id: employeeId } }
    });
    
    // Add documents to employee for processing
    (employee as any).documents = employeeDocuments;
    
    // Separate data for different tables
    const employeeFields = ['firstName', 'middleName', 'lastName', 'gender', 'dateOfBirth', 
                            'religion', 'cnicNumber', 'cnicExpiryDate', 'nationality', 
                            'maritalStatus', 'bloodType', 'profileImagePath', 'status',
                            'employeeId', 'fatherName', 'statusDate'];
    
    const contactFields = ['mobileNumber', 'officialNumber', 'officialEmail', 'personalEmail',
                           'permanentAddress', 'currentAddress', 'emergencyContactName',
                           'emergencyContactPhone', 'emergencyContactRelationship',
                           'linkedinProfile', 'otherSocialProfiles'];
    
    const jobFields = ['designation', 'department', 'project', 'location', 'employmentType',
                       'employmentStatus', 'employeeLevel', 'joinDate', 'probationEndDate',
                       'noticePeriod', 'reportingTo', 'remoteWorkEligible',
                       'nextReviewDate', 'trainingRequirements', 'workSchedule', 'shiftType'];
    
    const benefitFields = ['totalSalary', 'salaryTier', 'salaryTrench', 'cashAmount', 'bankAmount', 'paymentMode',
                          'foodAllowanceInSalary', 'fuelAllowanceInSalary', 'numberOfMeals',
                          'fuelInLiters', 'fuelAmount', 'foodProvidedByCompany',
                          'bankName', 'bankBranch', 'accountNumber', 'accountTitle', 'iban',
                          'healthInsuranceProvider', 'healthInsurancePolicyNumber', 'healthInsuranceExpiryDate',
                          'lifeInsuranceProvider', 'lifeInsurancePolicyNumber', 'lifeInsuranceExpiryDate',
                          'accommodationProvidedByEmployer', 'accommodationType', 'accommodationAddress'];
    
    // Create update objects for each table
    const employeeUpdate: Record<string, any> = {};
    const contactUpdate: Record<string, any> = {};
    const jobUpdate: Record<string, any> = {};
    const benefitUpdate: Record<string, any> = {};
    
    // Sort data into the appropriate update objects
    Object.keys(employeeData).forEach(key => {
      if (employeeFields.includes(key)) {
        employeeUpdate[key] = employeeData[key];
      } else if (contactFields.includes(key)) {
        contactUpdate[key] = employeeData[key];
      } else if (jobFields.includes(key)) {
        jobUpdate[key] = employeeData[key];
      } else if (benefitFields.includes(key)) {
        benefitUpdate[key] = employeeData[key];
      }
      // Skip educationEntries, experienceEntries, deviceEntries, etc. as they're now separate entities
    });
    
    // Remove fields that no longer exist in the Employee entity
    const fieldsToSkip = [
      'educationEntries', 'experienceEntries', 'deviceEntries',
      'children', 'dependents', 'documents', 'requiredDocuments', 'projectEntries'
    ];
    
    fieldsToSkip.forEach(field => {
      if (field in employeeUpdate) {
        console.log(`Removing ${field} from employeeUpdate as it's not a direct property of Employee entity`);
        delete employeeUpdate[field];
      }
    });
    
    // Process notes and special instructions
    if ('notes' in employeeData && employeeData.notes !== undefined) {
      employeeUpdate.notes = employeeData.notes;
    }
    
    if ('specialInstructions' in employeeData && employeeData.specialInstructions !== undefined) {
      employeeUpdate.specialInstructions = employeeData.specialInstructions;
    }
    
    console.log('Update data prepared:');
    console.log('- Employee fields:', Object.keys(employeeUpdate));
    console.log('- Contact fields:', Object.keys(contactUpdate));
    console.log('- Job fields:', Object.keys(jobUpdate));
    console.log('- Benefit fields:', Object.keys(benefitUpdate));
    
    // Begin transaction for updates
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      // Update employee
      if (Object.keys(employeeUpdate).length > 0) {
        await queryRunner.manager.update(Employee, id, employeeUpdate);
        console.log(`Updated employee record with fields: ${Object.keys(employeeUpdate).join(', ')}`);
      }
      
      // Update or create contact
      if (Object.keys(contactUpdate).length > 0) {
        if (employee.contact) {
          // Update existing contact
          await queryRunner.manager.update('EmployeeContact', employee.contact.id, contactUpdate);
        } else {
          // Create new contact
          const contactRepository = AppDataSource.getRepository('EmployeeContact');
          const contact = contactRepository.create({
            ...contactUpdate,
            employee
          });
          await queryRunner.manager.save(contact);
        }
      }
      
      // Update or create job
      if (Object.keys(jobUpdate).length > 0) {
        if (employee.job) {
          // Update existing job
          await queryRunner.manager.update('EmployeeJob', employee.job.id, jobUpdate);
        } else {
          // Create new job
          const jobRepository = AppDataSource.getRepository('EmployeeJob');
          const job = jobRepository.create({
            ...jobUpdate,
            employee
          });
          await queryRunner.manager.save(job);
        }
      }
      
      // Update or create benefit
      if (Object.keys(benefitUpdate).length > 0) {
        if (employee.benefit) {
          // Update existing benefit
          await queryRunner.manager.update('EmployeeBenefit', employee.benefit.id, benefitUpdate);
        } else {
          // Create new benefit
          const benefitRepository = AppDataSource.getRepository('EmployeeBenefit');
          const benefit = benefitRepository.create({
            ...benefitUpdate,
            employee
          });
          await queryRunner.manager.save(benefit);
        }
      }
      
      // Handle education entries
      if (employeeData.educationEntries && Array.isArray(employeeData.educationEntries)) {
        // First, get existing education entries for this employee
        const educationRepository = AppDataSource.getRepository('EmployeeEducation');
        const existingEducation = await educationRepository.find({
          where: { employee: { id: Number(id) } }
        });
        
        console.log(`Updating education entries: ${employeeData.educationEntries.length} new entries, ${existingEducation.length} existing entries`);
        
        // Create a map of existing entries by ID for easy lookup
        const existingEducationMap = new Map();
        existingEducation.forEach(entry => {
          existingEducationMap.set(entry.id, entry);
        });
        
        // Process each education entry from request
        for (const educationEntry of employeeData.educationEntries) {
          // Skip completely empty entries
          if (!educationEntry.educationLevel && !educationEntry.degree && !educationEntry.institution) {
            console.log('Skipping empty education entry');
            continue;
          }
          
          const educationEntryId = parseInt(educationEntry.id);
          if (educationEntry.id && !isNaN(educationEntryId) && existingEducationMap.has(educationEntryId)) {
            // Update existing entry
            const existingEntry = existingEducationMap.get(educationEntryId);
            existingEducationMap.delete(educationEntryId); // Remove from map to track which ones to delete
            
            await queryRunner.manager.update('EmployeeEducation', existingEntry.id, {
              educationLevel: educationEntry.educationLevel || existingEntry.educationLevel,
              degree: educationEntry.degree || existingEntry.degree,
              major: educationEntry.major || existingEntry.major,
              institution: educationEntry.institution || existingEntry.institution,
              graduationYear: educationEntry.graduationYear || existingEntry.graduationYear,
              grade: educationEntry.grade || existingEntry.grade
            });
            
            console.log(`Updated education entry id ${existingEntry.id}`);
          } else {
            // Create new entry
            const newEducation = educationRepository.create({
              educationLevel: educationEntry.educationLevel || '',
              degree: educationEntry.degree || '',
              major: educationEntry.major || '',
              institution: educationEntry.institution || '',
              graduationYear: educationEntry.graduationYear || '',
              grade: educationEntry.grade || '',
              employee: { id: Number(id) }
            });
            
            await queryRunner.manager.save(newEducation);
            console.log(`Created new education entry: ${newEducation.degree} at ${newEducation.institution}`);
          }
        }
        
        // Delete entries that were not in the update
        if (existingEducationMap.size > 0) {
          const idsToDelete = Array.from(existingEducationMap.keys());
          console.log(`Deleting ${idsToDelete.length} education entries not in the update`);
          
          if (idsToDelete.length > 0) {
            await queryRunner.manager.delete('EmployeeEducation', idsToDelete);
          }
        }
      }
      
      // Handle experience entries
      if (employeeData.experienceEntries && Array.isArray(employeeData.experienceEntries)) {
        // First, get existing experience entries for this employee
        const experienceRepository = AppDataSource.getRepository('EmployeeExperience');
        const existingExperience = await experienceRepository.find({
          where: { employee: { id: Number(id) } }
        });
        
        console.log(`Updating experience entries: ${employeeData.experienceEntries.length} new entries, ${existingExperience.length} existing entries`);
        
        // Create a map of existing entries by ID for easy lookup
        const existingExperienceMap = new Map();
        existingExperience.forEach(entry => {
          existingExperienceMap.set(entry.id, entry);
        });
        
        // Process each experience entry from request
        for (const experienceEntry of employeeData.experienceEntries) {
          // Skip completely empty entries
          if (!experienceEntry.companyName && !experienceEntry.jobTitle) {
            console.log('Skipping empty experience entry');
            continue;
          }
          
          const experienceEntryId = parseInt(experienceEntry.id);
          if (experienceEntry.id && !isNaN(experienceEntryId) && existingExperienceMap.has(experienceEntryId)) {
            // Update existing entry
            const existingEntry = existingExperienceMap.get(experienceEntryId);
            existingExperienceMap.delete(experienceEntryId); // Remove from map to track which ones to delete
            
            await queryRunner.manager.update('EmployeeExperience', existingEntry.id, {
              companyName: experienceEntry.companyName || existingEntry.companyName,
              jobTitle: experienceEntry.jobTitle || existingEntry.jobTitle,
              startDate: experienceEntry.startDate || existingEntry.startDate,
              endDate: experienceEntry.endDate || existingEntry.endDate,
              currentlyWorking: experienceEntry.currentlyWorking !== undefined ? experienceEntry.currentlyWorking : existingEntry.currentlyWorking,
              jobDescription: experienceEntry.jobDescription || existingEntry.jobDescription
            });
            
            console.log(`Updated experience entry id ${existingEntry.id}`);
          } else {
            // Create new entry
            const newExperience = experienceRepository.create({
              companyName: experienceEntry.companyName || '',
              jobTitle: experienceEntry.jobTitle || '',
              startDate: experienceEntry.startDate || '',
              endDate: experienceEntry.endDate || '',
              currentlyWorking: experienceEntry.currentlyWorking || false,
              jobDescription: experienceEntry.jobDescription || '',
              employee: { id: Number(id) }
            });
            
            await queryRunner.manager.save(newExperience);
            console.log(`Created new experience entry: ${newExperience.jobTitle} at ${newExperience.companyName}`);
          }
        }
        
        // Delete entries that were not in the update
        if (existingExperienceMap.size > 0) {
          const idsToDelete = Array.from(existingExperienceMap.keys());
          console.log(`Deleting ${idsToDelete.length} experience entries not in the update`);
          
          if (idsToDelete.length > 0) {
            await queryRunner.manager.delete('EmployeeExperience', idsToDelete);
          }
        }
      }
      
      // Handle device entries
      if (employeeData.deviceEntries && Array.isArray(employeeData.deviceEntries)) {
        // First, get existing device entries for this employee
        const deviceRepository = AppDataSource.getRepository('EmployeeDevice');
        const existingDevices = await deviceRepository.find({
          where: { employee: { id: Number(id) } }
        });
        
        console.log(`Updating device entries: ${employeeData.deviceEntries.length} new entries, ${existingDevices.length} existing entries`);
        
        // Create a map of existing entries by ID for easy lookup
        const existingDeviceMap = new Map();
        existingDevices.forEach(entry => {
          existingDeviceMap.set(entry.id, entry);
        });
        
        // Process each device entry from request
        for (const deviceEntry of employeeData.deviceEntries) {
          // Skip completely empty entries
          if (!deviceEntry.deviceName && !deviceEntry.makeModel && !deviceEntry.serialNumber) {
            console.log('Skipping empty device entry');
            continue;
          }
          
          const deviceEntryId = parseInt(deviceEntry.id);
          if (deviceEntry.id && !isNaN(deviceEntryId) && existingDeviceMap.has(deviceEntryId)) {
            // Update existing entry
            const existingEntry = existingDeviceMap.get(deviceEntryId);
            existingDeviceMap.delete(deviceEntryId); // Remove from map to track which ones to delete
            
            await queryRunner.manager.update('EmployeeDevice', existingEntry.id, {
              deviceName: deviceEntry.deviceName || existingEntry.deviceName,
              makeModel: deviceEntry.makeModel || existingEntry.makeModel,
              serialNumber: deviceEntry.serialNumber || existingEntry.serialNumber,
              handoverDate: deviceEntry.handoverDate || existingEntry.handoverDate,
              returnDate: deviceEntry.returnDate || existingEntry.returnDate,
              condition: deviceEntry.condition || existingEntry.condition
            });
            
            console.log(`Updated device entry id ${existingEntry.id}`);
          } else {
            // Create new entry
            const newDevice = deviceRepository.create({
              deviceName: deviceEntry.deviceName || '',
              makeModel: deviceEntry.makeModel || '',
              serialNumber: deviceEntry.serialNumber || '',
              handoverDate: deviceEntry.handoverDate || '',
              returnDate: deviceEntry.returnDate || '',
              condition: deviceEntry.condition || '',
              employee: { id: Number(id) }
            });
            
            await queryRunner.manager.save(newDevice);
            console.log(`Created new device entry: ${newDevice.deviceName} (${newDevice.makeModel})`);
          }
        }
        
        // Delete entries that were not in the update
        if (existingDeviceMap.size > 0) {
          const idsToDelete = Array.from(existingDeviceMap.keys());
          console.log(`Deleting ${idsToDelete.length} device entries not in the update`);
          
          if (idsToDelete.length > 0) {
            await queryRunner.manager.delete('EmployeeDevice', idsToDelete);
          }
        }
      }
      
      // Handle family members (children and dependents)
      // First, get existing family entries
      const familyRepository = AppDataSource.getRepository('EmployeeFamily');
      const existingFamily = await familyRepository.find({
        where: { employee: { id: Number(id) } }
      });
      
      console.log(`Found ${existingFamily.length} existing family members`);
      
      // Create a map of existing entries by ID for easy lookup
      const existingFamilyMap = new Map();
      existingFamily.forEach(entry => {
        existingFamilyMap.set(entry.id, entry);
      });
      
      // Create a list of family members to save
      const familyToSave = [];
      
      // Process children
      if (employeeData.children && Array.isArray(employeeData.children)) {
        console.log(`Processing ${employeeData.children.length} children`);
        
        for (const child of employeeData.children) {
          if (!child.name) {
            console.log('Skipping empty child entry');
            continue;
          }
          
          const childId = parseInt(child.id);
          if (child.id && !isNaN(childId) && existingFamilyMap.has(childId)) {
            // Update existing entry
            const existingEntry = existingFamilyMap.get(childId);
            existingFamilyMap.delete(childId);
            
            await queryRunner.manager.update('EmployeeFamily', existingEntry.id, {
              name: child.name || existingEntry.name,
              dateOfBirth: child.dateOfBirth || existingEntry.dateOfBirth,
              gender: child.gender || existingEntry.gender,
              relationship: 'child',
              cnic: child.cnic || existingEntry.cnic,
              type: 'child'
            });
            
            console.log(`Updated child entry id ${existingEntry.id}`);
          } else {
            // Create new entry
            familyToSave.push({
              name: child.name || '',
              dateOfBirth: child.dateOfBirth || '',
              gender: child.gender || '',
              relationship: 'child',
              cnic: child.cnic || '',
              type: 'child',
              employee: { id: Number(id) }
            });
          }
        }
      }
      
      // Process dependents
      if (employeeData.dependents && Array.isArray(employeeData.dependents)) {
        console.log(`Processing ${employeeData.dependents.length} dependents`);
        
        for (const dependent of employeeData.dependents) {
          if (!dependent.name) {
            console.log('Skipping empty dependent entry');
            continue;
          }
          
          const dependentId = parseInt(dependent.id);
          if (dependent.id && !isNaN(dependentId) && existingFamilyMap.has(dependentId)) {
            // Update existing entry
            const existingEntry = existingFamilyMap.get(dependentId);
            existingFamilyMap.delete(dependentId);
            
            await queryRunner.manager.update('EmployeeFamily', existingEntry.id, {
              name: dependent.name || existingEntry.name,
              dateOfBirth: dependent.dateOfBirth || existingEntry.dateOfBirth,
              gender: dependent.gender || existingEntry.gender,
              relationship: dependent.relationship || existingEntry.relationship || 'other',
              cnic: dependent.cnic || existingEntry.cnic,
              type: 'dependent'
            });
            
            console.log(`Updated dependent entry id ${existingEntry.id}`);
          } else {
            // Create new entry
            familyToSave.push({
              name: dependent.name || '',
              dateOfBirth: dependent.dateOfBirth || '',
              gender: dependent.gender || '',
              relationship: dependent.relationship || 'other',
              cnic: dependent.cnic || '',
              type: 'dependent',
              employee: { id: Number(id) }
            });
          }
        }
      }
      
      // Process spouse data if it exists
      if (employeeData.spouseName) {
        console.log('Processing spouse data for:', employeeData.spouseName);
        // Check if we already have a spouse record
        const existingSpouse = existingFamily.find(f => f.type === 'spouse' || f.relationship === 'spouse');
        
        if (existingSpouse) {
          console.log('Updating existing spouse record:', existingSpouse.id);
          existingFamilyMap.delete(existingSpouse.id);
          
          await queryRunner.manager.update('EmployeeFamily', existingSpouse.id, {
            name: employeeData.spouseName,
            dateOfBirth: employeeData.spouseDateOfBirth || existingSpouse.dateOfBirth,
            gender: '', // Not typically stored for spouse
            relationship: 'spouse',
            cnic: employeeData.spouseCNIC || existingSpouse.cnic,
            occupation: employeeData.spouseOccupation || existingSpouse.occupation,
            employer: employeeData.spouseEmployer || existingSpouse.employer,
            contactNumber: employeeData.spouseContactNumber || existingSpouse.contactNumber,
            type: 'spouse'
          });
          
          console.log('Updated spouse record');
        } else {
          // Create new spouse record
          console.log('Creating new spouse record');
          familyToSave.push({
            name: employeeData.spouseName || '',
            dateOfBirth: employeeData.spouseDateOfBirth || '',
            gender: '',
            relationship: 'spouse',
            cnic: employeeData.spouseCNIC || '',
            occupation: employeeData.spouseOccupation || '',
            employer: employeeData.spouseEmployer || '',
            contactNumber: employeeData.spouseContactNumber || '',
            type: 'spouse',
            employee: { id: Number(id) }
          });
        }
      }
      
      // Save new family members
      if (familyToSave.length > 0) {
        console.log(`Creating ${familyToSave.length} new family members`);
        
        for (const member of familyToSave) {
          const family = familyRepository.create(member);
          await queryRunner.manager.save(family);
          console.log(`Created new family member: ${family.name} (${family.relationship})`);
        }
      }
      
      // Delete family members that were not in the update
      if (existingFamilyMap.size > 0) {
        const idsToDelete = Array.from(existingFamilyMap.keys());
        console.log(`Deleting ${idsToDelete.length} family members not in the update`);
        
        if (idsToDelete.length > 0) {
          await queryRunner.manager.delete('EmployeeFamily', idsToDelete);
        }
      }
      
      // Handle documents
      if (employeeData.documents && Array.isArray(employeeData.documents)) {
        // First, get existing document entries for this employee
        const documentRepository = AppDataSource.getRepository('EmployeeDocument');
        const existingDocuments = await documentRepository.find({
          where: { employee: { id: Number(id) } }
        });
        
        console.log(`Updating document entries: ${employeeData.documents.length} new entries, ${existingDocuments.length} existing entries`);
        
        // Create a map of existing entries by ID for easy lookup
        const existingDocMap = new Map();
        existingDocuments.forEach(entry => {
          existingDocMap.set(entry.id, entry);
        });
        
        // Process each document entry from request
        for (const docEntry of employeeData.documents) {
          // Skip completely empty entries
          if (!docEntry.documentType) {
            console.log('Skipping empty document entry');
            continue;
          }
          
          const docEntryId = parseInt(docEntry.id);
          if (docEntry.id && !isNaN(docEntryId) && existingDocMap.has(docEntryId)) {
            // Update existing entry
            const existingEntry = existingDocMap.get(docEntryId);
            existingDocMap.delete(docEntryId); // Remove from map to track which ones to delete
            
            await queryRunner.manager.update('EmployeeDocument', existingEntry.id, {
              documentType: docEntry.documentType || existingEntry.documentType,
              verificationStatus: docEntry.verificationStatus || existingEntry.verificationStatus
            });
            
            console.log(`Updated document entry id ${existingEntry.id}`);
          } else {
            // Create new entry
            const newDoc = documentRepository.create({
              documentType: docEntry.documentType || '',
              verificationStatus: docEntry.verificationStatus || 'verified',
              employee: { id: Number(id) }
            });
            
            await queryRunner.manager.save(newDoc);
            console.log(`Created new document entry: ${newDoc.documentType}`);
          }
        }
        
        // Delete entries that were not in the update
        if (existingDocMap.size > 0) {
          const idsToDelete = Array.from(existingDocMap.keys());
          console.log(`Deleting ${idsToDelete.length} document entries not in the update`);
          
          if (idsToDelete.length > 0) {
            await queryRunner.manager.delete('EmployeeDocument', idsToDelete);
          }
        }
      }
      
      // Handle project entries
      if (employeeData.projectEntries && Array.isArray(employeeData.projectEntries)) {
        // First, get existing project entries for this employee
        const projectRepository = AppDataSource.getRepository('EmployeeProject');
        const existingProjects = await projectRepository.find({
          where: { employee: { id: Number(id) } }
        });
        
        console.log(`Updating project entries: ${employeeData.projectEntries.length} new entries, ${existingProjects.length} existing entries`);
        
        // Create a map of existing entries by ID for easy lookup
        const existingProjectMap = new Map();
        existingProjects.forEach(entry => {
          existingProjectMap.set(entry.id, entry);
        });
        
        // Process each project entry from request
        for (const projectEntry of employeeData.projectEntries) {
          // Skip completely empty entries
          if (!projectEntry.projectName && !projectEntry.role) {
            console.log('Skipping empty project entry');
            continue;
          }
          
          const projectEntryId = parseInt(projectEntry.id);
          if (projectEntry.id && !isNaN(projectEntryId) && existingProjectMap.has(projectEntryId)) {
            // Update existing entry
            const existingEntry = existingProjectMap.get(projectEntryId);
            existingProjectMap.delete(projectEntryId); // Remove from map to track which ones to delete
            
            await queryRunner.manager.update('EmployeeProject', existingEntry.id, {
              projectName: projectEntry.projectName || existingEntry.projectName,
              role: projectEntry.role || existingEntry.role,
              startDate: projectEntry.startDate || existingEntry.startDate,
              endDate: projectEntry.endDate || existingEntry.endDate,
              currentProject: projectEntry.currentProject !== undefined ? projectEntry.currentProject : existingEntry.currentProject,
              technologies: projectEntry.technologies || existingEntry.technologies,
              description: projectEntry.description || existingEntry.description,
              achievements: projectEntry.achievements || existingEntry.achievements,
              teamSize: projectEntry.teamSize || existingEntry.teamSize,
              clientName: projectEntry.clientName || existingEntry.clientName
            });
            
            console.log(`Updated project entry id ${existingEntry.id}`);
          } else {
            // Create new entry
            const newProject = projectRepository.create({
              projectName: projectEntry.projectName || '',
              role: projectEntry.role || '',
              startDate: projectEntry.startDate || '',
              endDate: projectEntry.endDate || '',
              currentProject: projectEntry.currentProject || false,
              technologies: projectEntry.technologies || '',
              description: projectEntry.description || '',
              achievements: projectEntry.achievements || '',
              teamSize: projectEntry.teamSize || '',
              clientName: projectEntry.clientName || '',
              employee: { id: Number(id) }
            });
            
            await queryRunner.manager.save(newProject);
            console.log(`Created new project entry: ${newProject.projectName} (${newProject.role})`);
          }
        }
        
        // Delete entries that were not in the update
        if (existingProjectMap.size > 0) {
          const idsToDelete = Array.from(existingProjectMap.keys());
          console.log(`Deleting ${idsToDelete.length} project entries not in the update`);
          
          if (idsToDelete.length > 0) {
            await queryRunner.manager.delete('EmployeeProject', idsToDelete);
          }
        }
      }
      
      // Save skills and certifications directly to employee record
      const skillsFields = [
        'professionalSkills', 'technicalSkills', 'certifications', 'languages'
      ];
      
      const skillsData: Record<string, any> = {};
      skillsFields.forEach(field => {
        if (field in employeeData && employeeData[field] !== undefined) {
          skillsData[field] = employeeData[field];
        }
      });
      
      if (Object.keys(skillsData).length > 0) {
        console.log('Updating skills and certifications data:', Object.keys(skillsData));
        
        // Find existing skill record or create new one
        const skillRepository = AppDataSource.getRepository('EmployeeSkill');
        const existingSkill = await skillRepository.findOne({
          where: { employee: { id: Number(id) } }
        });
        
        if (existingSkill) {
          // Update existing skill record
          await queryRunner.manager.update('EmployeeSkill', existingSkill.id, skillsData);
          console.log(`Updated existing skill record with ID ${existingSkill.id}`);
        } else {
          // Create new skill record
          const newSkill = skillRepository.create({
            ...skillsData,
            employee: { id: Number(id) }
          });
          await queryRunner.manager.save(newSkill);
          console.log(`Created new skill record for employee ID ${id}`);
        }
      }
      
      // Save vehicle details directly to employee record
      const vehicleFields = [
        'vehicleType', 'registrationNumber', 'providedByCompany', 
        'handingOverDate', 'returnDate', 'vehicleMakeModel',
        'vehicleColor', 'mileageAtIssuance'
      ];
      
      const vehicleData: Record<string, any> = {};
      vehicleFields.forEach(field => {
        if (field in employeeData && employeeData[field] !== undefined) {
          vehicleData[field] = employeeData[field];
        }
      });
      
      if (Object.keys(vehicleData).length > 0) {
        console.log('Updating vehicle details data:', Object.keys(vehicleData));
        
        // Find existing vehicle record or create new one
        const vehicleRepository = AppDataSource.getRepository('EmployeeVehicle');
        const existingVehicle = await vehicleRepository.findOne({
          where: { employee: { id: Number(id) } }
        });
        
        if (existingVehicle) {
          // Update existing vehicle record
          await queryRunner.manager.update('EmployeeVehicle', existingVehicle.id, vehicleData);
          console.log(`Updated existing vehicle record with ID ${existingVehicle.id}`);
        } else {
          // Create new vehicle record
          const newVehicle = vehicleRepository.create({
            ...vehicleData,
            employee: { id: Number(id) }
          });
          await queryRunner.manager.save(newVehicle);
          console.log(`Created new vehicle record for employee ID ${id}`);
        }
      }
      
      // Save medical records directly to employee record
      const medicalFields = [
        'vaccinationRecords', 'medicalHistory', 'bloodGroup',
        'allergies', 'chronicConditions', 'regularMedications'
      ];
      
      const medicalData: Record<string, any> = {};
      medicalFields.forEach(field => {
        if (field in employeeData && employeeData[field] !== undefined) {
          medicalData[field] = employeeData[field];
        }
      });
      
      if (Object.keys(medicalData).length > 0) {
        console.log('Updating medical records data:', Object.keys(medicalData));
        
        // Find existing health record or create new one
        const healthRepository = AppDataSource.getRepository('EmployeeHealth');
        const existingHealth = await healthRepository.findOne({
          where: { employee: { id: Number(id) } }
        });
        
        if (existingHealth) {
          // Update existing health record
          await queryRunner.manager.update('EmployeeHealth', existingHealth.id, medicalData);
          console.log(`Updated existing health record with ID ${existingHealth.id}`);
        } else {
          // Create new health record
          const newHealth = healthRepository.create({
            ...medicalData,
            employee: { id: Number(id) }
          });
          await queryRunner.manager.save(newHealth);
          console.log(`Created new health record for employee ID ${id}`);
        }
      }
      
      // Save notes and special instructions
      if (employeeData.notes !== undefined || employeeData.specialInstructions !== undefined) {
        console.log('Updating notes and special instructions');
        
        // Use raw SQL to update notes fields directly to avoid potential ORM issues
        const updateSql = `
          UPDATE employees 
          SET notes = ?, special_instructions = ?
          WHERE id = ?
        `;
        
        await queryRunner.query(updateSql, [
          employeeData.notes !== undefined ? employeeData.notes : null,
          employeeData.specialInstructions !== undefined ? employeeData.specialInstructions : null,
          id
        ]);
        
        console.log('Successfully updated notes fields with direct SQL query');
        
        // Remove these fields from employeeUpdate to avoid double updates
        delete employeeUpdate.notes;
        delete employeeUpdate.specialInstructions;
      }
      
      // Save requiredDocuments if provided
      if (employeeData.requiredDocuments !== undefined) {
        console.log('Updating requiredDocuments');
        
        try {
          // Use the values from the frontend - preserve the user's selections
          const docsToSave = employeeData.requiredDocuments;
          
          // Store requiredDocuments JSON in a special format in the notes field
          const reqDocsJson = JSON.stringify(docsToSave);
          
          // Get current notes value
          let currentNotes = employeeData.notes || '';
          
          // Remove existing requiredDocuments tag if present
          currentNotes = currentNotes.replace(/<REQUIRED_DOCS>.*?<\/REQUIRED_DOCS>/s, '');
          
          // Append requiredDocuments JSON to notes
          const updatedNotes = `${currentNotes}\n<REQUIRED_DOCS>${reqDocsJson}</REQUIRED_DOCS>`;
          
          // Update notes field to include requiredDocuments data
          await queryRunner.manager.update(Employee, id, { 
            notes: updatedNotes 
          });
          
          console.log('Successfully saved requiredDocuments with user selections');
          
          // Remove notes from employeeUpdate to avoid double updates
          delete employeeUpdate.notes;
        } catch (error) {
          console.error('Error storing requiredDocuments:', error);
          // Continue with the rest of the update - this is optional metadata
        }
      }
      
      // Update employee with all the direct field updates
      if (Object.keys(employeeUpdate).length > 0) {
        await queryRunner.manager.update(Employee, id, employeeUpdate);
      }
      
      // Commit transaction
      await queryRunner.commitTransaction();
      
      // Get updated employee with relations
      const updatedEmployee = await employeeRepository.findOne({ 
        where: { id: Number(id) },
        relations: [
          'contact', 
          'job', 
          'benefit', 
          'education', 
          'experience', 
          'devices', 
          'family',
          'projects',
          'skills',
          'vehicles',
          'healthRecords'
        ]
      });
      
      // Fetch documents separately
      const employeeDocuments = await documentRepository.find({
        where: { employee: { id: Number(id) } }
      });
      
      // Format documents for the frontend with validation
      const processedDocuments = employeeDocuments.map(doc => {
        // Construct the full file path to check existence
        const fullPath = doc.filePath ? path.join(process.cwd(), 'public', doc.filePath) : null;
        const fileExists = fullPath ? fs.existsSync(fullPath) : false;
        
        // Extract file extension
        const fileExtension = doc.filePath ? path.extname(doc.filePath).toLowerCase() : '';
        
        return {
          ...doc,
          preview: doc.filePath ? `/${doc.filePath}` : null,
          fileExists,
          fileExtension
        };
      });
      
      // Process data for frontend
      let transformedEmployee = null;
      
      if (updatedEmployee) {
        // Create a clean copy
        const processed = { ...updatedEmployee } as Record<string, any>;
        
        // Add documents that were fetched separately
        processed.documents = processedDocuments;
        
        // Convert related entities to the expected format for the frontend
        if (processed.education) {
          processed.educationEntries = processed.education;
          delete processed.education;
        }
        
        if (processed.experience) {
          processed.experienceEntries = processed.experience;
          delete processed.experience;
        }
        
        if (processed.family) {
          // Split family members into children and dependents
          console.log('Processing family members:', processed.family.length);
          processed.children = processed.family.filter((f: any) => f.relationship === 'child' || f.type === 'child');
          processed.dependents = processed.family.filter((f: any) => 
            f.relationship !== 'child' && f.type !== 'child' && f.relationship !== 'spouse' && f.type !== 'spouse');
            
          console.log(`Split into ${processed.children.length} children and ${processed.dependents.length} dependents`);
          
          // Look for spouse in family members
          const spouse = processed.family.find((f: any) => f.relationship === 'spouse' || f.type === 'spouse' || f.relationship === 'Spouse' || f.type === 'Spouse');
          if (spouse) {
            console.log('Found spouse in family members:', JSON.stringify(spouse, null, 2));
            processed.spouseName = spouse.name || '';
            processed.spouseDateOfBirth = spouse.dateOfBirth || '';
            processed.spouseOccupation = spouse.occupation || '';
            processed.spouseEmployer = spouse.employer || '';
            processed.spouseContactNumber = spouse.contactNumber || '';
            processed.spouseCNIC = spouse.cnic || '';
          } else {
            console.log('No spouse found in family members. Family data:', JSON.stringify(processed.family, null, 2));
          }
          
          delete processed.family;
        }
        
        if (processed.devices) {
          processed.deviceEntries = processed.devices;
          delete processed.devices;
        }
        
        // Projects are handled in a separate section to avoid duplicate code
        if (processed.projects) {
          processed.projectEntries = processed.projects;
          delete processed.projects;
        }
        
        // Process existing skills data to put fields at the top level
        if (processed.skills && processed.skills.length > 0) {
          // Take the first entry's skills (there should only be one)
          const skill = processed.skills[0];
          processed.professionalSkills = skill.professionalSkills || '';
          processed.technicalSkills = skill.technicalSkills || '';
          processed.certifications = skill.certifications || '';
          processed.languages = skill.languages || '';
          delete processed.skills;
        }
        
        // Include document entries
        if (processed.documents) {
          console.log('Processing documents:', processed.documents.length);
          processed.documentEntries = processed.documents.map((doc: any) => ({
            ...doc,
            // Add any missing required fields
            files: doc.filePath ? [{
              id: Date.now().toString(),
              file: null,
              preview: `/uploads/${doc.filePath}`
            }] : []
          }));
          console.log('Processed document entries:', processed.documentEntries.length);
          delete processed.documents;
        }
        
        // Include requiredDocuments if it exists in the database
        // Extract from notes field using <REQUIRED_DOCS> tags
        try {
          if (processed.notes) {
            const match = processed.notes.match(/<REQUIRED_DOCS>(.*?)<\/REQUIRED_DOCS>/s);
            if (match && match[1]) {
              console.log('Found requiredDocuments data in notes field');
              processed.requiredDocuments = JSON.parse(match[1]);
              
              // Clean up notes field by removing the tag - don't show this in the frontend
              processed.notes = processed.notes.replace(/<REQUIRED_DOCS>.*?<\/REQUIRED_DOCS>/s, '').trim();
              console.log('Cleaned up notes field for frontend display');
            } else {
              console.log('No requiredDocuments found in notes field, setting default values');
              processed.requiredDocuments = {
                cnic: false,
                passport: false,
                drivingLicense: false,
                educationalCertificates: false,
                experienceCertificates: false,
                bankAccountDetails: false,
                taxDocuments: false,
                medicalCertificate: false,
                policeClearanceCertificate: false,
                cv: false,
                affidavit: false,
                otherDocuments: false
              };
            }
          } else {
            console.log('No notes field found, setting default values for requiredDocuments');
            processed.requiredDocuments = {
              cnic: false,
              passport: false,
              drivingLicense: false,
              educationalCertificates: false,
              experienceCertificates: false,
              bankAccountDetails: false,
              taxDocuments: false,
              medicalCertificate: false,
              policeClearanceCertificate: false,
              cv: false,
              affidavit: false,
              otherDocuments: false
            };
          }
        } catch (error) {
          console.error('Error processing requiredDocuments:', error);
          processed.requiredDocuments = {
            cnic: false,
            passport: false,
            drivingLicense: false,
            educationalCertificates: false,
            experienceCertificates: false,
            bankAccountDetails: false,
            taxDocuments: false,
            medicalCertificate: false,
            policeClearanceCertificate: false,
            cv: false,
            affidavit: false,
            otherDocuments: false
          };
        }
        
        // Include vehicle data
        if (processed.vehicles && processed.vehicles.length > 0) {
          const vehicle = processed.vehicles[0];
          processed.vehicleType = vehicle.vehicleType || '';
          processed.registrationNumber = vehicle.registrationNumber || '';
          processed.providedByCompany = vehicle.providedByCompany || false;
          processed.handingOverDate = vehicle.handingOverDate || '';
          processed.returnDate = vehicle.returnDate || '';
          processed.vehicleMakeModel = vehicle.vehicleMakeModel || '';
          processed.vehicleColor = vehicle.vehicleColor || '';
          processed.mileageAtIssuance = vehicle.mileageAtIssuance || '';
          delete processed.vehicles;
        }
        
        // Include health data
        if (processed.healthRecords && processed.healthRecords.length > 0) {
          const health = processed.healthRecords[0];
          processed.vaccinationRecords = health.vaccinationRecords || '';
          processed.medicalHistory = health.medicalHistory || '';
          processed.bloodGroup = health.bloodGroup || '';
          processed.allergies = health.allergies || '';
          processed.chronicConditions = health.chronicConditions || '';
          processed.regularMedications = health.regularMedications || '';
          delete processed.healthRecords;
        }
        
        transformedEmployee = processed;
      }
      
      return res.json({
        success: true,
        message: 'Employee updated successfully',
        employee: transformedEmployee
      });
    } catch (error) {
      // Rollback transaction in case of error
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  } catch (error) {
    console.error('Error updating employee:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update employee',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete employee
export const deleteEmployee = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Validate ID is a number
    const employeeId = parseInt(id);
    if (isNaN(employeeId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid employee ID. ID must be a number.'
      });
    }
    
    console.log(`deleteEmployee: Deleting employee with ID ${id}`);
    
    // Check if employee exists
    const employee = await employeeRepository.findOne({ 
      where: { id: employeeId },
      relations: ['contact', 'job', 'benefit']
    });
    
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }
    
    console.log(`Found employee: ${employee.firstName} ${employee.lastName}`);
    
    // Delete related records first (TypeORM should handle cascading deletes,
    // but we'll make sure they're deleted to avoid orphaned records)
    const deleteResults: Record<string, any> = {};
    
    // Delete contact if exists
    if (employee.contact) {
      const contactRepo = AppDataSource.getRepository('EmployeeContact');
      deleteResults.contact = await contactRepo.delete(employee.contact.id);
      console.log(`Deleted contact record with ID ${employee.contact.id}`);
    }
    
    // Delete job if exists
    if (employee.job) {
      const jobRepo = AppDataSource.getRepository('EmployeeJob');
      deleteResults.job = await jobRepo.delete(employee.job.id);
      console.log(`Deleted job record with ID ${employee.job.id}`);
    }
    
    // Delete benefit if exists
    if (employee.benefit) {
      const benefitRepo = AppDataSource.getRepository('EmployeeBenefit');
      deleteResults.benefit = await benefitRepo.delete(employee.benefit.id);
      console.log(`Deleted benefit record with ID ${employee.benefit.id}`);
    }
    
    // Delete employee
    deleteResults.employee = await employeeRepository.delete(id);
    console.log(`Deleted employee record with ID ${id}`);
    
    return res.json({
      success: true,
      message: 'Employee and related records deleted successfully',
      results: deleteResults
    });
  } catch (error) {
    console.error('Error deleting employee:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete employee',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Upload employee documents
export const uploadEmployeeDocuments = async (req: Request, res: Response) => {
  try {
    // Check if files exist
    if (!req.files) {
      return res.status(400).json({
        success: false,
        message: 'No files were uploaded'
      });
    }
    
    const { id } = req.params;
    const { documentType, verificationStatus, notes, isProfileImage } = req.body;
    
    // Validate ID is a number
    const employeeId = parseInt(id);
    if (isNaN(employeeId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid employee ID. ID must be a number.'
      });
    }
    
    // Handle file upload using proper type for express-fileupload
    const files = req.files as unknown as { [fieldname: string]: fileUpload.UploadedFile | fileUpload.UploadedFile[] };
    
    // Check for common file field names used by the application
    const fileFieldNames = ['file', 'document', 'documentFile', 'attachment'];
    let uploadedFile: fileUpload.UploadedFile | undefined;
    
    // Find the first available file field
    for (const fieldName of fileFieldNames) {
      const fileField = files[fieldName];
      if (fileField) {
        // Handle both single file and array of files
        uploadedFile = Array.isArray(fileField) ? fileField[0] : fileField;
        if (uploadedFile) break;
      }
    }
    
    // If no file field was found, return an error
    if (!uploadedFile) {
      return res.status(400).json({
        success: false,
        message: 'No file field found in the request. Expected one of: ' + fileFieldNames.join(', ')
      });
    }
    
    // Check if employee exists
    const employee = await employeeRepository.findOne({ where: { id: Number(id) } });
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }
    
    // Determine if this is a profile image
    const isProfileImg = 
      isProfileImage === 'true' || 
      isProfileImage === true || 
      (documentType && (
        documentType.toLowerCase().includes('profile') || 
        documentType.toLowerCase().includes('photo')
      ));
    
    let uploadDir: string;
    let relativePath: string;
    
    if (isProfileImg) {
      // Store profile images directly under the employee's folder
      uploadDir = path.join(process.cwd(), 'public', 'uploads', 'employees', id);
      
      // Create directory if it doesn't exist
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }
      
      // Get file extension safely
      const fileExtension = path.extname(uploadedFile.name || '').toLowerCase() || '.jpg';
      
      // Create a safe filename for profile image
      const fileName = `profile${fileExtension}`;
      const filePath = path.join(uploadDir, fileName);
      
      // Move the file
      await uploadedFile.mv(filePath);
      
      // Create relative path for database storage that matches URL serving path
      relativePath = path.join('employees', id, fileName).replace(/\\/g, '/');
      
      // Update the employee's profileImagePath field
      await employeeRepository.update(id, { profileImagePath: relativePath });
      
      console.log('Updated employee profile image:', {
        employeeId,
        filePath,
        relativePath,
        fileSize: uploadedFile.size
      });
      
      return res.json({
        success: true,
        message: 'Profile image uploaded successfully',
        profileImagePath: relativePath
      });
    } else {
      // Regular document handling (existing code)
      // Create public/uploads/employees/{employeeId}/documents directory if it doesn't exist
      uploadDir = path.join(process.cwd(), 'public', 'uploads', 'employees', id, 'documents');
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }
      
      // Get file extension safely
      const fileExtension = path.extname(uploadedFile.name || '').toLowerCase() || '.pdf';
      
      // Create a safe filename (current timestamp + original filename without spaces)
      const fileName = `${Date.now()}_${uploadedFile.name?.replace(/\s+/g, '_') || `document${fileExtension}`}`;
      const filePath = path.join(uploadDir, fileName);
      
      // Move the file
      await uploadedFile.mv(filePath);
      
      // Create relative path for database storage that matches URL serving path
      relativePath = path.join('uploads', 'employees', id, 'documents', fileName).replace(/\\/g, '/');
      
      // Log the file path for debugging
      console.log('Uploaded employee document:', {
        employeeId,
        filePath,
        relativePath,
        fileSize: uploadedFile.size
      });
      
      // Store document metadata in employee_documents table
      const documentRepository = AppDataSource.getRepository('EmployeeDocument');
      const document = documentRepository.create({
        documentType: documentType || 'Unspecified Document',
        verificationStatus: verificationStatus || 'verified',
        filePath: relativePath,
        notes: notes || '',
        employee: { id: Number(id) }
      });
      
      const savedDocument = await documentRepository.save(document);
      
      return res.json({
        success: true,
        message: 'Document uploaded successfully',
        document: {
          id: savedDocument.id,
          documentType: savedDocument.documentType,
          filePath: relativePath,
          fileName
        }
      });
    }
  } catch (error) {
    console.error('Error uploading document:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to upload document',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get employee documents
export const getEmployeeDocuments = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Validate ID is a number
    const employeeId = parseInt(id);
    if (isNaN(employeeId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid employee ID. ID must be a number.'
      });
    }
    
    // Check if employee exists
    const employee = await employeeRepository.findOne({ where: { id: employeeId } });
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }
    
    // Fetch documents for this employee
    const documentRepository = AppDataSource.getRepository('EmployeeDocument');
    const documents = await documentRepository.find({
      where: { employee: { id: employeeId } }
    });
    
    console.log(`Found ${documents.length} documents in database for employee ID ${employeeId}`);
    
    // Check if each file exists and format for frontend display
    const formattedDocuments = documents.map(doc => {
      // Construct the full file path
      const fullPath = path.join(process.cwd(), 'public', doc.filePath);
      const fileExists = fs.existsSync(fullPath);
      
      // Extract file extension for debugging
      const ext = path.extname(doc.filePath).toLowerCase();
      
      // Log detailed info for each document
      console.log(`Document ID ${doc.id} (${doc.documentType}):`, {
        path: fullPath,
        exists: fileExists,
        extension: ext,
        size: fileExists ? fs.statSync(fullPath).size : 0,
        createdAt: doc.createdAt
      });
      
      return {
        id: doc.id,
        documentType: doc.documentType,
        verificationStatus: doc.verificationStatus,
        filePath: doc.filePath,
        notes: doc.notes,
        createdAt: doc.createdAt,
        updatedAt: doc.updatedAt,
        // Add preview URL for frontend convenience
        preview: `/${doc.filePath}`,
        // Add file existence check
        fileExists: fileExists,
        // Add file extension to help frontend decide how to display it
        fileExtension: ext
      };
    });
    
    // Log summary
    console.log(`Retrieved ${documents.length} documents for employee ID ${employeeId}`);
    console.log(`Documents with missing files: ${formattedDocuments.filter(d => !d.fileExists).length}`);
    
    return res.json({
      success: true,
      count: documents.length,
      documents: formattedDocuments
    });
  } catch (error) {
    console.error('Error fetching employee documents:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch employee documents',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Repair employee data by ensuring all related entities exist
export const repairEmployeeData = async (_req: Request, res: Response) => {
  try {
    console.log('repairEmployeeData: Starting employee data repair');
    
    // Get all employees
    const employees = await employeeRepository.find({
      relations: ['contact', 'job', 'benefit']
    });
    
    console.log(`Found ${employees.length} employees to check for repairs`);
    
    const results = {
      totalEmployees: employees.length,
      contactsCreated: 0,
      jobsCreated: 0,
      benefitsCreated: 0,
      errors: [] as string[]
    };
    
    // Process each employee
    for (const employee of employees) {
      try {
        // Check if contact exists
        if (!employee.contact) {
          console.log(`Creating missing contact for employee ${employee.id} (${employee.firstName} ${employee.lastName})`);
          
          // Create contact entity
          const contactRepository = AppDataSource.getRepository('EmployeeContact');
          const contact = contactRepository.create({
            mobileNumber: '0000000000', // Default placeholder
            employee
          });
          await contactRepository.save(contact);
          results.contactsCreated++;
        }
        
        // Check if job exists
        if (!employee.job) {
          console.log(`Creating missing job for employee ${employee.id} (${employee.firstName} ${employee.lastName})`);
          
          // Create job entity with default values
          const jobRepository = AppDataSource.getRepository('EmployeeJob');
          const job = jobRepository.create({
            designation: 'Not specified',
            department: 'Not specified',
            joinDate: new Date().toISOString().split('T')[0], // Today's date
            employee
          });
          await jobRepository.save(job);
          results.jobsCreated++;
        }
        
        // Check if benefit exists
        if (!employee.benefit) {
          console.log(`Creating missing benefit for employee ${employee.id} (${employee.firstName} ${employee.lastName})`);
          
          // Create benefit entity with default values
          const benefitRepository = AppDataSource.getRepository('EmployeeBenefit');
          const benefit = benefitRepository.create({
            totalSalary: '0', // Default placeholder
            employee
          });
          await benefitRepository.save(benefit);
          results.benefitsCreated++;
        }
      } catch (error) {
        const errorMsg = `Error repairing data for employee ${employee.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(errorMsg);
        results.errors.push(errorMsg);
      }
    }
    
    return res.json({
      success: true,
      message: 'Employee data repair completed',
      results
    });
  } catch (error) {
    console.error('Error repairing employee data:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to repair employee data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}; 

// Download a specific employee document
export const downloadEmployeeDocument = async (req: Request, res: Response) => {
  try {
    const { id, documentId } = req.params;
    
    // Validate employee ID is a number
    const employeeId = parseInt(id);
    if (isNaN(employeeId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid employee ID. ID must be a number.'
      });
    }
    
    // Validate document ID is a number
    const docId = parseInt(documentId);
    if (isNaN(docId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid document ID. ID must be a number.'
      });
    }
    
    // Check if employee exists
    const employee = await employeeRepository.findOne({ where: { id: employeeId } });
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }
    
    // Fetch document data
    const documentRepository = AppDataSource.getRepository('EmployeeDocument');
    const document = await documentRepository.findOne({
      where: { 
        id: docId,
        employee: { id: employeeId }
      }
    });
    
    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }
    
    // Check if document has a filePath
    if (!document.filePath) {
      return res.status(404).json({
        success: false,
        message: 'Document has no file path'
      });
    }
    
    // Extract file name from path
    const fileName = document.filePath.split('/').pop() || 'document.pdf';
    
    // Construct absolute file path - look in public directory
    const filePath = path.join(process.cwd(), 'public', document.filePath);
    
    console.log('Attempting to access file:', {
      employeeId,
      documentId: docId,
      filePath,
      exists: fs.existsSync(filePath),
      fileName,
      documentType: document.documentType
    });
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'Document file not found on server',
        path: filePath
      });
    }
    
    // Get file stats for size and debugging
    const stats = fs.statSync(filePath);
    console.log(`File size: ${stats.size} bytes`);
    
    // Use mime-types library if available, or fallback to manual detection
    let mimeType: string;
    
    try {
      // Try to get mime type from file extension
      const fileExtension = path.extname(fileName).toLowerCase();
      
      // Manual mapping for common types
      const mimeMap: Record<string, string> = {
        '.pdf': 'application/pdf',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.txt': 'text/plain',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.xls': 'application/vnd.ms-excel',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      };
      
      mimeType = mimeMap[fileExtension] || 'application/octet-stream';
      console.log(`Using MIME type: ${mimeType} for extension ${fileExtension}`);
    } catch (error) {
      // Fallback to generic binary type if mime detection fails
      mimeType = 'application/octet-stream';
      console.log('Falling back to generic MIME type: application/octet-stream');
    }
    
    // Set proper Content-Type
    res.setHeader('Content-Type', mimeType);
    
    // CORS headers - important for browser to properly handle files
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
    res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition, Content-Type, Content-Length');
    
    // Check if this is a download request or a view request
    const isDownload = req.query.download === 'true';
    
    // Handle special treatment for Word documents (docx) which browsers can't display natively
    const isWordDocument = mimeType === 'application/msword' || 
                          mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    
    // Word documents should ALWAYS be downloaded, regardless of view/download mode
    const forceDownload = isWordDocument;
    if (forceDownload) {
      console.log('Forcing download for Word document since browsers cannot display it natively');
    }
    
    // Set Content-Disposition header - CRITICAL for viewing vs downloading
    if (isDownload || forceDownload) {
      // Force download with 'attachment'
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      console.log('Serving file for DOWNLOAD with attachment disposition');
    } else {
      // Allow in-browser viewing with 'inline'
      res.setHeader('Content-Disposition', `inline; filename="${fileName}"`);
      console.log('Serving file for VIEWING with inline disposition');
    }
    
    // Log the headers being sent (for debugging)
    console.log('Response headers:', {
      'Content-Type': mimeType,
      'Content-Disposition': isDownload || forceDownload ? 
        `attachment; filename="${fileName}"` : 
        `inline; filename="${fileName}"`,
      'Content-Length': stats.size
    });
    
    // Send file using res.sendFile(), NOT res.download()
    res.sendFile(filePath, (err) => {
      if (err) {
        console.error('Error sending file:', err);
        res.status(500).json({
          success: false,
          message: 'Error sending file',
          error: err.message
        });
      } else {
        console.log('File sent successfully');
      }
    });
    
  } catch (error) {
    console.error('Error serving document:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to serve document',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete an employee document
export const deleteEmployeeDocument = async (req: Request, res: Response) => {
  try {
    const { id, documentId } = req.params;
    
    // Validate employee ID is a number
    const employeeId = parseInt(id);
    if (isNaN(employeeId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid employee ID. ID must be a number.'
      });
    }
    
    // Validate document ID is a number
    const docId = parseInt(documentId);
    if (isNaN(docId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid document ID. ID must be a number.'
      });
    }
    
    // Check if employee exists
    const employee = await employeeRepository.findOne({ where: { id: employeeId } });
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }
    
    // Fetch document data
    const documentRepository = AppDataSource.getRepository('EmployeeDocument');
    const document = await documentRepository.findOne({
      where: { 
        id: docId,
        employee: { id: employeeId }
      }
    });
    
    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }
    
    // Store file path for deletion
    const filePath = document.filePath ? path.join(process.cwd(), 'public', document.filePath) : null;
    
    console.log('Attempting to delete document and file:', {
      employeeId,
      documentId: docId,
      filePath,
      exists: filePath ? fs.existsSync(filePath) : false
    });

    // First, delete the database record to ensure data consistency
    await documentRepository.remove(document);
    console.log(`Deleted document record with ID ${docId} from database`);
    
    // Then try to delete the physical file if it exists
    if (filePath) {
      try {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          console.log(`Successfully deleted file: ${filePath}`);
        } else {
          console.log(`File not found at path: ${filePath}`);
        }
      } catch (fileError) {
        console.error('Error deleting file:', fileError);
        // Continue with response even if file deletion fails
      }
    }
    
    // Clear any employee cache to ensure views are updated
    try {
      // If you have a global cache mechanism, you could clear it here
      console.log(`Cleared cache for employee ${employeeId}`);
    } catch (cacheError) {
      console.error('Error clearing cache:', cacheError);
    }
    
    return res.json({
      success: true,
      message: 'Document deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting document:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete document',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Import employees in bulk
export const importEmployees = async (req: Request, res: Response) => {
  try {
    const { employees } = req.body;
    
    if (!employees || !Array.isArray(employees) || employees.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or empty employee data array'
      });
    }
    
    console.log(`Received import request for ${employees.length} employees`);
    
    const importedEmployees = [];
    const failedImports = [];
    
    // Process each employee separately (without transaction) to allow partial success
    for (const employeeData of employees) {
      try {
        console.log(`Processing employee: ${employeeData.firstName} ${employeeData.lastName}`);
        
        // Validate required fields
        if (!employeeData.firstName || !employeeData.lastName) {
          throw new Error('First name and last name are required');
        }
        
        // List of fields that exist in the Employee entity
        const employeeFields = [
          // Personal Info
          'firstName', 'middleName', 'lastName', 'gender', 'dateOfBirth', 'religion',
          'cnicNumber', 'cnicExpiryDate', 'nationality', 'maritalStatus', 'bloodType',
          'profileImagePath', 'employeeId', 'status', 'fatherName', 'statusDate',
          'passportNumber', 'passportExpiryDate', 'spouseName', 'spouseDateOfBirth',
          'spouseOccupation', 'spouseEmployer', 'spouseContactNumber', 'spouseCNIC',
          'notes', 'specialInstructions', 'updatedAt'
        ];
        
        // Fields for contact entity
        const contactFields = [
          'mobileNumber', 'officialNumber', 'officialEmail', 'personalEmail',
          'permanentAddress', 'currentAddress', 'emergencyContactName',
          'emergencyContactPhone', 'emergencyContactRelationship', 'linkedinProfile', 
          'otherSocialProfiles', 'professionalMemberships', 'hobbiesInterests'
        ];
        
        // Fields for job entity
        const jobFields = [
          'designation', 'department', 'project', 'location', 'employmentType',
          'employmentStatus', 'employeeLevel', 'joinDate', 'probationEndDate',
          'noticePeriod', 'reportingTo', 'remoteWorkEligible', 'remoteWork',
          'nextReviewDate', 'trainingRequirements', 'workSchedule', 'shiftType',
          'confirmationDate', 'contractEndDate', 'workLocation', 'previousEmployeeId'
        ];
        
        // Fields for benefit entity
        const benefitFields = [
          'salary', 'salaryTier', 'paymentMode', 'bankName', 'bankBranch',
          'accountNumber', 'accountTitle', 'iban', 'cashAmount', 'bankAmount',
          'totalSalary', 'foodAllowanceInSalary', 'foodProvidedByCompany',
          'numberOfMeals', 'fuelAllowanceInSalary', 'fuelInLiters', 'fuelAmount'
        ];
        
        // Fields for health entity
        const healthFields = [
          'healthInsuranceProvider', 'healthInsurancePolicyNumber', 'healthInsuranceExpiryDate',
          'lifeInsuranceProvider', 'lifeInsurancePolicyNumber', 'lifeInsuranceExpiryDate',
          'vaccinationRecords', 'medicalHistory', 'allergies',
          'chronicConditions', 'regularMedications'
        ];
        
        // Filter the data for the main employee entity
        const employeeEntityData: Record<string, any> = {};
        employeeFields.forEach(field => {
          if (field in employeeData && employeeData[field] !== undefined && employeeData[field] !== null && employeeData[field] !== "") {
            employeeEntityData[field] = employeeData[field];
          }
        });

        // Filter data for the contact entity
        const contactEntityData: Record<string, any> = {};
        contactFields.forEach(field => {
          if (field in employeeData && employeeData[field] !== undefined && employeeData[field] !== null && employeeData[field] !== "") {
            contactEntityData[field] = employeeData[field];
          }
        });
        
        // Filter data for the job entity
        const jobEntityData: Record<string, any> = {};
        jobFields.forEach(field => {
          if (field in employeeData && employeeData[field] !== undefined && employeeData[field] !== null && employeeData[field] !== "") {
            // Convert string boolean values to actual booleans
            if (['remoteWorkEligible', 'remoteWork'].includes(field)) {
              jobEntityData[field] = employeeData[field] === 'true' || 
                employeeData[field] === true || 
                employeeData[field] === 'Yes';
            } else {
              jobEntityData[field] = employeeData[field];
            }
          }
        });
        
        // Filter data for the benefit entity
        const benefitEntityData: Record<string, any> = {};
        benefitFields.forEach(field => {
          if (field in employeeData && employeeData[field] !== undefined && employeeData[field] !== null && employeeData[field] !== "") {
            // Convert string boolean values to actual booleans
            if (['foodAllowanceInSalary', 'foodProvidedByCompany', 'fuelAllowanceInSalary'].includes(field)) {
              benefitEntityData[field] = employeeData[field] === 'true' || 
                employeeData[field] === true || 
                employeeData[field] === 'Yes';
            } else {
              benefitEntityData[field] = employeeData[field];
            }
          }
        });
        
        // Filter data for the health entity
        const healthEntityData: Record<string, any> = {};
        healthFields.forEach(field => {
          if (field in employeeData && employeeData[field] !== undefined && employeeData[field] !== null && employeeData[field] !== "") {
            healthEntityData[field] = employeeData[field];
          }
        });
        
        // Set bloodGroup in health entity from bloodType in employee entity (they're the same data)
        if (employeeEntityData.bloodType) {
          healthEntityData.bloodGroup = employeeEntityData.bloodType;
        }
        
        // Generate employee ID if not provided
        if (!employeeEntityData.employeeId || employeeEntityData.employeeId.trim() === '') {
          employeeEntityData.employeeId = await generateEmployeeId(
            employeeEntityData.firstName, 
            employeeEntityData.lastName
          );
          console.log(`Generated employee ID: ${employeeEntityData.employeeId}`);
        }
        
        // Set default status if not provided
        if (!employeeEntityData.status) {
          employeeEntityData.status = 'active';
        }
        
        // Check if employee with this ID already exists
        const existingEmployee = await employeeRepository.findOne({ 
          where: { employeeId: employeeEntityData.employeeId } 
        });
        
        if (existingEmployee) {
          throw new Error(`Employee with ID ${employeeEntityData.employeeId} already exists`);
        }

        // Create new employee instance
        console.log('Creating employee with data:', {
          firstName: employeeEntityData.firstName,
          lastName: employeeEntityData.lastName,
          employeeId: employeeEntityData.employeeId
        });
        
        // Use a transaction for each employee to ensure consistency
        const queryRunner = AppDataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        
        try {
          // Create new employee instance
          const employee = employeeRepository.create(employeeEntityData);
          await queryRunner.manager.save(employee);
          
          // Create contact entity if we have contact data
          if (Object.keys(contactEntityData).length > 0) {
            const contactRepository = AppDataSource.getRepository('EmployeeContact');
            const contact = contactRepository.create({
              ...contactEntityData,
              employee
            });
            await queryRunner.manager.save(contact);
          }
          
          // Create job entity if we have job data
          if (Object.keys(jobEntityData).length > 0) {
            const jobRepository = AppDataSource.getRepository('EmployeeJob');
            const job = jobRepository.create({
              ...jobEntityData,
              employee
            });
            await queryRunner.manager.save(job);
          }
          
          // Create benefit entity if we have benefit data
          if (Object.keys(benefitEntityData).length > 0) {
            const benefitRepository = AppDataSource.getRepository('EmployeeBenefit');
            const benefit = benefitRepository.create({
              ...benefitEntityData,
              employee
            });
            await queryRunner.manager.save(benefit);
          }
          
          // Create health entity if we have health data
          if (Object.keys(healthEntityData).length > 0) {
            const healthRepository = AppDataSource.getRepository('EmployeeHealth');
            const health = healthRepository.create({
              ...healthEntityData,
              employee
            });
            await queryRunner.manager.save(health);
          }
          
          // Handle education entries if they exist
          if (employeeData.educationEntries && Array.isArray(employeeData.educationEntries)) {
            const educationRepository = AppDataSource.getRepository('EmployeeEducation');
            
            for (const eduEntry of employeeData.educationEntries) {
              // Skip empty entries
              if (!eduEntry.degree && !eduEntry.institution && !eduEntry.educationLevel) {
                continue;
              }
              
              const education = educationRepository.create({
                degree: eduEntry.degree || '',
                institution: eduEntry.institution || '',
                educationLevel: eduEntry.educationLevel || '',
                major: eduEntry.major || '',
                graduationYear: eduEntry.graduationYear || '',
                grade: eduEntry.grade || '',
                employee
              });
              
              await queryRunner.manager.save(education);
              console.log(`Created education entry: ${education.degree} at ${education.institution}`);
            }
          } 
          // Handle education fields from flat structure
          else if (employeeData.educationDegree || employeeData.educationInstitution || employeeData.educationLevel) {
            const educationRepository = AppDataSource.getRepository('EmployeeEducation');
            
            const education = educationRepository.create({
              degree: employeeData.educationDegree || '',
              institution: employeeData.educationInstitution || '',
              educationLevel: employeeData.educationLevel || '',
              major: employeeData.educationMajor || '',
              graduationYear: employeeData.educationGraduationYear || '',
              grade: employeeData.educationGPA || '',
              employee
            });
            
            await queryRunner.manager.save(education);
            console.log(`Created education entry from flat structure: ${education.degree} at ${education.institution}`);
          }
          
          // Commit the transaction
          await queryRunner.commitTransaction();
          
          importedEmployees.push({
            ...employeeData,
            id: employee.id
          });
          
          console.log(`Successfully imported employee: ${employee.firstName} ${employee.lastName} (ID: ${employee.id})`);
        } catch (txError) {
          // Rollback transaction in case of error
          await queryRunner.rollbackTransaction();
          throw txError;
        } finally {
          // Release query runner
          await queryRunner.release();
        }
      } catch (error) {
        console.error('Error importing employee:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        failedImports.push({
          ...employeeData,
          error: errorMessage
        });
      }
    }
    
    console.log(`Successfully imported ${importedEmployees.length} employees`);
    console.log(`Failed to import ${failedImports.length} employees`);
    
    if (failedImports.length > 0) {
      console.log('Sample of import failures:');
      failedImports.slice(0, 3).forEach((fail, idx) => {
        console.log(`Failure ${idx + 1}: ${fail.firstName} ${fail.lastName} - ${fail.error}`);
      });
    }
    
    return res.status(200).json({
      success: true,
      message: `Successfully imported ${importedEmployees.length} employees, ${failedImports.length} failed`,
      importedEmployees,
      failedImports
    });
    
  } catch (error) {
    console.error('Error importing employees:', error);
    return res.status(500).json({
      success: false,
      message: 'Error importing employees',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all unique locations from employees
export const getEmployeeLocations = async (req: Request, res: Response) => {
  try {
    console.log('🎯 Getting all unique employee locations...');
    
    const employeeRepository = AppDataSource.getRepository(Employee);
    
    // Get all employees with their job information
    const employees = await employeeRepository
      .createQueryBuilder('employee')
      .leftJoinAndSelect('employee.job', 'job')
      .select(['employee.id', 'job.location', 'job.project'])
      .where('employee.isActive = :isActive', { isActive: true })
      .getMany();

    // Extract unique locations
    const locations = new Set<string>();
    
    employees.forEach(employee => {
      if (employee.job?.location && employee.job.location.trim()) {
        locations.add(employee.job.location.trim());
      }
    });

    // Add default locations if none found
    const defaultLocations = [
      'Grand City Head Office Lahore',
      'Grand City Site Office Kharian',
      'Grand City Site Office Arifwala',
      'Grand City Site Office Vehari',
      'Grand City Site Office Faisalabad',
      'Grand City Site Office Murree',
      'ARD Head Office Lahore',
      'ARD Site Office RUDA',
      'Remote Work',
      'Office'
    ];

    if (locations.size === 0) {
      defaultLocations.forEach(loc => locations.add(loc));
    } else {
      // Add common locations to ensure they're always available
      ['Remote Work', 'Office'].forEach(loc => locations.add(loc));
    }

    const sortedLocations = Array.from(locations).sort();

    console.log(`✅ Found ${sortedLocations.length} unique locations`);

    return res.json({
      success: true,
      locations: sortedLocations,
      count: sortedLocations.length
    });
  } catch (error) {
    console.error('❌ Error fetching employee locations:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch employee locations',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get locations for a specific employee
export const getEmployeeLocationsByEmployee = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    console.log(`🎯 Getting locations for employee ID: ${id}`);
    
    const employeeRepository = AppDataSource.getRepository(Employee);
    
    const employee = await employeeRepository
      .createQueryBuilder('employee')
      .leftJoinAndSelect('employee.job', 'job')
      .select(['employee.id', 'job.location', 'job.project'])
      .where('employee.id = :id', { id })
      .andWhere('employee.isActive = :isActive', { isActive: true })
      .getOne();

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }

    const locations = new Set<string>();
    
    // Add employee's assigned location
    if (employee.job?.location && employee.job.location.trim()) {
      locations.add(employee.job.location.trim());
    }

    // Add project-specific locations if employee has a project
    if (employee.job?.project) {
      const projectLocations = getProjectLocations(employee.job.project);
      projectLocations.forEach(loc => locations.add(loc));
    }

    // Always add common locations
    ['Remote Work', 'Office'].forEach(loc => locations.add(loc));

    const sortedLocations = Array.from(locations).sort();

    console.log(`✅ Found ${sortedLocations.length} locations for employee ${id}`);

    return res.json({
      success: true,
      locations: sortedLocations,
      count: sortedLocations.length,
      employeeLocation: employee.job?.location || null,
      employeeProject: employee.job?.project || null
    });
  } catch (error) {
    console.error('❌ Error fetching employee locations:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch employee locations',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Helper function to get project-specific locations
const getProjectLocations = (project: string): string[] => {
  const PROJECT_LOCATIONS: Record<string, string[]> = {
    'Eurobiz Corporations': [
      'Grand City Head Office Lahore',
      'Grand City Site Office Kharian'
    ],
    'Guardian International': [
      'Grand City Head Office Lahore',
      'Grand City Site Office Arifwala'
    ],
    'Guardian Developers': [
      'Grand City Head Office Lahore',
      'Grand City Site Office Vehari'
    ],
    'Grand Developers': [
      'Grand City Head Office Lahore',
      'Grand City Site Office Faisalabad',
      'Grand City Site Office Murree'
    ],
    'ARD Developers': [
      'ARD Head Office Lahore',
      'ARD Site Office RUDA'
    ]
  };

  return PROJECT_LOCATIONS[project] || [];
};