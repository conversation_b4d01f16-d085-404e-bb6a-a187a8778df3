export interface AssetBase {
  id: string;
  assetType: 'Computing' | 'Mobile Device' | 'Tablet' | 'Networking' | 'Peripherals' | 'Security' | 'Communication' | 'Other';
  category: string;
  manufacturer: string;
  model: string;
  serialNumber: string;
  assetTag: string;
  purchaseDate: string;
  warrantyExpiry: string;
  location: string;
  condition: 'New' | 'Good' | 'Fair' | 'Needs Repair' | 'Retired' | 'Other';
  internetAccess: boolean;
  ipAddress?: string;
  status: 'Active' | 'Reserved' | 'Under Repair' | 'Retired' | 'Lost/Stolen' | 'Decommissioned' | 'In Transit' | 'Other';
  assignedTo?: string;
  lastMaintenance?: string;
  maintenanceBy?: string;
  nextMaintenance?: string;
  maintenanceCost?: number;
  notes?: string;
}

export interface ComputingAttributes {
  operatingSystem: string;
  processor: string;
  ramCapacity: string;
  storageType: string;
  storageCapacity: string;
  graphicsCard: string;
  domainJoined: boolean;
  assignedUserLogin?: string;
  remoteAccessEnabled: boolean;
  backupPolicyApplied: boolean;
}

export interface NetworkingAttributes {
  macAddress: string;
  subnetMask: string;
  defaultGateway: string;
  firewallRules: boolean;
  vlanConfigured: boolean;
  vlanId?: string;
  portsAvailable: number;
  portsUsed: number;
  firmwareVersion: string;
  lastFirmwareUpdate: string;
  snmpConfigured: boolean;
  managed: 'Managed' | 'Unmanaged' | 'Other';
}

export interface SecurityAttributes {
  resolution: string;
  retentionPeriod: string;
  nvrAssigned: boolean;
  nvrDevice?: string;
  accessControlIntegration: boolean;
  motionDetection: boolean;
  nightVision: boolean;
}

export type Asset = AssetBase & {
  attributes: ComputingAttributes | NetworkingAttributes | SecurityAttributes;
} 