import React, { useState, useRef, useEffect, useCallback } from 'react';
import { <PERSON>, User, Menu, LogOut, Shield, Moon, Sun, AlertTriangle, Ticket, Mail, Settings, X } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { socketService } from '../services/socket';
import { toast } from 'react-hot-toast';

interface HeaderProps {
  onMenuClick: () => void;
  currentPage: string;
  darkMode: boolean;
  toggleDarkMode: () => void;
  onPageChange?: (page: string) => void;
  sidebarCollapsed?: boolean;
}

interface TicketNotification {
  ticketId: string;
  subject: string;
  status?: string;
  priority?: string;
  department?: string;
  assignedTo?: string;
  updatedBy?: string;
}

interface Notification {
  id: string;
  type: 'ticket' | 'escalation' | 'email' | 'system';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
  link?: string;
  ticketData?: TicketNotification;
  destinationPage?: string;
}

const roleColors: Record<string, string> = {
  head: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
  manager: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  normal: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  view: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
};

export function Header({ onMenuClick, currentPage, darkMode, toggleDarkMode, onPageChange, sidebarCollapsed }: HeaderProps) {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [showNotifications, setShowNotifications] = useState(false);
  const notificationRef = useRef<HTMLDivElement>(null);
  const notificationMapRef = useRef(new Map<string, Notification>());
  const menuButtonRef = useRef<HTMLButtonElement>(null);
  // Add refs to track previous notification values
  const prevNotificationsLengthRef = useRef(0);
  const prevUnreadCountRef = useRef(0);

  // Load saved notifications from localStorage on component mount
  useEffect(() => {
    if (user) {
      try {
        const savedNotifications = localStorage.getItem('userNotifications');
        const savedUnreadCount = localStorage.getItem('unreadNotificationCount');
        
        if (savedNotifications) {
          const parsedNotifications = JSON.parse(savedNotifications) as Notification[];
          
          // Ensure timestamps are Date objects
          const formattedNotifications = parsedNotifications.map(notification => ({
            ...notification,
            timestamp: new Date(notification.timestamp)
          }));
          
          // Sort notifications by timestamp (newest first)
          formattedNotifications.sort((a, b) => 
            new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
          );
          
          // Initialize the notification map
          notificationMapRef.current.clear();
          formattedNotifications.forEach(notification => {
            notificationMapRef.current.set(notification.id, notification);
          });
          
          setNotifications(formattedNotifications);
          console.log('Header: Loaded saved notifications:', formattedNotifications.length);
        }
        
        if (savedUnreadCount) {
          const count = parseInt(savedUnreadCount, 10);
          setUnreadCount(count);
          console.log('Header: Loaded unread count:', count);
        }
      } catch (error) {
        console.error('Error loading saved notifications:', error);
      }
    }
  }, [user]);

  // Save notifications to localStorage when they change
  useEffect(() => {
    if (!user) return;
    
    // Use a debounce to avoid excessive writes to localStorage
    const saveTimeout = setTimeout(() => {
      // Only save to localStorage if values have actually changed
      if (notifications.length !== prevNotificationsLengthRef.current || 
          unreadCount !== prevUnreadCountRef.current) {
      try {
        localStorage.setItem('userNotifications', JSON.stringify(notifications));
        localStorage.setItem('unreadNotificationCount', unreadCount.toString());
        console.log('Header: Saved notifications to localStorage:', notifications.length);
          
          // Update refs with current values
          prevNotificationsLengthRef.current = notifications.length;
          prevUnreadCountRef.current = unreadCount;
      } catch (error) {
        console.error('Error saving notifications:', error);
      }
    }
    }, 300);
    
    return () => clearTimeout(saveTimeout);
  }, [notifications, unreadCount, user]);

  // Connect to socket service when component mounts - DISABLED TO PREVENT CONTINUOUS CALLS
  useEffect(() => {
    // TEMPORARILY DISABLE SOCKET CONNECTION TO STOP CONTINUOUS API CALLS
    console.log('Header: Socket connection temporarily disabled to prevent continuous API calls');
    
    // Return empty cleanup
    return () => {};
  }, []); // Empty dependency array

  // Original socket connection code (disabled)
  /*
  useEffect(() => {
    if (!user) return;

    let isComponentMounted = true;
    
    console.log('Header: User authenticated, connecting with ID:', user.id);
    
    // Connect to socket service once
    const connectTimeout = setTimeout(() => {
      console.log('Header: Connecting to socket service...');
      
      // Connect to socket service
      socketService.connect().then(() => {
        if (!isComponentMounted) return;
        
        console.log('Header: Successfully connected to socket service');
        
        // Subscribe to notifications for the current user
        if (user.id) {
          // Make sure we're using the correct user ID format
          const userDataStr = localStorage.getItem('userData');
          let userId = user.id;
          
          try {
            if (userDataStr) {
              const userData = JSON.parse(userDataStr);
              if (userData && userData.id) {
                userId = userData.id;
                console.log('Header: Using user ID from localStorage:', userId);
              }
            }
          } catch (error) {
            console.error('Header: Error parsing user data from localStorage:', error);
          }
          
          console.log('Header: Subscribing to notifications for user:', userId);
          
          // First, set up the handler for notifications - define outside to prevent recreating on each render
          const handleNotification = (notification: any) => {
            if (!isComponentMounted) return;
            
            console.log('Header: Received notification:', notification);
            
            // Skip if we already have this notification
            if (notificationMapRef.current.has(notification.id)) {
              console.log('Header: Skipping duplicate notification:', notification.id);
              return;
            }
            
            // Create a properly formatted notification object
            const formattedNotification: Notification = {
              id: notification.id,
              title: notification.title,
              message: notification.message,
              timestamp: notification.timestamp ? new Date(notification.timestamp) : new Date(),
              read: notification.read || false,
              type: notification.type || 'system',
              priority: notification.priority || 'medium',
              link: notification.link,
              ticketData: notification.ticketData,
              destinationPage: notification.destinationPage
            };
            
            // Add to our map
            notificationMapRef.current.set(formattedNotification.id, formattedNotification);
            
            // Update state with all notifications from the map
            setNotifications(prev => {
              const newNotifications = Array.from(notificationMapRef.current.values()).sort((a, b) => 
                new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
              );
              console.log('Header: Updated notifications state, count:', newNotifications.length);
              return newNotifications;
            });
            
            // Update unread count
            if (!formattedNotification.read) {
              setUnreadCount(prev => prev + 1);
            }
          };
          
          // Register the handler
          const unsubscribeHandler = socketService.onNotification(handleNotification);
          
          // Then subscribe to the user's notifications
          const unsubscribeUser = socketService.subscribeToNotifications(userId);
          
          // Make sure to clean up both subscriptions
          const cleanupSubscriptions = () => {
            console.log('Header: Unsubscribing from notifications');
            unsubscribeHandler();
            unsubscribeUser();
          };
          
          // Add to our cleanup function
          const originalCleanup = isComponentMounted;
          isComponentMounted = false; // Set to false to prevent duplicate cleanup
          return () => {
            cleanupSubscriptions();
            if (originalCleanup) {
              console.log('Header: Component unmounting, cleaning up');
              clearTimeout(connectTimeout);
              socketService.disconnect();
            }
          };
        }
      }).catch(error => {
        console.error('Header: Error connecting to socket service:', error);
      });
    }, 500);

    // Cleanup function
    return () => {
      console.log('Header: Component unmounting, cleaning up');
      isComponentMounted = false;
      clearTimeout(connectTimeout);
      socketService.disconnect();
    };
  }, [user?.id]); // Only re-run if user ID changes
  */

  // Close notification panel when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setShowNotifications(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Memoize the mark as read function
  const markNotificationAsRead = useCallback((id: string) => {
    setNotifications(prev => {
      const updated = prev.map(notif =>
        notif.id === id ? { ...notif, read: true } : notif
      );
      
      // Update the map
      const notification = notificationMapRef.current.get(id);
      if (notification) {
        notification.read = true;
        notificationMapRef.current.set(id, notification);
      }
      
      return updated;
    });
    
    setUnreadCount(prev => Math.max(0, prev - 1));
    
    // Try to mark as read on the server
    try {
      socketService.markNotificationAsRead(id);
    } catch (error) {
      console.error('Error marking notification as read on server:', error);
    }
  }, []);

  // Memoize notification click handler
  const handleNotificationClick = useCallback((notification: Notification) => {
    markNotificationAsRead(notification.id);
    setShowNotifications(false);

    // Determine destination based on notification content
    let targetPage = 'dashboard';
    let targetPath = '/dashboard';

    if (notification.destinationPage) {
      targetPage = notification.destinationPage;
      targetPath = `/${notification.destinationPage}`;
    } else if (notification.link) {
      targetPath = notification.link;
    }

    // Navigate and inform parent component if needed
      navigate(targetPath);
      if (onPageChange) {
          onPageChange(targetPage);
    }
  }, [navigate, onPageChange, markNotificationAsRead]);

  // Add clear all notifications function
  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
    setUnreadCount(0);
    notificationMapRef.current.clear();
    localStorage.removeItem('userNotifications');
    localStorage.removeItem('unreadNotificationCount');
  }, []);

  const renderNotificationPanel = () => (
    <div
      ref={notificationRef}
      className="absolute right-0 top-full mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg z-20 overflow-hidden"
    >
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h3 className="font-semibold text-gray-900 dark:text-white">Notifications</h3>
        <div className="flex items-center gap-2">
          {notifications.length > 0 && (
            <button
              onClick={clearAllNotifications}
              className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              Clear all
            </button>
          )}
          <button
            onClick={() => setShowNotifications(false)}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
      </div>
      <div className="max-h-96 overflow-y-auto">
        {notifications.length === 0 ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            No notifications
          </div>
        ) : (
          notifications.map(notification => (
            <div
              key={notification.id}
              className={`p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer ${
                !notification.read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
              }`}
              onClick={() => handleNotificationClick(notification)}
            >
              <div className="flex items-start gap-3">
                {notification.type === 'ticket' && <Ticket className="h-5 w-5 text-blue-500" />}
                {notification.type === 'escalation' && <AlertTriangle className="h-5 w-5 text-red-500" />}
                {notification.type === 'email' && <Mail className="h-5 w-5 text-green-500" />}
                {notification.type === 'system' && <Settings className="h-5 w-5 text-purple-500" />}
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">{notification.title}</h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{notification.message}</p>
                  <span className="text-xs text-gray-400 dark:text-gray-500">
                    {(() => {
                      try {
                        // Ensure we have a valid date
                        const date = notification.timestamp instanceof Date 
                          ? notification.timestamp 
                          : new Date(notification.timestamp);
                        
                        // Check if date is valid
                        if (isNaN(date.getTime())) {
                          return 'Just now';
                        }
                        
                        return date.toLocaleTimeString();
                      } catch (error) {
                        console.error('Error formatting date:', error);
                        return 'Just now';
                      }
                    })()}
                  </span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );

  return (
    <div className={`header-container h-16 bg-[#1a2234] dark:bg-gray-900 border-b border-gray-700 flex items-center justify-between px-3 sm:px-4 fixed top-0 right-0 left-0 ${sidebarCollapsed ? 'lg:left-16' : 'lg:left-64'} z-40`}>
      <div className="flex items-center gap-3">
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('Menu button clicked');
            onMenuClick();
          }}
          type="button"
          className="sidebar-toggle-button text-gray-300 hover:text-white lg:hidden"
          aria-label="Toggle menu"
        >
          <Menu className="h-6 w-6" />
        </button>
        <h2 className="text-white font-semibold text-start capitalize">{currentPage.replace(/-/g, ' ')}</h2>
      </div>
      
      <div className="flex items-center gap-4">
        <div className="relative">
          <button 
            onClick={() => setShowNotifications(!showNotifications)}
            className="text-gray-300 hover:text-white p-2 rounded-lg hover:bg-gray-700 transition-colors relative"
            aria-label="Notifications"
          >
            <Bell className="h-5 w-5" />
            {unreadCount > 0 && (
              <span className="absolute top-0 right-0 -mt-1 -mr-1 px-1.5 py-0.5 text-xs font-medium text-white bg-red-500 rounded-full">
                {unreadCount}
              </span>
            )}
          </button>
          {showNotifications && renderNotificationPanel()}
        </div>
        
        <button
          onClick={toggleDarkMode}
          title="Toggle Dark Mode"
          className="p-2 rounded-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
        >
          {darkMode ? (
            <Sun className="h-5 w-5 text-yellow-500" />
          ) : (
            <Moon className="h-5 w-5 text-gray-800" />
          )}
        </button>
        
        <div className="relative group">
          <button 
            className="text-gray-300 hover:text-white p-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2"
          >
            <User className="h-5 w-5" />
            <span className="hidden sm:block text-sm font-medium">{user?.name}</span>
            {user && (
              <span className={`hidden sm:flex items-center gap-1 text-xs px-2 py-0.5 rounded-full ${roleColors[user.role]}`}>
                <Shield className="h-3 w-3" />
                {user.role}
              </span>
            )}
          </button>
          
          <div className="absolute right-0 top-full mt-1 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-900 py-2 hidden group-hover:block">
            {user && (
              <div className="px-4 py-2 border-b border-gray-100 dark:border-gray-700">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium text-gray-900 dark:text-white">{user.name}</span>
                  <span className={`text-xs px-2 py-0.5 rounded-full ${roleColors[user.role]}`}>
                    {user.role}
                  </span>
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">{user.email}</div>
                {user.department && (
                  <div className="text-sm text-gray-500 dark:text-gray-400">{user.department}</div>
                )}
              </div>
            )}
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                
                // Prevent any notifications during logout
                const originalToastError = toast.error;
                // @ts-ignore - Temporarily override toast.error
                toast.error = () => 'suppressed';
                
                try {
                  // Clear notifications first to prevent any state issues
                  clearAllNotifications();
                  
                  // Perform logout
                  logout();
                } catch (error) {
                  console.error('Error during logout:', error);
                  
                  // Fallback if logout fails
                  localStorage.removeItem('authToken');
                  localStorage.removeItem('userData');
                  localStorage.removeItem('userNotifications');
                  localStorage.removeItem('unreadNotificationCount');
                  
                  // Restore toast function before redirect
                  toast.error = originalToastError;
                  
                  // Force redirect
                  window.location.href = '/login';
                }
              }}
              className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2 transition-colors"
            >
              <LogOut className="h-4 w-4" />
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}