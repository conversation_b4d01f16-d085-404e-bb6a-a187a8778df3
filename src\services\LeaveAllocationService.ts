import { AppDataSource } from '../config/database';
import { User } from '../entities/User';
import { LeaveTypePolicy } from '../entities/LeaveTypePolicy';
// Removed LeaveAccrualRule import as it was deleted
import { LeavePolicyConfiguration } from '../entities/LeavePolicyConfiguration';
import { LeaveBalance } from '../entities/LeaveBalance';
import { LeaveAllocation } from '../entities/LeaveAllocation';

export interface EmployeeAllocationResult {
  employeeId: number;
  leaveType: string;
  totalDays: number;
  calculation: string;
  source: 'leave_type_policy' | 'default';
  accrualDetails?: {
    accrualAmount: number;
    frequency: string;
    maxAccumulation: number;
    isProrated: boolean;
    waitingPeriod: number;
  };
}

export interface AllocationCalculationContext {
  employee: User;
  leaveTypePolicy: LeaveTypePolicy;
  year: number;
  effectiveDate?: Date;
}

export class LeaveAllocationService {
  private static instance: LeaveAllocationService;

  public static getInstance(): LeaveAllocationService {
    if (!LeaveAllocationService.instance) {
      LeaveAllocationService.instance = new LeaveAllocationService();
    }
    return LeaveAllocationService.instance;
  }

  /**
   * Calculate dynamic leave allocation for an employee based on policy configuration
   */
  async calculateEmployeeAllocation(
    employeeId: number,
    leaveType: string,
    year: number,
    effectiveDate?: Date
  ): Promise<EmployeeAllocationResult> {
    try {
      const userRepository = AppDataSource.getRepository(User);
      const policyConfigRepository = AppDataSource.getRepository(LeavePolicyConfiguration);

      // Get employee details
      const employee = await userRepository.findOne({ where: { id: employeeId.toString() } });
      if (!employee) {
        console.warn(`Employee with ID ${employeeId} not found - using default allocation`);
        return await this.getDefaultAllocation(employeeId, leaveType, year);
      }

      // Get active policy configuration
      const activePolicyConfig = await policyConfigRepository.findOne({
        where: { isActive: true },
        relations: ['leaveTypes', 'accrualRules']
      });

      if (!activePolicyConfig) {
        console.warn(`No active policy configuration found - using default allocation for ${leaveType}`);
        return await this.getDefaultAllocation(employeeId, leaveType, year);
      }

      // Get leave type policy - try exact match first, then case-insensitive match
      let leaveTypePolicy = activePolicyConfig.leaveTypes?.find(
        lt => lt.leaveType === leaveType && lt.enabled && lt.isActive
      );

      // If not found, try case-insensitive search
      if (!leaveTypePolicy) {
        leaveTypePolicy = activePolicyConfig.leaveTypes?.find(
          lt => lt.leaveType.toLowerCase() === leaveType.toLowerCase() && lt.enabled && lt.isActive
        );
      }

      // If still not found, try partial matching for custom leave types
      if (!leaveTypePolicy) {
        leaveTypePolicy = activePolicyConfig.leaveTypes?.find(
          lt => lt.enabled && lt.isActive && (
            lt.leaveType.includes(leaveType) || 
            leaveType.includes(lt.leaveType) ||
            lt.displayName?.toLowerCase().includes(leaveType.toLowerCase())
          )
        );
      }

      if (!leaveTypePolicy) {
        console.warn(`Leave type policy not found for: ${leaveType}`);
        
        // Try to get the configured value from any matching policy in the database
        try {
          const leaveTypePolicyRepository = AppDataSource.getRepository(LeaveTypePolicy);
          const directPolicy = await leaveTypePolicyRepository.findOne({
            where: { leaveType, enabled: true, isActive: true }
          });
          
          if (directPolicy) {
            leaveTypePolicy = directPolicy;
          } else {
            console.warn(`No leave type policy found in database for: ${leaveType}. Using default allocation.`);
            return await this.getDefaultAllocation(employeeId, leaveType, year);
          }
        } catch (dbError) {
          console.error(`Database error while looking up leave type policy:`, dbError);
          return await this.getDefaultAllocation(employeeId, leaveType, year);
        }
      }

      const context: AllocationCalculationContext = {
        employee,
        leaveTypePolicy,
        year,
        effectiveDate: effectiveDate || new Date()
      };

      // Calculate allocation based on leave type policy only
      return await this.calculatePolicyBasedAllocation(context);

    } catch (error) {
      console.error('Error calculating employee allocation:', error);
      // Return default allocation as fallback
      return await this.getDefaultAllocation(employeeId, leaveType, year);
    }
  }

  // Removed calculateAccrualBasedAllocation method as accrual rules were deleted

  /**
   * Calculate allocation based on leave type policy (fallback when no accrual rule)
   */
  private async calculatePolicyBasedAllocation(context: AllocationCalculationContext): Promise<EmployeeAllocationResult> {
    const { employee, leaveTypePolicy, year } = context;

    let totalDays = leaveTypePolicy.maxDaysPerYear || 0;
    let calculation = `Policy-based allocation: ${totalDays} days from ${leaveTypePolicy.displayName}`;

    // Apply proration for new employees if they joined this year
    if (this.shouldProrate(employee, year, leaveTypePolicy)) {
      const proratedDays = this.calculateProratedAllocation(totalDays, employee, year);
      if (proratedDays !== totalDays) {
        calculation += ` → Prorated: ${proratedDays} days`;
        totalDays = proratedDays;
      }
    }

    return {
      employeeId: parseInt(employee.id.toString()),
      leaveType: leaveTypePolicy.leaveType,
      totalDays,
      calculation,
      source: 'leave_type_policy'
    };
  }

  /**
   * Get default allocation when no policy is configured - REMOVED DEFAULTS
   */
  private async getDefaultAllocation(employeeId: number, leaveType: string, year: number): Promise<EmployeeAllocationResult> {
    console.warn(`No policy configuration found for leave type: ${leaveType} - returning zero allocation`);
    
    return {
      employeeId,
      leaveType,
      totalDays: 0,
      calculation: `No policy configuration found for leave type: ${leaveType}. Please configure leave policy.`,
      source: 'default'
    };
  }

  // Removed isRuleApplicable method as accrual rules were deleted

  /**
   * Check if allocation should be prorated for the employee
   */
  private shouldProrate(employee: User, year: number, leaveTypePolicy?: any): boolean {
    // First check if proration is enabled for this leave type
    if (leaveTypePolicy?.settings?.enableProratedLeave !== true) {
      return false;
    }
    
    const joiningYear = new Date(employee.createdAt).getFullYear();
    return joiningYear === year;
  }

  /**
   * Calculate prorated allocation for employees who joined mid-year
   */
  private calculateProratedAllocation(fullYearDays: number, employee: User, year: number): number {
    const joiningDate = new Date(employee.createdAt);
    const joiningYear = joiningDate.getFullYear();

    if (joiningYear !== year) {
      return fullYearDays; // No proration needed
    }

    const yearStart = new Date(year, 0, 1);
    const yearEnd = new Date(year, 11, 31);
    
    const totalDaysInYear = Math.ceil((yearEnd.getTime() - yearStart.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    const remainingDaysInYear = Math.ceil((yearEnd.getTime() - joiningDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    
    const proratedDays = Math.round((fullYearDays * remainingDaysInYear) / totalDaysInYear);
    
    return Math.max(0, proratedDays);
  }

  /**
   * Calculate months worked in a period
   */
  private calculateMonthsWorked(startDate: Date, endDate: Date): number {
    const startYear = startDate.getFullYear();
    const startMonth = startDate.getMonth();
    const endYear = endDate.getFullYear();
    const endMonth = endDate.getMonth();

    let months = (endYear - startYear) * 12 + (endMonth - startMonth);
    
    // Add fractional month if necessary
    const startDay = startDate.getDate();
    const endDay = endDate.getDate();
    
    if (endDay >= startDay) {
      months += 1;
    } else {
      months += endDay / new Date(endYear, endMonth + 1, 0).getDate();
    }

    return Math.max(0, months);
  }

  /**
   * Calculate quarters worked
   */
  private calculateQuartersWorked(employee: User, year: number, waitingPeriod: number): number {
    const joiningDate = new Date(employee.createdAt);
    const waitingEndDate = new Date(joiningDate);
    waitingEndDate.setDate(waitingEndDate.getDate() + waitingPeriod);

    const yearStart = new Date(year, 0, 1);
    const yearEnd = new Date(year, 11, 31);
    const workStartDate = new Date(Math.max(joiningDate.getTime(), yearStart.getTime(), waitingEndDate.getTime()));
    
    if (workStartDate > yearEnd) return 0;

    const workEndDate = new Date(Math.min(new Date().getTime(), yearEnd.getTime()));
    const monthsWorked = this.calculateMonthsWorked(workStartDate, workEndDate);
    
    return Math.round(monthsWorked / 3 * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Calculate weeks worked
   */
  private calculateWeeksWorked(employee: User, year: number, waitingPeriod: number): number {
    const joiningDate = new Date(employee.createdAt);
    const waitingEndDate = new Date(joiningDate);
    waitingEndDate.setDate(waitingEndDate.getDate() + waitingPeriod);

    const yearStart = new Date(year, 0, 1);
    const yearEnd = new Date(year, 11, 31);
    const workStartDate = new Date(Math.max(joiningDate.getTime(), yearStart.getTime(), waitingEndDate.getTime()));
    
    if (workStartDate > yearEnd) return 0;

    const workEndDate = new Date(Math.min(new Date().getTime(), yearEnd.getTime()));
    const daysWorked = Math.ceil((workEndDate.getTime() - workStartDate.getTime()) / (1000 * 60 * 60 * 24));
    
    return Math.round(daysWorked / 7 * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Calculate days worked
   */
  private calculateDaysWorked(employee: User, year: number, waitingPeriod: number): number {
    const joiningDate = new Date(employee.createdAt);
    const waitingEndDate = new Date(joiningDate);
    waitingEndDate.setDate(waitingEndDate.getDate() + waitingPeriod);

    const yearStart = new Date(year, 0, 1);
    const yearEnd = new Date(year, 11, 31);
    const workStartDate = new Date(Math.max(joiningDate.getTime(), yearStart.getTime(), waitingEndDate.getTime()));
    
    if (workStartDate > yearEnd) return 0;

    const workEndDate = new Date(Math.min(new Date().getTime(), yearEnd.getTime()));
    return Math.max(0, Math.ceil((workEndDate.getTime() - workStartDate.getTime()) / (1000 * 60 * 60 * 24)));
  }

  /**
   * Bulk calculate allocations for multiple employees
   */
  async bulkCalculateAllocations(
    employeeIds: number[],
    leaveTypes: string[],
    year: number
  ): Promise<EmployeeAllocationResult[]> {
    const results: EmployeeAllocationResult[] = [];

    for (const employeeId of employeeIds) {
      for (const leaveType of leaveTypes) {
        try {
          const allocation = await this.calculateEmployeeAllocation(employeeId, leaveType, year);
          results.push(allocation);
        } catch (error) {
          console.error(`Error calculating allocation for employee ${employeeId}, leave type ${leaveType}:`, error);
          // Add error result
          results.push({
            employeeId,
            leaveType,
            totalDays: 0,
            calculation: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            source: 'default'
          });
        }
      }
    }

    return results;
  }

  /**
   * Get existing balance and calculate new allocation based on carry forward rules
   */
  async calculateAllocationWithCarryForward(
    employeeId: number,
    leaveType: string,
    year: number
  ): Promise<{
    currentYearAllocation: EmployeeAllocationResult;
    carriedForward: number;
    totalAvailable: number;
    carryForwardExpiry?: Date;
  }> {
    const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);

    // Get current year allocation
    const currentYearAllocation = await this.calculateEmployeeAllocation(employeeId, leaveType, year);

    // Get previous year balance for carry forward calculation
    const previousYearBalance = await leaveBalanceRepository.findOne({
      where: {
        employeeId,
        leaveType,
        year: year - 1,
        isActive: true
      }
    });

    let carriedForward = 0;
    let carryForwardExpiry: Date | undefined;

    if (previousYearBalance && previousYearBalance.remaining > 0) {
      // Get leave type policy to check carry forward rules
      const policyConfigRepository = AppDataSource.getRepository(LeavePolicyConfiguration);
      const activePolicyConfig = await policyConfigRepository.findOne({
        where: { isActive: true },
        relations: ['leaveTypes']
      });

      const leaveTypePolicy = activePolicyConfig?.leaveTypes.find(
        lt => lt.leaveType === leaveType && lt.enabled && lt.isActive
      );

      if (leaveTypePolicy?.allowCarryForward) {
        const carryForwardLimit = leaveTypePolicy.carryForwardLimit || 0;
        carriedForward = Math.min(previousYearBalance.remaining, carryForwardLimit);
        
        // Calculate carry forward expiry (typically 3 months into the new year)
        if (carriedForward > 0) {
          carryForwardExpiry = new Date(year, 2, 31); // End of March
        }
      }
    }

    return {
      currentYearAllocation,
      carriedForward,
      totalAvailable: currentYearAllocation.totalDays + carriedForward,
      carryForwardExpiry
    };
  }

  /**
   * Diagnose allocation issues for a specific employee and leave type
   */
  async diagnoseAllocation(
    employeeId: number,
    leaveType: string,
    year: number
  ): Promise<{
    employeeFound: boolean;
    policyConfigFound: boolean;
    leaveTypePolicyFound: boolean;
    accrualRuleFound: boolean;
    allocationResult: EmployeeAllocationResult;
    diagnosis: string[];
    recommendations: string[];
  }> {
    const diagnosis: string[] = [];
    const recommendations: string[] = [];

    try {
      const userRepository = AppDataSource.getRepository(User);
      const policyConfigRepository = AppDataSource.getRepository(LeavePolicyConfiguration);

      // Check employee
      const employee = await userRepository.findOne({ where: { id: employeeId.toString() } });
      const employeeFound = !!employee;
      
      if (!employeeFound) {
        diagnosis.push(`❌ Employee with ID ${employeeId} not found`);
        recommendations.push('Verify the employee ID is correct and the employee exists in the system');
      } else {
        diagnosis.push(`✅ Employee found: ${employee.name || `User ID ${employee.id}`}`);
      }

      // Check policy configuration
      const activePolicyConfig = await policyConfigRepository.findOne({
        where: { isActive: true },
        relations: ['leaveTypes']
      });
      const policyConfigFound = !!activePolicyConfig;

      if (!policyConfigFound) {
        diagnosis.push(`❌ No active policy configuration found`);
        recommendations.push('Create and activate a leave policy configuration in the Policies tab');
      } else {
        diagnosis.push(`✅ Active policy configuration found (Version: ${activePolicyConfig.version})`);
        diagnosis.push(`📋 Available leave types: ${activePolicyConfig.leaveTypes?.map(lt => `${lt.leaveType} (${lt.displayName})`).join(', ')}`);
      }

      // Check leave type policy
      let leaveTypePolicy = null;
      let leaveTypePolicyFound = false;

      if (activePolicyConfig) {
        leaveTypePolicy = activePolicyConfig.leaveTypes.find(
          lt => lt.leaveType === leaveType && lt.enabled && lt.isActive
        );
        
        if (!leaveTypePolicy) {
          // Try case-insensitive search
          leaveTypePolicy = activePolicyConfig.leaveTypes.find(
            lt => lt.leaveType.toLowerCase() === leaveType.toLowerCase() && lt.enabled && lt.isActive
          );
        }

        leaveTypePolicyFound = !!leaveTypePolicy;

        if (!leaveTypePolicyFound) {
          diagnosis.push(`❌ Leave type policy not found for: ${leaveType}`);
          diagnosis.push(`📋 Searched in: ${activePolicyConfig.leaveTypes?.map(lt => lt.leaveType).join(', ')}`);
          recommendations.push(`Configure the leave type "${leaveType}" in the Leave Types tab`);
          recommendations.push('Ensure the leave type is enabled and active');
        } else if (leaveTypePolicy) {
          diagnosis.push(`✅ Leave type policy found: ${leaveTypePolicy.displayName} (${leaveTypePolicy.maxDaysPerYear} days/year)`);
        }
      }

      // Accrual rules have been removed - using policy-based allocation only
      const accrualRuleFound = false;
      diagnosis.push(`ℹ️ Using policy-based allocation (accrual rules have been removed from the system)`);

      // Get the actual allocation
      const allocationResult = await this.calculateEmployeeAllocation(employeeId, leaveType, year);

      // Provide summary
      diagnosis.push(`\n📊 Final Allocation: ${allocationResult.totalDays} days (Source: ${allocationResult.source})`);
      diagnosis.push(`📝 Calculation: ${allocationResult.calculation}`);

      // Additional recommendations based on source
      if (allocationResult.source === 'default') {
        recommendations.push('🎯 To get accurate allocations, configure leave policies');
        recommendations.push('💡 Default values are fallbacks and may not reflect your organization\'s actual policy');
      } else if (allocationResult.source === 'leave_type_policy') {
        recommendations.push('✅ Using policy-based allocation - configured leave type policy');
      }

      return {
        employeeFound,
        policyConfigFound,
        leaveTypePolicyFound,
        accrualRuleFound,
        allocationResult,
        diagnosis,
        recommendations
      };

    } catch (error) {
      diagnosis.push(`❌ Error during diagnosis: ${error instanceof Error ? error.message : 'Unknown error'}`);
      recommendations.push('Contact system administrator if this error persists');

      // Provide a basic fallback allocation for error cases
      const fallbackAllocation: EmployeeAllocationResult = {
        employeeId,
        leaveType,
        totalDays: 0,
        calculation: `Error occurred during allocation calculation: ${error instanceof Error ? error.message : 'Unknown error'}`,
        source: 'default'
      };

      return {
        employeeFound: false,
        policyConfigFound: false,
        leaveTypePolicyFound: false,
        accrualRuleFound: false,
        allocationResult: fallbackAllocation,
        diagnosis,
        recommendations
      };
    }
  }

  /**
   * Recalculate all allocations for a specific leave type
   * Useful when proration settings change
   */
  async recalculateAllocationsForLeaveType(leaveType: string, year?: number): Promise<void> {
    try {
      const targetYear = year || new Date().getFullYear();
      console.log(`🔄 Recalculating allocations for leave type: ${leaveType}, year: ${targetYear}`);

      // Get the leave type policy
      const leaveTypePolicyRepository = AppDataSource.getRepository(LeaveTypePolicy);
      const leaveTypePolicy = await leaveTypePolicyRepository.findOne({
        where: { leaveType, isActive: true }
      });

      if (!leaveTypePolicy) {
        throw new Error(`Leave type policy not found for: ${leaveType}`);
      }

      // Get all active employees
      const userRepository = AppDataSource.getRepository(User);
      const employees = await userRepository.find({
        where: { isActive: true }
      });

      // Recalculate allocation for each employee
      for (const employee of employees) {
        try {
          const allocation = await this.calculateEmployeeAllocation(
            parseInt(employee.id.toString()),
            leaveType,
            targetYear
          );

          // Update the allocation in database
          const leaveAllocationRepository = AppDataSource.getRepository(LeaveAllocation);
          const existingAllocation = await leaveAllocationRepository.findOne({
            where: {
              employeeId: parseInt(employee.id.toString()),
              leaveType,
              year: targetYear
            }
          });

          if (existingAllocation) {
            existingAllocation.policyAllocation = allocation.totalDays;
            existingAllocation.notes = `Recalculated allocation: ${allocation.calculation}`;
            existingAllocation.updatedAt = new Date();
            await leaveAllocationRepository.save(existingAllocation);
          } else {
            // Create new allocation if it doesn't exist
            const newAllocation = leaveAllocationRepository.create({
              employeeId: parseInt(employee.id.toString()),
              leaveType,
              year: targetYear,
              policyAllocation: allocation.totalDays,
              manualAdjustment: 0,
              carriedForward: 0,
              source: 'POLICY',
              notes: `New allocation: ${allocation.calculation}`,
              isActive: true
            });
            await leaveAllocationRepository.save(newAllocation);
          }

        } catch (employeeError) {
          console.error(`Error recalculating allocation for employee ${employee.id}:`, employeeError);
        }
      }

      console.log(`✅ Successfully recalculated allocations for ${leaveType}`);
    } catch (error) {
      console.error(`Error recalculating allocations for leave type ${leaveType}:`, error);
      throw error;
    }
  }
}

export const leaveAllocationService = LeaveAllocationService.getInstance(); 