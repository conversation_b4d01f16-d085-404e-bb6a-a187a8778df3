/*
 * PrinterMaintenanceForm.tsx
 * 
 * This component handles the form for creating and editing printer maintenance records.
 * 
 * NOTE: This component has been updated to use direct props (initialData) for editing records
 * instead of relying on localStorage. The localStorage fallback is kept for backward compatibility
 * but will be phased out in future updates.
 */

import React, { useState, useEffect, useRef, FC } from 'react';
import { toast } from 'react-hot-toast';
import api from '../services/api';
import axios from 'axios';

interface Printer {
  id: number;
  name: string;
  manufacturer?: string;
  model?: string;
  department?: string;
  location?: string;
  assignee?: string;
  vendor_id?: string;
}

interface Vendor {
  id: string;
  vendorName: string;
  companyName?: string;
  name?: string;  // Keep for backwards compatibility with existing code
}

interface Staff {
  id: string;
  name: string;
  department?: string;
}

interface PrinterFormData {
  printer_id: number;
  asset_id?: string;
  visit_date: string;
  assignee_name: string;
  department: string;
  vendor_name: string;
  service_type: string;
  technician_name: string;
  issue_description: string;
  parts_replaced: {
    toner: boolean;
    drum: boolean;
    fuser_unit: boolean;
    pickup_roller: boolean;
  };
  invoice_number: string;
  invoice_amount: string;
  invoice_file?: File;
  remarks: string;
}

interface PrinterMaintenanceFormProps {
  onComplete?: () => void;
  inPage?: boolean;
  isEditMode?: boolean;
  recordId?: string;
  initialData?: any;
}

const SERVICE_TYPES = [
  'Routine Visit',
  'Toner Change',
  'Drum Replacement',
  'Fuser Unit Replacement',
  'Roller Replacement',
  'Preventive Maintenance',
  'Repair',
  'Other'
];

const PrinterMaintenanceForm: FC<PrinterMaintenanceFormProps> = ({ 
  onComplete,
  inPage = false,
  isEditMode = false,
  recordId,
  initialData
}) => {
  console.log("PrinterMaintenanceForm - Initial render with props:", { 
    initialData, 
    isEditMode,
    recordId,
    "printer_id": initialData?.printer_id,
    "asset_id": initialData?.asset_id
  });

  const [formData, setFormData] = useState<PrinterFormData>({
    printer_id: 0,
    visit_date: new Date().toISOString().split('T')[0],
    assignee_name: '',
    department: '',
    vendor_name: '',
    service_type: 'Regular Maintenance',
    technician_name: '',
    issue_description: '',
    parts_replaced: {
      toner: false,
      drum: false,
      fuser_unit: false,
      pickup_roller: false
    },
    invoice_number: '',
    invoice_amount: '',
    remarks: ''
  });

  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [originalRecordId, setOriginalRecordId] = useState<string | null>(null);
  const [printers, setPrinters] = useState<Printer[]>([]);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [staff, setStaff] = useState<Staff[]>([]);
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null);
  const [fetchingData, setFetchingData] = useState(true);

  // Add state to track attempted record loads to prevent loops
  const [attemptedLoadIds, setAttemptedLoadIds] = useState<Set<string>>(new Set());
  const [isNewFormMode, setIsNewFormMode] = useState(false);

  // Helper function to extract files from initialData
  useEffect(() => {
    if (initialData && isEditMode) {
      // Check for file attachments in the data
      let existingFiles = [];
      
      if (initialData.hasOwnProperty('files') && initialData.files) {
        console.log('Found files in initialData.files:', initialData.files);
        existingFiles = Array.isArray(initialData.files) ? initialData.files : [initialData.files];
      } 
      else if (initialData.hasOwnProperty('attachments') && initialData.attachments) {
        console.log('Found files in initialData.attachments:', initialData.attachments);
        existingFiles = Array.isArray(initialData.attachments) ? initialData.attachments : [initialData.attachments];
      }
      else if (initialData.hasOwnProperty('invoice_file') && initialData.invoice_file) {
        console.log('Found invoice_file in initialData:', initialData.invoice_file);
        existingFiles = [{
          name: 'Invoice File',
          path: initialData.invoice_file,
          originalname: initialData.invoice_file.split('/').pop() || 'Invoice'
        }];
      }
      
      if (existingFiles && existingFiles.length > 0) {
        console.log('Setting existing attachments:', existingFiles);
        setExistingAttachments(existingFiles);
      } else {
        console.log('No valid file attachments found in initialData');
      }
    }
  }, [initialData, isEditMode]);

  // State to keep track of existing attachments
  const [existingAttachments, setExistingAttachments] = useState<any[]>([]);

  // Function to display existing file names
  const renderExistingFiles = () => {
    // Check if there's a file in initialData
    if (!initialData?.invoice_file && !initialData?.invoiceFilePath) return null;

    // Get auth token
    const token = localStorage.getItem('authToken');

    // Extract just the filename from the path
    const filename = (initialData.invoice_file || initialData.invoiceFilePath || '').split(/[\\/]/).pop() || 'invoice.pdf';
    
    // Determine which ID to use - first check recordId from props, then id from initialData
    const idToUse = recordId || initialData?.id;

    const handleDownload = async () => {
      try {
        if (!filename) {
          toast.error('No file available for download');
          return;
        }

        if (!idToUse) {
          console.error('Missing record ID for download', {
            recordId,
            initialDataId: initialData?.id,
            initialData
          });
          toast.error('Cannot download: Missing record ID');
          return;
        }

        // Show loading toast
        const loadingToast = toast.loading('Downloading file...');

        // Construct the URL with optional token as query parameter for auth
        let downloadUrl = `/api/printer-maintenance/${idToUse}/download`;
        if (token) {
          // Add token as query parameter for better compatibility
          downloadUrl += `?token=${encodeURIComponent(token)}`;
        }
        
        console.log('Download URL:', downloadUrl);

        // For direct download, use window.location.href
        // This works better than fetch as it doesn't have CORS issues
        window.location.href = downloadUrl;
        
        // Dismiss loading toast after a short delay
        setTimeout(() => {
          toast.dismiss(loadingToast);
          toast.success('Download started');
        }, 1000);
      } catch (error: any) {
        console.error('Download error:', error);
        toast.error(error.message || 'Failed to download file. Please try again.');
      }
    };

    return (
      <div className="mt-2">
        <p className="text-sm font-medium text-gray-700">Existing Invoice:</p>
        <div className="mt-1 flex items-center gap-2">
          <span className="text-sm text-gray-600">{filename}</span>
          <button
            type="button"
            onClick={handleDownload}
            className="text-[#1a73e8] hover:text-blue-700 cursor-pointer underline text-sm inline-flex items-center gap-1"
            title="Download invoice file"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-4 w-4" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" 
              />
            </svg>
            Download
          </button>
        </div>
      </div>
    );
  };

  // Fetch printers, vendors, and staff data
    const fetchData = async () => {
      setFetchingData(true);
      console.log('Starting to fetch data for form...');
      try {
        // Fetch printers
      console.log('Fetching printers from /assets endpoint...');
      let printersRes;
      try {
        printersRes = await api.get('/assets');
        console.log('Printer data fetched successfully');
      } catch (error: any) {
        console.error('Error fetching printers:', error);
        console.log('Attempting fallback to /printers endpoint...');
        printersRes = await api.get('/printers');
        console.log('Printer data fetched from fallback endpoint');
      }
      
        const rawPrinters = printersRes.data;
        console.log('CRITICAL - Raw printer data received from API:', rawPrinters);

        // Fetch vendors first (we'll need this for printer matching)
        // Specifically get printer vendors if possible
        let vendorsData = [];
      console.log('Fetching vendors...');
        try {
          // Try to get printer suppliers specifically first
          const vendorsRes = await api.get('/vendors', { 
          params: { type: 'PRINTER_SUPPLIER' } 
          });
          vendorsData = Array.isArray(vendorsRes.data) ? vendorsRes.data : [];
        console.log('Filtered vendors received:', vendorsData.length);
          
          // If no specific printer vendors, get all vendors
          if (vendorsData.length === 0) {
          console.log('No printer vendors found, fetching all vendors...');
            const allVendorsRes = await api.get('/vendors');
            vendorsData = Array.isArray(allVendorsRes.data) ? allVendorsRes.data : [];
          console.log('All vendors received:', vendorsData.length);
          }
          
          console.log('Raw vendor data from API:', vendorsData);
      } catch (error: any) {
        console.error('Error fetching vendors:', error);
        // Detailed error logging
        if (error.response) {
          console.error('Vendor fetch response status:', error.response.status);
          console.error('Vendor fetch response data:', error.response.data);
        }
          // Fallback to empty array
          vendorsData = [];
        toast.error('Could not load vendors. Please check console for details.');
        }
        
        // Create a vendor lookup map for matching with printers
        const vendorMap: Record<string, any> = {};
        vendorsData.forEach(vendor => {
          // Use vendorName (or name as fallback) as the key
          const vendorName = vendor.vendorName || vendor.name;
          if (vendorName) {
            // Convert to lowercase for case-insensitive matching
            vendorMap[vendorName.toLowerCase()] = vendor;
            
            // Also add manufacturer name as an alias if it exists
            if (vendor.companyName) {
              vendorMap[vendor.companyName.toLowerCase()] = vendor;
            }
          }
        });
        console.log('Vendor lookup map created:', vendorMap);

        // Verify structure of first printer to understand data format
        if (Array.isArray(rawPrinters) && rawPrinters.length > 0) {
          const samplePrinter = rawPrinters[0];
          console.log('SAMPLE PRINTER KEYS:', Object.keys(samplePrinter).sort().join(', '));
          
          // Print out ANY property that might contain user or vendor information
          console.log('PRINTER VENDOR INFO FIELDS:', {
            manufacturer: samplePrinter.manufacturer,
            vendor: samplePrinter.vendor,
            supplier: samplePrinter.supplier,
            vendor_id: samplePrinter.vendor_id
          });
          
          // Check for the new assignedTo structure
          console.log('PRINTER ASSIGNEE INFO:', {
            assignedTo: samplePrinter.assignedTo,
            assigned_to: samplePrinter.assigned_to,
            assignee: samplePrinter.assignee
          });
          
          // Specifically check the structure of assignedTo if it exists
          if (samplePrinter.assignedTo) {
            console.log('ASSIGNEDTO STRUCTURE:', 
              typeof samplePrinter.assignedTo === 'object' 
                ? Object.keys(samplePrinter.assignedTo) 
                : typeof samplePrinter.assignedTo
            );
            
            if (samplePrinter.assignedTo && typeof samplePrinter.assignedTo === 'object') {
              console.log('ASSIGNEDTO SAMPLE VALUE:', samplePrinter.assignedTo);
            }
          }
        }

        // Process and filter printers with better assignee detection and vendor matching
        const processedPrinters = Array.isArray(rawPrinters) 
          ? rawPrinters
              .filter((asset: any) => 
                asset.type?.toLowerCase().includes('print') ||
                asset.category?.toLowerCase().includes('print') ||
                asset.category?.toLowerCase().includes('plotter') ||
                asset.name?.toLowerCase().includes('print') ||
                asset.type?.toLowerCase() === 'printing'
              )
              .map((asset: any) => {
                // Extract manufacturer for vendor matching
                const manufacturer = asset.manufacturer || '';
                
                // Try to match printer to a vendor based on manufacturer name
                let matchedVendor = null;
                if (manufacturer && vendorMap[manufacturer.toLowerCase()]) {
                  matchedVendor = vendorMap[manufacturer.toLowerCase()];
                  console.log(`Matched printer ${asset.id} to vendor based on manufacturer:`, matchedVendor.vendorName || matchedVendor.name);
                }
                
                // Special handling for printer #34 (the one in the screenshot)
                if (asset.id === 34) {
                  console.log('FOUND PRINTER #34 - DETAILED INSPECTION:');
                  console.log('- Manufacturer:', manufacturer);
                  console.log('- Vendor Info:', asset.vendor, asset.supplier, asset.vendor_id);
                  console.log('- Matched Vendor:', matchedVendor);
                  console.log('- AssignedTo:', asset.assignedTo);
                }
                
                // Extract assignee from the asset data
                let assignee = 'Not Assigned';
                
                // Handle the new assignedTo object structure
                if (asset.assignedTo && typeof asset.assignedTo === 'object' && asset.assignedTo.name) {
                  assignee = asset.assignedTo.name;
                  console.log(`Printer ${asset.id}: Found assignee in assignedTo object:`, assignee);
                }
                // Fallbacks for backwards compatibility
                else if (asset.assigned_to && typeof asset.assigned_to === 'object') {
                  assignee = asset.assigned_to.name || asset.assigned_to.full_name || asset.assigned_to.display_name || 'Not Assigned';
                } else if (asset.assigned_to && typeof asset.assigned_to === 'string' && asset.assigned_to.trim() !== '') {
                  assignee = asset.assigned_to;
                } else if (asset.assignee && asset.assignee.trim() !== '') {
                  assignee = asset.assignee;
                } 
                
                // Create printer object with determined assignee and vendor info
                const printer = {
                  id: Number(asset.id),
                  name: asset.name || '',
                  manufacturer: asset.manufacturer || 'Unknown',
                  model: asset.model || asset.assetModel || 'Unknown',
                  department: asset.department || 'IT',
                  location: asset.location || '',
                  assignee: assignee,
                  vendor_id: matchedVendor ? matchedVendor.id : (asset.vendor_id || '')
                };
                
                console.log('Created printer object:', printer);
                return printer;
              })
          : [];
        
        console.log('Final processed printers with vendors:', processedPrinters);
        setPrinters(processedPrinters);

        // Process vendors to match the structure from VendorContacts
        const processedVendors = vendorsData.map(vendor => {
          // Check for vendorName (used in VendorContacts) before falling back to name
          if (!vendor.vendorName && !vendor.name) {
            console.log('Found vendor with missing name:', vendor);
            return {
              ...vendor,
              vendorName: vendor.companyName || vendor.company || vendor.vendor_name || `Vendor ${vendor.id}` || 'Unknown Vendor'
            };
          }
          return vendor;
        });
        
        console.log('Processed vendors:', processedVendors);
        setVendors(processedVendors);

        // Fetch staff first so we can use it for matching
        const staffRes = await api.get('/users');
        const staffData = Array.isArray(staffRes.data) ? staffRes.data : [];
        console.log('Staff data received from API:', staffData);
        
        // Create a lookup map for staff by ID
        const staffMap = staffData.reduce((acc: Record<string, Staff>, staff: Staff) => {
          acc[staff.id] = staff;
          return acc;
        }, {});
        console.log('Staff lookup map created, with keys:', Object.keys(staffMap));
        
        // Also map by name for fuzzy matching
        const staffByName = staffData.reduce((acc: Record<string, Staff>, staff: Staff) => {
          if (staff.name) {
            acc[staff.name.toLowerCase()] = staff;
          }
          return acc;
        }, {});
        
        setStaff(staffData);

      } catch (error) {
        console.error('Error fetching data:', error);
      toast.error('Failed to load data for form');
      } finally {
        setFetchingData(false);
      setDataLoaded(true);
    }
  };

  // Initialize form with initialData if provided
  useEffect(() => {
    // Initialize form with initialData if provided (for edit mode)
    if (initialData) {
      console.log("Initializing form with initialData:", initialData);
      
      // Format date correctly for visit_date
      let visitDate = '';
      try {
        // First check visit_date, then service_date, and use a default if neither exists
        if (initialData.visit_date) {
          visitDate = new Date(initialData.visit_date).toISOString().split('T')[0];
        } else if (initialData.service_date) {
          visitDate = new Date(initialData.service_date).toISOString().split('T')[0];
        } else if (initialData.serviceDate) {
          visitDate = new Date(initialData.serviceDate).toISOString().split('T')[0];
        } else {
          // Default to today if no date is found
          visitDate = new Date().toISOString().split('T')[0];
        }
      } catch (error) {
        console.error("Error formatting visit date:", error);
        // Fallback to today
        visitDate = new Date().toISOString().split('T')[0];
      }
      
      // Parse parts_replaced
      let partsReplaced = {
        toner: false,
        drum: false,
        fuser_unit: false,
        pickup_roller: false,
      };
      
      try {
        if (initialData.parts_replaced) {
          // Handle array format [{partName: "Toner", quantity: 1}, ...]
          if (Array.isArray(initialData.parts_replaced)) {
            initialData.parts_replaced.forEach((part: any) => {
              const partName = (part.partName || part.name || '').toLowerCase().replace(/\s+/g, '_');
              if (partName === 'toner') partsReplaced.toner = true;
              if (partName === 'drum') partsReplaced.drum = true;
              if (partName === 'fuser_unit' || partName === 'fuser unit') partsReplaced.fuser_unit = true;
              if (partName === 'pickup_roller' || partName === 'pickup roller') partsReplaced.pickup_roller = true;
            });
          }
          // Handle string format (JSON)
          else if (typeof initialData.parts_replaced === 'string') {
            try {
              const parsedParts = JSON.parse(initialData.parts_replaced);
              if (Array.isArray(parsedParts)) {
                parsedParts.forEach((part: any) => {
                  const partName = (part.partName || part.name || '').toLowerCase().replace(/\s+/g, '_');
                  if (partName === 'toner') partsReplaced.toner = true;
                  if (partName === 'drum') partsReplaced.drum = true;
                  if (partName === 'fuser_unit' || partName === 'fuser unit') partsReplaced.fuser_unit = true;
                  if (partName === 'pickup_roller' || partName === 'pickup roller') partsReplaced.pickup_roller = true;
                });
              } else if (typeof parsedParts === 'object') {
                Object.keys(parsedParts).forEach(key => {
                  const normalizedKey = key.toLowerCase().replace(/\s+/g, '_');
                  if (normalizedKey === 'toner') partsReplaced.toner = Boolean(parsedParts[key]);
                  if (normalizedKey === 'drum') partsReplaced.drum = Boolean(parsedParts[key]);
                  if (normalizedKey === 'fuser_unit') partsReplaced.fuser_unit = Boolean(parsedParts[key]);
                  if (normalizedKey === 'pickup_roller') partsReplaced.pickup_roller = Boolean(parsedParts[key]);
                });
              }
            } catch (error) {
              console.error("Error parsing parts_replaced string:", error);
            }
          }
          // Handle object format directly
          else if (typeof initialData.parts_replaced === 'object') {
            Object.keys(initialData.parts_replaced).forEach(key => {
              const normalizedKey = key.toLowerCase().replace(/\s+/g, '_');
              if (normalizedKey === 'toner') partsReplaced.toner = Boolean(initialData.parts_replaced[key]);
              if (normalizedKey === 'drum') partsReplaced.drum = Boolean(initialData.parts_replaced[key]);
              if (normalizedKey === 'fuser_unit') partsReplaced.fuser_unit = Boolean(initialData.parts_replaced[key]);
              if (normalizedKey === 'pickup_roller') partsReplaced.pickup_roller = Boolean(initialData.parts_replaced[key]);
            });
          }
        }
      } catch (error) {
        console.error("Error processing parts_replaced:", error);
      }
      
      console.log("Parsed parts_replaced:", partsReplaced);
      
      // Set form data
      setFormData(prev => ({
        ...prev,
        printer_id: parseInt(String(initialData.printer_id || initialData.asset_id || 0)),
        visit_date: visitDate,
        assignee_name: initialData.assignee_name || '',
        department: initialData.department || '',
        vendor_name: initialData.vendor_name || '',
        service_type: initialData.service_type || initialData.action_taken || 'Regular Maintenance',
        technician_name: initialData.technician_name || '',
        issue_description: initialData.issue_description || initialData.description || '',
        parts_replaced: partsReplaced,
        invoice_number: initialData.invoice_number || '',
        invoice_amount: String(initialData.invoice_amount || initialData.cost || ''),
        remarks: initialData.remarks || ''
      }));
    }
  }, [initialData]);

  // Get recordId from URL if not provided in props
  useEffect(() => {
    // Determine if we're in new form mode based on URL
    const currentPath = window.location.pathname;
    const isAddOrNewPath = currentPath.includes('/new') || 
                           currentPath.endsWith('/add') || 
                           currentPath === '/vendor-management/contacts' ||
                           currentPath === '/vendors/contacts' ||
                           currentPath === '/printer-maintenance';
    
    setIsNewFormMode(isAddOrNewPath);
    
    // Skip loading records on base paths that should never load existing records
    if (isAddOrNewPath) {
      console.log('On add/new path, not attempting to load records');
      return;
    }
    
    if (!recordId) {
      // Check if we have an ID in the URL
      const pathParts = window.location.pathname.split('/');
      const urlRecordId = pathParts[pathParts.length - 1];
      
      // If urlRecordId looks like an ID and not just "edit" or "new"
      if (urlRecordId && 
          urlRecordId !== "edit" && 
          urlRecordId !== "new" && 
          !isNaN(Number(urlRecordId)) && // Check if it's a numeric ID
          urlRecordId !== "contacts" &&   // Avoid treating "contacts" as an ID
          !attemptedLoadIds.has(urlRecordId)) {  // Skip if we've already tried to load this ID
        // This is an edit mode with ID in the URL
        loadExistingRecord(urlRecordId);
      } else if (localStorage.getItem('editMaintenanceRecord') && !initialData) {
        // Try to load from localStorage only if initialData is not provided
        try {
          const savedRecord = JSON.parse(localStorage.getItem('editMaintenanceRecord') || '');
          if (savedRecord && savedRecord.id && !attemptedLoadIds.has(savedRecord.id)) {
            console.log('Found complete record data in localStorage:', savedRecord);
            
            // If the record contains enough information, populate the form directly
            if (savedRecord.service_type || savedRecord.technician_name || savedRecord.visit_date) {
              console.log('Using direct data from localStorage to populate form');
              
              // Format date correctly
              const visitDate = savedRecord.visit_date || savedRecord.service_date;
              const formattedDate = visitDate ? new Date(visitDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0];
              
              // Parse parts_replaced if available
              let parsedParts = {
                toner: false,
                drum: false,
                fuser_unit: false,
                pickup_roller: false
              };
              
              if (savedRecord.parts_replaced) {
                try {
                  // Handle string format (JSON)
                  if (typeof savedRecord.parts_replaced === 'string') {
                    const partsJson = JSON.parse(savedRecord.parts_replaced);
                    if (typeof partsJson === 'object') {
                      Object.keys(partsJson).forEach(key => {
                        const normalizedKey = key.toLowerCase().replace(/\s+/g, '_');
                        if (normalizedKey in parsedParts && partsJson[key] === true) {
                          parsedParts[normalizedKey as keyof typeof parsedParts] = true;
                        }
                      });
                    }
                  } 
                  // Handle object format
                  else if (typeof savedRecord.parts_replaced === 'object') {
                    Object.keys(savedRecord.parts_replaced).forEach(key => {
                      const normalizedKey = key.toLowerCase().replace(/\s+/g, '_');
                      if (normalizedKey in parsedParts && savedRecord.parts_replaced[key] === true) {
                        parsedParts[normalizedKey as keyof typeof parsedParts] = true;
                      }
                    });
                  }
                } catch (e) {
                  console.error('Error parsing parts_replaced:', e);
                }
              }
              
              // Set form data directly from localStorage
              setFormData({
                printer_id: parseInt(String(savedRecord.printer_id || savedRecord.asset_id || 0)),
                visit_date: formattedDate,
                assignee_name: savedRecord.assignee_name || '',
                department: savedRecord.department || '',
                vendor_name: savedRecord.vendor_name || '',
                service_type: savedRecord.service_type || 'Regular Maintenance',
                technician_name: savedRecord.technician_name || '',
                issue_description: savedRecord.issue_description || '',
                parts_replaced: parsedParts,
                invoice_number: savedRecord.invoice_number || '',
                invoice_amount: String(savedRecord.invoice_amount || savedRecord.cost || ''),
                remarks: savedRecord.remarks || ''
              });
              
              // Store the original ID for use during submit
              setOriginalRecordId(String(savedRecord.id));
              setAttemptedLoadIds(prev => new Set(prev).add(String(savedRecord.id)));
              
              // Mark as loaded
              setDataLoaded(true);
              
              // Ensure printer and vendor data is loaded
              if (!dataLoaded) {
                fetchData();
              }
              
              // Show success message
              toast.success('Record loaded for editing');
            } else {
              // If not enough data in localStorage, try loading from API
              loadExistingRecord(String(savedRecord.id));
            }
            
            // Clear localStorage after using it
            localStorage.removeItem('editMaintenanceRecord');
          }
        } catch (err) {
          console.error('Error parsing saved record:', err);
        }
      }
    } else if (isEditMode && recordId && !attemptedLoadIds.has(recordId)) {
      // We have a recordId from props and haven't tried to load it yet
      loadExistingRecord(recordId);
    }
  }, [recordId, isEditMode, attemptedLoadIds]);
  
  // Load existing record for editing
  const loadExistingRecord = async (id: string, silent: boolean = false) => {
    // Prevent loading if we're in a "new" form mode
    if (isNewFormMode) {
      console.log('In new form mode, skipping record loading');
      return;
    }

    console.log('loadExistingRecord called with ID:', id);

    // Mark this ID as attempted to prevent future loading attempts
    setAttemptedLoadIds(prev => new Set(prev).add(id));

    // Check if this is a contacts path
    const currentPath = window.location.pathname;
    const isContactsPath = currentPath.includes('/contacts') || 
                           currentPath.includes('/vendor-management') || 
                           currentPath.includes('/vendors');
                           
    try {
      setLoading(true);
      setOriginalRecordId(id); // Store the original ID for use during submit
      
      // Explicitly set isEditMode to true when we're loading a record
      if (id) {
        console.log('🔴 EDITING MODE ACTIVATED - Working with existing record:', id);
        // We're explicitly in edit mode now
        console.log('📢 Setting form to EDIT MODE');
      }
      
      console.log(`Attempting to load record with ID: ${id}`);
      console.log('Current path:', currentPath);
      console.log('Is contacts path:', isContactsPath);
      
      // Based on the console errors, we can see the specific paths being tried
      // Let's match these exactly to fix the 404 errors
      let record;
      
      if (isContactsPath) {
        // For contacts, we need to try multiple specific patterns based on the error logs
        try {
          console.log('Trying first contacts endpoint pattern...');
          // Remove the /api prefix as it's being added by the api service
          const response = await fetch(`/vendor-management/contacts/${id}`);
          if (!response.ok) {
            throw new Error(`Response not OK: ${response.status}`);
          }
          const data = await response.json();
          console.log('Successfully loaded from vendor-management/contacts endpoint');
          record = data;
        } catch (firstErr) {
          console.log('First contacts endpoint failed, trying direct API call...', firstErr);
          try {
            // Try with api service directly, without the /contacts suffix
            const response = await api.get(`/vendor-management/contacts/${id}`); 
            console.log('Successfully loaded from api.get vendor-management/contacts endpoint');
            record = response.data;
          } catch (secondErr) {
            console.log('Second attempt failed, trying vendors/contacts endpoint...');
            try {
              const response = await fetch(`/vendors/contacts/${id}`);
              if (!response.ok) {
                throw new Error(`Response not OK: ${response.status}`);
              }
              const data = await response.json();
              console.log('Successfully loaded from vendors/contacts endpoint');
              record = data;
            } catch (thirdErr) {
              console.log('Third attempt failed, trying direct vendors endpoint...');
              try {
                // Try with api service directly
                const response = await api.get(`/vendors/contacts/${id}`);
                console.log('Successfully loaded from api.get vendors/contacts endpoint');
                record = response.data;
              } catch (fourthErr) {
                console.error('All contacts endpoints failed, using empty data');
                record = {}; // Use empty record as fallback
                toast.error('Could not load contact data');
              }
            }
          }
        }
      } else {
        // First, try a direct API call with the exact ID
        try {
          console.log(`Trying direct API call to /api/printer-maintenance/${id}`);
          const response = await api.get(`/printer-maintenance/${id}`);
          console.log('API Response:', response);
          
          if (response.data) {
            console.log('Successfully loaded record with direct call:', response.data);
            // If response.data is itself an object with a data property, use that
            record = response.data.data || response.data;
          } else {
            throw new Error('Empty response data');
          }
        } catch (directErr) {
          console.error('Direct API call failed:', directErr);
          
          // Fallback to trying multiple endpoints
        try {
          console.log('Trying standard printer maintenance endpoint...');
          const response = await api.get(`/printer-maintenance/${id}`);
          console.log('Successfully loaded from standard endpoint');
          record = response.data;
        } catch (firstErr) {
          console.log('First attempt failed, trying record endpoint...');
          try {
            const response = await api.get(`/printer-maintenance/record/${id}`);
            console.log('Successfully loaded from record endpoint');
            record = response.data;
          } catch (secondErr) {
            console.log('Second attempt failed, trying find endpoint...');
            try {
              const response = await api.get(`/printer-maintenance/find/${id}`);
              console.log('Successfully loaded from find endpoint');
              record = response.data;
            } catch (thirdErr) {
              console.error('All endpoints failed');
              throw thirdErr;
              }
            }
          }
        }
      }
      
      if (record) {
        console.log('Record data loaded:', record);
        
        // Format date correctly
        const visitDate = record.visit_date || record.service_date;
        const formattedDate = visitDate ? new Date(visitDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0];
        
        // Parse parts_replaced - handle different formats
        let parsedParts = {
          toner: false,
          drum: false,
          fuser_unit: false,
          pickup_roller: false
        };
        
        if (record.parts_replaced) {
          try {
            // If it's a string, try to parse as JSON
            if (typeof record.parts_replaced === 'string') {
              const partsJson = JSON.parse(record.parts_replaced);
              
              // Check if it's an array of objects with partName
              if (Array.isArray(partsJson)) {
                partsJson.forEach(part => {
                  const partName = (part.partName || part.name || '').toLowerCase().replace(/\s+/g, '_');
                  if (partName in parsedParts) {
                    parsedParts[partName as keyof typeof parsedParts] = true;
                  }
                });
              } 
              // If it's a direct object with boolean values
              else if (typeof partsJson === 'object') {
                Object.keys(partsJson).forEach(key => {
                  const normalizedKey = key.toLowerCase().replace(/\s+/g, '_');
                  if (normalizedKey in parsedParts && partsJson[key] === true) {
                    parsedParts[normalizedKey as keyof typeof parsedParts] = true;
                  }
                });
              }
            } 
            // If it's already an array or object
            else if (typeof record.parts_replaced === 'object') {
              if (Array.isArray(record.parts_replaced)) {
                record.parts_replaced.forEach((part: any) => {
                  const partName = (part.partName || part.name || '').toLowerCase().replace(/\s+/g, '_');
                  if (partName in parsedParts) {
                    parsedParts[partName as keyof typeof parsedParts] = true;
                  }
                });
              } else {
                Object.keys(record.parts_replaced).forEach(key => {
                  const normalizedKey = key.toLowerCase().replace(/\s+/g, '_');
                  if (normalizedKey in parsedParts && record.parts_replaced[key] === true) {
                    parsedParts[normalizedKey as keyof typeof parsedParts] = true;
                  }
                });
              }
            }
          } catch (e) {
            console.error('Error parsing parts:', e);
          }
        }
        
        // Set form data from record
        setFormData({
          printer_id: record.asset_id || record.printer_id || 0,
          visit_date: formattedDate,
          assignee_name: record.assignee_name || '',
          department: record.department || '',
          vendor_name: record.vendor_name || '',
          service_type: record.service_type || record.action_taken || 'Regular Maintenance',
          technician_name: record.technician_name || '',
          issue_description: record.issue_description || '',
          parts_replaced: parsedParts,
          invoice_number: record.invoice_number || '',
          invoice_amount: record.invoice_amount || record.cost || '',
          remarks: record.remarks || ''
        });
        
        // Ensure data is loaded - if we haven't fetched data yet, do it now
        if (!dataLoaded) {
          // Use the existing fetchData function
          fetchData();
        }
        
        // Set selected vendor if we have one
        if (record.vendor_name && vendors.length > 0) {
          const vendor = vendors.find(v => 
            v.vendorName === record.vendor_name || 
            v.companyName === record.vendor_name ||
            v.name === record.vendor_name
          );
          
          if (vendor) {
            setSelectedVendor(vendor);
          }
        }
        
        toast.success('Record loaded for editing');
      } else {
        toast.error('Could not find maintenance record');
      }
    } catch (error) {
      console.error("Error loading record:", error);
      
      // Only show the toast error if not in silent mode and we're not already showing this error
      if (!silent) {
        const errorMessage = isContactsPath ? 
          "Could not load contact data" : 
          "Could not load maintenance record";
          
        // Show error toast only once per session for the same error type
        const errorKey = isContactsPath ? 'contact-load-error' : 'maintenance-load-error';
        if (!localStorage.getItem(errorKey)) {
          toast.error(errorMessage);
          // Set a marker in localStorage to prevent showing the same toast repeatedly
          localStorage.setItem(errorKey, 'shown');
          // Clear the marker after 10 seconds to allow future errors to show
          setTimeout(() => localStorage.removeItem(errorKey), 10000);
        }
      }
      
      // Still set the form with empty/default values
      setFormData({
        printer_id: 0,
        visit_date: new Date().toISOString().split('T')[0],
        assignee_name: '',
        department: '',
        vendor_name: '',
        service_type: 'Regular Maintenance',
        technician_name: '',
        issue_description: '',
        parts_replaced: {
          toner: false,
          drum: false,
          fuser_unit: false,
          pickup_roller: false
        },
        invoice_number: '',
        invoice_amount: '',
        remarks: ''
      });
      setLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, []);
  
  // Auto-fill printer details when printer is selected
  const handlePrinterChange = async (printerId: string) => {
    const numericId = Number(printerId);
    console.log('Selected printer ID:', numericId);
    
    if (numericId === 0) {
      // Reset form if no printer selected
      setFormData(prev => ({
        ...prev,
        printer_id: 0,
        assignee_name: '',
        department: '',
        vendor_name: ''
      }));
      return;
    }
    
    try {
      // Fetch complete asset data directly from API to get the latest assignee
      console.log(`Fetching complete asset data for printer ID: ${numericId}`);
      const assetResponse = await api.get(`/assets/${numericId}`);
      const assetData = assetResponse.data;
      
      console.log('Complete asset data received:', assetData);
      
      // Extract assignee from asset data (try multiple possible fields)
      let assigneeName = 'Not Assigned';
      
      // Handle the new assignedTo object structure
      if (assetData.assignedTo && typeof assetData.assignedTo === 'object' && assetData.assignedTo.name) {
        assigneeName = assetData.assignedTo.name;
        console.log('Found assignee in assignedTo object:', assigneeName);
      } 
      // Fallbacks for older or alternative data structures
      else if (assetData.assigned_to && typeof assetData.assigned_to === 'object') {
        assigneeName = assetData.assigned_to.name || assetData.assigned_to.full_name || assetData.assigned_to.display_name || 'Not Assigned';
        console.log('Found assignee in assigned_to object:', assigneeName);
      } else if (assetData.assigned_to && typeof assetData.assigned_to === 'string' && assetData.assigned_to.trim() !== '') {
        assigneeName = assetData.assigned_to;
        console.log('Found assignee in assigned_to string:', assigneeName);
      } else if (assetData.assignee && assetData.assignee.trim() !== '') {
        assigneeName = assetData.assignee;
        console.log('Found assignee in assignee field:', assigneeName);
      } else if (assetData.user && typeof assetData.user === 'object') {
        assigneeName = assetData.user.name || assetData.user.full_name || assetData.user.display_name || 'Not Assigned';
        console.log('Found assignee in user object:', assigneeName);
      } else if (assetData.owner && typeof assetData.owner === 'string' && assetData.owner.trim() !== '') {
        assigneeName = assetData.owner;
        console.log('Found assignee in owner field:', assigneeName);
      }
      
      // Extract department from asset data
      const printerDepartment = assetData.department || 'IT';
      
      // Find matching vendor
      let vendorName = '';
      const manufacturerName = assetData.manufacturer || '';
      const vendorId = assetData.vendor_id || '';
      
      // Try finding vendor by ID first
      if (vendorId) {
        const matchingVendor = vendors.find(v => v.id === vendorId);
        if (matchingVendor) {
          vendorName = matchingVendor.vendorName || matchingVendor.name || '';
          console.log('Found matching vendor by ID:', vendorName);
        }
      }
      
      // If no vendor found by ID, try matching by manufacturer
      if (!vendorName && manufacturerName) {
        const matchingVendor = vendors.find(v => 
          ((v.vendorName || '').toLowerCase().includes(manufacturerName.toLowerCase()) || 
          (v.companyName || '').toLowerCase().includes(manufacturerName.toLowerCase()))
        );
        
        if (matchingVendor) {
          vendorName = matchingVendor.vendorName || matchingVendor.name || '';
          console.log('Found matching vendor by manufacturer:', vendorName);
        }
      }
      
      console.log('Setting assignee name to:', assigneeName);
      console.log('Setting department to:', printerDepartment);
      console.log('Setting vendor name to:', vendorName);
      
      // Update the local printers array with the fresh data
      setPrinters(prev => prev.map(p => 
        p.id === numericId 
          ? {...p, assignee: assigneeName, department: printerDepartment} 
          : p
      ));
      
      // Update form data with printer information
      setFormData(prev => {
        // Create the updated data, ensuring assignee_name is never empty
        const updatedData = {
          ...prev,
          printer_id: numericId,
          assignee_name: assigneeName,
          department: printerDepartment,
          vendor_name: vendorName || prev.vendor_name // Keep existing vendor if no match found
        };
        
        // Debug after updating
        console.log('Updated form data to:', updatedData);
        
        return updatedData;
      });
      
      } catch (error) {
      console.error(`Error fetching asset ${numericId}:`, error);
      toast.error('Error loading printer details. Please try again.');
      
      // Fall back to the basic printer data from the list
      const selectedPrinter = printers.find(p => p.id === numericId);
      if (selectedPrinter) {
        console.log('Falling back to basic printer data:', selectedPrinter);
        setFormData(prev => ({
      ...prev,
          printer_id: numericId,
          assignee_name: selectedPrinter.assignee || 'Not Assigned',
          department: selectedPrinter.department || 'IT'
        }));
      }
    }
  };

  // Add an effect to log form data changes
  useEffect(() => {
    console.log('Form data changed:', formData);
    
    // Specifically check the assignee_name field
    if (formData.assignee_name) {
      console.log('ASSIGNEE NAME IS SET TO:', formData.assignee_name);
    } else {
      console.log('ASSIGNEE NAME IS EMPTY');
    }
    
    // Check if printer_id is set but assignee_name is not - this would indicate a problem
    if (formData.printer_id > 0 && !formData.assignee_name) {
      console.error('ERROR: Printer selected but assignee name is empty!');
      // Try to recover by forcing a lookup
      const selectedPrinter = printers.find(p => p.id === formData.printer_id);
      if (selectedPrinter) {
        console.log('RECOVERY: Found printer:', selectedPrinter);
        console.log('RECOVERY: Printer assignee value:', selectedPrinter.assignee);
      }
    }
  }, [formData, printers]);

  // Add a useEffect to properly update printer fields when the printer_id changes
  useEffect(() => {
    if (formData.printer_id > 0) {
      // Force update the assignee when printer_id changes
      const selectedPrinter = printers.find(p => p.id === formData.printer_id);
      if (selectedPrinter) {
        // Get assignee directly from printer data, ensuring it's never empty
        const printerAssignee = selectedPrinter.assignee || 'Not Assigned';
        
        console.log('Forced update of assignee:', printerAssignee);
        
        // This guarantees the fields are updated after the printer is selected
        setFormData(prev => ({
          ...prev,
          assignee_name: printerAssignee === '' ? 'Not Assigned' : printerAssignee,
          department: selectedPrinter.department || 'IT'
        }));
      }
    }
  }, [formData.printer_id, printers]);

  const handleInputChange = (field: keyof PrinterFormData, value: any) => {
    setFormData(prev => ({
          ...prev,
      [field]: value
    }));
  };

  const handlePartsChange = (part: keyof typeof formData.parts_replaced) => {
    setFormData(prev => ({
        ...prev,
      parts_replaced: {
        ...prev.parts_replaced,
        [part]: !prev.parts_replaced[part]
    }
    }));
  };

  // Update the file upload handling code
  const handleFileUpload = async (file: File) => {
    try {
      console.log('Starting file upload, file details:', {
        name: file.name,
        type: file.type,
        size: file.size
      });

      const formData = new FormData();
      formData.append('file', file);

      console.log('FormData created with file field');

      // Log request details for debugging
      const response = await fetch('/api/printer-maintenance/test-upload', {
        method: 'POST',
        body: formData
        // Don't set Content-Type header - the browser will set it correctly with boundary
      });

      console.log('Upload response status:', response.status);
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Upload error response:', errorData);
        throw new Error(errorData.message || 'File upload failed');
      }

      const result = await response.json();
      console.log('Upload result:', result);
      return result;
    } catch (error) {
      console.error('File upload error:', error);
      throw error;
    }
  };

  // Handle form submission with enhanced logging
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (submitting) return;
    
    try {
      setSubmitting(true);
      
      // Get the effective ID from all possible sources - this will be our source of truth
      const currentRecordId = originalRecordId || recordId || initialData?.id;
      
      console.log("🔍 FORM SUBMISSION DIAGNOSIS:");
      console.log("🆔 recordId (from props):", recordId);
      console.log("🆔 originalRecordId (from state):", originalRecordId);
      console.log("🆔 initialData.id:", initialData?.id);
      console.log("🆔 Effective ID:", currentRecordId);
      console.log("🆔 Form isEditMode prop:", isEditMode);
      console.log("🆔 Current form data:", formData);

      console.log("🚀 Submitting maintenance record...");
      console.log("Is editing existing record:", !!currentRecordId);
      console.log("Form data being submitted:", formData);

      // Get the auth token
      const token = localStorage.getItem('authToken');

      // Ensure technician name is always explicitly set and not empty
      const technicianName = formData.technician_name || '';
      console.log('Technician name being submitted:', technicianName);
      
      // Ensure issue description is properly set
      const issueDescription = formData.issue_description || '';
      console.log('Issue description being submitted:', issueDescription);
      
      // Ensure remarks are properly set
      const remarks = formData.remarks || '';
      console.log('Remarks being submitted:', remarks);
      
      // Prepare the form data object with the effective ID
      // First, determine if this is an update or create operation
      const idToUse = originalRecordId || recordId || initialData?.id;
      const isUpdateMode = !!idToUse;

      console.log('🔥 CRITICAL CHECK - Is this an update?', isUpdateMode ? 'YES' : 'NO');
      console.log('🔥 ID used for operation:', idToUse || 'NO ID (create mode)');

      // Prepare the payload
      const formPayload = {
        asset_id: formData.printer_id?.toString() || "",
        printer_id: formData.printer_id?.toString() || "",
        service_date: formData.visit_date || "",
        visit_date: formData.visit_date || "",
        technician_name: technicianName,
        issue_description: issueDescription,
        department: formData.department || "IT",
        service_type: formData.service_type || "",
        assignee_name: formData.assignee_name || "Not Assigned",
        invoice_number: formData.invoice_number || "",
        invoice_amount: formData.invoice_amount || "",
        cost: formData.invoice_amount || "",
        remarks: remarks,
        // Convert boolean object to array of objects with partName and quantity
        parts_replaced: JSON.stringify(
          Object.entries(formData.parts_replaced)
            .filter(([_, isReplaced]) => isReplaced)
            .map(([part]) => ({
              partName: part.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
              quantity: 1
            }))
        ),
        vendor_name: formData.vendor_name || "",
        vendor: formData.vendor_name || "", // Ensure both vendor and vendor_name are set to the same value
        status: 'completed'
      };

      // Always add the ID explicitly for updates
      if (isUpdateMode) {
        // Add all the possible id fields to ensure it gets recognized as an update
        (formPayload as any).id = idToUse;
        (formPayload as any).record_id = idToUse;
        (formPayload as any)._id = idToUse;
        (formPayload as any)._isUpdate = true;
        (formPayload as any)._preventNewRecord = true;
        console.log('🔴 Record ID added to payload:', idToUse);
      }

      // We've already decided if this is an update or not
      // We've already set up the formPayload with all required flags
      // Now we just need to do the right operation based on isUpdateMode
      let recordRes;
      
      if (isUpdateMode) {
        // We know we have an ID - it's in idToUse
        console.log(`🔴 FINAL CONFIRMATION: THIS IS AN UPDATE - Using ID: ${idToUse}`);
        
        // Put request to update the existing record
        console.log(`🔄 Updating existing record with ID: ${idToUse}`);
        
        // Log exactly what we're submitting for key fields
        console.log('Submitting update with these values:');
        console.log('- id:', idToUse);
        console.log('- issue_description:', formPayload.issue_description);
        console.log('- technician_name:', formPayload.technician_name);
        console.log('- service_type:', formPayload.service_type);
        
        try {
          // More explicit debugging
          console.log("SENDING UPDATE REQUEST with method PUT to:", `/api/printer-maintenance/${idToUse}`);
          console.log("Full payload being sent:", JSON.stringify(formPayload, null, 2));
          
          // Try the standard endpoint first with a more complete payload
          recordRes = await axios.put(
            `/api/printer-maintenance/${idToUse}`,
            formPayload,  // Send the entire formPayload
            {
              headers: {
                Authorization: token ? `Bearer ${token}` : '',
                'Content-Type': 'application/json'
              }
            }
          );
          console.log("Update successful with standard endpoint");
          console.log("RESPONSE DATA:", {
            status: recordRes.status,
            statusText: recordRes.statusText,
            data: recordRes.data,
            message: recordRes.data?.message || 'No message',
            id: recordRes.data?.record?.id || recordRes.data?.id || 'No ID returned',
            headers: recordRes.headers
          });
        } catch (firstError) {
          console.error("Standard update endpoint failed:", firstError);
          
          try {
            // Try using direct fetch to bypass any Axios configuration issues
            console.log("Trying direct fetch PUT as fallback...");
            const fetchResponse = await fetch(`/api/printer-maintenance/${idToUse}`, {
              method: 'PUT',
              headers: {
                'Authorization': token ? `Bearer ${token}` : '',
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(formPayload)
            });
            
            if (!fetchResponse.ok) {
              throw new Error(`Failed with status ${fetchResponse.status}`);
            }
            
            recordRes = { data: await fetchResponse.json() };
            console.log("Direct fetch PUT successful");
            console.log("FETCH RESPONSE:", recordRes.data);
          } catch (fetchError) {
            console.error("Direct fetch PUT failed:", fetchError);
            // As a last resort, try the record endpoint
            try {
              recordRes = await axios.put(
                `/api/printer-maintenance/record/${idToUse}`,
                formPayload,
                {
                  headers: {
                    Authorization: token ? `Bearer ${token}` : '',
                    'Content-Type': 'application/json'
                  }
                }
              );
              console.log("Update successful with record endpoint");
              console.log("RECORD ENDPOINT RESPONSE:", recordRes.data);
            } catch (secondError) {
              console.error("Record update endpoint failed:", secondError);
              
              // Don't fall back to POST - that creates duplicates
              // Show error instead
              toast.error("Failed to update record. Please try again.");
              throw new Error("Failed to update record. All update endpoints failed.");
            }
          }
        }
      } else {
        // Post request to create a new record
        console.log('🆕 Creating new maintenance record');
        recordRes = await axios.post(
          '/api/printer-maintenance',
          formPayload,
          {
            headers: {
              Authorization: token ? `Bearer ${token}` : '',
              'Content-Type': 'application/json'
            }
          }
        );
      }

      // Extract record ID, making sure we always get a value
      if (!recordId) {
      if (recordRes?.data?.record?.id) {
        recordId = recordRes.data.record.id.toString();
      } else if (recordRes?.data?.id) {
        recordId = recordRes.data.id.toString();
      } else if (recordRes?.data?.maintenance?.id) {
        recordId = recordRes.data.maintenance.id.toString();
      } else if (typeof recordRes?.data === 'object' && recordRes?.data !== null) {
        // Look for any id field at the top level
        for (const key in recordRes.data) {
          if (recordRes.data[key] && recordRes.data[key].id) {
            recordId = recordRes.data[key].id.toString();
            break;
            }
          }
        }
      }

      console.log("Step 1 complete. Raw response:", recordRes?.data);
      console.log("Final Record ID:", recordId);

      // Step 2: Upload invoice file if we have both a file and record ID
      if (formData.invoice_file && recordId) {
        try {
          console.log(`Attempting to upload file for record ${recordId}:`, {
            fileName: formData.invoice_file.name,
            fileSize: formData.invoice_file.size,
            fileType: formData.invoice_file.type
          });
          
          const uploadResult = await handleFileUpload(formData.invoice_file);
          console.log('Upload result:', uploadResult);
          
          if (uploadResult.success) {
            console.log("✅ File upload succeeded, now associating with record");
            
            // Ensure we have the file path and original name from the result
            const filePath = uploadResult.file.path;
            const fileName = uploadResult.file.originalname;
            
            if (!filePath || !fileName) {
              console.error("Missing file information in upload result:", uploadResult);
              toast.error("File uploaded but information is incomplete");
            } else {
              // Log the exact data we're sending to associate the file
              console.log(`Sending association request for record ${recordId}:`, {
                filePath,
                fileName
              });
              
              try {
                // Add this debugging code right before the file association fetch call
                console.log("Actual record ID used in request:", recordId);
                
                const associateResponse = await fetch(`/api/printer-maintenance/${recordId}/associate-file`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'Authorization': token ? `Bearer ${token}` : ''
                  },
                  body: JSON.stringify({
                    filePath,
                    fileName
                  })
                });
                
                console.log('Associate response status:', associateResponse.status);
                
                // If first attempt fails, try with a cleaned ID
                if (!associateResponse.ok) {
                  console.log("First association attempt failed. ID format inspection:");
                  console.log("- ID type:", typeof recordId);
                  console.log("- ID length:", recordId?.length);
                  console.log("- ID characters:", recordId?.split('').map(c => c.charCodeAt(0)));
                  
                  // Check if there are any hidden characters or encoding issues
                  const cleanedId = recordId?.replace(/[\u200B-\u200D\uFEFF]/g, '');
                  if (cleanedId !== recordId) {
                    console.log("Found hidden characters in ID. Trying with cleaned ID:", cleanedId);
                    
                    const retryResponse = await fetch(`/api/printer-maintenance/${cleanedId}/associate-file`, {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token ? `Bearer ${token}` : ''
                      },
                      body: JSON.stringify({
                        filePath,
                        fileName
                      })
                    });
                    
                    console.log('Retry response status:', retryResponse.status);
                    
                    // Get the response body for detailed error reporting
                    const retryData = await retryResponse.json();
                    console.log('Retry response data:', retryData);
                    
                    if (retryResponse.ok) {
                      console.log("✅ File associated with record successfully on retry");
                      // Don't show another toast here - we'll show a single message at the end
                    } else {
                      console.error("Failed to associate file with record on retry:", retryData);
                      toast.error(`File uploaded but couldn't associate with record: ${retryData.message || 'Unknown error'}`);
                    }
                  } else {
                    // Get the response data from the first attempt
                    try {
                      const errorData = await associateResponse.json();
                      console.error("Failed to associate file with record:", errorData);
                      toast.error(`File uploaded but couldn't associate with record: ${errorData.message || 'Unknown error'}`);
                    } catch (parseError) {
                      console.error("Error parsing response:", parseError);
                      toast.error(`File uploaded but couldn't associate with record: Status ${associateResponse.status}`);
                    }
                  }
                } else {
                  // First attempt succeeded
                  try {
                    const associateData = await associateResponse.json();
                    console.log('Associate response data:', associateData);
                    console.log("✅ File associated with record successfully");
                    // Don't show another toast here - we'll show a single message at the end
                  } catch (parseError) {
                    console.error("Error parsing success response:", parseError);
                    // Don't show another toast here - we'll show a single message at the end
                  }
                }
              } catch (associateError: any) {
                console.error("❌ Error during file association:", associateError);
                toast.error(`File uploaded but association failed: ${associateError.message}`);
              }
            }
          } else {
            throw new Error(uploadResult.message || "Upload failed");
          }
        } catch (error: any) {
          console.error("❌ File upload error:", error);
          toast.error(`Record saved but file upload failed: ${error.message}`);
        }
      } else {
        console.log("⚠️ No file selected or recordId missing. Skipping upload.");
        if (!recordId) {
          console.error("Record ID was not extracted from response!");
        }
      }

      // After successful submission, clear localStorage
      if (localStorage.getItem('editMaintenanceRecord')) {
        console.log("Clearing maintenance record data from localStorage");
        localStorage.removeItem('editMaintenanceRecord');
      }

      // Update the success message section
      // ALWAYS use the originalRecordId to determine the operation type
      // This ignores the server message which might be incorrect
      const successMessage = isUpdateMode 
        ? 'Record updated successfully'
        : 'New record created successfully';

      console.log(`Operation completed: ${isUpdateMode ? 'UPDATE' : 'CREATE'}`);
      console.log(`Using message: ${successMessage}`);
      console.log(`Server actually returned: ${recordRes?.data?.message || 'No message'}`);
      console.log(`Server operation type: ${recordRes?.data?.operation_type || 'Not specified'}`);
      console.log(`Result type: ${recordRes?.data?.result_type || 'Not specified'}`);

      // Clear any existing toasts before showing the success message
      toast.dismiss();

      // Show toast with longer duration and prominent styling
      toast.success(successMessage, {
        duration: 5000, // Show for 5 seconds
        style: {
          background: isUpdateMode ? '#4caf50' : '#2196f3',
          color: 'white',
          fontWeight: 'bold',
          padding: '16px',
          minWidth: '250px',
          textAlign: 'center'
        },
      });

      // Reset the form or redirect after submission
      if (onComplete) {
        console.log('Calling onComplete callback');
        onComplete();
      } else if (isUpdateMode) {
        // For edit operations, preserve the edit state
        console.log('Preserving edit state after update for record:', idToUse);
        // Reset the form but preserve the edit state
        resetForm(true);
      } else {
        // For new records, completely reset the form
        console.log('Completely resetting form after creating new record');
        resetForm(false);
      }
    } catch (error: any) {
      console.error("❌ Submission failed:", error);
      
      // Error handling
      let errorMessage = 'Failed to submit record';
      
      if (error.response && error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      console.error('Showing error message:', errorMessage);
      toast.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  // Add a function to handle form cancellation 
  const handleCancel = () => {
    // Call onComplete if provided, otherwise just reset
    if (onComplete) {
      onComplete();
    } else {
      // Reset form
      setFormData({
        printer_id: 0,
        visit_date: new Date().toISOString().split('T')[0],
        assignee_name: '',
        department: '',
        vendor_name: '',
        service_type: 'Regular Maintenance',
        technician_name: '',
        issue_description: '',
        parts_replaced: {
          toner: false,
          drum: false,
          fuser_unit: false,
          pickup_roller: false
        },
        invoice_number: '',
        invoice_amount: '',
        invoice_file: undefined,
        remarks: ''
      });
      window.scrollTo(0, 0);
    }
  };
  
  // Reset form to initial state
  const resetForm = (preserveEditState = false) => {
    setFormData({
      printer_id: 0,
      visit_date: new Date().toISOString().split('T')[0],
      assignee_name: '',
      department: '',
      vendor_name: '',
      service_type: 'Regular Maintenance',
      technician_name: '',
      issue_description: '',
      parts_replaced: {
        toner: false,
        drum: false,
        fuser_unit: false,
        pickup_roller: false
      },
      invoice_number: '',
      invoice_amount: '',
      remarks: ''
    });
    setSelectedVendor(null);
    
    // Only clear the originalRecordId if we're not preserving edit state
    if (!preserveEditState) {
      console.log('Clearing originalRecordId - form will be in CREATE mode next time');
      setOriginalRecordId(null);
    } else {
      console.log('Preserving originalRecordId for continued editing:', originalRecordId);
    }
  };

  if (fetchingData) {
    return (
      <div className="max-w-4xl mx-auto p-6 text-center">
        <div className="animate-pulse">Loading form data...</div>
          </div>
    );
  }

  return (
    <div className={inPage ? '' : 'max-w-4xl mx-auto p-6'}>
      <div className={`bg-white ${!inPage && 'rounded-lg shadow-sm'}`}>
        {!inPage && (
          <div className="bg-[#4a76cf] text-white p-4 rounded-t-lg flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h1 className="text-xl font-semibold">📝 Log Vendor Visit</h1>
              <span className="bg-[#8ba7e5] text-white px-2 py-0.5 rounded text-sm">Maintenance</span>
            </div>
            {/* X close button */}
            <button 
              type="button" 
              onClick={handleCancel}
              className="text-white hover:bg-[#3a66bf] rounded-full w-7 h-7 flex items-center justify-center"
              aria-label="Close"
            >
              ✕
            </button>
          </div>
        )}
            
        {/* Form */}
        <form className="p-6" onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Printer ID and Visit Date */}
              <div>
              <label className="block text-sm font-medium mb-1">
                Printer ID <span className="text-red-500">*</span>
              </label>
              <select
                value={String(formData.printer_id) || ''}
                onChange={(e) => handlePrinterChange(e.target.value)}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
                required
                  >
                    <option value="">Select Printer</option>
                {printers.map(printer => (
                  <option key={printer.id} value={String(printer.id)}>
                    {printer.manufacturer} - {printer.model} {printer.department ? `(${printer.department})` : ''}
                      </option>
                    ))}
              </select>
              {formData.printer_id === 0 && isEditMode && initialData && (
                <div className="text-xs text-orange-500 mt-1">
                  Debug: Printer ID is not set correctly. 
                  initialData.printer_id={initialData.printer_id ?? 'null'}, 
                  asset_id={initialData.asset_id ?? 'null'}
                </div>
              )}
              </div>
              
            <div>
              <label className="block text-sm font-medium mb-1">
                Visit Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                value={formData.visit_date}
                onChange={(e) => handleInputChange('visit_date', e.target.value)}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
                required
              />
              {(!formData.visit_date || formData.visit_date === "Invalid Date") && isEditMode && initialData && (
                <div className="text-xs text-orange-500 mt-1">
                  Debug: Visit date is not set correctly. 
                  initialData.visit_date={initialData.visit_date ?? 'null'}, 
                  service_date={initialData.service_date ?? 'null'},
                  serviceDate={initialData.serviceDate ?? 'null'},
                  form value={formData.visit_date || 'empty'}
                </div>
              )}
            </div>

            {/* Assignee Name and Department */}
              <div>
              <label className="block text-sm font-medium mb-1">
                Assignee Name
              </label>
              <input
                type="text"
                value={formData.assignee_name || 'Not Assigned'}
                onChange={(e) => {}}
                className="w-full p-2 border rounded bg-gray-50 text-gray-700 cursor-not-allowed"
                placeholder="Auto-filled from printer selection"
                readOnly
                disabled
              />
              </div>
              
              <div>
              <label className="block text-sm font-medium mb-1">
                Department
              </label>
              <input
                type="text"
                    value={formData.department}
                onChange={(e) => {}}
                className="w-full p-2 border rounded bg-gray-50 text-gray-700 cursor-not-allowed"
                placeholder="Auto-filled from printer selection"
                readOnly
                disabled
              />
              </div>
              
            {/* Vendor Name and Service Type */}
              <div>
              <label className="block text-sm font-medium mb-1">
                Vendor Name <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.vendor_name}
                onChange={(e) => handleInputChange('vendor_name', e.target.value)}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Select Vendor</option>
                {vendors.length === 0 && (
                  <option value="No Vendors">No vendors available</option>
                )}
                {vendors.map(vendor => (
                  <option key={vendor.id} value={vendor.vendorName || vendor.name}>
                    {vendor.vendorName || vendor.name || 'Unnamed Vendor'} {vendor.companyName ? `(${vendor.companyName})` : ''}
                      </option>
                    ))}
              </select>
              {vendors.length === 0 && (
                <div className="text-xs text-red-500 mt-1">
                  No vendors found. Please check API connection.
                </div>
              )}
              </div>
              
              <div>
              <label className="block text-sm font-medium mb-1">
                Service Type <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.service_type}
                onChange={(e) => handleInputChange('service_type', e.target.value)}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Select Service Type</option>
                {SERVICE_TYPES.map(type => (
                      <option key={type} value={type}>
                        {type}
                      </option>
                    ))}
              </select>
              {!formData.service_type && initialData && (
                <div className="text-xs text-orange-500 mt-1">
                  Debug: initialData.service_type={initialData.service_type || 'null'}, 
                  action_taken={initialData.action_taken || 'null'}, 
                  maintenance_type={initialData.maintenance_type || 'null'}
                </div>
              )}
              </div>
              
              {/* Technician Name */}
              <div>
              <label className="block text-sm font-medium mb-1">
                Technician Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.technician_name}
                onChange={(e) => handleInputChange('technician_name', e.target.value)}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
                placeholder="Enter technician name"
                required
              />
              {!formData.technician_name && initialData && (
                <div className="text-xs text-orange-500 mt-1">
                  Debug: initialData.technician_name={initialData.technician_name || 'null'}, 
                  technician={initialData.technician || 'null'}
                  <br />
                  <strong>Note:</strong> The technician name may appear empty or as "Completed" due to a 
                  backend data storage issue that has been fixed. Please enter the technician name manually.
                </div>
              )}
              </div>
              
              {/* Issue Description */}
              <div className="col-span-2">
                <label className="block text-sm font-medium mb-1">
                  Issue Description <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={formData.issue_description}
                  onChange={(e) => handleInputChange('issue_description', e.target.value)}
                  placeholder="Describe the issue that required maintenance..."
                  className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 h-20"
                  required
                />
                {!formData.issue_description && initialData && (
                  <div className="text-xs text-orange-500 mt-1">
                    Debug: initialData.issue_description={initialData.issue_description || 'null'}, 
                    description={initialData.description || 'null'}, 
                    issues={initialData.issues || 'null'}
                  </div>
                )}
              </div>
              
            {/* Parts Replaced */}
            <div className="col-span-2 bg-gray-50 rounded-lg p-3 border border-gray-200">
              <label className="block text-sm font-medium mb-2">
                Parts Replaced
              </label>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                <label className="flex items-center hover:bg-gray-100 p-1.5 rounded">
                    <input 
                      type="checkbox" 
                      checked={formData.parts_replaced.toner}
                      onChange={() => handlePartsChange('toner')}
                      className="w-4 h-4 text-blue-600 rounded"
                    />
                    <span className="ml-2 text-sm">Toner</span>
                </label>
                <label className="flex items-center hover:bg-gray-100 p-1.5 rounded">
                    <input 
                      type="checkbox" 
                      checked={formData.parts_replaced.drum}
                      onChange={() => handlePartsChange('drum')}
                      className="w-4 h-4 text-blue-600 rounded"
                    />
                    <span className="ml-2 text-sm">Drum</span>
                </label>
                <label className="flex items-center hover:bg-gray-100 p-1.5 rounded">
                    <input
                      type="checkbox"
                      checked={formData.parts_replaced.fuser_unit}
                      onChange={() => handlePartsChange('fuser_unit')}
                      className="w-4 h-4 text-blue-600 rounded"
                    />
                    <span className="ml-2 text-sm">Fuser Unit</span>
                </label>
                <label className="flex items-center hover:bg-gray-100 p-1.5 rounded">
                    <input
                      type="checkbox"
                      checked={formData.parts_replaced.pickup_roller}
                      onChange={() => handlePartsChange('pickup_roller')}
                      className="w-4 h-4 text-blue-600 rounded"
                    />
                    <span className="ml-2 text-sm">Pickup Roller</span>
                </label>
              </div>
            </div>
              
            {/* Invoice Number and Amount */}
              <div>
              <label className="block text-sm font-medium mb-1">
                Invoice Number <span className="text-gray-500">(Optional)</span>
              </label>
              <input
                type="text"
                value={formData.invoice_number}
                onChange={(e) => handleInputChange('invoice_number', e.target.value)}
                placeholder="123456789"
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
              />
              </div>
              
              <div>
              <label className="block text-sm font-medium mb-1">
                Invoice Amount <span className="text-gray-500">(Optional)</span>
              </label>
              <input
                type="text"
                value={formData.invoice_amount}
                onChange={(e) => handleInputChange('invoice_amount', e.target.value)}
                placeholder="Rs. 4,200"
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
              />
                {!formData.invoice_amount && isEditMode && initialData && (
                  <div className="text-xs text-orange-500 mt-1">
                    Debug: Invoice amount not set. 
                    initialData.invoice_amount={initialData.invoice_amount ?? 'null'}, 
                    invoiceAmount={initialData.invoiceAmount ?? 'null'},
                    cost={initialData.cost ?? 'null'}
                  </div>
                )}
              </div>
              
            {/* Invoice Upload */}
              <div className="col-span-2">
              <label className="block text-sm font-medium mb-1">
                Signed Invoice <span className="text-gray-500">(Optional)</span>
              </label>
              <div className={`border-2 border-dashed rounded-lg p-4 text-center ${formData.invoice_file ? 'bg-blue-50 border-blue-300' : ''}`}>
                <input
                  type="file"
                  onChange={(e) => {
                    if (e.target.files && e.target.files[0]) {
                      const selectedFile = e.target.files[0];
                      handleInputChange('invoice_file', selectedFile);
                    }
                  }}
                  accept=".pdf,.jpg,.jpeg,.png"
                  className="hidden"
                  id="invoice-upload"
                />
                <label
                  htmlFor="invoice-upload"
                  className="cursor-pointer text-sm text-gray-600"
                >
                  {formData.invoice_file ? (
                      <div className="text-center">
                        <p className="font-medium text-blue-600">{formData.invoice_file.name}</p>
                        <p className="text-xs mt-1">Click to change file</p>
                    </div>
                  ) : (
                      <div>
                        <svg className="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <p className="mt-1">Click to upload invoice</p>
                        <p className="text-xs text-gray-500">PDF, JPG or PNG (Max 5MB)</p>
                      </div>
                  )}
                </label>
              </div>
                
                {/* Display existing files if any */}
                {renderExistingFiles()}
              </div>
              
            {/* Remarks */}
            <div className="col-span-2">
              <label className="block text-sm font-medium mb-1">
                  Remarks <span className="text-gray-500">(Optional)</span>
              </label>
              <textarea
                value={formData.remarks}
                onChange={(e) => handleInputChange('remarks', e.target.value)}
                placeholder="Enter any additional notes here..."
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 h-24"
              />
                {!formData.remarks && initialData && (
                  <div className="text-xs text-orange-500 mt-1">
                    Debug: initialData.remarks={initialData.remarks || 'null'}, 
                    Extracted remarks from notes={initialData.hasOwnProperty('notes') ? (
                      String(initialData.notes)
                      .replace(/Technician: [^\n]+(\n|$)/g, '')
                      .replace(/Issue: [^\n]+(\n|$)/g, '')
                      .replace(/Department: [^\n]+(\n|$)/g, '')
                      .replace(/Assignee: [^\n]+(\n|$)/g, '')
                      .replace(/Original filename: [^\n]+(\n|$)/g, '')
                      .replace(/Approved by: [^\n]+(\n|$)/g, '')
                      .trim() || 'empty after extraction'
                    ) : 'no notes field'}
                  </div>
                )}
              </div>
            </div>
            
          {/* Form Actions */}
          <div className="flex justify-end gap-3 mt-6">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 text-gray-600 border rounded hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#4a76cf] text-white rounded hover:bg-[#3a66bf]"
              disabled={loading}
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PrinterMaintenanceForm; 