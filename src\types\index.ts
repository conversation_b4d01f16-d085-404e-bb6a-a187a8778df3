export interface PrinterMaintenanceRecord {
  id: string | number;
  printer_id: number;
  printer_name?: string;
  asset_id?: number;
  asset_name?: string;
  manufacturer?: string;
  model?: string;
  visit_date: string;
  service_date?: string;
  assignee_name: string;
  department: string;
  department_name?: string;
  vendor_id: string;
  vendor_name: string;
  technician_name: string;
  service_type: string;
  status?: string;
  serviceStatus?: string;
  action_taken?: string;
  issue_description: string;
  parts_replaced: string[] | Array<{partName: string, quantity: number}>;
  invoice_number: string;
  invoice_amount: string;
  cost?: number | string;
  invoice_file?: string;
  invoiceFilePath?: string;
  remarks?: string;
  created_at: string;
  updated_at?: string;
  approvalStatus?: string;
  approvedBy?: any;
  approvalDate?: string;
  submittedToFinance?: boolean;
  submittedToFinanceDate?: string;
  asset?: any;
  vendor?: any;
  assignee?: any;
}

export interface MaintenanceTableFilters {
  printer_id?: number;
  vendor_name?: string;
  department?: string;
  start_date?: string;
  end_date?: string;
  search?: string;
} 