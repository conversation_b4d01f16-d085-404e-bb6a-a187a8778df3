import { Request, Response } from 'express';
import { AppDataSource } from '../config/database';
import { Task, TaskStatus, TaskPriority, TaskType, TaskRecurrenceType, TaskApprovalStatus } from '../entities/Task';
import { TaskComment } from '../entities/TaskComment';
import { TaskAttachment } from '../entities/TaskAttachment';
import { TaskTimeEntry } from '../entities/TaskTimeEntry';
import { TaskTemplate } from '../entities/TaskTemplate';
import { TaskAutomationRule } from '../entities/TaskAutomationRule';
import { Project } from '../entities/Project';
import { ProjectMember, ProjectRole } from '../entities/ProjectMember';
import { User } from '../entities/User';
import { validate } from 'class-validator';
import { In, Like, Between } from 'typeorm';

export class TaskController {
  private taskRepository = AppDataSource.getRepository(Task);
  private taskCommentRepository = AppDataSource.getRepository(TaskComment);
  private taskAttachmentRepository = AppDataSource.getRepository(TaskAttachment);
  private taskTimeEntryRepository = AppDataSource.getRepository(TaskTimeEntry);
  private taskTemplateRepository = AppDataSource.getRepository(TaskTemplate);
  private taskAutomationRuleRepository = AppDataSource.getRepository(TaskAutomationRule);
  private projectRepository = AppDataSource.getRepository(Project);
  private projectMemberRepository = AppDataSource.getRepository(ProjectMember);
  private userRepository = AppDataSource.getRepository(User);

  // Get all tasks with filtering and pagination
  async getAllTasks(req: Request, res: Response) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        priority,
        type,
        assignedToId,
        projectId,
        search,
        startDate,
        endDate
      } = req.query;

      const queryBuilder = this.taskRepository
        .createQueryBuilder('task')
        .leftJoinAndSelect('task.project', 'project')
        .leftJoinAndSelect('task.createdBy', 'createdBy')
        .leftJoinAndSelect('task.assignedTo', 'assignedTo')
        .leftJoinAndSelect('task.parentTask', 'parentTask')
        .leftJoinAndSelect('task.subtasks', 'subtasks')
        .where('task.isActive = :isActive', { isActive: true });

      // Apply filters
      if (status) {
        const statusArray = Array.isArray(status) ? status : [status];
        queryBuilder.andWhere('task.status IN (:...status)', { status: statusArray });
      }

      if (priority) {
        const priorityArray = Array.isArray(priority) ? priority : [priority];
        queryBuilder.andWhere('task.priority IN (:...priority)', { priority: priorityArray });
      }

      if (type) {
        const typeArray = Array.isArray(type) ? type : [type];
        queryBuilder.andWhere('task.type IN (:...type)', { type: typeArray });
      }

      if (assignedToId) {
        queryBuilder.andWhere('task.assignedToId = :assignedToId', { assignedToId });
      }

      if (projectId) {
        const projectIdArray = Array.isArray(projectId) ? projectId : [projectId];
        queryBuilder.andWhere('task.projectId IN (:...projectId)', { projectId: projectIdArray });
      }

      if (search) {
        queryBuilder.andWhere(
          '(task.title LIKE :search OR task.description LIKE :search)',
          { search: `%${search}%` }
        );
      }

      if (startDate && endDate) {
        queryBuilder.andWhere('task.startDate BETWEEN :startDate AND :endDate', {
          startDate,
          endDate
        });
      }

      // Pagination
      const skip = (Number(page) - 1) * Number(limit);
      queryBuilder.skip(skip).take(Number(limit));

      // Order by creation date
      queryBuilder.orderBy('task.createdAt', 'DESC');

      const [tasks, total] = await queryBuilder.getManyAndCount();

      res.json({
        tasks,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error('Error fetching tasks:', error);
      res.status(500).json({ error: 'Failed to fetch tasks' });
    }
  }

  // Get task by ID
  async getTaskById(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const task = await this.taskRepository
        .createQueryBuilder('task')
        .leftJoinAndSelect('task.project', 'project')
        .leftJoinAndSelect('task.createdBy', 'createdBy')
        .leftJoinAndSelect('task.assignedTo', 'assignedTo')
        .leftJoinAndSelect('task.parentTask', 'parentTask')
        .leftJoinAndSelect('task.subtasks', 'subtasks')
        .leftJoinAndSelect('task.comments', 'comments')
        .leftJoinAndSelect('comments.author', 'commentAuthor')
        .leftJoinAndSelect('task.attachments', 'attachments')
        .leftJoinAndSelect('attachments.uploadedBy', 'attachmentUploader')
        .where('task.id = :id', { id })
        .andWhere('task.isActive = :isActive', { isActive: true })
        .getOne();

      if (!task) {
        return res.status(404).json({ error: 'Task not found' });
      }

      res.json(task);
    } catch (error) {
      console.error('Error fetching task:', error);
      res.status(500).json({ error: 'Failed to fetch task' });
    }
  }

  // Create new task
  async createTask(req: Request, res: Response) {
    try {
      const userId = (req as any).user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const taskData = req.body;
      
      // Verify project exists and user has access
      const project = await this.projectRepository.findOne({
        where: { id: taskData.projectId, isActive: true },
        relations: ['members']
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      // Check if user is a member of the project
      const isMember = project.createdById === userId ||
                      project.managerId === userId ||
                      project.members.some(member => member.userId === userId && member.isActive);

      if (!isMember) {
        return res.status(403).json({ error: 'You are not a member of this project' });
      }

      // Create task instance
      const task = this.taskRepository.create({
        ...taskData,
        createdById: userId
      });

      // Validate task data
      const errors = await validate(task);
      if (errors.length > 0) {
        return res.status(400).json({ 
          error: 'Validation failed', 
          details: errors.map(err => Object.values(err.constraints || {})).flat()
        });
      }

      // Save task
      const savedTask = await this.taskRepository.save(task);

      // Fetch the complete task with relations
      const completeTask = await this.taskRepository
        .createQueryBuilder('task')
        .leftJoinAndSelect('task.project', 'project')
        .leftJoinAndSelect('task.createdBy', 'createdBy')
        .leftJoinAndSelect('task.assignedTo', 'assignedTo')
        .leftJoinAndSelect('task.parentTask', 'parentTask')
        .where('task.id = :id', { id: savedTask.id })
        .getOne();

      res.status(201).json(completeTask);
    } catch (error) {
      console.error('Error creating task:', error);
      res.status(500).json({ error: 'Failed to create task' });
    }
  }

  // Update task
  async updateTask(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;
      const updateData = req.body;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const task = await this.taskRepository.findOne({
        where: { id: Number(id), isActive: true },
        relations: ['project', 'project.members']
      });

      if (!task) {
        return res.status(404).json({ error: 'Task not found' });
      }

      // Check if user has permission to update task
      const canUpdate = task.createdById === userId || 
                       task.assignedToId === userId ||
                       task.project.createdById === userId ||
                       task.project.managerId === userId ||
                       task.project.members.some(member => 
                         member.userId === userId && 
                         [ProjectRole.OWNER, ProjectRole.MANAGER, ProjectRole.LEAD].includes(member.role)
                       );

      if (!canUpdate) {
        return res.status(403).json({ error: 'Insufficient permissions to update task' });
      }

      // If status is being changed to done, set completion date
      if (updateData.status === TaskStatus.DONE && task.status !== TaskStatus.DONE) {
        updateData.completedDate = new Date().toISOString().split('T')[0];
        updateData.progress = 100;
      }

      // Update task
      await this.taskRepository.update(id, updateData);

      // Fetch updated task
      const updatedTask = await this.taskRepository
        .createQueryBuilder('task')
        .leftJoinAndSelect('task.project', 'project')
        .leftJoinAndSelect('task.createdBy', 'createdBy')
        .leftJoinAndSelect('task.assignedTo', 'assignedTo')
        .leftJoinAndSelect('task.parentTask', 'parentTask')
        .leftJoinAndSelect('task.subtasks', 'subtasks')
        .where('task.id = :id', { id })
        .getOne();

      res.json(updatedTask);
    } catch (error) {
      console.error('Error updating task:', error);
      res.status(500).json({ error: 'Failed to update task' });
    }
  }

  // Delete task (soft delete)
  async deleteTask(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const task = await this.taskRepository.findOne({
        where: { id: Number(id), isActive: true },
        relations: ['project', 'project.members']
      });

      if (!task) {
        return res.status(404).json({ error: 'Task not found' });
      }

      // Check if user has permission to delete task
      const canDelete = task.createdById === userId ||
                       task.project.createdById === userId ||
                       task.project.managerId === userId ||
                       task.project.members.some(member =>
                         member.userId === userId &&
                         [ProjectRole.OWNER, ProjectRole.MANAGER].includes(member.role)
                       );

      if (!canDelete) {
        return res.status(403).json({ error: 'Insufficient permissions to delete task' });
      }

      // Soft delete task
      await this.taskRepository.update(id, { isActive: false });

      res.json({ message: 'Task deleted successfully' });
    } catch (error) {
      console.error('Error deleting task:', error);
      res.status(500).json({ error: 'Failed to delete task' });
    }
  }

  // Get tasks by project ID
  async getTasksByProject(req: Request, res: Response) {
    try {
      const { projectId } = req.params;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Verify user has access to project
      const project = await this.projectRepository.findOne({
        where: { id: Number(projectId), isActive: true },
        relations: ['members']
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      const isMember = project.createdById === userId ||
                      project.managerId === userId ||
                      project.members.some(member => member.userId === userId && member.isActive);

      if (!isMember) {
        return res.status(403).json({ error: 'You are not a member of this project' });
      }

      const tasks = await this.taskRepository
        .createQueryBuilder('task')
        .leftJoinAndSelect('task.createdBy', 'createdBy')
        .leftJoinAndSelect('task.assignedTo', 'assignedTo')
        .leftJoinAndSelect('task.parentTask', 'parentTask')
        .leftJoinAndSelect('task.subtasks', 'subtasks')
        .where('task.projectId = :projectId', { projectId })
        .andWhere('task.isActive = :isActive', { isActive: true })
        .orderBy('task.createdAt', 'DESC')
        .getMany();

      res.json(tasks);
    } catch (error) {
      console.error('Error fetching project tasks:', error);
      res.status(500).json({ error: 'Failed to fetch project tasks' });
    }
  }

  // Get user's assigned tasks
  async getMyTasks(req: Request, res: Response) {
    try {
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const tasks = await this.taskRepository
        .createQueryBuilder('task')
        .leftJoinAndSelect('task.project', 'project')
        .leftJoinAndSelect('task.createdBy', 'createdBy')
        .leftJoinAndSelect('task.assignedTo', 'assignedTo')
        .leftJoinAndSelect('task.parentTask', 'parentTask')
        .where('task.assignedToId = :userId', { userId })
        .andWhere('task.isActive = :isActive', { isActive: true })
        .orderBy('task.dueDate', 'ASC')
        .addOrderBy('task.priority', 'DESC')
        .getMany();

      res.json(tasks);
    } catch (error) {
      console.error('Error fetching user tasks:', error);
      res.status(500).json({ error: 'Failed to fetch user tasks' });
    }
  }

  // Get task statistics
  async getTaskStats(req: Request, res: Response) {
    try {
      const { projectId } = req.query;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      let whereCondition: any = { isActive: true };

      if (projectId) {
        whereCondition.projectId = Number(projectId);
      }

      const todoTasks = await this.taskRepository.count({
        where: { ...whereCondition, status: TaskStatus.TODO }
      });

      const inProgressTasks = await this.taskRepository.count({
        where: { ...whereCondition, status: TaskStatus.IN_PROGRESS }
      });

      const inReviewTasks = await this.taskRepository.count({
        where: { ...whereCondition, status: TaskStatus.IN_REVIEW }
      });

      const doneTasks = await this.taskRepository.count({
        where: { ...whereCondition, status: TaskStatus.DONE }
      });

      const cancelledTasks = await this.taskRepository.count({
        where: { ...whereCondition, status: TaskStatus.CANCELLED }
      });

      // Get overdue tasks
      const today = new Date().toISOString().split('T')[0];
      const overdueTasks = await this.taskRepository
        .createQueryBuilder('task')
        .where('task.isActive = :isActive', { isActive: true })
        .andWhere('task.dueDate < :today', { today })
        .andWhere('task.status != :status', { status: TaskStatus.DONE })
        .andWhere(projectId ? 'task.projectId = :projectId' : '1=1', { projectId: Number(projectId) })
        .getCount();

      const stats = {
        todo: todoTasks,
        inProgress: inProgressTasks,
        inReview: inReviewTasks,
        done: doneTasks,
        cancelled: cancelledTasks,
        overdue: overdueTasks
      };

      res.json(stats);
    } catch (error) {
      console.error('Error fetching task stats:', error);
      res.status(500).json({ error: 'Failed to fetch task statistics' });
    }
  }

  // ===== ENHANCED TASK MANAGEMENT METHODS =====

  // Add task dependencies
  async addTaskDependency(req: Request, res: Response) {
    try {
      const { taskId, dependsOnTaskId } = req.body;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Get both tasks
      const task = await this.taskRepository.findOne({
        where: { id: taskId, isActive: true },
        relations: ['project', 'dependencies']
      });

      const dependsOnTask = await this.taskRepository.findOne({
        where: { id: dependsOnTaskId, isActive: true },
        relations: ['project']
      });

      if (!task || !dependsOnTask) {
        return res.status(404).json({ error: 'Task not found' });
      }

      // Check if user has permission
      const hasPermission = await this.checkTaskPermission(task, userId);
      if (!hasPermission) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      // Check for circular dependencies
      if (await this.wouldCreateCircularDependency(taskId, dependsOnTaskId)) {
        return res.status(400).json({ error: 'This would create a circular dependency' });
      }

      // Add dependency
      task.dependencies.push(dependsOnTask);
      await this.taskRepository.save(task);

      res.json({ message: 'Dependency added successfully' });
    } catch (error) {
      console.error('Error adding task dependency:', error);
      res.status(500).json({ error: 'Failed to add task dependency' });
    }
  }

  // Remove task dependency
  async removeTaskDependency(req: Request, res: Response) {
    try {
      const { taskId, dependsOnTaskId } = req.body;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const task = await this.taskRepository.findOne({
        where: { id: taskId, isActive: true },
        relations: ['project', 'dependencies']
      });

      if (!task) {
        return res.status(404).json({ error: 'Task not found' });
      }

      const hasPermission = await this.checkTaskPermission(task, userId);
      if (!hasPermission) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      // Remove dependency
      task.dependencies = task.dependencies.filter(dep => dep.id !== dependsOnTaskId);
      await this.taskRepository.save(task);

      res.json({ message: 'Dependency removed successfully' });
    } catch (error) {
      console.error('Error removing task dependency:', error);
      res.status(500).json({ error: 'Failed to remove task dependency' });
    }
  }

  // Create subtask
  async createSubtask(req: Request, res: Response) {
    try {
      const { parentTaskId } = req.params;
      const userId = (req as any).user?.id;
      const subtaskData = req.body;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const parentTask = await this.taskRepository.findOne({
        where: { id: Number(parentTaskId), isActive: true },
        relations: ['project']
      });

      if (!parentTask) {
        return res.status(404).json({ error: 'Parent task not found' });
      }

      const hasPermission = await this.checkTaskPermission(parentTask, userId);
      if (!hasPermission) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      // Create subtask
      const subtask = this.taskRepository.create({
        ...subtaskData,
        parentTaskId: Number(parentTaskId),
        projectId: parentTask.projectId,
        createdById: userId,
        type: 'subtask'
      });

      const savedSubtask = await this.taskRepository.save(subtask);

      // Fetch complete subtask
      const completeSubtask = await this.taskRepository.findOne({
        where: { id: savedSubtask.id },
        relations: ['createdBy', 'assignedTo', 'parentTask']
      });

      res.status(201).json(completeSubtask);
    } catch (error) {
      console.error('Error creating subtask:', error);
      res.status(500).json({ error: 'Failed to create subtask' });
    }
  }

  // Update task checklist
  async updateTaskChecklist(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { checklist } = req.body;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const task = await this.taskRepository.findOne({
        where: { id: Number(id), isActive: true },
        relations: ['project']
      });

      if (!task) {
        return res.status(404).json({ error: 'Task not found' });
      }

      const hasPermission = await this.checkTaskPermission(task, userId);
      if (!hasPermission) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      // Update checklist with completion tracking
      const updatedChecklist = checklist.map((item: any) => ({
        ...item,
        completedAt: item.completed && !task.checklist?.find((existing: any) => existing.id === item.id)?.completed
          ? new Date().toISOString()
          : item.completedAt,
        completedBy: item.completed && !task.checklist?.find((existing: any) => existing.id === item.id)?.completed
          ? userId
          : item.completedBy
      }));

      await this.taskRepository.update(id, { checklist: updatedChecklist });

      // Calculate progress based on checklist completion
      const completedItems = updatedChecklist.filter((item: any) => item.completed).length;
      const totalItems = updatedChecklist.length;
      const progress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

      await this.taskRepository.update(id, { progress });

      res.json({ message: 'Checklist updated successfully', progress });
    } catch (error) {
      console.error('Error updating checklist:', error);
      res.status(500).json({ error: 'Failed to update checklist' });
    }
  }

  // Helper method to check task permissions
  private async checkTaskPermission(task: Task, userId: string): Promise<boolean> {
    return task.createdById === userId ||
           task.assignedToId === userId ||
           task.project.createdById === userId ||
           task.project.managerId === userId ||
           task.project.members?.some(member =>
             member.userId === userId &&
             [ProjectRole.OWNER, ProjectRole.MANAGER, ProjectRole.LEAD].includes(member.role)
           );
  }

  // Helper method to check for circular dependencies
  private async wouldCreateCircularDependency(taskId: number, dependsOnTaskId: number): Promise<boolean> {
    // Simple check - in a real implementation, you'd do a more thorough graph traversal
    const dependsOnTask = await this.taskRepository.findOne({
      where: { id: dependsOnTaskId },
      relations: ['dependencies']
    });

    if (!dependsOnTask) return false;

    // Check if the task we're trying to depend on already depends on us
    return dependsOnTask.dependencies.some(dep => dep.id === taskId);
  }
}
