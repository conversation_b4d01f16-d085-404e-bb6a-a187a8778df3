import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  Save, X, ChevronLeft, User, Upload, Image, BookOpen, Award, Briefcase, Plus, Trash2, Phone, 
  Building2, DollarSign, Landmark, Car, Laptop, Home, ClipboardList, Heart, Users, FileText,
  UserCircle, Calendar, Users2, Flag, CreditCard, BadgeCheck, Fingerprint, Camera,
  Mail, Smartphone, Link, GraduationCap, ScrollText, Target, School, CalendarDays, Building,
  CalendarCheck2, NotebookText, Zap, Terminal, FileBadge, Languages,
  Badge, Network, FolderKanban, MapPin, TrendingUp, Clock, ShieldCheck, CalendarPlus, AlarmClock, UserCog, Activity,
  FolderGit2, Code2, Timer, GitBranch, Boxes, Trophy,
  FileCode,
  Wallet, Layers, Coins, Landmark as LandmarkIcon, Utensils, Fuel, CheckCircle,
  GitBranch as BranchIcon, Hash, UserCircle as UserIcon, Globe,
  List, Tag, Palette, CalendarMinus, Gauge,
  Monitor, C<PERSON>, Hash as HashIcon, CalendarRange, AlertCircle,
  Pill,
  Baby, // For Child's Name/Gender
  HeartHandshake, // For Relationship
  FileSearch, // For Document Tracking
  MoreHorizontal, // Added for dropdown
  Copy, // Added for copy icon
  ExternalLink, // For external links
  ImageOff, // For image error fallback
  FilePlus, // For other documents
  ChevronUp, // For back to top button
  ChevronDown // For go to bottom button
} from 'lucide-react';
import { useAuth } from '../../../contexts/AuthContext';
import { format } from 'date-fns';
import { toast } from 'react-hot-toast'; // Import toast for notifications
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import EmployeeService from '../../../services/EmployeeService';
import 'react-toastify/dist/ReactToastify.css';
import { fileService } from '../../../services/fileService';

/**
 * A component for loading images with error handling
 */
const SafeImage: React.FC<{
  src: string;
  alt: string;
  className?: string;
  fallbackText?: string;
  onClick?: () => void;
}> = ({ src, alt, className = "", fallbackText = "Image Unavailable", onClick }) => {
  const [error, setError] = useState(false);
  const localPlaceholderUrl = "/assets/images/placeholder.jpg"; // Path to a local placeholder image

  const handleError = () => {
    console.error(`Image load failed: ${src}`);
    setError(true);
  };

  // Reset error state if src changes
  useEffect(() => {
    setError(false);
  }, [src]);

  if (error) {
    // Check if we have a local placeholder first
    return (
      <div 
        className={`flex items-center justify-center bg-gray-100 dark:bg-gray-700 ${className}`}
        onClick={onClick}
      >
        <div className="flex flex-col items-center justify-center p-2 text-center">
          <ImageOff className="w-6 h-6 text-gray-400 dark:text-gray-500 mb-1" />
          <span className="text-xs text-gray-500 dark:text-gray-400">{fallbackText}</span>
        </div>
      </div>
    );
  }

  return (
    <img
      src={src}
      alt={alt}
      className={className}
      onError={handleError}
      onClick={onClick}
    />
  );
};

// Employee status options with colors
const statusOptions = [
  { value: 'active', label: 'Active', color: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' },
  { value: 'inactive', label: 'Inactive', color: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' },
  { value: 'onleave', label: 'On Leave', color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' },
  { value: 'resigned', label: 'Resigned', color: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' },
  { value: 'terminated', label: 'Terminated', color: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400' },
  { value: 'suspended', label: 'Suspended', color: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400' },
  { value: 'layoff', label: 'Layoff', color: 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400' },
  { value: 'retired', label: 'Retired', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400' }
];

// Department options in alphabetical order
const departments = [
  'Accounts & Finance',
  'Admin',
  'Audit',
  'CSD',
  'Digital Sales',
  'Executive Office',
  'Human Resources',
  'IT',
  'Land',
  'Legal',
  'Management',
  'Marketing',
  'P & D',
  'Procurement',
  'Sales'
];

// Gender options
const genderOptions = ['Male', 'Female', 'Other'];

// Religion options
const religionOptions = [
  'Islam',
  'Christianity',
  'Hinduism',
  'Buddhism',
  'Judaism',
  'Sikhism',
  'Other'
];

// Marital status options
const maritalStatusOptions = ['Single', 'Married', 'Divorced', 'Widowed'];

// Blood type options
const bloodTypeOptions = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];

// Employment type options
const employmentTypeOptions = ['Full-Time', 'Part-Time', 'Contract'];

// Employment status options
const employmentStatusOptions = ['Permanent', 'Probation', 'Temporary'];

// Education level options
const educationLevelOptions = [
  'High School',
  'Associate Degree',
  'Bachelor\'s Degree',
  'Master\'s Degree',
  'Doctorate',
  'Professional Certification',
  'Other'
];

// Employee level options (hierarchical order from entry to executive)
const employeeLevelOptions = [
  'Entry Level',
  'Junior',
  'Mid-Level',
  'Executive',
  'Senior',
  'Team Lead',
  'Manager',
  'Department Head',
  'Director',
  'CFO',
  'COO',
  'CEO'
];

// Salary tier options
const salaryTierOptions = [
  '1st Tier',
  '2nd Tier',
  '3rd Tier'
];

// Payment mode options
const paymentModeOptions = [
  'Cash',
  'Bank',
  'Both'
];

// Bank options
const bankOptions = [
  'Meezan Bank',
  'HBL',
  'UBL',
  'MCB',
  'Bank Alfalah',
  'Bank Al Habib',
  'Allied Bank',
  'Faysal Bank',
  'Standard Chartered',
  'Dubai Islamic Bank',
  'Other'
];

// Vehicle type options
const vehicleTypeOptions = [
  'Car',
  'Motorcycle',
  'SUV',
  'Van',
  'Pickup Truck',
  'Other'
];

// Accommodation type options
const accommodationTypeOptions = [
  'Single Room',
  'Shared Room',
  'Studio Apartment',
  'One Bedroom Apartment',
  'Two Bedroom Apartment',
  'House',
  'Other'
];

// Education entry interface
interface EducationEntry {
  id: string;
  educationLevel: string;
  degree: string;
  major: string;
  institution: string;
  graduationYear: string;
  grade: string;
  errors?: {
    educationLevel?: string;
    degree?: string;
    major?: string;
    institution?: string;
    graduationYear?: string;
    grade?: string;
  };
}

// Experience entry interface
interface ExperienceEntry {
  id: string;
  companyName: string;
  jobTitle: string;
  startDate: string;
  endDate: string;
  currentlyWorking: boolean;
  jobDescription: string;
  errors?: {
    companyName?: string;
    jobTitle?: string;
    startDate?: string;
    endDate?: string;
    jobDescription?: string;
  };
}

// Device entry interface
interface DeviceEntry {
  id: string;
  deviceName: string;
  makeModel: string;
  serialNumber: string;
  handoverDate: string;
  returnDate: string;
  condition: string;
  errors?: {
    deviceName?: string;
    makeModel?: string;
    serialNumber?: string;
    handoverDate?: string;
    returnDate?: string;
    condition?: string;
  };
}

// Shift options
const shiftOptions = [
  '10:00 AM to 6:00 PM',
  '11:00 AM to 7:00 PM',
  'Custom Rotational'
];

// Shift type options as a type
type ShiftType = 'Fixed' | 'Rotational' | 'Flexible';

const shiftTypeOptions: ShiftType[] = [
  'Fixed',
  'Rotational',
  'Flexible'
];

interface DocumentFile {
  id: string;
  file: File;
  preview: string;
  uploadStatus?: 'uploading' | 'success' | 'error';
  serverId?: number;
  serverPath?: string;
}

// Document entry interface
interface DocumentEntry {
  id: string;
  documentType: string;
  files: DocumentFile[];
}

// Project entry interface
interface ProjectEntry {
  id: string;
  projectName: string;
  role: string;
  startDate: string;
  endDate: string;
  currentProject: boolean;
  technologies: string;
  description: string;
  achievements: string;
  teamSize: string;
  clientName: string;
  errors?: {
    projectName?: string;
    role?: string;
    startDate?: string;
    endDate?: string;
    technologies?: string;
    description?: string;
    achievements?: string;
    teamSize?: string;
    clientName?: string;
  };
}

// Employee form interface
export interface EmployeeForm {
  // Personal Info
  firstName: string;
  middleName: string;
  lastName: string;
  gender: string;
  dateOfBirth: string;
  religion: string;
  cnicNumber: string;
  cnicExpiryDate: string;
  nationality: string;
  maritalStatus: string;
  bloodType: string;
  profileImage: File | null;
  profileImagePreview: string;
  fatherName: string;
  
  // Additional Personal Info
  passportNumber: string;
  passportExpiryDate: string;
  drivingLicenseNumber: string;
  drivingLicenseExpiryDate: string;
  linkedinProfile: string;
  otherSocialProfiles: string;
  professionalMemberships: string;
  hobbiesInterests: string;
  
  // Educational Info
  educationEntries: EducationEntry[];
  
  // Experience Info
  experienceEntries: ExperienceEntry[];
  
  // Skills & Certifications
  professionalSkills: string;
  technicalSkills: string;
  certifications: string;
  languages: string;
  
  // Contact Info
  mobileNumber: string;
  officialNumber: string;
  officialEmail: string;
  personalEmail: string;
  permanentAddress: string;
  currentAddress: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  emergencyContactRelationship: string;
  
  // Job Details
  employeeId: string;
  designation: string;
  department: string;
  project: string;
  location: string;
  employmentType: string;
  employmentStatus: string;
  employeeLevel: string;
  joinDate: string;
  probationEndDate: string; // Added probation end date
  noticePeriod: string;
  reportingTo: string;
  contractStartDate: string; // Added contract start date
  contractEndDate: string; // Added contract end date
  
  // Compensation & Benefits
  totalSalary: string;
  salaryTier: string;
  cashAmount: string;
  bankAmount: string;
  paymentMode: string;
  
  // Allowances
  foodAllowanceInSalary: boolean;
  fuelAllowanceInSalary: boolean;
  numberOfMeals: string;
  fuelInLiters: string;
  fuelAmount: string;
  foodProvidedByCompany: boolean;
  
  // Bank Details
  bankName: string;
  bankBranch: string;
  accountNumber: string;
  accountTitle: string;
  iban: string;
  
  // Vehicle Details
  vehicleType: string;
  registrationNumber: string;
  providedByCompany: boolean;
  handingOverDate: string;
  returnDate: string;
  vehicleMakeModel: string;
  vehicleColor: string;
  mileageAtIssuance: string;
  
  // Company Devices
  deviceEntries: DeviceEntry[];
  
  // Accommodation Details
  accommodationProvidedByEmployer: boolean;
  accommodationType: string;
  accommodationAddress: string;
  
  // Professional/Work-Related Info
  workSchedule: string;
  shiftType: ShiftType | '';
  remoteWorkEligible: boolean;
  nextReviewDate: string;
  trainingRequirements: string;
  
  // System Status
  status: 'active' | 'inactive' | 'onleave' | 'resigned' | 'terminated' | 'suspended' | 'layoff' | 'retired';
  statusDate: string; // Added status date
  
  // Health & Insurance Info
  healthInsuranceProvider: string;
  healthInsurancePolicyNumber: string;
  healthInsuranceExpiryDate: string;
  lifeInsuranceProvider: string;
  lifeInsurancePolicyNumber: string;
  lifeInsuranceExpiryDate: string;
  vaccinationRecords: string;
  medicalHistory: string;
  bloodGroup: string;
  allergies: string;
  chronicConditions: string;
  regularMedications: string;
  
  // Family Information
  spouseName: string;
  spouseDateOfBirth: string;
  spouseOccupation: string;
  spouseEmployer: string;
  spouseContactNumber: string;
  spouseCNIC: string;
  children: Array<{
    id: string;
    name: string;
    dateOfBirth: string;
    gender: string;
    relationship: string;
    type: string; // 'child', 'spouse', 'dependent'
  }>;
  dependents: Array<{
    id: string;
    name: string;
    dateOfBirth: string;
    relationship: string;
    cnic: string;
    type: string; // 'child', 'spouse', 'dependent'
  }>;
  
  // Document Management
  documents: DocumentEntry[];
  requiredDocuments: {
    cnic: boolean;
    passport: boolean;
    drivingLicense: boolean;
    educationalCertificates: boolean;
    experienceCertificates: boolean;
    bankAccountDetails: boolean;
    taxDocuments: boolean;
    medicalCertificate: boolean;
    policeClearanceCertificate: boolean;
    cv: boolean;
    affidavit: boolean;
    otherDocuments: boolean;
  };
  projectEntries: ProjectEntry[];
  notes: string;
  specialInstructions: string;
  errors: {
    [key: string]: string;
  };
  salaryTrench: string;
}

// Add these constants at the top of the file near other options constants
const PROJECTS = [
  'Eurobiz Corporations',
  'Guardian International',
  'Guardian Developers',
  'Grand Developers',
  'ARD Developers'
];

const PROJECT_LOCATIONS: Record<string, string[]> = {
  'Eurobiz Corporations': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Kharian'
  ],
  'Guardian International': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Arifwala'
  ],
  'Guardian Developers': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Vehari'
  ],
  'Grand Developers': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Faisalabad',
    'Grand City Site Office Murree'
  ],
  'ARD Developers': [
    'ARD Head Office Lahore',
    'ARD Site Office RUDA'
  ]
};

// Add the validateEducationField function before the handleEducationChange function
const validateEducationField = (name: string, value: string): string => {
  // If field is empty, it's valid since all fields are optional
  if (!value) return '';

  switch (name) {
    case 'degree':
      if (value.length > 100) return 'Degree title cannot exceed 100 characters';
      return '';

    case 'major':
      if (value.length > 100) return 'Major/Field cannot exceed 100 characters';
      return '';

    case 'institution':
      if (value.length > 100) return 'Institution name cannot exceed 100 characters';
      return '';

    case 'graduationYear':
      const year = parseInt(value);
      if (isNaN(year) || year < 1950 || year > new Date().getFullYear() + 5) {
        return 'Please enter a valid year between 1950 and ' + (new Date().getFullYear() + 5);
      }
      return '';

    case 'grade':
      // Only validate format if grade is provided
      if (!/^([0-4](\.\d{1,2})?|[0-9]|[1-9][0-9]|100|[A-F][+-]?)$/.test(value)) {
        return 'Please enter a valid grade format (e.g., 3.5, 85, A+)';
      }
      return '';

    default:
      return '';
  }
};

// Add the validateExperienceField function before the handleExperienceChange function
const validateExperienceField = (name: string, value: string): string => {
  // If field is empty, it's valid since all fields are optional
  if (!value) return '';

  switch (name) {
    case 'companyName':
      if (value.length > 100) return 'Company name cannot exceed 100 characters';
      return '';

    case 'jobTitle':
      if (value.length > 100) return 'Job title cannot exceed 100 characters';
      return '';

    case 'startDate':
      const startDate = new Date(value);
      if (isNaN(startDate.getTime())) return 'Please enter a valid start date';
      if (startDate > new Date()) return 'Start date cannot be in the future';
      return '';

    case 'endDate':
      const endDate = new Date(value);
      if (isNaN(endDate.getTime())) return 'Please enter a valid end date';
      if (endDate > new Date()) return 'End date cannot be in the future';
      return '';

    case 'jobDescription':
      if (value.length > 500) return 'Job description cannot exceed 500 characters';
      return '';

    default:
      return '';
  }
};

// Validate device field
const validateDeviceField = (name: string, value: string): string => {
    switch (name) {
    case 'deviceName':
      if (value.trim().length > 100) {
        return 'Device name should not exceed 100 characters';
      }
        return '';
    case 'makeModel':
      if (value.trim().length > 100) {
        return 'Make/Model should not exceed 100 characters';
      }
        return '';
    case 'serialNumber':
      if (value.trim().length > 50) {
        return 'Serial number should not exceed 50 characters';
      }
        return '';
    case 'handoverDate':
      if (value && new Date(value) > new Date()) {
        return 'Handover date cannot be in the future';
        }
        return '';
    case 'returnDate':
      if (value && new Date(value) < new Date()) {
        return 'Return date should be in the future';
        }
        return '';
    default:
        return '';
  }
};

// Update the component signature to accept employeeId prop
interface AddEmployeePageProps {
  employeeId?: string;
}

// Define the steps for the form progress
const formSteps = [
  { id: 1, name: 'Personal Information' },
  { id: 2, name: 'Contact Information' },
  { id: 3, name: 'Education & Experience' },
  { id: 4, name: 'Job Details' },
  { id: 5, name: 'Compensation & Benefits' },
  { id: 6, name: 'Health & Insurance' },
  { id: 7, name: 'Family Information' },
  { id: 8, name: 'Document Management' }
];

const AddEmployeePage: React.FC<AddEmployeePageProps> = ({ employeeId: propEmployeeId }): JSX.Element => {
  const [activeStep, setActiveStep] = useState(1);
  const navigate = useNavigate();
  const { id: urlEmployeeId } = useParams<{ id: string }>();
  const actualEmployeeId = propEmployeeId || urlEmployeeId;
  const [isEditMode, setIsEditMode] = useState(!!actualEmployeeId);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [showBackToTop, setShowBackToTop] = useState(false);

  // Function to format date string for the date input
  const formatDateForInput = (dateString: string | undefined | null): string => {
    if (!dateString) return '';
    try {
      // Parse the date and format using local time (not UTC)
      const date = new Date(dateString);
      
      // Use local time components instead of UTC
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    } catch (e) {
      console.error('Error formatting date:', e);
      return '';
    }
  };

  // Add useEffect to fetch employee data when in edit mode
  useEffect(() => {
    if (actualEmployeeId) {
      fetchEmployeeData();
    }
  }, [actualEmployeeId]);

  // Function to fetch employee data for editing
  const fetchEmployeeData = async (): Promise<void> => {
    if (!actualEmployeeId) return;
    
    setIsLoading(true);
    try {
      // Add timestamp to prevent caching issues
      const timestamp = Date.now();
      const { data, error } = await EmployeeService.getEmployee(actualEmployeeId, { t: timestamp });
      
      if (error) {
        toast.error(`Failed to fetch employee data: ${error}`);
        return;
      }
      
      if (data?.employee) {
        // Populate the form with employee data
        const employee = data.employee;
        console.log('Received employee data for editing:', employee);
        
        // Log status date field specifically
        console.log('Status date fields:', {
          statusDate: employee.statusDate,
          status_date: employee.status_date,
          rawStatus: employee.status,
          formattedDate: formatDateForInput(employee.statusDate || employee.status_date)
        });
        
        // Add detailed logging for debugging
        console.log('Raw API response structure:', JSON.stringify(data, null, 2));
        
        // Retrieve contact, job, and benefit from the API response
        const employeeJob = employee.job || {};
        const employeeContact = employee.contact || {};
        const employeeBenefit = employee.benefit || {};
        
        // Additional detailed logging to see what fields are available in each section
        console.log('Benefit fields available:', Object.keys(employeeBenefit));
        console.log('Benefit data:', employeeBenefit);
        console.log('Contact fields available:', Object.keys(employeeContact));
        console.log('Job fields available:', Object.keys(employeeJob));
        
        // Complex entities should already be arrays from the API
        let educationEntries = employee.educationEntries || [];
        let experienceEntries = employee.experienceEntries || [];
        const deviceEntries = employee.deviceEntries || [];
        const children = employee.children || [];
        const dependents = employee.dependents || [];
        const documents = employee.documentEntries || employee.documents || [];
        
        // Log medical fields for debugging
        console.log('Medical fields available:', {
          vaccinationRecords: employee.vaccinationRecords,
          medicalHistory: employee.medicalHistory,
          bloodGroup: employee.bloodGroup,
          allergies: employee.allergies,
          chronicConditions: employee.chronicConditions,
          regularMedications: employee.regularMedications
        });
        
        // Log family fields for debugging
        console.log('Family fields available:', {
          spouseName: employee.spouseName,
          spouseDateOfBirth: employee.spouseDateOfBirth,
          spouseOccupation: employee.spouseOccupation,
          spouseEmployer: employee.spouseEmployer,
          spouseContactNumber: employee.spouseContactNumber,
          spouseCNIC: employee.spouseCNIC,
          children: children.length,
          dependents: dependents.length
        });
        
        // Log detailed child/dependent data for debugging
        console.log('Children data:', children);
        console.log('Dependents data:', dependents);
        
        // Process children and dependents to ensure they have the right structure
        const processedChildren = Array.isArray(children) ? children.map((child: any) => ({
          id: child.id?.toString() || uuidv4(),
          name: child.name || '',
          dateOfBirth: child.dateOfBirth || '',
          gender: child.gender || '',
          relationship: child.relationship || 'child',
          type: child.type || 'child'
        })) : [];
        
        const processedDependents = Array.isArray(dependents) ? dependents.map((dependent: any) => ({
          id: dependent.id?.toString() || uuidv4(),
          name: dependent.name || '',
          dateOfBirth: dependent.dateOfBirth || '',
          relationship: dependent.relationship || 'other',
          cnic: dependent.cnic || '',
          type: dependent.type || 'dependent'
        })) : [];
        
        console.log('Processed children:', processedChildren);
        console.log('Processed dependents:', processedDependents);
        
        // Check if there's a spouse in the family members
        let spouseData = {
          name: '',
          dateOfBirth: '',
          occupation: '',
          employer: '',
          contactNumber: '',
          cnic: ''
        };
        
        // Look for spouse in family members
        if (Array.isArray(employee.family)) {
          console.log('Looking for spouse in family array:', employee.family);
          const spouse = employee.family.find((f: any) => 
            f.type === 'spouse' || 
            f.type === 'Spouse' || 
            f.relationship === 'spouse' || 
            f.relationship === 'Spouse'
          );
          if (spouse) {
            console.log('Found spouse in family members:', spouse);
            spouseData = {
              name: spouse.name || '',
              dateOfBirth: spouse.dateOfBirth || '',
              occupation: spouse.occupation || '',
              employer: spouse.employer || '',
              contactNumber: spouse.contactNumber || '',
              cnic: spouse.cnic || ''
            };
          } else {
            console.log('No spouse found in family array');
          }
        }
        
        // Also check for direct spouse fields in case they're set directly
        console.log('Checking for direct spouse fields:', {
          spouseName: employee.spouseName,
          spouseDateOfBirth: employee.spouseDateOfBirth,
          spouseOccupation: employee.spouseOccupation
        });
        
        // Log document fields for debugging
        console.log('Document data:', documents);
        
        // Process document data for the frontend
        const processedDocuments = Array.isArray(documents) 
          ? documents
              .filter((doc: any) => {
                // Filter out any document that might be a profile image
                const isProfileImage = 
                  doc.documentType?.toLowerCase().includes('profile') || 
                  doc.documentType?.toLowerCase().includes('photo') ||
                  (doc.filePath && !doc.filePath.includes('/documents/'));
                  
                if (isProfileImage) {
                  console.log('Filtering out profile image from documents:', doc.filePath);
                }
                
                return !isProfileImage;
              })
              .map((doc: any): DocumentEntry => {
                console.log('Processing document:', doc);
                const fileType = doc.filePath?.toLowerCase().endsWith('.pdf') 
                  ? 'application/pdf' 
                  : doc.filePath?.toLowerCase().match(/\.(jpg|jpeg|png|gif)$/) 
                    ? 'image/' + (doc.filePath.toLowerCase().endsWith('png') ? 'png' : 'jpeg')
                    : 'application/octet-stream';
                
                // Create a placeholder file object with the correct name
                const fileName = doc.filePath ? doc.filePath.split('/').pop() || 'document file' : 'document file';
                const mockFile = new File([], fileName, { type: fileType });
                
                return {
                  id: doc.id?.toString() || uuidv4(),
                  documentType: doc.documentType || '',
                  files: doc.filePath ? [{
                    id: uuidv4(),
                    file: mockFile,
                    preview: doc.filePath || '',
                    uploadStatus: 'success',
                    serverId: doc.id,
                    serverPath: doc.filePath
                  }] : []
                };
              })
          : [];
        
        console.log('Processed documents:', processedDocuments);
        
        // Helper function to safely parse JSON strings
        const safelyParseJSON = (jsonString: string | null | undefined, defaultValue: any = []) => {
          if (!jsonString) return defaultValue;
          try {
            return typeof jsonString === 'string' ? JSON.parse(jsonString) : jsonString;
          } catch (e) {
            console.warn('Failed to parse JSON:', jsonString);
            return defaultValue;
          }
        };
        
        // Define the prepared entries types
        type PreparedEducationEntry = {
          id: string;
          educationLevel: string;
          degree: string;
          major: string;
          institution: string;
          graduationYear: string;
          grade: string;
        };

        type PreparedExperienceEntry = {
          id: string;
          companyName: string;
          jobTitle: string;
          startDate: string;
          endDate: string;
          currentlyWorking: boolean;
          jobDescription: string;
        };

        type PreparedDeviceEntry = {
          id: string;
          deviceName: string;
          makeModel: string;
          serialNumber: string;
          handoverDate: string;
          returnDate: string;
          condition: string;
        };
        
        // Convert education entries to expected format
        const preparedEducationEntries: PreparedEducationEntry[] = educationEntries.map((entry: any) => ({
          id: entry.id?.toString() || uuidv4(),
          educationLevel: entry.educationLevel || '',
          degree: entry.degree || '',
          major: entry.major || '',
          institution: entry.institution || '',
          graduationYear: entry.graduationYear || '',
          grade: entry.grade || ''
        }));
        
        // Add an empty education entry if none exist
        if (preparedEducationEntries.length === 0) {
          preparedEducationEntries.push({
              id: uuidv4(),
              educationLevel: '',
              degree: '',
              major: '',
              institution: '',
              graduationYear: '',
              grade: ''
          });
        }
        
        // Convert experience entries to expected format
        const preparedExperienceEntries: PreparedExperienceEntry[] = experienceEntries.map((entry: any) => ({
          id: entry.id?.toString() || uuidv4(),
          companyName: entry.companyName || '',
          jobTitle: entry.jobTitle || '',
          startDate: entry.startDate || '',
          endDate: entry.endDate || '',
          currentlyWorking: entry.currentlyWorking || false,
          jobDescription: entry.jobDescription || ''
        }));
        
        // Add an empty experience entry if none exist
        if (preparedExperienceEntries.length === 0) {
          preparedExperienceEntries.push({
              id: uuidv4(),
              companyName: '',
              jobTitle: '',
              startDate: '',
              endDate: '',
              currentlyWorking: false,
              jobDescription: ''
          });
        }
        
        // Convert device entries to expected format
        const preparedDeviceEntries: PreparedDeviceEntry[] = deviceEntries.map((entry: any) => ({
          id: entry.id?.toString() || uuidv4(),
          deviceName: entry.deviceName || '',
          makeModel: entry.makeModel || '',
          serialNumber: entry.serialNumber || '',
          handoverDate: entry.handoverDate || '',
          returnDate: entry.returnDate || '',
          condition: entry.condition || ''
        }));
        
        // Add an empty device entry if none exist
        if (preparedDeviceEntries.length === 0) {
          preparedDeviceEntries.push({
              id: uuidv4(),
              deviceName: '',
              makeModel: '',
              serialNumber: '',
              handoverDate: '',
              returnDate: '',
              condition: ''
          });
        }
        
        // Update form state with employee data
        setEmployeeForm(prevForm => {
          // Create updated form data
          const updatedForm = {
            ...prevForm,
            // Personal Info
            firstName: employee.firstName || '',
            middleName: employee.middleName || '',
            lastName: employee.lastName || '',
            gender: employee.gender || '',
            dateOfBirth: employee.dateOfBirth || '',
            religion: employee.religion || '',
            cnicNumber: employee.cnicNumber || '',
            cnicExpiryDate: employee.cnicExpiryDate || '',
            nationality: employee.nationality || '',
            maritalStatus: employee.maritalStatus || '',
            bloodType: employee.bloodType || employee.bloodGroup || '',
            // Updated path handling to account for both formats
            profileImagePreview: employee.profileImagePath 
              ? (employee.profileImagePath.startsWith('/') 
                ? employee.profileImagePath 
                : `/uploads/${employee.profileImagePath}`) 
              : '',
            
            // Contact Info - Use the retrieved contact object directly
            mobileNumber: employeeContact.mobileNumber || employee.mobileNumber || '',
            officialNumber: employeeContact.officialNumber || employee.officialNumber || '',
            officialEmail: employeeContact.officialEmail || employee.officialEmail || '',
            personalEmail: employeeContact.personalEmail || employee.personalEmail || '',
            permanentAddress: employeeContact.permanentAddress || employee.permanentAddress || '',
            currentAddress: employeeContact.currentAddress || employee.currentAddress || '',
            emergencyContactName: employeeContact.emergencyContactName || employee.emergencyContactName || '',
            emergencyContactPhone: employeeContact.emergencyContactPhone || employee.emergencyContactPhone || '',
            emergencyContactRelationship: employeeContact.emergencyContactRelationship || employee.emergencyContactRelationship || '',
            linkedinProfile: employeeContact.linkedinProfile || employee.linkedinProfile || '',
            otherSocialProfiles: employeeContact.otherSocialProfiles || employee.otherSocialProfiles || '',
            
            // Job Info - Use the retrieved job object directly
            employeeId: employee.employeeId || '',
            designation: employeeJob.designation || employee.designation || '',
            department: employeeJob.department || employee.department || '',
            project: employeeJob.project || employee.project || '',
            location: employeeJob.location || employee.location || '',
            employmentType: employeeJob.employmentType || employee.employmentType || '',
            employmentStatus: employeeJob.employmentStatus || employee.status || 'active',
            employeeLevel: employeeJob.employeeLevel || employee.employeeLevel || '',
            joinDate: employeeJob.joinDate || employee.joinDate || '',
            probationEndDate: employeeJob.probationEndDate || employee.probationEndDate || '',
            noticePeriod: employeeJob.noticePeriod || employee.noticePeriod || '',
            reportingTo: employeeJob.reportingTo || employee.reportingTo || '',
            contractStartDate: employeeJob.contractStartDate || employee.contractStartDate || '',
            contractEndDate: employeeJob.contractEndDate || employee.contractEndDate || '',
            
            // Compensation & Benefits - Use the retrieved benefit object directly
            totalSalary: employeeBenefit.totalSalary || employee.totalSalary || '',
            salaryTier: employeeBenefit.salaryTier || employee.salaryTier || '',
            cashAmount: employeeBenefit.cashAmount || employee.cashAmount || '',
            bankAmount: employeeBenefit.bankAmount || employee.bankAmount || '',
            paymentMode: employeeBenefit.paymentMode || employee.paymentMode || '',
            
            // Allowances
            foodAllowanceInSalary: employeeBenefit.foodAllowanceInSalary || employee.foodAllowanceInSalary || false,
            fuelAllowanceInSalary: employeeBenefit.fuelAllowanceInSalary || employee.fuelAllowanceInSalary || false,
            numberOfMeals: employeeBenefit.numberOfMeals || employee.numberOfMeals || '',
            fuelInLiters: employeeBenefit.fuelInLiters || employee.fuelInLiters || '',
            fuelAmount: employeeBenefit.fuelAmount || employee.fuelAmount || '',
            foodProvidedByCompany: employeeBenefit.foodProvidedByCompany || employee.foodProvidedByCompany || false,
            
            // Bank Details
            bankName: employeeBenefit.bankName || employee.bankName || '',
            bankBranch: employeeBenefit.bankBranch || employee.bankBranch || '',
            accountNumber: employeeBenefit.accountNumber || employee.accountNumber || '',
            accountTitle: employeeBenefit.accountTitle || employee.accountTitle || '',
            iban: employeeBenefit.iban || employee.iban || '',
            
            // Health & Insurance
            healthInsuranceProvider: employeeBenefit.healthInsuranceProvider || employee.healthInsuranceProvider || '',
            healthInsurancePolicyNumber: employeeBenefit.healthInsurancePolicyNumber || employee.healthInsurancePolicyNumber || '',
            healthInsuranceExpiryDate: employeeBenefit.healthInsuranceExpiryDate || employee.healthInsuranceExpiryDate || '',
            lifeInsuranceProvider: employeeBenefit.lifeInsuranceProvider || employee.lifeInsuranceProvider || '',
            lifeInsurancePolicyNumber: employeeBenefit.lifeInsurancePolicyNumber || employee.lifeInsurancePolicyNumber || '',
            lifeInsuranceExpiryDate: employeeBenefit.lifeInsuranceExpiryDate || employee.lifeInsuranceExpiryDate || '',
            
            // Health Records - Make sure to include these
            vaccinationRecords: employee.vaccinationRecords || '',
            medicalHistory: employee.medicalHistory || '',
            bloodGroup: employee.bloodGroup || employee.bloodType || '',  
            allergies: employee.allergies || '',
            chronicConditions: employee.chronicConditions || '',
            regularMedications: employee.regularMedications || '',
            
            // Family Information
            spouseName: spouseData.name || employee.spouseName || '',
            spouseDateOfBirth: spouseData.dateOfBirth || employee.spouseDateOfBirth || '',
            spouseOccupation: spouseData.occupation || employee.spouseOccupation || '',
            spouseEmployer: spouseData.employer || employee.spouseEmployer || '',
            spouseContactNumber: spouseData.contactNumber || employee.spouseContactNumber || '',
            spouseCNIC: spouseData.cnic || employee.spouseCNIC || '',
            
            // Accommodation
            accommodationProvidedByEmployer: employeeBenefit.accommodationProvidedByEmployer || employee.accommodationProvidedByEmployer || false,
            accommodationType: employeeBenefit.accommodationType || employee.accommodationType || '',
            accommodationAddress: employeeBenefit.accommodationAddress || employee.accommodationAddress || '',
            
            // Skills & Certifications
            professionalSkills: employee.professionalSkills || '',
            technicalSkills: employee.technicalSkills || '',
            certifications: employee.certifications || '',
            languages: employee.languages || '',
            
            // Vehicle Details
            vehicleType: employee.vehicleType || '',
            registrationNumber: employee.registrationNumber || '',
            providedByCompany: employee.providedByCompany || false,
            handingOverDate: employee.handingOverDate || '',
            returnDate: employee.returnDate || '',
            vehicleMakeModel: employee.vehicleMakeModel || '',
            vehicleColor: employee.vehicleColor || '',
            mileageAtIssuance: employee.mileageAtIssuance || '',
            
            // Professional/Work-Related Info
            workSchedule: employeeJob.workSchedule || employee.workSchedule || '',
            shiftType: (employeeJob.shiftType || employee.shiftType || '') as ShiftType,
            remoteWorkEligible: employeeJob.remoteWorkEligible || employee.remoteWorkEligible || false,
            nextReviewDate: employeeJob.nextReviewDate || employee.nextReviewDate || '',
            trainingRequirements: employeeJob.trainingRequirements || employee.trainingRequirements || '',
            
            // Complex arrays - use prepared data
            educationEntries: preparedEducationEntries,
            experienceEntries: preparedExperienceEntries,
            deviceEntries: preparedDeviceEntries,
            
            // Documents, children and dependents
            documents: processedDocuments,
            children: processedChildren,
            dependents: processedDependents,
            
            // Project entries from JSON string or array
            projectEntries: safelyParseJSON(employee.projectEntries, []),
            
            // Document required flags
            requiredDocuments: safelyParseJSON(employee.requiredDocuments, {
              cnic: false,
              passport: false,
              drivingLicense: false,
              educationalCertificates: false,
              experienceCertificates: false,
              bankAccountDetails: false,
              taxDocuments: false,
              medicalCertificate: false,
              policeClearanceCertificate: false,
              cv: false,
              affidavit: false,
              otherDocuments: false
            }),
            
            // System Status
            status: employee.status || 'active',
            statusDate: formatDateForInput(employee.statusDate || employee.status_date),
            
            // Notes and special instructions
            notes: employee.notes || '',
            specialInstructions: employee.specialInstructions || '',
            salaryTrench: employeeBenefit.salaryTrench || '',
            fatherName: employee.fatherName || '',
          };
          
          // Log updated form data
          console.log('Form data after update:', {
            totalSalary: updatedForm.totalSalary, 
            salaryTier: updatedForm.salaryTier,
            paymentMode: updatedForm.paymentMode,
            educationEntries: updatedForm.educationEntries.length,
            experienceEntries: updatedForm.experienceEntries.length,
            deviceEntries: updatedForm.deviceEntries.length,
            documents: updatedForm.documents.length,
            children: updatedForm.children.length,
            dependents: updatedForm.dependents.length,
            vaccinationRecords: updatedForm.vaccinationRecords ? 'Present' : 'Missing',
            medicalHistory: updatedForm.medicalHistory ? 'Present' : 'Missing',
            allergies: updatedForm.allergies ? 'Present' : 'Missing'
          });
          
          return updatedForm;
        });
        
        // Set page title to reflect edit mode
        document.title = `Edit Employee - ${employee.firstName} ${employee.lastName}`;
        
      } else {
        toast.error('Employee not found');
        navigate('/hr/employees');
      }
    } catch (err) {
      console.error("Error fetching employee:", err);
      toast.error('Failed to load employee data. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  // Add click outside handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Validation function
  const validateField = (field: string, value: any): string => {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      // Skip empty check for contractStartDate and contractEndDate if employment type is not Contract
      if ((field === 'contractStartDate' || field === 'contractEndDate') && 
          employeeForm.employmentType !== 'Contract') {
        return '';
      }
      
      // Skip empty check for IBAN field as it's optional
      if (field === 'iban') {
        return '';
      }
      return `${field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1')} is required`;
    }
    
    if (field === 'officialEmail' || field === 'personalEmail') {
      // Email validation using regex
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        return 'Please enter a valid email address';
      }
    }
    
    // Contract dates validation
    if (field === 'contractEndDate' && employeeForm.employmentType === 'Contract') {
      if (employeeForm.contractStartDate && new Date(value) <= new Date(employeeForm.contractStartDate)) {
        return 'Contract end date must be after the start date';
      }
    }
    
    // Return empty string if validation passes
    return '';
  };

  const [employeeForm, setEmployeeForm] = useState<EmployeeForm>({
    // Personal Info
    firstName: '',
    middleName: '',
    lastName: '',
    gender: '',
    dateOfBirth: '',
    religion: '',
    cnicNumber: '',
    cnicExpiryDate: '',
    nationality: '',
    maritalStatus: '',
    bloodType: '',
    profileImage: null,
    profileImagePreview: '',
    fatherName: '',
    
    // Additional Personal Info
    passportNumber: '',
    passportExpiryDate: '',
    drivingLicenseNumber: '',
    drivingLicenseExpiryDate: '',
    linkedinProfile: '',
    otherSocialProfiles: '',
    professionalMemberships: '',
    hobbiesInterests: '',
    
    // Educational Info
    educationEntries: [
      {
        id: uuidv4(),
        educationLevel: '',
        degree: '',
        major: '',
        institution: '',
        graduationYear: '',
        grade: ''
      }
    ],
    
    // Experience Info
    experienceEntries: [
      {
        id: uuidv4(),
        companyName: '',
        jobTitle: '',
        startDate: '',
        endDate: '',
        currentlyWorking: false,
        jobDescription: ''
      }
    ],
    
    // Skills & Certifications
    professionalSkills: '',
    technicalSkills: '',
    certifications: '',
    languages: '',
    
    // Contact Info
    mobileNumber: '',
    officialNumber: '',
    officialEmail: '',
    personalEmail: '',
    permanentAddress: '',
    currentAddress: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',
    
    // Job Details
    employeeId: '',
    designation: '',
    department: '',
    project: '',
    location: '',
    employmentType: '',
    employmentStatus: '',
    employeeLevel: '',
    joinDate: '',
    probationEndDate: '',
    noticePeriod: '',
    reportingTo: '',
    contractStartDate: '',
    contractEndDate: '',
    
    // Compensation & Benefits
    totalSalary: '',
    salaryTier: '',
    cashAmount: '',
    bankAmount: '',
    paymentMode: '',
    
    // Allowances
    foodAllowanceInSalary: false,
    fuelAllowanceInSalary: false,
    numberOfMeals: '',
    fuelInLiters: '',
    fuelAmount: '',
    foodProvidedByCompany: false,
    
    // Bank Details
    bankName: '',
    bankBranch: '',
    accountNumber: '',
    accountTitle: '',
    iban: '',
    
    // Vehicle Details
    vehicleType: '',
    registrationNumber: '',
    providedByCompany: false,
    handingOverDate: '',
    returnDate: '',
    vehicleMakeModel: '',
    vehicleColor: '',
    mileageAtIssuance: '',
    
    // Company Devices
    deviceEntries: [{
      id: uuidv4(),
      deviceName: '',
      makeModel: '',
      serialNumber: '',
      handoverDate: '',
      returnDate: '',
      condition: ''
    }],
    
    // Accommodation Details
    accommodationProvidedByEmployer: false,
    accommodationType: '',
    accommodationAddress: '',
    
    // Professional/Work-Related Info
    workSchedule: '',
    shiftType: '',
    remoteWorkEligible: false,
    nextReviewDate: '',
    trainingRequirements: '',
    
    // System Status
    status: 'active',
    
    // Health & Insurance Info
    healthInsuranceProvider: '',
    healthInsurancePolicyNumber: '',
    healthInsuranceExpiryDate: '',
    lifeInsuranceProvider: '',
    lifeInsurancePolicyNumber: '',
    lifeInsuranceExpiryDate: '',
    vaccinationRecords: '',
    medicalHistory: '',
    bloodGroup: '',
    allergies: '',
    chronicConditions: '',
    regularMedications: '',
    
    // Family Information
    spouseName: '',
    spouseDateOfBirth: '',
    spouseOccupation: '',
    spouseEmployer: '',
    spouseContactNumber: '',
    spouseCNIC: '',
    children: [],
    dependents: [],
    
    // Document Management
    documents: [],
    requiredDocuments: {
      cnic: false,
      passport: false,
      drivingLicense: false,
      educationalCertificates: false,
      experienceCertificates: false,
      bankAccountDetails: false,
      taxDocuments: false,
      medicalCertificate: false,
      policeClearanceCertificate: false,
      cv: false,
      affidavit: false,
      otherDocuments: false
    },
    projectEntries: [{
      id: uuidv4(),
      projectName: '',
      role: '',
      startDate: '',
      endDate: '',
      currentProject: false,
      technologies: '',
      description: '',
      achievements: '',
      teamSize: '',
      clientName: ''
    }],
    notes: '',
    specialInstructions: '',
    errors: {},
    salaryTrench: '',
    statusDate: '', // Added status date
  });

  // Handle form input changes
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    // Special handling for date inputs to prevent timezone issues
    if (type === 'date') {
      // For date inputs, store the value exactly as entered without any conversion
      // This ensures the date isn't shifted due to timezone differences
      console.log(`Date field ${name} changed to: ${value}`);
      
      setEmployeeForm(prev => ({
        ...prev,
        [name]: value, // Store the exact date string as entered
        errors: {
          ...prev.errors,
          [name]: validateField(name, value)
        }
      }));
      return;
    }
    
    // Special handling for bloodType/bloodGroup to keep them in sync
    if (name === 'bloodType' || name === 'bloodGroup') {
      // Validate the field
      const error = validateField(name, value);
      
      setEmployeeForm(prev => ({
        ...prev,
        // Update both fields regardless of which was changed
        bloodType: value,
        bloodGroup: value,
        errors: {
          ...prev.errors,
          [name]: error,
          // Also clear any error on the other field
          ...(name === 'bloodType' ? { bloodGroup: '' } : { bloodType: '' })
        }
      }));
      return;
    }
    
    // Validate the field
    const error = validateField(name, value);
    
    setEmployeeForm(prev => ({
      ...prev,
      [name]: value,
      errors: {
        ...prev.errors,
        [name]: error
      }
    }));
  };
  
  // Copy permanent address to current address
  const copyAddress = () => {
    setEmployeeForm(prev => ({
      ...prev,
      currentAddress: prev.permanentAddress,
      errors: {
        ...prev.errors,
        currentAddress: validateField('currentAddress', prev.permanentAddress)
      }
    }));
  };

  // Handle image upload
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
          setEmployeeForm(prev => ({
            ...prev,
            profileImage: file,
        profileImagePreview: URL.createObjectURL(file),
        errors: prev.errors
          }));
    }
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle education form change
  const handleEducationChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>, index: number) => {
    const { name, value } = e.target;
    const updatedEntries = [...employeeForm.educationEntries];
    updatedEntries[index] = {
      ...updatedEntries[index],
      [name]: value,
      errors: {
        ...updatedEntries[index].errors,
        [name]: validateEducationField(name, value)
      }
    };
    
    setEmployeeForm(prev => ({
      ...prev,
      educationEntries: updatedEntries
    }));
  };
  
  // Add new education entry
  const addEducationEntry = () => {
    const newEntry: EducationEntry = {
      id: '1',
      educationLevel: '',
      degree: '',
      major: '',
      institution: '',
      graduationYear: '',
      grade: ''
    };
    
    setEmployeeForm(prev => ({
      ...prev,
      educationEntries: [...prev.educationEntries, newEntry]
    }));
  };
  
  // Remove education entry
  const removeEducationEntry = (index: number) => {
    if (employeeForm.educationEntries.length <= 1) {
      return; // Don't remove if it's the only entry
    }
    
    const updatedEntries = [...employeeForm.educationEntries];
    updatedEntries.splice(index, 1);
    
    setEmployeeForm(prev => ({
      ...prev,
      educationEntries: updatedEntries
    }));
  };
  
  // Handle experience form change
  const handleExperienceChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, index: number) => {
    const { name, value, type } = e.target;
    const updatedEntries = [...employeeForm.experienceEntries];
    
    // Update the field value
      updatedEntries[index] = {
        ...updatedEntries[index],
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
      };

    // Validate the field if it's not a checkbox
    if (type !== 'checkbox') {
      const error = validateExperienceField(name, value);
      updatedEntries[index] = {
        ...updatedEntries[index],
        errors: {
          ...updatedEntries[index].errors,
          [name]: error
        }
      };
    }
    
    setEmployeeForm(prev => ({
      ...prev,
      experienceEntries: updatedEntries
    }));
  };
  
  // Add new experience entry
  const addExperienceEntry = () => {
    const newEntry: ExperienceEntry = {
      id: Date.now().toString(),
      companyName: '',
      jobTitle: '',
      startDate: '',
      endDate: '',
      currentlyWorking: false,
      jobDescription: ''
    };
    
    setEmployeeForm(prev => ({
      ...prev,
      experienceEntries: [...prev.experienceEntries, newEntry]
    }));
  };
  
  // Remove experience entry
  const removeExperienceEntry = (index: number) => {
    if (employeeForm.experienceEntries.length <= 1) {
      return; // Don't remove if it's the only entry
    }
    
    const updatedEntries = [...employeeForm.experienceEntries];
    updatedEntries.splice(index, 1);
    
    setEmployeeForm(prev => ({
      ...prev,
      experienceEntries: updatedEntries
    }));
  };

  // Handle device form change
  const handleDeviceChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>, index: number) => {
    const { name, value } = e.target;
    const error = validateDeviceField(name, value);
    
    setEmployeeForm(prev => {
      const newDeviceEntries = [...prev.deviceEntries];
      newDeviceEntries[index] = {
        ...newDeviceEntries[index],
        [name]: value,
        errors: {
          ...newDeviceEntries[index].errors,
          [name]: error
        }
      };
      return {
        ...prev,
        deviceEntries: newDeviceEntries
      };
    });
  };
  
  // Add new device entry
  const addDeviceEntry = () => {
    const newEntry: DeviceEntry = {
      id: Date.now().toString(),
      deviceName: '',
      makeModel: '',
      serialNumber: '',
      handoverDate: '',
      returnDate: '',
      condition: '',
      errors: {}
    };
    
    setEmployeeForm(prev => ({
      ...prev,
      deviceEntries: [...prev.deviceEntries, newEntry]
    }));
  };
  
  // Remove device entry
  const removeDeviceEntry = (index: number) => {
    const updatedEntries = [...employeeForm.deviceEntries];
    updatedEntries.splice(index, 1);
    
    setEmployeeForm(prev => ({
      ...prev,
      deviceEntries: updatedEntries
    }));
  };

  // Rename addEmployee to handleEmployeeSubmit to better reflect its dual purpose
  const handleEmployeeSubmit = async (): Promise<void> => {
    const errors: Record<string, string> = {};
    
    // Required fields validation
    const requiredFields = [
      'firstName', 'lastName', 'gender', 'dateOfBirth', 
      'cnicNumber', 'mobileNumber', 'designation', 
      'department', 'joinDate'
    ];
    
    requiredFields.forEach(field => {
      // Type-safe approach to access fields
      let value: any;
      switch(field) {
        case 'firstName': value = employeeForm.firstName; break;
        case 'lastName': value = employeeForm.lastName; break;
        case 'gender': value = employeeForm.gender; break;
        case 'dateOfBirth': value = employeeForm.dateOfBirth; break;
        case 'cnicNumber': value = employeeForm.cnicNumber; break;
        case 'mobileNumber': value = employeeForm.mobileNumber; break;
        case 'designation': value = employeeForm.designation; break;
        case 'department': value = employeeForm.department; break;
        case 'joinDate': value = employeeForm.joinDate; break;
        default: value = '';
      }
      
      const error = validateField(field, value);
      if (error) {
        errors[field] = error;
      }
    });
    
    // Set errors and return if validation fails
    if (Object.keys(errors).length > 0) {
      setEmployeeForm(prev => ({
        ...prev,
        errors: {
          ...prev.errors,
          ...errors
        }
      }));
      
      // Scroll to the first error
      const firstErrorField = document.querySelector('[data-error="true"]');
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      
      toast.error('Please fill in all required fields correctly');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      console.log(`${isEditMode ? 'Updating' : 'Creating'} employee...`);
      
      // Debug log to check statusDate is included in form submission
      console.log('Form data being submitted:', {
        statusDate: employeeForm.statusDate,
        status: employeeForm.status,
        isNonActiveStatus: employeeForm.status !== 'active',
        hasStatusDate: !!employeeForm.statusDate
      });
      
      // Helper function to convert date fields to either null or string in API request
      // This avoids TypeScript errors but lets us transform data before API call
      const processDateFields = (data: any) => {
        const processedData = { ...data };
        // Convert empty date strings to null in a way backend will understand
        if (!processedData.healthInsuranceExpiryDate) processedData.healthInsuranceExpiryDate = null;
        if (!processedData.lifeInsuranceExpiryDate) processedData.lifeInsuranceExpiryDate = null;
        if (!processedData.nextReviewDate) processedData.nextReviewDate = null;
        if (!processedData.probationEndDate) processedData.probationEndDate = null;
        if (!processedData.contractStartDate) processedData.contractStartDate = null;
        if (!processedData.contractEndDate) processedData.contractEndDate = null;
        if (!processedData.passportExpiryDate) processedData.passportExpiryDate = null;
        if (!processedData.drivingLicenseExpiryDate) processedData.drivingLicenseExpiryDate = null;
        if (!processedData.handingOverDate) processedData.handingOverDate = null;
        if (!processedData.returnDate) processedData.returnDate = null;
        if (!processedData.spouseDateOfBirth) processedData.spouseDateOfBirth = null;
        if (!processedData.statusDate) processedData.statusDate = null;
        
        // Ensure bloodType and bloodGroup are synced
        if (processedData.bloodType || processedData.bloodGroup) {
          const bloodValue = processedData.bloodType || processedData.bloodGroup;
          processedData.bloodType = bloodValue;
          processedData.bloodGroup = bloodValue;
        }
        
        return processedData;
      };
      
      // Create or update employee based on edit mode
      if (isEditMode && actualEmployeeId) {
        console.log(`Updating employee with ID ${actualEmployeeId}`);
        const response = await EmployeeService.updateEmployee(actualEmployeeId, processDateFields(employeeForm));
        
        if (response.error) {
          toast.error(`Failed to update employee: ${response.error}`);
          setIsSubmitting(false);
          return;
        }
        
        // Handle file upload if a new profile image is selected
        if (employeeForm.profileImage) {
          console.log('Uploading new profile image for existing employee');
          const formData = new FormData();
          formData.append('file', employeeForm.profileImage);
          formData.append('isProfileImage', 'true');
          formData.append('documentType', 'Profile Photo');
          
          const uploadResponse = await EmployeeService.uploadEmployeeDocuments(
            actualEmployeeId, 
            formData
          );
          
          if (uploadResponse.error) {
            console.error('Error uploading profile image:', uploadResponse.error);
            toast.error('Employee updated, but profile image upload failed.');
          }
        }
        
        // Reload the employee data to get the latest values from server
        await fetchEmployeeData();
        
        toast.success('Employee updated successfully');
      } else {
        // Create new employee
        console.log('Creating new employee');
        const response = await EmployeeService.createEmployee(processDateFields(employeeForm));
        
        if (response.error) {
          toast.error(`Failed to create employee: ${response.error}`);
          setIsSubmitting(false);
          return;
        }
    
        // Handle file upload if a profile image exists
        if (employeeForm.profileImage && response.data?.employee?.id) {
          console.log('Uploading profile image for new employee');
          const formData = new FormData();
          formData.append('file', employeeForm.profileImage);
          formData.append('isProfileImage', 'true');
          formData.append('documentType', 'Profile Photo');
          
          const uploadResponse = await EmployeeService.uploadEmployeeDocuments(
            response.data.employee.id.toString(), 
            formData
          );
          
          if (uploadResponse.error) {
            console.error('Error uploading profile image:', uploadResponse.error);
            toast.error('Employee created, but profile image upload failed.');
          }
        }
        
        toast.success('Employee created successfully');
      }
      
      // Navigate to employee list with a short delay to allow toast to be seen
      setTimeout(() => {
        navigate('/hr/employees');
      }, 1000);
      
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Cancel and go back to employees list
  const handleCancel = () => {
    navigate('/hr/employees');
  };
  
  // Render personal info form
  const renderPersonalInfoForm = () => {
    return (
      <div>
        {/* Profile Image Upload Section */}
      <div className="mb-8">
          <label className="flex items-center text-lg font-medium text-gray-700 dark:text-gray-300 mb-3">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg mr-2">
              <Camera className="w-6 h-6 text-purple-600 dark:text-purple-400" />
        </div>
            Profile Image
          </label>
          <div>
            {/* Clickable Image Area */}
            <div 
              className="w-40 h-40 border-2 border-dashed border-purple-300 dark:border-purple-600 rounded-lg overflow-hidden bg-purple-50 dark:bg-purple-900/20 flex items-center justify-center hover:border-purple-400 dark:hover:border-purple-500 transition-colors cursor-pointer relative group mb-2"
              onClick={triggerFileInput}
            >
              {employeeForm.profileImagePreview ? (
                <>
                <img 
                  src={employeeForm.profileImagePreview} 
                    alt="Profile preview"
                  className="w-full h-full object-cover"
                />
                  {/* Remove Icon Overlay */}
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      setEmployeeForm(prev => ({ ...prev, profileImagePreview: '', profileImage: null }));
                    }}
                    className="absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors z-10"
                    title="Remove photo"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </>
              ) : (
                <div className="flex flex-col items-center p-4 text-center">
                  <User className="w-16 h-16 text-purple-300 dark:text-purple-500 mb-2" />
                  <span className="text-sm text-purple-500 dark:text-purple-400">
                    Click to upload profile photo
                  </span>
                </div>
              )}
              {/* Upload Icon Overlay on hover (only if no image) */}
              {!employeeForm.profileImagePreview && (
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-opacity flex items-center justify-center">
                  <Upload className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
            </div>
              )}
            </div>
            
            {/* Image Recommendations (Below the box) */}
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Recommended: Square image, at least 300x300px
            </div>
            
            {/* Hidden File Input */}
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              onChange={handleImageChange}
            />
          </div>
        </div>
        
        {/* Form Fields Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <UserCircle className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              First Name
            </label>
              <input
                type="text"
                name="firstName"
                value={employeeForm.firstName}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="Enter first name"
              />
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <UserCircle className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Middle Name
            </label>
              <input
                type="text"
                name="middleName"
                value={employeeForm.middleName}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="Enter middle name (optional)"
              />
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <UserCircle className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Last Name
            </label>
              <input
                type="text"
                name="lastName"
                value={employeeForm.lastName}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="Enter last name"
              />
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <UserCircle className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Father Name
            </label>
              <input
                type="text"
                name="fatherName"
                value={employeeForm.fatherName}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="Enter father name"
              />
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Users2 className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Gender
            </label>
              <select
                name="gender"
                value={employeeForm.gender}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">Select Gender</option>
              <option value="Male">Male</option>
              <option value="Female">Female</option>
              <option value="Other">Other</option>
              </select>
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Calendar className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Date of Birth
            </label>
              <input
                type="date"
                name="dateOfBirth"
                value={employeeForm.dateOfBirth}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Heart className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Religion
            </label>
            <input
              type="text"
                name="religion"
                value={employeeForm.religion}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="Enter religion"
            />
        </div>
        
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Fingerprint className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              CNIC Number
            </label>
              <input
                type="text"
                name="cnicNumber"
                value={employeeForm.cnicNumber}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="Enter CNIC number"
              />
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Calendar className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              CNIC Expiry Date
            </label>
              <input
                type="date"
                name="cnicExpiryDate"
                value={employeeForm.cnicExpiryDate}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Flag className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Nationality
            </label>
              <input
                type="text"
                name="nationality"
                value={employeeForm.nationality}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="Enter nationality"
              />
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Users className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Marital Status
            </label>
              <select
                name="maritalStatus"
                value={employeeForm.maritalStatus}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">Select Marital Status</option>
              <option value="Single">Single</option>
              <option value="Married">Married</option>
              <option value="Divorced">Divorced</option>
              <option value="Widowed">Widowed</option>
              </select>
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Heart className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Blood Type
            </label>
              <select
                name="bloodType"
                value={employeeForm.bloodType}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">Select Blood Type (Optional)</option>
              <option value="A+">A+</option>
              <option value="A-">A-</option>
              <option value="B+">B+</option>
              <option value="B-">B-</option>
              <option value="AB+">AB+</option>
              <option value="AB-">AB-</option>
              <option value="O+">O+</option>
              <option value="O-">O-</option>
              </select>
          </div>
        </div>
      </div>
    );
  };
  
  // Render contact info form
  const renderContactInfoForm = () => {
    return (
      <div className="mb-6 p-4 border rounded-lg border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 transition-colors duration-300">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Smartphone className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Mobile Number*
            </label>
              <input
              type="tel"
                name="mobileNumber"
                value={employeeForm.mobileNumber}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="Enter mobile number"
                required
              />
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Phone className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Official Number
            </label>
              <input
              type="tel"
                name="officialNumber"
                value={employeeForm.officialNumber}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="Enter official number"
              />
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Mail className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Official Email*
            </label>
              <input
                type="email"
                name="officialEmail"
                value={employeeForm.officialEmail}
                onChange={handleFormChange}
                onBlur={(e) => {
                  const error = validateField('officialEmail', e.target.value);
                  setEmployeeForm(prev => ({
                    ...prev,
                    errors: {
                      ...prev.errors,
                      officialEmail: error
                    }
                  }));
                }}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                  employeeForm.errors.officialEmail ? 'border-red-500 dark:border-red-400' : ''
                }`}
                placeholder="<EMAIL> (Optional)"
                required
              />
              {employeeForm.errors.officialEmail && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.officialEmail}</p>
              )}
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Mail className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Personal Email
              <span className="text-red-500 ml-1">*</span>
            </label>
              <input
                type="email"
                name="personalEmail"
                value={employeeForm.personalEmail}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="<EMAIL>"
              />
              {employeeForm.errors.personalEmail && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.personalEmail}</p>
              )}
            </div>
            
          <div className="md:col-span-2">
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Home className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Permanent Address
            </label>
              <textarea
                name="permanentAddress"
                value={employeeForm.permanentAddress}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                rows={2}
              placeholder="Enter permanent address"
              />
            </div>
            
          <div className="md:col-span-2">
              <div className="flex justify-between items-center mb-1">
              <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                <Home className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Current Address
              </label>
                <button 
                  type="button"
                  onClick={copyAddress}
                  className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                >
                  Same as Permanent Address
                </button>
              </div>
              <textarea
                name="currentAddress"
                value={employeeForm.currentAddress}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                rows={2}
              placeholder="Enter current address"
              />
          </div>
        </div>
        
        <h3 className="text-md font-medium text-gray-900 dark:text-white mb-4">Emergency Contact</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <UserCircle className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Emergency Contact Name
            </label>
              <input
                type="text"
                name="emergencyContactName"
                value={employeeForm.emergencyContactName}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="Enter name"
              />
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Phone className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Emergency Contact Phone
            </label>
              <input
              type="tel"
                name="emergencyContactPhone"
                value={employeeForm.emergencyContactPhone}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="Enter phone number"
              />
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <HeartHandshake className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Emergency Contact Relationship
              <span className="text-red-500 ml-1">*</span>
            </label>
              <input
                type="text"
                name="emergencyContactRelationship"
                value={employeeForm.emergencyContactRelationship}
                onChange={handleFormChange}
                onBlur={(e) => {
                  const error = validateField('emergencyContactRelationship', e.target.value);
                  setEmployeeForm(prev => ({
                    ...prev,
                    errors: {
                      ...prev.errors,
                      emergencyContactRelationship: error
                    }
                  }));
                }}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                  employeeForm.errors.emergencyContactRelationship ? 'border-red-500 dark:border-red-400' : ''
                }`}
                placeholder="e.g., Father, Mother, Spouse, Sibling"
              />
              {employeeForm.errors.emergencyContactRelationship && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.emergencyContactRelationship}</p>
              )}
          </div>
        </div>
      </div>
    );
  };
  
  // Render educational info form
  const renderEducationalInfoForm = () => {
    return (
      <div>
        {employeeForm.educationEntries.map((education, index) => (
          <div key={education.id} className="mb-6 p-4 border rounded-lg border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 relative transition-colors duration-300">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-md font-medium text-gray-900 dark:text-white">Education Entry #{index + 1}</h3>
                {employeeForm.educationEntries.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeEducationEntry(index)}
                  className="absolute top-3 right-3 text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 p-1 rounded-full hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors duration-300"
                  title="Remove Education Entry"
                  >
                    <Trash2 size={16} />
                  </button>
                )}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <GraduationCap className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Education Level
                </label>
                <select
                  name="educationLevel"
                  value={education.educationLevel}
                  onChange={(e) => handleEducationChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                    education.errors?.educationLevel ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                >
                  <option value="">Select Education Level</option>
                  {educationLevelOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
                {education.errors?.educationLevel && (
                  <p className="mt-1 text-sm text-red-500 dark:text-red-400">{education.errors.educationLevel}</p>
                )}
              </div>
              
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <ScrollText className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Degree Title
                </label>
                <input
                  type="text"
                  name="degree"
                  value={education.degree}
                  onChange={(e) => handleEducationChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    education.errors?.degree ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                  placeholder="e.g., Bachelor of Science"
                />
                {education.errors?.degree && (
                  <p className="mt-1 text-sm text-red-500 dark:text-red-400">{education.errors.degree}</p>
                )}
              </div>
              
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Target className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Major/Field of Study
                </label>
                <input
                  type="text"
                  name="major"
                  value={education.major}
                  onChange={(e) => handleEducationChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    education.errors?.major ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                  placeholder="e.g., Computer Science"
                />
                {education.errors?.major && (
                  <p className="mt-1 text-sm text-red-500 dark:text-red-400">{education.errors.major}</p>
                )}
              </div>
              
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Building className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Institution Name
                </label>
                <input
                  type="text"
                  name="institution"
                  value={education.institution}
                  onChange={(e) => handleEducationChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    education.errors?.institution ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                  placeholder="e.g., University of ABC"
                />
                {education.errors?.institution && (
                  <p className="mt-1 text-sm text-red-500 dark:text-red-400">{education.errors.institution}</p>
                )}
              </div>
              
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <CalendarDays className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Graduation Year
                </label>
                <input
                  type="number"
                  name="graduationYear"
                  value={education.graduationYear}
                  onChange={(e) => handleEducationChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    education.errors?.graduationYear ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                  min="1950"
                  max={new Date().getFullYear() + 5}
                  placeholder="e.g., 2020"
                />
                {education.errors?.graduationYear && (
                  <p className="mt-1 text-sm text-red-500 dark:text-red-400">{education.errors.graduationYear}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Award className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Grade/CGPA
                </label>
                <input
                  type="text"
                  name="grade"
                  value={education.grade}
                  onChange={(e) => handleEducationChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    education.errors?.grade ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                  placeholder="e.g., 3.5 or A+"
                />
                {education.errors?.grade && (
                  <p className="mt-1 text-sm text-red-500 dark:text-red-400">{education.errors.grade}</p>
                )}
              </div>
            </div>
          </div>
        ))}
                <button
                  type="button"
                  onClick={addEducationEntry}
          className="flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium text-sm transition-colors duration-300"
                >
          <Plus size={16} className="mr-1" />
          Add Another Education
                </button>
      </div>
    );
  };
  
  // Render experience form
  const renderExperienceForm = () => {
    return (
      <div className="space-y-4">
        {employeeForm.experienceEntries.map((entry, index) => (
          <div key={entry.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 space-y-4 relative bg-white dark:bg-gray-800 transition-colors duration-300"> {/* Add relative positioning back */}
            <div className="flex justify-between items-center mb-4"> {/* Add header for title and delete button */} 
              <h3 className="text-md font-medium text-gray-900 dark:text-white">Experience #{index + 1}</h3>
                {employeeForm.experienceEntries.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeExperienceEntry(index)}
                  className="absolute top-3 right-3 text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 p-1 rounded-full hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors duration-300"
                  title="Remove Experience Entry"
                  >
                    <Trash2 size={16} />
                  </button>
                )}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> {/* Add flex and icon */}
                  <Building2 className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Company Name
                </label>
                <input
                  type="text"
                  name="companyName"
                  value={entry.companyName}
                  onChange={(e) => handleExperienceChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    entry.errors?.companyName ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                />
                {entry.errors?.companyName && (
                  <p className="text-red-500 dark:text-red-400 text-sm mt-1">{entry.errors.companyName}</p>
                )}
              </div>
              
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> {/* Add flex and icon */}
                  <Briefcase className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Job Title
                </label>
                <input
                  type="text"
                  name="jobTitle"
                  value={entry.jobTitle}
                  onChange={(e) => handleExperienceChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    entry.errors?.jobTitle ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                />
                {entry.errors?.jobTitle && (
                  <p className="text-red-500 dark:text-red-400 text-sm mt-1">{entry.errors.jobTitle}</p>
                )}
              </div>
              
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> {/* Add flex and icon */}
                  <CalendarDays className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Start Date
                </label>
                <input
                  type="date"
                  name="startDate"
                  value={entry.startDate}
                  onChange={(e) => handleExperienceChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                    entry.errors?.startDate ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                />
                {entry.errors?.startDate && (
                  <p className="text-red-500 dark:text-red-400 text-sm mt-1">{entry.errors.startDate}</p>
                )}
              </div>
              
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> {/* Add flex and icon */}
                    <CalendarCheck2 className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                    End Date
                  </label>
                <input
                  type="date"
                  name="endDate"
                  value={entry.endDate}
                  onChange={(e) => handleExperienceChange(e, index)}
                  disabled={entry.currentlyWorking}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-600 disabled:text-gray-500 dark:disabled:text-gray-400 ${
                    entry.errors?.endDate ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                />
                {entry.errors?.endDate && (
                  <p className="text-red-500 dark:text-red-400 text-sm mt-1">{entry.errors.endDate}</p>
                )}
              </div>
              
              <div className="md:col-span-2">
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> {/* Add flex and icon */}
                  <NotebookText className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Job Description
                </label>
                <textarea
                  name="jobDescription"
                  value={entry.jobDescription}
                  onChange={(e) => handleExperienceChange(e, index)}
                  rows={3}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    entry.errors?.jobDescription ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                />
                {entry.errors?.jobDescription && (
                  <p className="text-red-500 dark:text-red-400 text-sm mt-1">{entry.errors.jobDescription}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    name="currentlyWorking"
                    checked={entry.currentlyWorking}
                    onChange={(e) => handleExperienceChange(e, index)}
                    className="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">I am currently working here</span>
                </label>
              </div>
            </div>
          </div>
        ))}
              <button
                type="button"
                onClick={addExperienceEntry}
          className="flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium text-sm mt-2 transition-colors duration-300"
              >
          <Plus size={16} className="mr-1" />
          Add Another Experience
              </button>
      </div>
    );
  };
  
  // Render skills & certifications form
  const renderSkillsCertificationsForm = () => {
    return (
      <div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Zap className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Professional Skills
            </label>
              <textarea
                name="professionalSkills"
                value={employeeForm.professionalSkills}
              onChange={handleFormChange} // Uses the main handler which calls validateField
              className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                employeeForm.errors.professionalSkills ? 'border-red-500 dark:border-red-400' : ''
              }`}
                rows={3}
              placeholder="e.g., Project Management, Leadership, Communication"
              maxLength={1000} // Optional: Add browser-level max length
              />
            {employeeForm.errors.professionalSkills && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.professionalSkills}</p>
            )}
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Terminal className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Technical Skills
            </label>
              <textarea
                name="technicalSkills"
                value={employeeForm.technicalSkills}
                onChange={handleFormChange}
              className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                employeeForm.errors.technicalSkills ? 'border-red-500 dark:border-red-400' : ''
              }`}
                rows={3}
              placeholder="e.g., JavaScript, React, SQL"
              maxLength={1000}
              />
            {employeeForm.errors.technicalSkills && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.technicalSkills}</p>
            )}
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <FileBadge className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Certifications
            </label>
              <textarea
                name="certifications"
                value={employeeForm.certifications}
                onChange={handleFormChange}
              className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                employeeForm.errors.certifications ? 'border-red-500 dark:border-red-400' : ''
              }`}
                rows={3}
              placeholder="e.g., PMP, AWS Certified Solutions Architect"
              maxLength={1000}
              />
            {employeeForm.errors.certifications && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.certifications}</p>
            )}
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Languages className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Languages
            </label>
              <textarea
                name="languages"
                value={employeeForm.languages}
                onChange={handleFormChange}
              className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                employeeForm.errors.languages ? 'border-red-500 dark:border-red-400' : ''
              }`}
                rows={3}
              placeholder="e.g., English (Fluent), Urdu (Native)"
              maxLength={1000}
              />
            {employeeForm.errors.languages && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.languages}</p>
            )}
          </div>
        </div>
      </div>
    );
  };
  
  // Render job details form
  const renderJobDetailsForm = () => {
    return (
      <div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div> {/* Added Employee ID Field */}
              <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <Fingerprint className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" /> {/* Using Fingerprint icon for ID */}
                Employee ID*
              </label>
              <input
                type="text"
                name="employeeId"
                value={employeeForm.employeeId}
                onChange={handleFormChange}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                  employeeForm.errors.employeeId ? 'border-red-500 dark:border-red-400' : ''
                }`}
                placeholder="e.g., A123, EmpID-1" // Updated placeholder
                required
                maxLength={15} // Add maxLength to match validation
              />
              {employeeForm.errors.employeeId && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.employeeId}</p>
              )}
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Badge className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Designation*
            </label>
              <input
                type="text"
                name="designation"
                value={employeeForm.designation}
                onChange={handleFormChange}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                  employeeForm.errors.designation ? 'border-red-500 dark:border-red-400' : ''
                }`}
              placeholder="Enter designation"
                required
              />
              {employeeForm.errors.designation && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.designation}</p>
              )}
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Network className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Department*
            </label>
              <select
                name="department"
                value={employeeForm.department}
                onChange={handleFormChange}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                  employeeForm.errors.department ? 'border-red-500 dark:border-red-400' : ''
                }`}
                required
              >
                <option value="">Select Department</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
              {employeeForm.errors.department && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.department}</p>
              )}
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <FolderKanban className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Project
            </label>
              <select
                name="project"
                value={employeeForm.project}
                onChange={handleFormChange}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                  employeeForm.errors.project ? 'border-red-500 dark:border-red-400' : '' // Added error check
                }`}
              >
                <option value="">Select Project</option>
                {PROJECTS.map(project => (
                  <option key={project} value={project}>{project}</option>
                ))}
              </select>
               {employeeForm.errors.project && ( // Added error message display
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.project}</p>
              )}
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <MapPin className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Location
            </label>
              <select
                name="location"
                value={employeeForm.location}
                onChange={handleFormChange}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                  employeeForm.errors.location ? 'border-red-500 dark:border-red-400' : '' // Added error check
                }`}
                disabled={!employeeForm.project} 
              >
                <option value="">Select Location</option>
                {employeeForm.project && PROJECT_LOCATIONS[employeeForm.project]?.map(loc => (
                  <option key={loc} value={loc}>{loc}</option>
                ))}
              </select>
              {employeeForm.errors.location && ( // Added error message display
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.location}</p>
              )}
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <TrendingUp className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Employee Level
            </label>
              <select
                name="employeeLevel"
                value={employeeForm.employeeLevel}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                // No specific validation added here, assuming select is sufficient
              >
                <option value="">Select Employee Level</option>
                {employeeLevelOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Clock className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Employment Type
            </label>
              <select
                name="employmentType"
                value={employeeForm.employmentType}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">Select Employment Type</option>
                {employmentTypeOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <ShieldCheck className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Employment Status
            </label>
              <select
                name="employmentStatus"
                value={employeeForm.employmentStatus}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">Select Employment Status</option>
                {employmentStatusOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>

            {/* Moved Date of Joining here */}
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <CalendarPlus className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Date of Joining*
              </label>
              <input
                type="date"
                name="joinDate"
                value={employeeForm.joinDate}
                onChange={handleFormChange}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                  employeeForm.errors.joinDate ? 'border-red-500 dark:border-red-400' : ''
                }`}
                required
              />
               {employeeForm.errors.joinDate && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.joinDate}</p>
              )}
            </div>

            {/* Conditional Contract Start Date Field */}
            {employeeForm.employmentType === 'Contract' && (
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <CalendarRange className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Contract Start Date
                </label>
                <input
                  type="date"
                  name="contractStartDate"
                  value={employeeForm.contractStartDate}
                  onChange={handleFormChange}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                    employeeForm.errors.contractStartDate ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                />
                {employeeForm.errors.contractStartDate && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.contractStartDate}</p>
                )}
              </div>
            )}

            {/* Conditional Contract End Date Field */}
            {employeeForm.employmentType === 'Contract' && (
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <CalendarRange className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Contract End Date
                </label>
                <input
                  type="date"
                  name="contractEndDate"
                  value={employeeForm.contractEndDate}
                  onChange={handleFormChange}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                    employeeForm.errors.contractEndDate ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                />
                {employeeForm.errors.contractEndDate && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.contractEndDate}</p>
                )}
              </div>
            )}

            {/* Conditional Probation End Date Field */}
            {employeeForm.employmentStatus === 'Probation' && (
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <CalendarRange className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Probation End Date
                </label>
                <input
                  type="date"
                  name="probationEndDate"
                  value={employeeForm.probationEndDate}
                  onChange={handleFormChange}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                {/* Add validation message display if needed */}
              </div>
            )}
            
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <AlarmClock className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Notice Period (days)
              </label>
              <input
                type="number"
                name="noticePeriod"
                value={employeeForm.noticePeriod}
                onChange={handleFormChange}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                  employeeForm.errors.noticePeriod ? 'border-red-500 dark:border-red-400' : ''
                }`}
              placeholder="Enter days"
                min="0"
              />
              {employeeForm.errors.noticePeriod && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.noticePeriod}</p>
              )}
            </div>
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <UserCog className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Reporting To
            </label>
              <input
                type="text"
                name="reportingTo"
                value={employeeForm.reportingTo}
                onChange={handleFormChange}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                  employeeForm.errors.reportingTo ? 'border-red-500 dark:border-red-400' : ''
                }`}
              placeholder="Enter manager name/ID"
              />
               {employeeForm.errors.reportingTo && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.reportingTo}</p>
              )}
            </div>
            
            {/* Professional Information Fields Start */}
            <div className="md:col-span-2">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <Clock className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Shift Type
                </label>
                <select
                  name="shiftType"
                  value={employeeForm.shiftType}
                  onChange={handleFormChange}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="">Select Shift Type</option>
                  {shiftTypeOptions.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              {employeeForm.shiftType === 'Fixed' && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                    <CalendarCheck2 className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                    Select Fixed Shift
                  </label>
                  <select
                    name="workSchedule"
                    value={(employeeForm.workSchedule !== '10:00 AM to 6:00 PM' && employeeForm.workSchedule !== '11:00 AM to 7:00 PM') && employeeForm.workSchedule ? 'Custom' : employeeForm.workSchedule}
                    onChange={(e) => {
                      const value = e.target.value;
                      setEmployeeForm(prev => ({
                        ...prev,
                        workSchedule: value === 'Custom' ? '' : value
                      }));
                    }}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white mb-2"
                  >
                    <option value="">Select Shift Time</option>
                    <option value="10:00 AM to 6:00 PM">10:00 AM to 6:00 PM</option>
                    <option value="11:00 AM to 7:00 PM">11:00 AM to 7:00 PM</option>
                    <option value="Custom">Custom Fixed Schedule</option>
                  </select>

                  {(employeeForm.workSchedule === '' || (employeeForm.workSchedule !== '10:00 AM to 6:00 PM' && employeeForm.workSchedule !== '11:00 AM to 7:00 PM')) && (
                    <textarea
                      name="workSchedule"
                      value={employeeForm.workSchedule}
                      onChange={handleFormChange}
                      className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 mt-2"
                      rows={3}
                      placeholder="Enter custom fixed schedule details (e.g., 9:30 AM to 5:30 PM)"
                    />
                  )}
                </div>
              )}

              {(employeeForm.shiftType === 'Rotational' || employeeForm.shiftType === 'Flexible') && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                    <CalendarCheck2 className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                    Work Schedule Details
                  </label>
                  <textarea
                    name="workSchedule"
                    value={employeeForm.workSchedule}
                    onChange={handleFormChange}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                    rows={3}
                    placeholder={employeeForm.shiftType === 'Rotational' ? 
                      "e.g., Week 1: 10:00 AM to 6:00 PM, Week 2: 11:00 AM to 7:00 PM" :
                      "e.g., Core hours 11:00 AM to 4:00 PM, flexible start/end times"}
                  />
                </div>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                <CalendarCheck2 className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Next Performance Review Date
              </label>
              <input
                type="date"
                name="nextReviewDate"
                value={employeeForm.nextReviewDate}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                <GraduationCap className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Training Requirements
              </label>
              <textarea
                name="trainingRequirements"
                value={employeeForm.trainingRequirements}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                rows={3}
                placeholder="List any required training or certifications"
              />
            </div>

            <div className="flex items-center mt-2">
              <input
                type="checkbox"
                id="remoteWorkEligible"
                name="remoteWorkEligible"
                checked={employeeForm.remoteWorkEligible}
                onChange={(e) => {
                  setEmployeeForm(prev => ({
                    ...prev,
                    remoteWorkEligible: e.target.checked
                  }));
                }}
                className="h-4 w-4 text-blue-600 dark:text-blue-400 mr-2 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500 dark:focus:ring-blue-400"
              />
              <label htmlFor="remoteWorkEligible" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <Laptop className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Eligible for Remote Work
              </label>
            </div>
            {/* Professional Information Fields End */}
            
            <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Activity className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Status
            </label>
              <select
                name="status"
                value={employeeForm.status}
                onChange={(e) => {
                  const newStatus = e.target.value as 'active' | 'inactive' | 'onleave' | 'resigned' | 'terminated' | 'suspended' | 'retired';
                  console.log('Status changed to:', newStatus);
                  
                  // Set today's date as statusDate if changing from active to non-active
                  if (employeeForm.status === 'active' && newStatus !== 'active') {
                    // Create today's date in local timezone
                    const today = new Date();
                    const year = today.getFullYear();
                    const month = String(today.getMonth() + 1).padStart(2, '0');
                    const day = String(today.getDate()).padStart(2, '0');
                    const localDateString = `${year}-${month}-${day}`;
                    
                    console.log('Setting status date to local date:', localDateString);
                    
                    setEmployeeForm(prev => ({
                      ...prev,
                      status: newStatus,
                      statusDate: localDateString
                    }));
                  } else {
                    handleFormChange(e);
                  }
                }}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required // Status is usually required
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
              {/* No error message needed for select with required if default option is disabled or handled */}
          </div>
          
          {/* Status Date Field - Always show it regardless of status */}
          <div>
            <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
              <CalendarRange className="w-4 h-4 mr-2 text-gray-500" />
              Status Date
            </label>
            <input
              type="date"
              name="statusDate"
              value={employeeForm.statusDate || ''}
              onChange={handleFormChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
    );
  };
  
  // Render compensation & benefits form
  const renderCompensationForm = () => {
    // Calculate salary breakdown
    const calculateBreakdown = (total: string) => {
      const totalValue = parseFloat(total) || 0;
      const basic = (totalValue * 0.65).toFixed(2);
      const hra = (totalValue * 0.20).toFixed(2);
      const ma = (totalValue * 0.10).toFixed(2);
      const other = (totalValue * 0.05).toFixed(2);
      
      return { basic, hra, ma, other };
    };
    
    // Add debug logging for compensation form rendering
    console.log('Rendering compensation form with values:', {
      totalSalary: employeeForm.totalSalary,
      salaryTier: employeeForm.salaryTier,
      paymentMode: employeeForm.paymentMode,
      cashAmount: employeeForm.cashAmount,
      bankAmount: employeeForm.bankAmount
    });
    
    const salaryBreakdown = calculateBreakdown(employeeForm.totalSalary);
    
    return (
      <div>
        <div className="mb-6 p-4 border rounded-lg border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 relative transition-colors duration-300">
          <div className="mb-4">
            <h3 className="text-md font-medium text-gray-900 dark:text-white">Salary Details</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <Wallet className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Total Salary*
              </label>
              <input
                type="number"
                name="totalSalary"
                value={employeeForm.totalSalary}
                onChange={handleFormChange}
                onBlur={(e) => {
                  const error = validateField('totalSalary', e.target.value);
                  setEmployeeForm(prev => ({
                    ...prev,
                    errors: {
                      ...prev.errors,
                      totalSalary: error
                    }
                  }));
                }}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                  employeeForm.errors.totalSalary ? 'border-red-500 dark:border-red-400' : ''
                }`}
                placeholder="Enter total salary amount"
                min="0"
                required
              />
              {employeeForm.errors.totalSalary && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.totalSalary}</p>
              )}
            </div>
            
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <Layers className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Salary Tier
              </label>
              <select
                name="salaryTier"
                value={employeeForm.salaryTier}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">Select Salary Tier</option>
                {salaryTierOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <Layers className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Salary Trench
              </label>
              <select
                name="salaryTrench"
                value={employeeForm.salaryTrench}
                onChange={handleFormChange}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">Select Trench</option>
                <option value="1st">1st Trench</option>
                <option value="2nd">2nd Trench</option>
                <option value="3rd">3rd Trench</option>
              </select>
            </div>
            
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <CreditCard className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Payment Mode*
              </label>
              <select
                name="paymentMode"
                value={employeeForm.paymentMode}
                onChange={handleFormChange}
                onBlur={(e) => {
                  const error = validateField('paymentMode', e.target.value);
                  setEmployeeForm(prev => ({
                    ...prev,
                    errors: {
                      ...prev.errors,
                      paymentMode: error
                    }
                  }));
                }}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                  employeeForm.errors.paymentMode ? 'border-red-500 dark:border-red-400' : ''
                }`}
                required
              >
                <option value="">Select Payment Mode</option>
                {paymentModeOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
              {employeeForm.errors.paymentMode && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.paymentMode}</p>
              )}
            </div>
            
            {employeeForm.paymentMode === 'Cash' || employeeForm.paymentMode === 'Both' ? (
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Coins className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Cash Amount*
                </label>
                <input
                  type="number"
                  name="cashAmount"
                  value={employeeForm.cashAmount}
                  onChange={handleFormChange}
                  onBlur={(e) => {
                    const error = validateField('cashAmount', e.target.value);
                    setEmployeeForm(prev => ({
                      ...prev,
                      errors: {
                        ...prev.errors,
                        cashAmount: error
                      }
                    }));
                  }}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    employeeForm.errors.cashAmount ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                  placeholder="Enter cash amount"
                  min="0"
                  required
                />
                {employeeForm.errors.cashAmount && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.cashAmount}</p>
                )}
              </div>
            ) : null}
            
            {employeeForm.paymentMode === 'Bank' || employeeForm.paymentMode === 'Both' ? (
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <LandmarkIcon className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Bank Amount*
                </label>
                <input
                  type="number"
                  name="bankAmount"
                  value={employeeForm.bankAmount}
                  onChange={handleFormChange}
                  onBlur={(e) => {
                    const error = validateField('bankAmount', e.target.value);
                    setEmployeeForm(prev => ({
                      ...prev,
                      errors: {
                        ...prev.errors,
                        bankAmount: error
                      }
                    }));
                  }}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    employeeForm.errors.bankAmount ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                  placeholder="Enter bank amount"
                  min="0"
                  required
                />
                {employeeForm.errors.bankAmount && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.bankAmount}</p>
                )}
              </div>
            ) : null}
            
            <div className="lg:col-span-3 bg-gray-100 dark:bg-gray-700 p-3 rounded">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Salary Breakdown (65%-20%-10%-5%)</div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                <div className="bg-white dark:bg-gray-600 p-2 rounded shadow-sm">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Basic (65%)</span>
                  <p className="font-medium text-gray-900 dark:text-white">{salaryBreakdown.basic}</p>
                </div>
                <div className="bg-white dark:bg-gray-600 p-2 rounded shadow-sm">
                  <span className="text-xs text-gray-500 dark:text-gray-400">HRA (20%)</span>
                  <p className="font-medium text-gray-900 dark:text-white">{salaryBreakdown.hra}</p>
                </div>
                <div className="bg-white dark:bg-gray-600 p-2 rounded shadow-sm">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Medical (10%)</span>
                  <p className="font-medium text-gray-900 dark:text-white">{salaryBreakdown.ma}</p>
                </div>
                <div className="bg-white dark:bg-gray-600 p-2 rounded shadow-sm">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Other (5%)</span>
                  <p className="font-medium text-gray-900 dark:text-white">{salaryBreakdown.other}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mb-6 p-4 border rounded-lg border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 relative transition-colors duration-300">
          <div className="mb-4">
            <h3 className="text-md font-medium text-gray-800 dark:text-white">Allowances</h3>
          </div>
          
          <div className="grid grid-cols-1 gap-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
                <input
                  type="checkbox"
                  id="foodAllowanceInSalary"
                  name="foodAllowanceInSalary"
                  checked={employeeForm.foodAllowanceInSalary}
                  onChange={(e) => {
                    setEmployeeForm(prev => ({
                      ...prev,
                      foodAllowanceInSalary: e.target.checked
                    }));
                  }}
                  className="h-4 w-4 text-blue-600 mr-3"
                />
                <label htmlFor="foodAllowanceInSalary" className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                  <CheckCircle className="w-4 h-4 mr-2 text-green-500 dark:text-green-400" />
                  Food Allowance added in Salary
                </label>
              </div>
              
              <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
                <input
                  type="checkbox"
                  id="fuelAllowanceInSalary"
                  name="fuelAllowanceInSalary"
                  checked={employeeForm.fuelAllowanceInSalary}
                  onChange={(e) => {
                    setEmployeeForm(prev => ({
                      ...prev,
                      fuelAllowanceInSalary: e.target.checked
                    }));
                  }}
                  className="h-4 w-4 text-blue-600 mr-3"
                />
                <label htmlFor="fuelAllowanceInSalary" className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                  <CheckCircle className="w-4 h-4 mr-2 text-green-500 dark:text-green-400" />
                  Fuel Allowance in Salary
                </label>
              </div>
              
              <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
                <input
                  type="checkbox"
                  id="foodProvidedByCompany"
                  name="foodProvidedByCompany"
                  checked={employeeForm.foodProvidedByCompany}
                  onChange={(e) => {
                    setEmployeeForm(prev => ({
                      ...prev,
                      foodProvidedByCompany: e.target.checked
                    }));
                  }}
                  className="h-4 w-4 text-blue-600 mr-3"
                />
                <label htmlFor="foodProvidedByCompany" className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                  <CheckCircle className="w-4 h-4 mr-2 text-green-500 dark:text-green-400" />
                  Food Provided by Company
                </label>
              </div>
            </div>
            
            {/* Conditional fields based on checkbox selection */}
            {(employeeForm.foodAllowanceInSalary || employeeForm.fuelAllowanceInSalary) && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-4 border-t pt-4">
                {employeeForm.foodAllowanceInSalary && (
                  <div>
                    <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      <Utensils className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                      Number of Meals
                    </label>
                    <input
                      type="number"
                      name="numberOfMeals"
                      value={employeeForm.numberOfMeals}
                      onChange={handleFormChange}
                      onBlur={(e) => {
                        const error = validateField('numberOfMeals', e.target.value);
                        setEmployeeForm(prev => ({
                          ...prev,
                          errors: {
                            ...prev.errors,
                            numberOfMeals: error
                          }
                        }));
                      }}
                      className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 ${
                        employeeForm.errors.numberOfMeals ? 'border-red-500' : ''
                      }`}
                      placeholder="Enter number of meals"
                      min="0"
                    />
                    {employeeForm.errors.numberOfMeals && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.numberOfMeals}</p>
                    )}
                  </div>
                )}
                
                {employeeForm.fuelAllowanceInSalary && (
                  <>
                    <div>
                      <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        <Fuel className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                        Fuel in Liters
                      </label>
                      <input
                        type="number"
                        name="fuelInLiters"
                        value={employeeForm.fuelInLiters}
                        onChange={handleFormChange}
                        onBlur={(e) => {
                          const error = validateField('fuelInLiters', e.target.value);
                          setEmployeeForm(prev => ({
                            ...prev,
                            errors: {
                              ...prev.errors,
                              fuelInLiters: error
                            }
                          }));
                        }}
                        className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 ${
                          employeeForm.errors.fuelInLiters ? 'border-red-500' : ''
                        }`}
                        placeholder="Enter fuel in liters"
                        min="0"
                      />
                      {employeeForm.errors.fuelInLiters && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.fuelInLiters}</p>
                      )}
                    </div>
                    
                    <div>
                      <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        <Wallet className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                        Fuel Amount
                      </label>
                      <input
                        type="number"
                        name="fuelAmount"
                        value={employeeForm.fuelAmount}
                        onChange={handleFormChange}
                        onBlur={(e) => {
                          const error = validateField('fuelAmount', e.target.value);
                          setEmployeeForm(prev => ({
                            ...prev,
                            errors: {
                              ...prev.errors,
                              fuelAmount: error
                            }
                          }));
                        }}
                        className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 ${
                          employeeForm.errors.fuelAmount ? 'border-red-500' : ''
                        }`}
                        placeholder="Enter fuel amount"
                        min="0"
                      />
                      {employeeForm.errors.fuelAmount && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.fuelAmount}</p>
                      )}
                    </div>
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };
  
  // Render bank details form
  const renderBankDetailsForm = () => {
    const isBank = employeeForm.paymentMode === 'Bank' || employeeForm.paymentMode === 'Both';
        
    return (
      <div>
        <div className="mb-6 p-4 border rounded-lg border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 relative transition-colors duration-300">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <LandmarkIcon className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Bank Name{isBank && <span className="text-red-500 ml-1">*</span>}
              </label>
              <select
                name="bankName"
                value={employeeForm.bankName}
                onChange={handleFormChange}
                onBlur={(e) => {
                  const error = validateField('bankName', e.target.value);
                  setEmployeeForm(prev => ({
                    ...prev,
                    errors: {
                      ...prev.errors,
                      bankName: error
                    }
                  }));
                }}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                  employeeForm.errors.bankName ? 'border-red-500 dark:border-red-400' : ''
                }`}
                required={isBank}
              >
                <option value="">Select Bank</option>
                {bankOptions.map(bank => (
                  <option key={bank} value={bank}>{bank}</option>
                ))}
              </select>
              {employeeForm.errors.bankName && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.bankName}</p>
              )}
            </div>
            
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <BranchIcon className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Bank Branch{isBank && <span className="text-red-500 ml-1">*</span>}
              </label>
              <input
                type="text"
                name="bankBranch"
                value={employeeForm.bankBranch}
                onChange={handleFormChange}
                onBlur={(e) => {
                  const error = validateField('bankBranch', e.target.value);
                  setEmployeeForm(prev => ({
                    ...prev,
                    errors: {
                      ...prev.errors,
                      bankBranch: error
                    }
                  }));
                }}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                  employeeForm.errors.bankBranch ? 'border-red-500 dark:border-red-400' : ''
                }`}
                placeholder="Enter bank branch"
                required={isBank}
              />
              {employeeForm.errors.bankBranch && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.bankBranch}</p>
              )}
            </div>
            
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <Hash className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Account Number{isBank && <span className="text-red-500 ml-1">*</span>}
              </label>
              <input
                type="text"
                name="accountNumber"
                value={employeeForm.accountNumber}
                onChange={handleFormChange}
                onBlur={(e) => {
                  const error = validateField('accountNumber', e.target.value);
                  setEmployeeForm(prev => ({
                    ...prev,
                    errors: {
                      ...prev.errors,
                      accountNumber: error
                    }
                  }));
                }}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                  employeeForm.errors.accountNumber ? 'border-red-500 dark:border-red-400' : ''
                }`}
                placeholder="Enter account number"
                required={isBank}
              />
              {employeeForm.errors.accountNumber && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.accountNumber}</p>
              )}
            </div>
            
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <UserIcon className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Account Title{isBank && <span className="text-red-500 ml-1">*</span>}
              </label>
              <input
                type="text"
                name="accountTitle"
                value={employeeForm.accountTitle}
                onChange={handleFormChange}
                onBlur={(e) => {
                  const error = validateField('accountTitle', e.target.value);
                  setEmployeeForm(prev => ({
                    ...prev,
                    errors: {
                      ...prev.errors,
                      accountTitle: error
                    }
                  }));
                }}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                  employeeForm.errors.accountTitle ? 'border-red-500 dark:border-red-400' : ''
                }`}
                placeholder="Enter account title"
                required={isBank}
              />
              {employeeForm.errors.accountTitle && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.accountTitle}</p>
              )}
            </div>
            
            <div className="md:col-span-2">
              <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <Globe className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                IBAN
              </label>
              <input
                type="text"
                name="iban"
                value={employeeForm.iban}
                onChange={handleFormChange}
                onBlur={(e) => {
                  const error = validateField('iban', e.target.value);
                  setEmployeeForm(prev => ({
                    ...prev,
                    errors: {
                      ...prev.errors,
                      iban: error
                    }
                  }));
                }}
                className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                  employeeForm.errors.iban ? 'border-red-500 dark:border-red-400' : ''
                }`}
                placeholder="Enter IBAN (e.g., ************************)"
                required={false}
              />
              {employeeForm.errors.iban && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.iban}</p>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };
  
  // Render vehicle details form
  const renderVehicleDetailsForm = () => {
    return (
      <div>
        <div className="mb-6 p-4 border rounded-lg border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 relative transition-colors duration-300">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-center md:col-span-2">
              <input
                type="checkbox"
                id="providedByCompany"
                name="providedByCompany"
                checked={employeeForm.providedByCompany}
                onChange={(e) => {
                  setEmployeeForm(prev => ({
                    ...prev,
                    providedByCompany: e.target.checked
                  }));
                }}
                className="h-4 w-4 text-blue-600 mr-2"
              />
              <label htmlFor="providedByCompany" className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                <Building className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Provided by Company
              </label>
            </div>
          </div>
          
          {employeeForm.providedByCompany && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4 border-t pt-4">
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Car className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Vehicle Type<span className="text-red-500 ml-1">*</span>
                </label>
                <select
                  name="vehicleType"
                  value={employeeForm.vehicleType}
                  onChange={handleFormChange}
                  onBlur={(e) => {
                    const error = validateField('vehicleType', e.target.value);
                    setEmployeeForm(prev => ({
                      ...prev,
                      errors: {
                        ...prev.errors,
                        vehicleType: error
                      }
                    }));
                  }}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 ${
                    employeeForm.errors.vehicleType ? 'border-red-500' : ''
                  }`}
                  required={employeeForm.providedByCompany}
                >
                  <option value="">Select Vehicle Type</option>
                  {vehicleTypeOptions.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
                {employeeForm.errors.vehicleType && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.vehicleType}</p>
                )}
              </div>
              
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <List className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Registration Number
                </label>
                <input
                  type="text"
                  name="registrationNumber"
                  value={employeeForm.registrationNumber}
                  onChange={handleFormChange}
                  onBlur={(e) => {
                    const error = validateField('registrationNumber', e.target.value);
                    setEmployeeForm(prev => ({
                      ...prev,
                      errors: {
                        ...prev.errors,
                        registrationNumber: error
                      }
                    }));
                  }}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 ${
                    employeeForm.errors.registrationNumber ? 'border-red-500' : ''
                  }`}
                  placeholder="Enter registration number"
                />
                {employeeForm.errors.registrationNumber && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.registrationNumber}</p>
                )}
              </div>
              
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Car className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Vehicle Make/Model
                </label>
                <input
                  type="text"
                  name="vehicleMakeModel"
                  value={employeeForm.vehicleMakeModel}
                  onChange={handleFormChange}
                  onBlur={(e) => {
                    const error = validateField('vehicleMakeModel', e.target.value);
                    setEmployeeForm(prev => ({
                      ...prev,
                      errors: {
                        ...prev.errors,
                        vehicleMakeModel: error
                      }
                    }));
                  }}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 ${
                    employeeForm.errors.vehicleMakeModel ? 'border-red-500' : ''
                  }`}
                  placeholder="Enter vehicle make and model"
                  required={employeeForm.providedByCompany}
                />
                {employeeForm.errors.vehicleMakeModel && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.vehicleMakeModel}</p>
                )}
              </div>              
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Palette className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Vehicle Color
                </label>
                <input
                  type="text"
                  name="vehicleColor"
                  value={employeeForm.vehicleColor}
                  onChange={handleFormChange}
                  onBlur={(e) => {
                    const error = validateField('vehicleColor', e.target.value);
                    setEmployeeForm(prev => ({
                      ...prev,
                      errors: {
                        ...prev.errors,
                        vehicleColor: error
                      }
                    }));
                  }}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 ${
                    employeeForm.errors.vehicleColor ? 'border-red-500' : ''
                  }`}
                  placeholder="Enter vehicle color"
                  required={employeeForm.providedByCompany}
                />
                {employeeForm.errors.vehicleColor && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.vehicleColor}</p>
                )}
              </div>
              
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Gauge className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Mileage at Issuance
                </label>
                <input
                  type="number"
                  name="mileageAtIssuance"
                  value={employeeForm.mileageAtIssuance}
                  onChange={handleFormChange}
                  onBlur={(e) => {
                    const error = validateField('mileageAtIssuance', e.target.value);
                    setEmployeeForm(prev => ({
                      ...prev,
                      errors: {
                        ...prev.errors,
                        mileageAtIssuance: error
                      }
                    }));
                  }}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 ${
                    employeeForm.errors.mileageAtIssuance ? 'border-red-500' : ''
                  }`}
                  placeholder="Enter mileage at issuance"
                  required={employeeForm.providedByCompany}
                  min="0"
                />
                {employeeForm.errors.mileageAtIssuance && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.mileageAtIssuance}</p>
                )}
              </div>
              
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Calendar className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Handing Over Date
                </label>
                <input
                  type="date"
                  name="handingOverDate"
                  value={employeeForm.handingOverDate}
                  onChange={handleFormChange}
                  onBlur={(e) => {
                    const error = validateField('handingOverDate', e.target.value);
                    setEmployeeForm(prev => ({
                      ...prev,
                      errors: {
                        ...prev.errors,
                        handingOverDate: error
                      }
                    }));
                  }}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 ${
                    employeeForm.errors.handingOverDate ? 'border-red-500' : ''
                  }`}
                  required={employeeForm.providedByCompany}
                />
                {employeeForm.errors.handingOverDate && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.handingOverDate}</p>
                )}
              </div>
              
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Calendar className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Return Date
                </label>
                <input
                  type="date"
                  name="returnDate"
                  value={employeeForm.returnDate}
                  onChange={handleFormChange}
                  onBlur={(e) => {
                    const error = validateField('returnDate', e.target.value);
                    setEmployeeForm(prev => ({
                      ...prev,
                      errors: {
                        ...prev.errors,
                        returnDate: error
                      }
                    }));
                  }}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 ${
                    employeeForm.errors.returnDate ? 'border-red-500' : ''
                  }`}
                  min={employeeForm.handingOverDate}
                />
                {employeeForm.errors.returnDate && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.returnDate}</p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };
  
  // Render company devices form
  const renderDevicesForm = () => {
    return (
      <div>
        {employeeForm.deviceEntries.map((device, index) => (
          <div key={device.id} className="mb-6 p-4 border rounded-lg border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 relative transition-colors duration-300">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-md font-medium text-gray-800 dark:text-white">Device #{index + 1}</h3>
              {employeeForm.deviceEntries.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeDeviceEntry(index)}
                  className="absolute top-3 right-3 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1 rounded-full hover:bg-red-100 dark:hover:bg-red-900"
                  title="Remove Device Entry"
                >
                  <Trash2 size={16} />
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Monitor className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Device Name
                </label>
                <input
                  type="text"
                  name="deviceName"
                  value={device.deviceName}
                  onChange={(e) => handleDeviceChange(e, index)}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 ${
                    device.errors?.deviceName ? 'border-red-500' : ''
                  }`}
                  placeholder="Enter device name"
                />
                {device.errors?.deviceName && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{device.errors.deviceName}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Cpu className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Make/Model
                </label>
                <input
                  type="text"
                  name="makeModel"
                  value={device.makeModel}
                  onChange={(e) => handleDeviceChange(e, index)}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 ${
                    device.errors?.makeModel ? 'border-red-500' : ''
                  }`}
                  placeholder="Enter make and model"
                />
                {device.errors?.makeModel && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{device.errors.makeModel}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <HashIcon className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Serial Number
                </label>
                <input
                  type="text"
                  name="serialNumber"
                  value={device.serialNumber}
                  onChange={(e) => handleDeviceChange(e, index)}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 ${
                    device.errors?.serialNumber ? 'border-red-500' : ''
                  }`}
                  placeholder="Enter serial number"
                />
                {device.errors?.serialNumber && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{device.errors.serialNumber}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <CalendarPlus className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Handover Date
                </label>
                <input
                  type="date"
                  name="handoverDate"
                  value={device.handoverDate}
                  onChange={(e) => handleDeviceChange(e, index)}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 ${
                    device.errors?.handoverDate ? 'border-red-500' : ''
                  }`}
                />
                {device.errors?.handoverDate && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{device.errors.handoverDate}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <CalendarRange className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Return Date
                </label>
                <input
                  type="date"
                  name="returnDate"
                  value={device.returnDate}
                  onChange={(e) => handleDeviceChange(e, index)}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 ${
                    device.errors?.returnDate ? 'border-red-500' : ''
                  }`}
                />
                {device.errors?.returnDate && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{device.errors.returnDate}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <AlertCircle className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Condition
                </label>
                <select
                  name="condition"
                  value={device.condition}
                  onChange={(e) => handleDeviceChange(e, index)}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 ${
                    device.errors?.condition ? 'border-red-500' : ''
                  }`}
                >
                  <option value="">Select Condition</option>
                  <option value="New">New</option>
                  <option value="Good">Good</option>
                  <option value="Fair">Fair</option>
                  <option value="Poor">Poor</option>
                </select>
                {device.errors?.condition && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{device.errors.condition}</p>
                )}
              </div>
            </div>
          </div>
        ))}

        <div className="flex justify-start mt-4">
          <button
            type="button"
            onClick={addDeviceEntry}
            className="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
          >
            <Plus size={20} className="mr-1" />
            Add Another Device
          </button>
        </div>
      </div>
    );
  };
  
  // Render accommodation form
  const renderAccommodationForm = () => {
    return (
      <div>
        <div className="mb-6 p-4 border rounded-lg border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 relative transition-colors duration-300">
          <div className="mb-4">
            <h3 className="text-md font-medium text-gray-800 dark:text-white">Accommodation Details</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-center mt-2">
              <input
                type="checkbox"
                id="accommodationProvidedByEmployer"
                name="accommodationProvidedByEmployer"
                checked={employeeForm.accommodationProvidedByEmployer}
                onChange={(e) => {
                  setEmployeeForm(prev => ({
                    ...prev,
                    accommodationProvidedByEmployer: e.target.checked
                  }));
                }}
                className="h-4 w-4 text-blue-600 mr-2"
              />
              <label htmlFor="accommodationProvidedByEmployer" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <Building className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Accommodation Provided by Employer
              </label>
            </div>

            {employeeForm.accommodationProvidedByEmployer && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                    <Home className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                    Accommodation Type
                  </label>
                  <select
                    name="accommodationType"
                    value={employeeForm.accommodationType}
                    onChange={handleFormChange}
                    className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600"
                  >
                    <option value="">Select Accommodation Type</option>
                    <option value="Apartment">Apartment</option>
                    <option value="House">House</option>
                    <option value="Shared Room">Shared Room</option>
                    <option value="Single Room">Single Room</option>
                    <option value="Hotel">Hotel</option>
                    <option value="Hostel">Hostel</option>
                    <option value="Other">Other</option>
                  </select>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                    <MapPin className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                    Accommodation Address
                  </label>
                  <textarea
                    name="accommodationAddress"
                    value={employeeForm.accommodationAddress}
                    onChange={handleFormChange}
                    className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                    rows={3}
                    placeholder="Enter the full address of the accommodation provided"
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render health and insurance form
  const renderHealthInsuranceForm = () => {
  return (
      // Remove the outer mb-8 div, as the parent component provides the main container
      <div> 
        {/* Main container for health info, matching professional info style */}
        <div className="mb-6 p-4 border rounded-lg border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 relative transition-colors duration-300"> 
          {/* Insurance Details Sub-section */}
          <div className="mb-6"> {/* Add margin bottom to separate from medical records */}
            <div className="mb-4 border-b pb-2 border-gray-200 dark:border-gray-600"> {/* Add border bottom for separation */}
              <h3 className="text-md font-medium flex items-center text-gray-800 dark:text-white"> {/* Adjust text color */}
                <ShieldCheck className="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400" /> {/* Adjust icon color */}
                Insurance Details
              </h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <Building2 className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Health Insurance Provider
                </label>
              <input
                type="text"
                name="healthInsuranceProvider"
                value={employeeForm.healthInsuranceProvider}
                onChange={handleFormChange}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="Enter health insurance provider"
              />
            </div>
            
            <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <FileText className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Health Insurance Policy Number
                </label>
              <input
                type="text"
                name="healthInsurancePolicyNumber"
                value={employeeForm.healthInsurancePolicyNumber}
                onChange={handleFormChange}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="Enter policy number"
              />
            </div>
            
            <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <CalendarRange className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Health Insurance Expiry Date
                </label>
              <input
                type="date"
                name="healthInsuranceExpiryDate"
                value={employeeForm.healthInsuranceExpiryDate || ''}
                onChange={handleFormChange}
                onBlur={(e) => {
                  // If the field is empty, we'll set it to an empty string
                  // which will be converted to null during form submission
                  if (!e.target.value) {
                    setEmployeeForm(prev => ({
                      ...prev,
                      healthInsuranceExpiryDate: ''
                    }));
                    console.log('Health insurance expiry date field cleared and will be set to null');
                  }
                }}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600"
              />
            </div>
            
            <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <Building className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Life Insurance Provider
                </label>
              <input
                type="text"
                name="lifeInsuranceProvider"
                value={employeeForm.lifeInsuranceProvider}
                onChange={handleFormChange}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="Enter life insurance provider"
              />
            </div>
            
            <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <FileText className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Life Insurance Policy Number
                </label>
              <input
                type="text"
                name="lifeInsurancePolicyNumber"
                value={employeeForm.lifeInsurancePolicyNumber}
                onChange={handleFormChange}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="Enter policy number"
              />
            </div>
            
            <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <CalendarRange className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Life Insurance Expiry Date
                </label>
              <input
                type="date"
                name="lifeInsuranceExpiryDate"
                value={employeeForm.lifeInsuranceExpiryDate || ''}
                onChange={handleFormChange}
                onBlur={(e) => {
                  // If the field is empty, we'll set it to an empty string
                  // which will be converted to null during form submission
                  if (!e.target.value) {
                    setEmployeeForm(prev => ({
                      ...prev,
                      lifeInsuranceExpiryDate: ''
                    }));
                    console.log('Life insurance expiry date field cleared and will be set to null');
                  }
                }}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600"
              />
            </div>
          </div>
        </div>
        
          {/* Medical Records Sub-section */}
          <div className="mb-6"> {/* Use mb-6 to separate from potential next section */}
            <div className="mb-4 border-b pb-2 border-gray-200 dark:border-gray-600"> {/* Add border bottom */}
              <h3 className="text-md font-medium flex items-center text-gray-800 dark:text-white"> {/* Adjust text color */}
                <Activity className="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400" /> {/* Adjust icon color */}
                Medical Records
              </h3>
          </div>
          
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6"> {/* Use md:grid-cols-2 for consistency */}
            <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <Zap className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Vaccination Records
                </label>
              <textarea
                name="vaccinationRecords"
                value={employeeForm.vaccinationRecords}
                onChange={handleFormChange}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                rows={3}
                placeholder="List vaccination records and dates"
              />
            </div>
            
            <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <ScrollText className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Medical History
                </label>
              <textarea
                name="medicalHistory"
                value={employeeForm.medicalHistory}
                onChange={handleFormChange}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                rows={3}
                  placeholder="Enter medical history details"
              />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Handle children form change
  const handleChildrenChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>, index: number) => {
    const { name, value } = e.target;
    const updatedChildren = [...employeeForm.children];
    updatedChildren[index] = {
      ...updatedChildren[index],
      [name]: value
    };
    
    setEmployeeForm(prev => ({
      ...prev,
      children: updatedChildren
    }));
  };
  
  // Add new child
  const addChild = () => {
    setEmployeeForm(prevState => ({
      ...prevState,
      children: [
        ...prevState.children,
        {
          id: uuidv4(),
      name: '',
      dateOfBirth: '',
      gender: '',
          relationship: 'child',
          type: 'child' // Add the required type field
        }
      ]
    }));
  };
  
  // Remove child
  const removeChild = (index: number) => {
    const updatedChildren = [...employeeForm.children];
    updatedChildren.splice(index, 1);
    
    setEmployeeForm(prev => ({
      ...prev,
      children: updatedChildren
    }));
  };

  // Handle dependents form change
  const handleDependentsChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const { name, value } = e.target;
    const updatedDependents = [...employeeForm.dependents];
    updatedDependents[index] = {
      ...updatedDependents[index],
      [name]: value
    };
    
    setEmployeeForm(prev => ({
      ...prev,
      dependents: updatedDependents
    }));
  };
  
  // Add new dependent
  const addDependent = () => {
    setEmployeeForm(prevState => ({
      ...prevState,
      dependents: [
        ...prevState.dependents,
        {
          id: uuidv4(),
      name: '',
      dateOfBirth: '',
          relationship: 'other',
          cnic: '',
          type: 'dependent' // Add the required type field
        }
      ]
    }));
  };
  
  // Remove dependent
  const removeDependent = (index: number) => {
    const updatedDependents = [...employeeForm.dependents];
    updatedDependents.splice(index, 1);
    
    setEmployeeForm(prev => ({
      ...prev,
      dependents: updatedDependents
    }));
  };

  // Render family information form
  const renderFamilyInfoForm = () => {
    console.log('Rendering family form with spouse data:', {
      spouseName: employeeForm.spouseName || 'Not set',
      spouseDateOfBirth: employeeForm.spouseDateOfBirth || 'Not set',
      spouseOccupation: employeeForm.spouseOccupation || 'Not set',
      spouseEmployer: employeeForm.spouseEmployer || 'Not set',
      spouseContactNumber: employeeForm.spouseContactNumber || 'Not set',
      spouseCNIC: employeeForm.spouseCNIC || 'Not set',
      childrenCount: employeeForm.children.length,
      dependentsCount: employeeForm.dependents.length
    });
    
    // Check if spouse data exists - even partial data
    const hasAnySpouseData = !!(
      employeeForm.spouseName || 
      employeeForm.spouseDateOfBirth || 
      employeeForm.spouseOccupation || 
      employeeForm.spouseEmployer || 
      employeeForm.spouseContactNumber || 
      employeeForm.spouseCNIC
    );
    
    console.log('Has any spouse data:', hasAnySpouseData);
    console.log('Children:', employeeForm.children);
    console.log('Dependents:', employeeForm.dependents);
    
    return (
      <div className="mb-8">
        {/* Remove top-level header and wrapper if handled outside */}
        {/* Adjust the main container styling to match other sections */}
        <div className="mb-6 p-4 border rounded-lg border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 relative transition-colors duration-300"> 
          <div className="mb-4 border-b pb-2 border-gray-200 dark:border-gray-600"> {/* Add border bottom */}
            <h3 className="text-md font-medium flex items-center text-gray-800 dark:text-white"> 
              <Users className="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400" /> {/* Icon for section header */}
              Spouse Details
            </h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                <UserCircle className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" /> {/* Icon for Name */}
                Spouse Name
              </label>
              <input
                type="text"
                name="spouseName"
                value={employeeForm.spouseName}
                onChange={handleFormChange}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="Enter spouse name"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                <Calendar className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" /> {/* Icon for Date */}
                Date of Birth
              </label>
              <input
                type="date"
                name="spouseDateOfBirth"
                value={employeeForm.spouseDateOfBirth}
                onChange={handleFormChange}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                <Briefcase className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" /> {/* Icon for Occupation */}
                Occupation
              </label>
              <input
                type="text"
                name="spouseOccupation"
                value={employeeForm.spouseOccupation}
                onChange={handleFormChange}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="Enter occupation"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                <Building className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" /> {/* Icon for Employer */}
                Employer
              </label>
              <input
                type="text"
                name="spouseEmployer"
                value={employeeForm.spouseEmployer}
                onChange={handleFormChange}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="Enter employer name"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                <Phone className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" /> {/* Icon for Phone */}
                Contact Number
              </label>
              <input
                type="text"
                name="spouseContactNumber"
                value={employeeForm.spouseContactNumber}
                onChange={handleFormChange}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="Enter contact number"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                <Fingerprint className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" /> {/* Icon for CNIC */}
                CNIC
              </label>
              <input
                type="text"
                name="spouseCNIC"
                value={employeeForm.spouseCNIC}
                onChange={handleFormChange}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="Enter CNIC number"
              />
            </div>
          </div>
        </div>
        
        {/* Children Details Section */}
        <div className="mb-6 p-4 border rounded-lg border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 relative mt-6 transition-colors duration-300"> {/* Added mt-6 for spacing */} 
          <div className="flex justify-between items-center mb-4 border-b pb-2 border-gray-200 dark:border-gray-600"> {/* Add border bottom */}
            <h3 className="text-md font-medium flex items-center text-gray-800 dark:text-white"> 
              <Users className="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400" /> {/* Consistent Icon */}
              Children Details
            </h3>
            <button
              type="button"
              onClick={addChild}
              className="flex items-center px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-600 transition text-sm"
            >
              <Plus size={16} className="mr-1" /> Add Child
            </button>
          </div>
          
          {employeeForm.children.map((child, index) => (
            <div key={child.id} className="mb-6 p-4 border rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 relative transition-colors duration-300"> 
              <div className="flex justify-between mb-4">
                <h4 className="text-sm font-medium text-gray-800 dark:text-white">Child #{index + 1}</h4>
                <button
                  type="button"
                  onClick={() => removeChild(index)}
                  className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 absolute top-3 right-3 p-1 rounded-full hover:bg-red-100 dark:hover:bg-red-900/20 transition-colors duration-300" /* Adjust button style */
                  title="Remove Child"
                >
                  <Trash2 size={16} />
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                    <Baby className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" /> {/* Icon for Child Name */}
                    Name
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={child.name}
                    onChange={(e) => handleChildrenChange(e, index)}
                    className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder="Enter child name"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                    <Calendar className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" /> {/* Icon for Date */}
                    Date of Birth
                  </label>
                  <input
                    type="date"
                    name="dateOfBirth"
                    value={child.dateOfBirth}
                    onChange={(e) => handleChildrenChange(e, index)}
                    className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                    <Users2 className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" /> {/* Icon for Gender */}
                    Gender
                  </label>
                  <select
                    name="gender"
                    value={child.gender}
                    onChange={(e) => handleChildrenChange(e, index)}
                    className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600"
                  >
                    <option value="">Select Gender</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                    <HeartHandshake className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" /> {/* Icon for Relationship */}
                    Relationship
                  </label>
                  <input
                    type="text"
                    name="relationship"
                    value={child.relationship}
                    onChange={(e) => handleChildrenChange(e, index)}
                    className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder="e.g. Son, Daughter"
                  />
                </div>
              </div>
            </div>
          ))}
           {employeeForm.children.length === 0 && (
            <p className="text-sm text-gray-500 dark:text-gray-400 text-center italic">No children added yet.</p>
          )}
        </div>
        
        {/* Dependents Information Section */}
        <div className="mb-6 p-4 border rounded-lg border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 relative mt-6 transition-colors duration-300"> {/* Added mt-6 */}
          <div className="flex justify-between items-center mb-4 border-b pb-2 border-gray-200 dark:border-gray-600"> {/* Add border bottom */}
            <h3 className="text-md font-medium flex items-center text-gray-800 dark:text-white"> 
              <Users className="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400" /> {/* Consistent Icon */}
              Dependents Information
            </h3>
            <button
              type="button"
              onClick={addDependent}
              className="flex items-center px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-600 transition text-sm"
            >
              <Plus size={16} className="mr-1" /> Add Dependent
            </button>
          </div>
          
          {employeeForm.dependents.map((dependent, index) => (
            <div key={dependent.id} className="mb-6 p-4 border rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 relative transition-colors duration-300"> {/* Lighter background */}
              <div className="flex justify-between mb-4">
                <h4 className="text-sm font-medium text-gray-800 dark:text-white">Dependent #{index + 1}</h4>
                <button
                  type="button"
                  onClick={() => removeDependent(index)}
                  className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 absolute top-3 right-3 p-1 rounded-full hover:bg-red-100 dark:hover:bg-red-900/20 transition-colors duration-300" /* Adjust button style */
                  title="Remove Dependent"
                >
                  <Trash2 size={16} />
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                    <UserCircle className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" /> {/* Icon for Name */}
                    Name
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={dependent.name}
                    onChange={(e) => handleDependentsChange(e, index)}
                    className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder="Enter dependent name"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                    <Calendar className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" /> {/* Icon for Date */}
                    Date of Birth
                  </label>
                  <input
                    type="date"
                    name="dateOfBirth"
                    value={dependent.dateOfBirth}
                    onChange={(e) => handleDependentsChange(e, index)}
                    className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                    <HeartHandshake className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" /> {/* Icon for Relationship */}
                    Relationship
                  </label>
                  <input
                    type="text"
                    name="relationship"
                    value={dependent.relationship}
                    onChange={(e) => handleDependentsChange(e, index)}
                    className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder="e.g. Father, Mother, Sibling"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                    <Fingerprint className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" /> {/* Icon for CNIC */}
                    CNIC
                  </label>
                  <input
                    type="text"
                    name="cnic"
                    value={dependent.cnic}
                    onChange={(e) => handleDependentsChange(e, index)}
                    className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder="Enter CNIC number"
                  />
                </div>
              </div>
            </div>
          ))}
          {employeeForm.dependents.length === 0 && (
            <p className="text-sm text-gray-500 dark:text-gray-400 text-center italic">No dependents added yet.</p>
          )}
        </div>
      </div>
    );
  };

  // Handle document form change
  const handleDocumentChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>, index: number) => {
    const { name, value } = e.target;
    const updatedDocuments = [...employeeForm.documents];
    updatedDocuments[index] = {
      ...updatedDocuments[index],
      [name]: value
    };
    
    setEmployeeForm(prev => ({
      ...prev,
      documents: updatedDocuments
    }));
  };

  // Handle document file upload
  const handleDocumentFileChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const newFiles = Array.from(e.target.files || []);
    if (newFiles.length === 0) return;
    
    // Create a copy of the current document
    const targetDocument = { ...employeeForm.documents[index] };
    
    // Add uploading state for each file
    const newDocumentFiles = newFiles.map(file => ({
      id: uuidv4(),
      file,
      preview: URL.createObjectURL(file),
      uploadStatus: 'uploading' as 'uploading' | 'success' | 'error'
    }));

    // Update the document with the new files in uploading state
    setEmployeeForm(prev => ({
      ...prev,
      documents: prev.documents.map((doc, i) => 
        i === index 
          ? { ...doc, files: [...doc.files, ...newDocumentFiles] }
          : doc
      )
    }));
    
    // Process each file upload
    newFiles.forEach(async (file, fileIndex) => {
      try {
        // Create FormData for this specific file
        const formData = new FormData();
        formData.append('file', file);
        formData.append('documentType', targetDocument.documentType || '');
        formData.append('notes', '');
        
        console.log(`Uploading file ${file.name} for document type ${targetDocument.documentType}`);
        
        // Upload the file
        const response = await axios.post(
          `/api/employees/${propEmployeeId}/documents`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }
        );
        
        console.log('Document upload response:', response.data);
        
        // Update the file status based on response
        if (response.data.success) {
          const fileId = newDocumentFiles[fileIndex].id;
          const serverPath = response.data.document?.filePath || '';
          
          setEmployeeForm(prev => ({
            ...prev,
            documents: prev.documents.map((doc, i) => 
              i === index 
                ? { 
                    ...doc, 
                    files: doc.files.map(f => 
                      f.id === fileId 
                        ? { 
                            ...f, 
                            uploadStatus: 'success',
                            serverId: response.data.document.id,
                            serverPath: serverPath  // Use the direct path returned from server
                          } 
                        : f
                    )
                  }
                : doc
            )
          }));
          
          toast.success(`Document "${file.name}" uploaded successfully`);
          
          // Refresh employee data after successful upload
          // Add a small delay to ensure server has processed the upload
          setTimeout(() => {
            fetchEmployeeData();
          }, 800);
        } else {
          throw new Error(response.data.message || 'Upload failed');
        }
      } catch (error) {
        console.error('Error uploading document:', error);
        
        // Update the file status to error
        const fileId = newDocumentFiles[fileIndex].id;
        
        setEmployeeForm(prev => ({
          ...prev,
          documents: prev.documents.map((doc, i) => 
            i === index 
              ? { 
                  ...doc, 
                  files: doc.files.map(f => 
                    f.id === fileId 
                      ? { ...f, uploadStatus: 'error' } 
                      : f
                  )
                }
              : doc
          )
        }));
        
        toast.error(`Failed to upload "${file.name}": ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    });
  };

  const removeDocumentFile = (docIndex: number, fileId: string) => {
    const targetFile = employeeForm.documents[docIndex]?.files.find(f => f.id === fileId);
    
    // If file has a serverId, it exists on the server and needs to be deleted there first
    if (targetFile?.serverId && propEmployeeId) {
      // Show deletion in progress
      const toastId = toast.loading('Deleting document...');
      
      // Delete from server first
      axios.delete(`/api/employees/${propEmployeeId}/documents/${targetFile.serverId}`)
        .then(response => {
          toast.dismiss(toastId);
          if (response.data.success) {
            toast.success('Document deleted successfully');
            // After successful server deletion, remove from local state
            setEmployeeForm(prev => ({
              ...prev,
              documents: prev.documents.map((doc, i) => 
                i === docIndex 
                  ? { 
                      ...doc, 
                      files: doc.files.filter(file => file.id !== fileId)
                    }
                  : doc
              )
            }));
            
            // Refresh employee data after successful deletion
            setTimeout(() => {
              fetchEmployeeData();
            }, 800);
          } else {
            toast.error(response.data.message || 'Failed to delete document');
          }
        })
        .catch(error => {
          toast.dismiss(toastId);
          console.error('Error deleting document:', error);
          toast.error(`Failed to delete document: ${error instanceof Error ? error.message : 'Unknown error'}`);
        });
    } else {
      // If file doesn't have serverId or there's no employee ID, just remove from UI state
      setEmployeeForm(prev => ({
        ...prev,
        documents: prev.documents.map((doc, i) => 
          i === docIndex 
            ? { 
                ...doc, 
                files: doc.files.filter(file => file.id !== fileId)
              }
            : doc
        )
      }));
    }
  };

  // Add new document
  const addDocument = () => {
    const newDocument: DocumentEntry = {
      id: uuidv4(),
      documentType: '',
      files: []
    };
    
    console.log('Adding new document entry:', newDocument);
    
    setEmployeeForm(prev => {
      // First, create a copy of existing documents and add the new one
      const updatedDocuments = [...prev.documents, newDocument];
      console.log('Updated documents array:', updatedDocuments);
      
      return {
        ...prev,
        documents: updatedDocuments
      };
    });
  };

  // Remove document
  const removeDocument = (index: number) => {
    const updatedDocuments = [...employeeForm.documents];
    updatedDocuments.splice(index, 1);
    
    setEmployeeForm(prev => ({
      ...prev,
      documents: updatedDocuments
    }));
  };

  // Handle required documents change
  const handleRequiredDocumentsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setEmployeeForm(prev => ({
      ...prev,
      requiredDocuments: {
        ...prev.requiredDocuments,
        [name]: checked
      }
    }));
  };

  // Document types that require expiry dates
  const documentTypesWithExpiry = [
    'CNIC',
    'Passport',
    'Driving License',
    'Medical Certificate',
    'Educational Certificate',
    'Experience Certificate',
    'Police Clearance Certificate'
  ];
  
  // Render document management form
  const renderDocumentManagementForm = () => {
    console.log('Rendering document form with:', {
      documentCount: employeeForm.documents.length
    });
    console.log('Documents:', employeeForm.documents);
    
    return (
      <div className="mb-8">
        <div className="mb-6 p-4 border rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 transition-colors duration-300">
          <div className="mb-4">
            <h3 className="text-md font-medium text-gray-800 dark:text-white">Required Documents Checklist</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="cv"
                name="cv"
                checked={employeeForm.requiredDocuments.cv}
                onChange={handleRequiredDocumentsChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label htmlFor="cv" className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <FileText className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                CV/Resume
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="cnic"
                name="cnic"
                checked={employeeForm.requiredDocuments.cnic}
                onChange={handleRequiredDocumentsChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label htmlFor="cnic" className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <Fingerprint className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                CNIC
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="passport"
                name="passport"
                checked={employeeForm.requiredDocuments.passport}
                onChange={handleRequiredDocumentsChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label htmlFor="passport" className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <FileText className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                Passport
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="drivingLicense"
                name="drivingLicense"
                checked={employeeForm.requiredDocuments.drivingLicense}
                onChange={handleRequiredDocumentsChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label htmlFor="drivingLicense" className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <Car className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                Driving License
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="educationalCertificates"
                name="educationalCertificates"
                checked={employeeForm.requiredDocuments.educationalCertificates}
                onChange={handleRequiredDocumentsChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label htmlFor="educationalCertificates" className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <GraduationCap className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                Educational Certificates
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="experienceCertificates"
                name="experienceCertificates"
                checked={employeeForm.requiredDocuments.experienceCertificates}
                onChange={handleRequiredDocumentsChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label htmlFor="experienceCertificates" className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <Briefcase className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                Experience Certificates
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="bankAccountDetails"
                name="bankAccountDetails"
                checked={employeeForm.requiredDocuments.bankAccountDetails}
                onChange={handleRequiredDocumentsChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label htmlFor="bankAccountDetails" className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <Landmark className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                Bank Account Details
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="taxDocuments"
                name="taxDocuments"
                checked={employeeForm.requiredDocuments.taxDocuments}
                onChange={handleRequiredDocumentsChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label htmlFor="taxDocuments" className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <FileText className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                Tax Documents
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="medicalCertificate"
                name="medicalCertificate"
                checked={employeeForm.requiredDocuments.medicalCertificate}
                onChange={handleRequiredDocumentsChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label htmlFor="medicalCertificate" className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <Heart className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                Medical Certificate
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="policeClearanceCertificate"
                name="policeClearanceCertificate"
                checked={employeeForm.requiredDocuments.policeClearanceCertificate}
                onChange={handleRequiredDocumentsChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label htmlFor="policeClearanceCertificate" className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <ShieldCheck className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                Police Clearance
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="affidavit"
                name="affidavit"
                checked={employeeForm.requiredDocuments.affidavit}
                onChange={handleRequiredDocumentsChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label htmlFor="affidavit" className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <FileText className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                Affidavit
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="otherDocuments"
                name="otherDocuments"
                checked={employeeForm.requiredDocuments.otherDocuments}
                onChange={handleRequiredDocumentsChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label htmlFor="otherDocuments" className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <FilePlus className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                Other Documents
              </label>
            </div>
          </div>
        </div>
        
        <div className="mt-8 p-4 border rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 transition-colors duration-300">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-md font-medium text-gray-800 dark:text-white">Document Management</h3>
          </div>
          
          <div className="mb-6 p-4 border rounded-lg border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 transition-colors duration-300">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <FileText className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Document Type
                </label>
                <select
                  name="documentType"
                  value={employeeForm.documents[0]?.documentType || ''}
                  onChange={(e) => handleDocumentChange(e, 0)}
                  className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600"
                >
                  <option value="">Select Document Type</option>
                  <option value="CV/Resume">CV/Resume</option>
                  <option value="CNIC">CNIC</option>
                  <option value="Passport">Passport</option>
                  <option value="Driving License">Driving License</option>
                  <option value="Educational Certificate">Educational Certificate</option>
                  <option value="Experience Certificate">Experience Certificate</option>
                  <option value="Bank Statement">Bank Statement</option>
                  <option value="Tax Document">Tax Document</option>
                  <option value="Medical Certificate">Medical Certificate</option>
                  <option value="Police Clearance Certificate">Police Clearance Certificate</option>
                  <option value="Affidavit">Affidavit</option>
                  <option value="Other">Other</option>
                </select>
              </div>
            </div>

            {/* File Upload Section */}
            <div className="mt-4">
              <label htmlFor="file-upload" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                <Upload className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Upload Files
              </label>
              <div className="relative">
                <input
                  id="file-upload"
                  type="file"
                  multiple
                  onChange={(e) => handleDocumentFileChange(e, 0)}
                  className="sr-only"
                />
                <label
                  htmlFor="file-upload"
                  className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 transition-colors duration-200 ease-in-out"
                >
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <Upload className="w-8 h-8 mb-3 text-gray-400 dark:text-gray-500" />
                    <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                      <span className="font-semibold">Click to upload</span> or drag and drop
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      PDF, DOC, DOCX, JPG, PNG (MAX. 5MB)
                    </p>
                  </div>
                </label>
              </div>
            </div>
          </div>
          
          {/* Group all uploaded files by document type */}
          <div className="mt-4">
            {/* All files in a single flex layout */}
            <div className="flex flex-wrap gap-3">
              {Object.entries(
                employeeForm.documents.reduce((acc, doc) => {
                  if (Array.isArray(doc.files) && doc.files.length > 0) {
                    if (!acc[doc.documentType || 'Other']) {
                      acc[doc.documentType || 'Other'] = [];
                    }
                    acc[doc.documentType || 'Other'].push(...doc.files.map(file => ({ file, docId: doc.id })));
                  }
                  return acc;
                }, {} as Record<string, { file: DocumentFile; docId: string }[]>)
              ).flatMap(([docType, fileEntries]) => 
                fileEntries.map(({ file, docId }) => (
                  <div key={file.id} className="relative p-2 border rounded-lg border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 w-[calc(25%-6px)] sm:w-[calc(20%-8px)] md:w-[calc(16.666%-10px)]">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs font-medium text-gray-700 dark:text-gray-300 truncate flex items-center max-w-[80%]" title={docType}>
                        <FileText className="w-3 h-3 mr-1 text-gray-500 dark:text-gray-400 flex-shrink-0" />
                        {docType}
                      </span>
                      <button
                        type="button"
                        onClick={() => {
                          const docIndex = employeeForm.documents.findIndex(doc => doc.id === docId);
                          if (docIndex !== -1) {
                            removeDocumentFile(docIndex, file.id);
                          }
                        }}
                        className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-0.5 rounded-full hover:bg-red-100 dark:hover:bg-red-900/20 transition-colors duration-200 flex-shrink-0"
                        title="Remove file"
                        disabled={file.uploadStatus === 'uploading'}
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                    
                    <div className="flex items-center">
                      <span className="text-xs text-gray-600 dark:text-gray-400 truncate" title={file.file?.name || ''}>
                        {file.file?.name || 'Document'}
                      </span>
                    </div>
                    
                    <div className="mb-1 mt-1">
                      {file.uploadStatus === 'uploading' && (
                        <span className="text-xs text-blue-500 dark:text-blue-400 animate-pulse">Uploading...</span>
                      )}
                      {file.uploadStatus === 'success' && !isEditMode && (
                        <span className="text-xs text-green-500 dark:text-green-400">Uploaded</span>
                      )}
                      {file.uploadStatus === 'error' && (
                        <span className="text-xs text-red-500 dark:text-red-400">Failed</span>
                      )}
                    </div>
                    
                    {file.uploadStatus !== 'uploading' && (file.file?.type?.startsWith('image/') || file.preview?.match(/\.(jpg|jpeg|png|gif|webp)$/i)) && (
                      <div className="mt-1">
                        <button 
                          onClick={(e) => handleViewDocument(e, file.serverPath || file.preview)}
                          className="relative w-full h-14 overflow-hidden rounded group"
                          title="View full size image"
                        >
                          <div className="relative w-full h-full">
                            <SafeImage
                              src={file.serverPath || file.preview}
                              alt="Preview"
                              className="w-full h-14 object-cover rounded group-hover:opacity-90 transition-opacity"
                              fallbackText="Preview Unavailable"
                            />
                            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all">
                              <ExternalLink className="w-5 h-5 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                            </div>
                          </div>
                          <div className="text-center mt-1">
                            <span className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors flex justify-center items-center">
                              View Full Size
                            </span>
                          </div>
                        </button>
                      </div>
                    )}
                    
                    {file.uploadStatus !== 'uploading' && !file.file?.type?.startsWith('image/') && !file.preview?.match(/\.(jpg|jpeg|png|gif|webp)$/i) && (
                      <div className="flex justify-center mt-1">
                        <button 
                          onClick={(e) => handleViewDocument(e, file.serverPath || file.preview)}
                          className="px-3 py-1.5 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 transition-colors flex items-center justify-center w-full"
                          title="Open document in new window"
                        >
                          <FileText className="w-3 h-3 mr-1" />
                          View Document
                        </button>
                      </div>
                    )}
                    
                    {file.uploadStatus === 'uploading' && (
                      <div className="mt-1">
                        <div className="w-full h-1 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden">
                          <div className="h-full bg-blue-500 dark:bg-blue-400 rounded-full animate-pulse"></div>
                        </div>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Add validateProjectField function
  const validateProjectField = (name: string, value: string, currentProject: boolean): string => {
    // All fields are now optional, only validate if a value exists
    if (!value.trim()) return ''; 

    // Removed the initial required check block

    switch (name) {
      case 'projectName':
      case 'role':
      case 'clientName':
        if (value.length > 100) return 'Cannot exceed 100 characters';
        return '';

      case 'startDate':
        const startDate = new Date(value);
        if (isNaN(startDate.getTime())) return 'Please enter a valid start date';
        if (startDate > new Date()) return 'Start date cannot be in the future';
        return '';

      case 'endDate':
        if (!currentProject) {
          const endDate = new Date(value);
          if (isNaN(endDate.getTime())) return 'Please enter a valid end date';
          if (endDate > new Date()) return 'End date cannot be in the future';
        }
        return '';

      case 'technologies':
        if (value.length > 200) return 'Cannot exceed 200 characters';
        return '';

      case 'description':
        if (value.length > 1000) return 'Cannot exceed 1000 characters';
        return '';

      case 'achievements':
        if (value.length > 500) return 'Cannot exceed 500 characters';
        return '';

      case 'teamSize':
        const size = parseInt(value);
        if (isNaN(size) || size <= 0) return 'Please enter a valid positive number';
        if (size > 1000) return 'Team size seems too large';
        return '';

      default:
        return '';
    }
  };

  // Update handleProjectChange
  const handleProjectChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
    index: number
  ) => {
    const { name, value, type } = e.target;
    const checked = type === 'checkbox' ? (e.target as HTMLInputElement).checked : undefined;

    setEmployeeForm(prev => {
      const newProjectEntries = prev.projectEntries.map((entry, i): ProjectEntry => {
        if (i !== index) {
          return entry; // Return unchanged entries
        }

        // Create the updated entry by spreading the old one
        const updatedEntry = { ...entry };

        // Type-safe assignment based on the field name
        const fieldName = name as keyof Omit<ProjectEntry, 'id' | 'errors'>;

        if (fieldName === 'currentProject') {
          updatedEntry.currentProject = checked ?? false; // Assign boolean
        } else if (fieldName === 'teamSize') {
          // Although teamSize is string in the interface, treat value as string
          updatedEntry.teamSize = value; 
        } else if (fieldName === 'projectName' || fieldName === 'role' || fieldName === 'startDate' || fieldName === 'endDate' || fieldName === 'technologies' || fieldName === 'description' || fieldName === 'achievements' || fieldName === 'clientName'){
          // Explicitly handle string fields to satisfy TypeScript 
          updatedEntry[fieldName] = value;
        } 
        // else: This case should ideally not be reached if `name` is always a valid key
        // but adding it helps TypeScript understand all paths are handled.
        // If new fields are added, this logic might need updating.


        // Clear end date if 'currentProject' is now checked
        if (updatedEntry.currentProject) {
          updatedEntry.endDate = '';
          if (updatedEntry.errors) {
            updatedEntry.errors = { ...updatedEntry.errors, endDate: '' };
          }
        }

        // --- Validation ---
        let currentErrors = updatedEntry.errors || {};
        if (type !== 'checkbox') {
          const error = validateProjectField(name, value, updatedEntry.currentProject);
          currentErrors = { ...currentErrors, [name]: error };
        }
        if (name === 'startDate' || name === 'currentProject') {
          const endDateError = validateProjectField('endDate', updatedEntry.endDate, updatedEntry.currentProject);
          currentErrors = { ...currentErrors, endDate: endDateError };
        }
        updatedEntry.errors = currentErrors;
        // --- End Validation ---

        return updatedEntry; // Return the fully updated entry for the map function
      });

      // Return the updated state object for setEmployeeForm
      return {
        ...prev,
        projectEntries: newProjectEntries
      };
    });
  };

  const addProjectEntry = () => {
    setEmployeeForm(prev => ({
      ...prev,
      projectEntries: [
        ...prev.projectEntries,
        {
          id: uuidv4(),
          projectName: '',
          role: '',
          startDate: '',
          endDate: '',
          currentProject: false,
          technologies: '',
          description: '',
          achievements: '',
          teamSize: '',
          clientName: ''
        }
      ]
    }));
  };

  const removeProjectEntry = (index: number) => {
    setEmployeeForm(prev => ({
      ...prev,
      projectEntries: prev.projectEntries.filter((_, i) => i !== index)
    }));
  };

  // Update renderProjectsForm to display errors
  const renderProjectsForm = () => {
    return (
      <div>
        {employeeForm.projectEntries.map((project, index) => (
          <div key={project.id} className="mb-6 p-4 border rounded-lg border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 relative transition-colors duration-300">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-md font-medium text-gray-900 dark:text-white">Project #{index + 1}</h3>
              {employeeForm.projectEntries.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeProjectEntry(index)}
                  className="absolute top-3 right-3 text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 p-1 rounded-full hover:bg-red-100 dark:hover:bg-red-900/30"
                  title="Remove Project Entry"
                >
                  <Trash2 size={16} />
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <GitBranch className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Project Name
                </label>
                <input
                  type="text"
                  name="projectName"
                  value={project.projectName}
                  onChange={(e) => handleProjectChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    project.errors?.projectName ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                  placeholder="Enter project name"
                />
                {project.errors?.projectName && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{project.errors.projectName}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <UserCog className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Role in Project
                </label>
                <input
                  type="text"
                  name="role"
                  value={project.role}
                  onChange={(e) => handleProjectChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    project.errors?.role ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                  placeholder="Enter your role"
                />
                {project.errors?.role && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{project.errors.role}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <UserCircle className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Client Name
                </label>
                <input
                  type="text"
                  name="clientName"
                  value={project.clientName}
                  onChange={(e) => handleProjectChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    project.errors?.clientName ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                  placeholder="Enter client name"
                />
                {project.errors?.clientName && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{project.errors.clientName}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <CalendarDays className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Start Date
                </label>
                <input
                  type="date"
                  name="startDate"
                  value={project.startDate}
                  onChange={(e) => handleProjectChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                  project.errors?.startDate ? 'border-red-500 dark:border-red-400' : ''
                }`}
                />
                {project.errors?.startDate && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{project.errors.startDate}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <CalendarCheck2 className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  End Date
                </label>
                <input
                  type="date"
                  name="endDate"
                  value={project.endDate}
                  onChange={(e) => handleProjectChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                  project.errors?.endDate ? 'border-red-500 dark:border-red-400' : ''
                }`}
                  disabled={project.currentProject}
                />
                {project.errors?.endDate && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{project.errors.endDate}</p>
                )}
              </div>

              <div className="flex items-center self-end mb-1">
                <input
                  type="checkbox"
                  id={`currentProject-${index}`}
                  name="currentProject"
                  checked={project.currentProject}
                  onChange={(e) => handleProjectChange(e, index)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
                />
                <label htmlFor={`currentProject-${index}`} className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Current Project
                </label>
              </div>

              <div className="lg:col-span-3">
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Code2 className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Technologies Used
                </label>
                <input
                  type="text"
                  name="technologies"
                  value={project.technologies}
                  onChange={(e) => handleProjectChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                  project.errors?.technologies ? 'border-red-500 dark:border-red-400' : ''
                }`}
                  placeholder="e.g., React, Node.js, MongoDB"
                />
                {project.errors?.technologies && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{project.errors.technologies}</p>
                )}
              </div>

              <div className="lg:col-span-3">
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <ScrollText className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Project Description
                </label>
                <textarea
                  name="description"
                  value={project.description}
                  onChange={(e) => handleProjectChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                  project.errors?.description ? 'border-red-500 dark:border-red-400' : ''
                }`}
                  rows={3}
                  placeholder="Describe the project, its objectives, and your responsibilities"
                />
                {project.errors?.description && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{project.errors.description}</p>
                )}
              </div>

              <div className="lg:col-span-3">
                <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Trophy className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Key Achievements
                </label>
                <textarea
                  name="achievements"
                  value={project.achievements}
                  onChange={(e) => handleProjectChange(e, index)}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                  project.errors?.achievements ? 'border-red-500 dark:border-red-400' : ''
                }`}
                  rows={2}
                  placeholder="List your key achievements and contributions"
                />
                {project.errors?.achievements && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{project.errors.achievements}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                  <Boxes className="w-4 h-4 mr-2 text-gray-500" />
                  Team Size
                </label>
                <input
                  type="number"
                  name="teamSize"
                  value={project.teamSize}
                  onChange={(e) => handleProjectChange(e, index)}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  project.errors?.teamSize ? 'border-red-500' : ''
                }`}
                  placeholder="Enter team size"
                  min="1"
                />
                {project.errors?.teamSize && (
                  <p className="mt-1 text-sm text-red-600">{project.errors.teamSize}</p>
                )}
              </div>
            </div>
          </div>
        ))}

        <button
          type="button"
          onClick={addProjectEntry}
          className="flex items-center text-blue-600 hover:text-blue-800 font-medium text-sm"
        >
          <Plus className="w-5 h-5 mr-1" />
          Add Another Project
        </button>
      </div>
    );
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleEmployeeSubmit();
  };

  // Set page title based on mode when component mounts
  useEffect(() => {
    document.title = isEditMode ? "Edit Employee" : "Add New Employee";
  }, [isEditMode]);

  // Add the handleViewDocument function before the renderDocumentManagementForm function
  
  // Function to securely view documents
  const handleViewDocument = async (e: React.MouseEvent, filePath: string) => {
    e.preventDefault();
    
    if (!filePath) {
      toast.error('No document path available');
      return;
    }
    
    try {
      // Show loading toast
      const loadingToast = toast.loading('Preparing document for viewing...');
      
      // Get secure URL through our file service
      const result = await fileService.getSecureFileUrl(filePath);
      
      // Close loading toast
      toast.dismiss(loadingToast);
      
      if (result.success) {
        // Determine file type to handle it appropriately
        const fileExt = filePath.split('.').pop()?.toLowerCase() || '';
        
        // Build URL with proper parameters
        let url = result.fileUrl;
        
        // For PDFs specifically ensure we add parameters to force display
        if (fileExt === 'pdf') {
          // Make sure we're using 'view=inline' for PDFs
          if (!url.includes('view=inline')) {
            url = url.includes('?') ? `${url}&view=inline&disposition=inline` : `${url}?view=inline&disposition=inline`;
          } else if (!url.includes('disposition=inline')) {
            url += '&disposition=inline';
          }
        } else {
          // For other files, just make sure view=inline is present
          if (!url.includes('view=inline')) {
            url = url.includes('?') ? `${url}&view=inline` : `${url}?view=inline`;
          }
        }
        
        // Add a random parameter to prevent caching issues
        url = `${url}&t=${Date.now()}`;
        
        console.log(`Opening document in new tab: ${url}`);
        
        // Open in new tab - only use this method, not the fallback that creates a second tab
        window.open(url, '_blank', 'noopener,noreferrer');
      } else {
        toast.error(result.message || 'Failed to access document');
      }
    } catch (error) {
      toast.error('Error accessing document');
      console.error('Document access error:', error);
    }
  };

  // Function to scroll to section when clicking on progress bar
  const scrollToSection = (sectionId: number): void => {
    setActiveStep(sectionId);
    
    // Map section IDs to the corresponding DOM elements
    const sectionMap: {[key: number]: string} = {
      1: 'personal-info',
      2: 'emergency-contact',
      3: 'educational-info',
      4: 'job-details',
      5: 'compensation',
      6: 'health-insurance',
      7: 'family-info',
      8: 'document-management'
    };
    
    const sectionElement = document.getElementById(sectionMap[sectionId]);
    if (sectionElement) {
      sectionElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Add a scroll listener to update the active step when user scrolls
  useEffect(() => {
    const handleScroll = (): void => {
      const sectionIds = [
        'personal-info',
        'emergency-contact',
        'educational-info',
        'job-details', 
        'compensation',
        'health-insurance',
        'family-info',
        'document-management'
      ];
      
      // Calculate which section is most visible in the viewport
      let mostVisibleSection = 1;
      let maxVisibility = 0;
      
      // Get current scroll position
      const scrollPosition = window.scrollY + window.innerHeight / 3; // Add some offset to catch sections earlier
      
      // Find the section that's most visible
      sectionIds.forEach((id, index) => {
        const element = document.getElementById(id);
        if (element) {
          const rect = element.getBoundingClientRect();
          const topOffset = rect.top + window.scrollY;
          
          // If we've scrolled past the start of this section and it's the furthest section we've reached
          if (scrollPosition >= topOffset && topOffset > maxVisibility) {
            maxVisibility = topOffset;
            mostVisibleSection = index + 1; // 1-based index
          }
        }
      });
      
      // Update the active step if needed
      if (mostVisibleSection !== activeStep) {
        setActiveStep(mostVisibleSection);
      }
    };
    
    // Add throttling to prevent too many updates
    let ticking = false;
    const scrollListener = () => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };
    
    window.addEventListener('scroll', scrollListener);
    return () => {
      window.removeEventListener('scroll', scrollListener);
    };
  }, [activeStep as number]);

  // Show back to top button when user scrolls down
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 500) {
        setShowBackToTop(true);
      } else {
        setShowBackToTop(false);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Function to scroll back to top
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // Function to scroll to bottom
  const scrollToBottom = () => {
    window.scrollTo({
      top: document.documentElement.scrollHeight,
      behavior: 'smooth'
    });
  };

  return (
    <div className="container mx-auto p-6 bg-white dark:bg-gray-800 shadow rounded-lg transition-colors duration-300">
      <div className="mb-8 border-b border-gray-200 dark:border-gray-700 pb-4">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
          {isEditMode ? 'Edit Employee' : 'Add New Employee'}
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          {isEditMode 
            ? 'Update employee information in the form below' 
            : 'Fill out the form below to add a new employee to the system'
          }
        </p>
      </div>

      {/* Progress Bar */}
      <div className="mb-8 relative overflow-x-auto">
        <div className="min-w-max pb-3">
          {/* Horizontal line connecting all steps */}
          <div className="absolute top-5 left-0 right-0 h-0.5 bg-gray-200 dark:bg-gray-600" style={{ width: '90%', left: '5%' }}></div>
          
          {/* Filled line showing progress */}
          <div className="absolute top-5 left-0 h-0.5 bg-blue-500 dark:bg-blue-400" style={{ width: `${(activeStep-1) * 11.25}%`, left: '5%' }}></div>
          
          <div className="flex items-center justify-between relative px-4">
            {formSteps.map((step) => (
              <div 
                key={step.id}
                className="flex flex-col items-center space-y-2 cursor-pointer z-10 px-2 mx-3"
                onClick={() => scrollToSection(step.id)}
              >
                <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
                  activeStep >= step.id 
                    ? 'bg-blue-500 dark:bg-blue-400 text-white border-blue-500 dark:border-blue-400' 
                    : 'bg-white dark:bg-gray-700 text-gray-400 dark:text-gray-500 border-gray-300 dark:border-gray-600'
                }`}>
                  {step.id}
                </div>
                <span className={`text-xs text-center w-20 ${
                  activeStep >= step.id ? 'text-blue-500 dark:text-blue-400 font-medium' : 'text-gray-500 dark:text-gray-400'
                }`}>
                  {step.name}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Personal Information Section */}
        <div id="personal-info" className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <User className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Personal Information</h2>
          </div>
          <div className="space-y-6">
            {/* Profile Image at the top */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 transition-colors duration-300">
              <div className="flex flex-col items-start space-y-4">
                <label className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                  <Image className="w-5 h-5 mr-2 text-blue-500 dark:text-blue-400" />
                  Profile Image
                </label>
                
                <div className="relative group">
                  {employeeForm.profileImagePreview ? (
                    <div className="relative">
                      <SafeImage
                        src={employeeForm.profileImagePreview}
                        alt="Profile Preview"
                        className="w-40 h-40 object-cover rounded-lg border-2 border-gray-200 dark:border-gray-600 shadow-md"
                        fallbackText="Profile Preview"
                      />
                      <div className="absolute bottom-2 right-2">
                        <div className="relative">
                          <button
                            type="button"
                            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                            className="inline-flex items-center px-3 py-1.5 bg-white/90 dark:bg-gray-800/90 hover:bg-white dark:hover:bg-gray-800 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 shadow-sm border border-gray-200 dark:border-gray-600"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </button>
                          {isDropdownOpen && (
                            <div className="absolute bottom-full right-0 mb-1 w-32 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-600 py-1">
                              <button
                                type="button"
                                onClick={() => {
                                  fileInputRef.current?.click();
                                  setIsDropdownOpen(false);
                                }}
                                className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center"
                              >
                                <Upload className="h-3.5 w-3.5 mr-2" />
                                Change
                              </button>
                              <button
                                type="button"
                                onClick={() => {
                                  setEmployeeForm(prev => ({
                                    ...prev,
                                    profileImage: null,
                                    profileImagePreview: ''
                                  }));
                                  setIsDropdownOpen(false);
                                }}
                                className="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center"
                              >
                                <Trash2 className="h-3.5 w-3.5 mr-2" />
                                Remove
                              </button>
                            </div>
                          )}
                        </div>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleImageChange}
                          className="hidden"
                          ref={fileInputRef}
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="w-40 h-40 rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/30 border-2 border-gray-200 dark:border-gray-600 shadow-md flex items-center justify-center group-hover:border-blue-300 dark:group-hover:border-blue-500 transition-colors duration-200">
                      <div className="flex flex-col items-center">
                        <UserCircle className="h-16 w-16 text-blue-500/70 dark:text-blue-400/70 mb-2" />
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleImageChange}
                          className="hidden"
                          ref={fileInputRef}
                        />
                        <button
                          type="button"
                          onClick={() => fileInputRef.current?.click()}
                          className="inline-flex items-center px-3 py-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                        >
                          <Upload className="h-3 w-3 mr-1" />
                          Upload Photo
                        </button>
                      </div>
                    </div>
                  )}
                </div>
                
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Recommended: Square image, 400x400 pixels
                </p>
              </div>
            </div>

            {/* Name Fields in one row */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <User className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  First Name
                </label>
                <input
                  type="text"
                  name="firstName"
                  value={employeeForm.firstName}
                  onChange={handleFormChange}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    employeeForm.errors.firstName ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'
                  }`}
                  placeholder="Enter first name"
                  maxLength={50}
                />
                {employeeForm.errors.firstName && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.firstName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <User className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Middle Name
                </label>
                <input
                  type="text"
                  name="middleName"
                  value={employeeForm.middleName}
                  onChange={handleFormChange}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  placeholder="Enter middle name (optional)"
                  maxLength={50}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <User className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Last Name
                </label>
                <input
                  type="text"
                  name="lastName"
                  value={employeeForm.lastName}
                  onChange={handleFormChange}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    employeeForm.errors.lastName ? 'border-red-500' : ''
                  }`}
                  placeholder="Enter last name"
                  maxLength={50}
                />
                {employeeForm.errors.lastName && (
                  <p className="mt-1 text-sm text-red-600">{employeeForm.errors.lastName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                  <UserCircle className="w-4 h-4 mr-2 text-gray-500" />
                  Father Name
                </label>
                <input
                  type="text"
                  name="fatherName"
                  value={employeeForm.fatherName}
                  onChange={handleFormChange}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    employeeForm.errors.fatherName ? 'border-red-500' : ''
                  }`}
                  placeholder="Enter father name"
                  maxLength={50}
                />
                {employeeForm.errors.fatherName && (
                  <p className="mt-1 text-sm text-red-600">{employeeForm.errors.fatherName}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                  <Users2 className="w-4 h-4 mr-2 text-gray-500" />
                  Gender
                </label>
                <select
                  name="gender"
                  value={employeeForm.gender}
                  onChange={handleFormChange}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    employeeForm.errors.gender ? 'border-red-500' : ''
                  }`}
                >
                  <option value="">Select Gender</option>
                  {genderOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
                {employeeForm.errors.gender && (
                  <p className="mt-1 text-sm text-red-600">{employeeForm.errors.gender}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                  <Calendar className="w-4 h-4 mr-2 text-gray-500" />
                  Date of Birth
                </label>
                <input
                  type="date"
                  name="dateOfBirth"
                  value={employeeForm.dateOfBirth}
                  onChange={handleFormChange}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    employeeForm.errors.dateOfBirth ? 'border-red-500' : ''
                  }`}
                />
                {employeeForm.errors.dateOfBirth && (
                  <p className="mt-1 text-sm text-red-600">{employeeForm.errors.dateOfBirth}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                  <Flag className="w-4 h-4 mr-2 text-gray-500" />
                  Religion
                </label>
                <select
                  name="religion"
                  value={employeeForm.religion}
                  onChange={handleFormChange}
                  className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Religion</option>
                  {religionOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                  <Fingerprint className="w-4 h-4 mr-2 text-gray-500" />
                  CNIC Number
                </label>
                <input
                  type="text"
                  name="cnicNumber"
                  value={employeeForm.cnicNumber}
                  onChange={handleFormChange}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    employeeForm.errors.cnicNumber ? 'border-red-500' : ''
                  }`}
                  placeholder="XXXXX-XXXXXXX-X"
                  maxLength={15}
                />
                {employeeForm.errors.cnicNumber && (
                  <p className="mt-1 text-sm text-red-600">{employeeForm.errors.cnicNumber}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                  <Calendar className="w-4 h-4 mr-2 text-gray-500" />
                  CNIC Expiry Date
                </label>
                <input
                  type="date"
                  name="cnicExpiryDate"
                  value={employeeForm.cnicExpiryDate}
                  onChange={handleFormChange}
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    employeeForm.errors.cnicExpiryDate ? 'border-red-500' : ''
                  }`}
                />
                {employeeForm.errors.cnicExpiryDate && (
                  <p className="mt-1 text-sm text-red-600">{employeeForm.errors.cnicExpiryDate}</p>
                )}
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                  <Globe className="w-4 h-4 mr-2 text-gray-500" />
                  Nationality
                </label>
                <input
                  type="text"
                  name="nationality"
                  value={employeeForm.nationality}
                  onChange={handleFormChange}
                  className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter nationality"
                />
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                  <Users2 className="w-4 h-4 mr-2 text-gray-500" />
                  Marital Status
                </label>
                <select
                  name="maritalStatus"
                  value={employeeForm.maritalStatus}
                  onChange={handleFormChange}
                  className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Marital Status</option>
                  <option value="Single">Single</option>
                  <option value="Married">Married</option>
                  <option value="Divorced">Divorced</option>
                  <option value="Widowed">Widowed</option>
                </select>
              </div>

              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                  <Heart className="w-4 h-4 mr-2 text-gray-500" />
                  Blood Group
                </label>
                <select
                  name="bloodGroup"
                  value={employeeForm.bloodGroup}
                  onChange={handleFormChange}
                  className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Blood Group</option>
                  <option value="A+">A+</option>
                  <option value="A-">A-</option>
                  <option value="B+">B+</option>
                  <option value="B-">B-</option>
                  <option value="AB+">AB+</option>
                  <option value="AB-">AB-</option>
                  <option value="O+">O+</option>
                  <option value="O-">O-</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information Section */}
        <div id="emergency-contact" className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <Phone className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Contact Information</h2>
          </div>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <Phone className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Mobile Number
                </label>
                <input
                  type="tel"
                  name="mobileNumber"
                  value={employeeForm.mobileNumber}
                  onChange={handleFormChange}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  placeholder="+92-XXX-XXXXXXX"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <Phone className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Official Number
                </label>
                <input
                  type="tel"
                  name="officialNumber"
                  value={employeeForm.officialNumber}
                  onChange={handleFormChange}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  placeholder="+92-XXX-XXXXXXX (Optional)"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <Mail className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Official Email
                </label>
                <input
                  type="email"
                  name="officialEmail"
                  value={employeeForm.officialEmail}
                  onChange={handleFormChange}
                  onBlur={(e) => {
                    const error = validateField('officialEmail', e.target.value);
                    setEmployeeForm(prev => ({
                      ...prev,
                      errors: {
                        ...prev.errors,
                        officialEmail: error
                      }
                    }));
                  }}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    employeeForm.errors.officialEmail ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                  placeholder="<EMAIL> (Optional)"
                />
                {employeeForm.errors.officialEmail && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.officialEmail}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <Mail className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Personal Email
                  <span className="text-red-500 ml-1">*</span>
                </label>
                <input
                  type="email"
                  name="personalEmail"
                  value={employeeForm.personalEmail}
                  onChange={handleFormChange}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  placeholder="<EMAIL>"
                />
                {employeeForm.errors.personalEmail && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.personalEmail}</p>
                )}
              </div>

              <div className="md:col-span-3">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <MapPin className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Permanent Address
                  <span className="text-red-500 ml-1">*</span>
                </label>
                <textarea
                  name="permanentAddress"
                  value={employeeForm.permanentAddress}
                  onChange={handleFormChange}
                  rows={2}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    employeeForm.errors.permanentAddress ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                  placeholder="Enter permanent address"
                />
                {employeeForm.errors.permanentAddress && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.permanentAddress}</p>
                )}
              </div>

              <div className="md:col-span-3">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <MapPin className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Current Address
                  <span className="text-red-500 ml-1">*</span>
                </label>
                <div className="flex flex-col space-y-2">
                  <textarea
                    name="currentAddress"
                    value={employeeForm.currentAddress}
                    onChange={handleFormChange}
                    rows={2}
                    className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                      employeeForm.errors.currentAddress ? 'border-red-500 dark:border-red-400' : ''
                    }`}
                    placeholder="Enter current address"
                  />
                  <button
                    type="button"
                    onClick={copyAddress}
                    className="self-end text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 flex items-center"
                  >
                    <Copy className="w-3 h-3 mr-1" />
                    Same as permanent address
                  </button>
                </div>
                {employeeForm.errors.currentAddress && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.currentAddress}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <User className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Emergency Contact Name
                  <span className="text-red-500 ml-1">*</span>
                </label>
                <input
                  type="text"
                  name="emergencyContactName"
                  value={employeeForm.emergencyContactName}
                  onChange={handleFormChange}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    employeeForm.errors.emergencyContactName ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                  placeholder="Enter emergency contact name"
                />
                {employeeForm.errors.emergencyContactName && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.emergencyContactName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <Phone className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Emergency Contact Phone
                  <span className="text-red-500 ml-1">*</span>
                </label>
                <input
                  type="tel"
                  name="emergencyContactPhone"
                  value={employeeForm.emergencyContactPhone}
                  onChange={handleFormChange}
                  onBlur={(e) => {
                    const error = validateField('emergencyContactPhone', e.target.value);
                    setEmployeeForm(prev => ({
                      ...prev,
                      errors: {
                        ...prev.errors,
                        emergencyContactPhone: error
                      }
                    }));
                  }}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    employeeForm.errors.emergencyContactPhone ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                  placeholder="+92-XXX-XXXXXXX"
                />
                {employeeForm.errors.emergencyContactPhone && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.emergencyContactPhone}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                  <HeartHandshake className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                  Emergency Contact Relationship
                  <span className="text-red-500 ml-1">*</span>
                </label>
                <input
                  type="text"
                  name="emergencyContactRelationship"
                  value={employeeForm.emergencyContactRelationship}
                  onChange={handleFormChange}
                  onBlur={(e) => {
                    const error = validateField('emergencyContactRelationship', e.target.value);
                    setEmployeeForm(prev => ({
                      ...prev,
                      errors: {
                        ...prev.errors,
                        emergencyContactRelationship: error
                      }
                    }));
                  }}
                  className={`w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                    employeeForm.errors.emergencyContactRelationship ? 'border-red-500 dark:border-red-400' : ''
                  }`}
                  placeholder="e.g., Father, Mother, Spouse, Sibling"
                />
                {employeeForm.errors.emergencyContactRelationship && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{employeeForm.errors.emergencyContactRelationship}</p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Educational Information Section */}
        <div id="educational-info" className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <GraduationCap className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Educational Information</h2>
          </div>
        {renderEducationalInfoForm()}
        </div>

        {/* Experience Section */}
        <div id="experience" className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
              <Briefcase className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Experience</h2>
          </div>
        {renderExperienceForm()}
        </div>

        {/* Projects Section */}
        <div id="projects" className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
              <Award className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Projects</h2>
          </div>
        {renderProjectsForm()}
        </div>

        {/* Skills & Certifications Section */}
        <div id="skills-certifications" className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <Zap className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Skills & Certifications</h2>
          </div>
        {renderSkillsCertificationsForm()}
        </div>

        {/* Job Details Section */}
        <div id="job-details" className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg">
              <Building2 className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Job Details</h2>
          </div>
        {renderJobDetailsForm()}
        </div>

        {/* Compensation & Benefits Section */}
        <div id="compensation" className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg">
              <DollarSign className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Compensation & Benefits</h2>
          </div>
        {renderCompensationForm()}
        </div>

        {/* Bank Details Section */}
        <div id="bank-details" className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-cyan-100 dark:bg-cyan-900/30 rounded-lg">
              <Landmark className="w-6 h-6 text-cyan-600 dark:text-cyan-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Bank Details</h2>
          </div>
        {renderBankDetailsForm()}
        </div>

        {/* Vehicle Details Section */}
        <div id="vehicle-details" className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-rose-100 dark:bg-rose-900/30 rounded-lg">
              <Car className="w-6 h-6 text-rose-600 dark:text-rose-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Vehicle Details</h2>
          </div>
        {renderVehicleDetailsForm()}
        </div>

        {/* Company Devices Section */}
        <div id="company-devices" className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-teal-100 dark:bg-teal-900/30 rounded-lg">
              <Laptop className="w-6 h-6 text-teal-600 dark:text-teal-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Company Devices</h2>
          </div>
          {renderDevicesForm()}
        </div>

        {/* Accommodation Details Section */}
        <div id="work-location" className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-amber-100 dark:bg-amber-900/30 rounded-lg">
              <Home className="w-6 h-6 text-amber-600 dark:text-amber-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Accommodation Details</h2>
          </div>
        {renderAccommodationForm()}
        </div>

        {/* Health & Insurance Section */}
        <div id="health-insurance" className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-lg">
              <Heart className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Health & Insurance</h2>
          </div>
          {renderHealthInsuranceForm()}
        </div>

        {/* Family Information Section */}
        <div id="family-info" className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-pink-100 dark:bg-pink-900/30 rounded-lg">
              <Users className="w-6 h-6 text-pink-600 dark:text-pink-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Family Information</h2>
          </div>
          {renderFamilyInfoForm()}
        </div>

        {/* Document Management Section */}
        <div id="document-management" className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-violet-100 dark:bg-violet-900/30 rounded-lg">
              <FileText className="w-6 h-6 text-violet-600 dark:text-violet-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Document Management</h2>
          </div>
          {renderDocumentManagementForm()}
        </div>

        {/* Notes Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <NotebookText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h2 className="text-xl font-semibold ml-2 text-gray-900 dark:text-white">Notes</h2>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                <NotebookText className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                General Notes
              </label>
              <textarea
                name="notes"
                value={employeeForm.notes}
                onChange={handleFormChange}
                rows={4}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="Enter any additional notes or comments about the employee..."
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                <ClipboardList className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                Special Instructions
              </label>
              <textarea
                name="specialInstructions"
                value={employeeForm.specialInstructions}
                onChange={handleFormChange}
                rows={4}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="Enter any special instructions or requirements..."
              />
            </div>
          </div>
        </div>

        {/* Navigation Buttons */}
        {showBackToTop && (
          <div className="fixed right-5 bottom-24 z-50 flex flex-col space-y-2">
            <button
              type="button"
              onClick={scrollToTop}
              className="p-2 bg-blue-600 dark:bg-blue-500 text-white rounded-full shadow-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-300 flex items-center justify-center"
              aria-label="Back to top"
            >
              <ChevronUp className="h-4 w-4" />
            </button>
            <button
              type="button"
              onClick={scrollToBottom}
              className="p-2 bg-blue-600 dark:bg-blue-500 text-white rounded-full shadow-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-300 flex items-center justify-center"
              aria-label="Go to bottom"
            >
              <ChevronDown className="h-4 w-4" />
            </button>
          </div>
        )}
        
        {/* Form Actions */}
        <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 shadow-lg p-4 flex justify-end items-center space-x-4 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={`px-6 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors flex items-center ${
              isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
            }`}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </>
            ) : (
              <>{isEditMode ? 'Update Employee' : 'Add Employee'}</>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddEmployeePage; 