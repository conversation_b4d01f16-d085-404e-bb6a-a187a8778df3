import express, { Request<PERSON><PERSON><PERSON>, Response } from 'express';
import { ticketController } from '../controllers/ticketController';
import { authMiddleware } from '../middleware/authMiddleware';
import { AuthRequest } from '../middleware/authMiddleware';
import { UserRole, TicketStatus } from '../../types/common';
import path from 'path';
import { AppDataSource } from '../../config/database';
import { Ticket } from '../../entities/Ticket';
import { Comment } from '../../entities/Comment';
import { EntityManager } from 'typeorm';

const router = express.Router();

// Apply auth middleware to all routes
router.use(authMiddleware.verify as RequestHandler);

// Get all IDs (users and tickets) - must be before /:id routes to avoid conflict
router.get('/ids', 
  authMiddleware.checkRoles([UserRole.IT_ADMIN, UserRole.IT_STAFF]) as RequestHandler,
  (req, res, next) => {
    ticketController.getIds(req as any, res).catch(next);
  }
);

// Get a single ticket by ID
router.get('/:id',
  authMiddleware.verify as RequestHandler,
  (req, res, next) => {
    ticketController.getTicket(req as any, res).catch(next);
  }
);

// Get tickets
router.get('/', (req, res, next) => {
  ticketController.getTickets(req as any, res).catch(next);
});

// Create ticket
router.post('/', 
  authMiddleware.checkRoles([UserRole.IT_ADMIN, UserRole.IT_STAFF, UserRole.EMPLOYEE]) as RequestHandler,
  (req, res, next) => {
    ticketController.createTicket(req as any, res).catch(next);
  }
);

// Update ticket status
router.patch('/:id/status', 
  authMiddleware.checkRoles([UserRole.IT_ADMIN, UserRole.IT_STAFF]) as RequestHandler,
  (req, res, next) => {
    ticketController.updateStatus(req as any, res).catch(next);
  }
);

// Lock ticket
router.post('/:id/lock',
  authMiddleware.checkRoles([UserRole.IT_ADMIN, UserRole.IT_STAFF]) as RequestHandler,
  (req, res, next) => {
    ticketController.lockTicket(req as any, res).catch(next);
  }
);

// Unlock ticket
router.post('/:id/unlock',
  authMiddleware.checkRoles([UserRole.IT_ADMIN, UserRole.IT_STAFF]) as RequestHandler,
  (req, res, next) => {
    ticketController.unlockTicket(req as any, res).catch(next);
  }
);

// Update ticket
router.patch('/:id',
  authMiddleware.checkRoles([UserRole.IT_ADMIN, UserRole.IT_STAFF]) as RequestHandler,
  (req, res, next) => {
    ticketController.updateTicket(req as any, res).catch(next);
  }
);

// Delete ticket
router.delete('/:id',
  authMiddleware.checkRoles([UserRole.IT_ADMIN]) as RequestHandler,
  (req, res, next) => {
    ticketController.deleteTicket(req as any, res).catch(next);
  }
);

// Add comment to ticket
router.post('/:ticketId/comments', 
  (req, res, next) => {
    ticketController.addComment(req as any, res).catch(next);
  }
);

// Add file upload route
router.post('/:ticketId/attachments',
  authMiddleware.verify as RequestHandler,
  (req, res, next) => {
    ticketController.uploadAttachments(req as any, res).catch(next);
  }
);

// Start working on a ticket (combines lock and status change)
router.post('/:id/start-working',
  authMiddleware.checkRoles([UserRole.IT_ADMIN, UserRole.IT_STAFF]) as RequestHandler,
  async (req: AuthRequest, res: Response) => {
    try {
      const { id } = req.params;
      const user = req.user;

      if (!user) {
        console.error('Start working failed: No user found in request');
        return res.status(401).json({ error: 'Unauthorized' });
      }

      console.log(`Attempting to start working on ticket ${id} by user ${user.id}`);

      // Start a transaction
      await AppDataSource.manager.transaction(async (transactionalEntityManager: EntityManager) => {
        const ticket = await transactionalEntityManager.findOne(Ticket, {
          where: { id: parseInt(id) },
          relations: ['createdBy', 'assignedTo', 'lockedBy']
        });

        if (!ticket) {
          console.error(`Start working failed: Ticket ${id} not found`);
          return res.status(404).json({ error: 'Ticket not found' });
        }

        console.log(`Current ticket state - Status: ${ticket.status}, LockedBy: ${ticket.lockedById}`);

        // Check if ticket is already locked
        if (ticket.lockedById) {
          if (ticket.lockedById === user.id) {
            console.error(`Start working failed: User ${user.id} is already working on ticket ${id}`);
            return res.status(400).json({ 
              error: 'You are already working on this ticket',
              ticket: ticket
            });
          }
          console.error(`Start working failed: Ticket ${id} is locked by user ${ticket.lockedById}`);
          return res.status(400).json({ 
            error: 'Ticket is locked by another user',
            ticket: ticket
          });
        }

        // Check if ticket is in OPEN status
        if (ticket.status !== TicketStatus.OPEN) {
          console.error(`Start working failed: Ticket ${id} is not in OPEN status (current: ${ticket.status})`);
          return res.status(400).json({ 
            error: `Can only start working on OPEN tickets. Current status: ${ticket.status}`,
            ticket: ticket
          });
        }

        // Update ticket status and lock
        ticket.status = TicketStatus.IN_PROGRESS;
        ticket.lockedById = user.id;
        ticket.lockedBy = user;
        ticket.lockedAt = new Date();

        console.log(`Updating ticket ${id} - Setting status to IN_PROGRESS and locking for user ${user.id}`);

        // Save the updated ticket
        const updatedTicket = await transactionalEntityManager.save(ticket);

        // Create system comment
        const comment = transactionalEntityManager.create(Comment, {
          content: `Ticket status changed to IN_PROGRESS - ${user.name} started working on this ticket`,
          ticket,
          createdBy: user,
          createdById: user.id,
          ticketId: ticket.id,
          isSystemComment: true
        });

        await transactionalEntityManager.save(comment);

        console.log(`Successfully started working on ticket ${id}`);

        // Return updated ticket
        return res.json(updatedTicket);
      });
    } catch (error) {
      console.error('Error starting work on ticket:', error);
      return res.status(500).json({ 
        error: 'Failed to start working on ticket',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

export default router;


