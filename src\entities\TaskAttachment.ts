import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { IsNotEmpty, IsOptional, Length } from 'class-validator';
import { User } from './User';
import { Task } from './Task';

@Entity('task_attachments')
export class TaskAttachment {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Original filename is required' })
  @Length(1, 255, { message: 'Filename must be between 1 and 255 characters' })
  originalName: string;

  @Column({ type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Stored filename is required' })
  storedName: string;

  @Column({ type: 'varchar', length: 500 })
  @IsNotEmpty({ message: 'File path is required' })
  filePath: string;

  @Column({ type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'MIME type is required' })
  mimeType: string;

  @Column({ type: 'bigint' })
  fileSize: number;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  description: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  // Relations
  @ManyToOne(() => Task, task => task.attachments, { nullable: false, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'taskId' })
  task: Task;

  @Column({ type: 'int' })
  taskId: number;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'uploadedById' })
  uploadedBy: User;

  @Column({ type: 'uuid' })
  uploadedById: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;
}
