interface Logger {
  error: (...args: unknown[]) => void;
  warn: (...args: unknown[]) => void;
  info: (...args: unknown[]) => void;
  debug: (...args: unknown[]) => void;
  log: (...args: unknown[]) => void;
}

// Simple browser logger implementation
const browserLogger: Logger = {
  error: (...args: unknown[]) => console.error('[ERROR]', ...args),
  warn: (...args: unknown[]) => console.warn('[WARN]', ...args),
  info: (...args: unknown[]) => console.info('[INFO]', ...args),
  debug: (...args: unknown[]) => console.debug('[DEBUG]', ...args),
  log: (...args: unknown[]) => console.log('[LOG]', ...args)
};

// Export browser logger directly - no conditional imports
export default browserLogger;