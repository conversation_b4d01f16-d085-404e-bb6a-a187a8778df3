import React, { useEffect, useMemo } from 'react';
import { XCircle, AlertTriangle, CheckCircle, Activity, Clock, User, LogIn } from 'lucide-react';
import { SystemLog } from './types';
import { formatSystemDate } from './utils/dateFormat';
import { DateDisplay } from './DateDisplay';

interface LogCardProps {
  log: SystemLog;
  onClick: () => void;
  allLogs: SystemLog[];
}

/**
 * Simply pass through the date string without modification
 */
const ensureSystemTimeZone = (dateString: string): string => {
  return dateString;
};

export const LogCard: React.FC<LogCardProps> = ({ log, onClick, allLogs }) => {
  // Format date directly without conversion
  const formatDate = (dateString: string) => {
    return dateString;
  };

  // Check if this is a login event
  const isLoginEvent = useMemo(() => {
    return log.action.toLowerCase().includes('login') || 
           log.action.toLowerCase().includes('logged in');
  }, [log.action]);

  // Get the most recent login time for this user
  const lastLoginTime = useMemo(() => {
    if (!allLogs) return null;
    
    const loginLogs = allLogs
      .filter(l => l.user === log.user && (
        l.action.toLowerCase().includes('login') || 
        l.action.toLowerCase().includes('logged in')
      ))
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
    return loginLogs.length > 0 ? loginLogs[0].timestamp : null;
  }, [allLogs, log.user]);

  // Get background color based on log type
  const getBackgroundColor = (type: string) => {
    if (isLoginEvent) {
      return 'bg-green-50 dark:bg-green-900/10 border-green-200 dark:border-green-800/30';
    }
    
    switch(type) {
      case 'error':
        return 'bg-red-50 dark:bg-red-900/10 border-red-200 dark:border-red-800/30';
      case 'warning':
        return 'bg-yellow-50 dark:bg-yellow-900/10 border-yellow-200 dark:border-yellow-800/30';
      case 'success':
        return 'bg-green-50 dark:bg-green-900/10 border-green-200 dark:border-green-800/30';
      default:
        return 'bg-blue-50 dark:bg-blue-900/10 border-blue-200 dark:border-blue-800/30';
    }
  };

  // Get icon based on log type
  const getIcon = (type: string) => {
    if (isLoginEvent) {
      return <LogIn className="h-5 w-5 text-green-600 dark:text-green-400" />;
    }
    
    switch(type) {
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600 dark:text-red-400" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />;
      default:
        return <Activity className="h-5 w-5 text-blue-600 dark:text-blue-400" />;
    }
  };

  // Get badge text color based on log type
  const getTextColor = (type: string) => {
    if (isLoginEvent) {
      return 'text-green-800 dark:text-green-300';
    }
    
    switch(type) {
      case 'error':
        return 'text-red-800 dark:text-red-300';
      case 'warning':
        return 'text-yellow-800 dark:text-yellow-300';
      case 'success':
        return 'text-green-800 dark:text-green-300';
      default:
        return 'text-blue-800 dark:text-blue-300';
    }
  };

  return (
    <div 
      className={`p-4 rounded-lg cursor-pointer border transition-all h-[190px] flex flex-col ${getBackgroundColor(log.type)} hover:shadow-md dark:hover:shadow-gray-900/30 hover:translate-y-[-2px]`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between mb-2.5">
        <div className="flex items-center gap-1.5 flex-1 min-w-0">
          {getIcon(log.type)}
          <span className={`font-medium ${getTextColor(log.type)} truncate`}>
            {log.action}
          </span>
        </div>
        <div className={`text-xs ml-2 px-2 py-0.5 rounded-full flex-shrink-0 font-medium ${getTextColor(log.type)}`}>
          {log.type.charAt(0).toUpperCase() + log.type.slice(1)}
        </div>
      </div>
      
      <div className="flex-1">
        <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3 mb-auto">
          {log.details}
        </p>
      </div>
      
      <div className="flex flex-col gap-1.5 text-xs text-gray-500 dark:text-gray-400 pt-2 mt-2 border-t border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-1">
            <User className="h-3.5 w-3.5" />
            <span className="truncate max-w-[90px]" title={log.user}>{log.user}</span>
          </div>
          <DateDisplay 
            timestamp={log.timestamp} 
            className="ml-auto" 
            showIcons={false}
          />
        </div>
        
        {lastLoginTime && (
          <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
            <LogIn className="h-3.5 w-3.5" />
            <span className="whitespace-nowrap">System Login:</span>
            <DateDisplay 
              timestamp={lastLoginTime}
              className="ml-auto" 
              showIcons={false}
            />
          </div>
        )}
      </div>
    </div>
  );
}; 