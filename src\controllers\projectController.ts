import { Request, Response } from 'express';
import { AppDataSource } from '../config/database';
import { Project, ProjectStatus, ProjectPriority } from '../entities/Project';
import { ProjectMember, ProjectRole } from '../entities/ProjectMember';
import { Task } from '../entities/Task';
import { User } from '../entities/User';
import { validate } from 'class-validator';
import { In, Like, Between } from 'typeorm';

export class ProjectController {
  private projectRepository = AppDataSource.getRepository(Project);
  private projectMemberRepository = AppDataSource.getRepository(ProjectMember);
  private taskRepository = AppDataSource.getRepository(Task);
  private userRepository = AppDataSource.getRepository(User);

  // Get all projects with filtering and pagination
  async getAllProjects(req: Request, res: Response) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        priority,
        department,
        managerId,
        search,
        startDate,
        endDate
      } = req.query;

      const queryBuilder = this.projectRepository
        .createQueryBuilder('project')
        .leftJoinAndSelect('project.createdBy', 'createdBy')
        .leftJoinAndSelect('project.manager', 'manager')
        .leftJoinAndSelect('project.members', 'members')
        .leftJoinAndSelect('members.user', 'memberUser')
        .leftJoinAndSelect('project.tasks', 'tasks')
        .where('project.isActive = :isActive', { isActive: true });

      // Apply filters
      if (status) {
        const statusArray = Array.isArray(status) ? status : [status];
        queryBuilder.andWhere('project.status IN (:...status)', { status: statusArray });
      }

      if (priority) {
        const priorityArray = Array.isArray(priority) ? priority : [priority];
        queryBuilder.andWhere('project.priority IN (:...priority)', { priority: priorityArray });
      }

      if (department) {
        const departmentArray = Array.isArray(department) ? department : [department];
        queryBuilder.andWhere('project.department IN (:...department)', { department: departmentArray });
      }

      if (managerId) {
        queryBuilder.andWhere('project.managerId = :managerId', { managerId });
      }

      if (search) {
        queryBuilder.andWhere(
          '(project.name LIKE :search OR project.description LIKE :search OR project.clientName LIKE :search)',
          { search: `%${search}%` }
        );
      }

      if (startDate && endDate) {
        queryBuilder.andWhere('project.startDate BETWEEN :startDate AND :endDate', {
          startDate,
          endDate
        });
      }

      // Pagination
      const skip = (Number(page) - 1) * Number(limit);
      queryBuilder.skip(skip).take(Number(limit));

      // Order by creation date
      queryBuilder.orderBy('project.createdAt', 'DESC');

      const [projects, total] = await queryBuilder.getManyAndCount();

      res.json({
        projects,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error('Error fetching projects:', error);
      res.status(500).json({ error: 'Failed to fetch projects' });
    }
  }

  // Get project by ID
  async getProjectById(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const project = await this.projectRepository
        .createQueryBuilder('project')
        .leftJoinAndSelect('project.createdBy', 'createdBy')
        .leftJoinAndSelect('project.manager', 'manager')
        .leftJoinAndSelect('project.members', 'members')
        .leftJoinAndSelect('members.user', 'memberUser')
        .leftJoinAndSelect('project.tasks', 'tasks')
        .leftJoinAndSelect('tasks.assignedTo', 'taskAssignedTo')
        .leftJoinAndSelect('tasks.createdBy', 'taskCreatedBy')
        .where('project.id = :id', { id })
        .andWhere('project.isActive = :isActive', { isActive: true })
        .getOne();

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      res.json(project);
    } catch (error) {
      console.error('Error fetching project:', error);
      res.status(500).json({ error: 'Failed to fetch project' });
    }
  }

  // Create new project
  async createProject(req: Request, res: Response) {
    try {
      const userId = (req as any).user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const projectData = req.body;
      
      // Create project instance
      const project = this.projectRepository.create({
        ...projectData,
        createdById: userId
      });

      // Validate project data
      const errors = await validate(project);
      if (errors.length > 0) {
        return res.status(400).json({ 
          error: 'Validation failed', 
          details: errors.map(err => Object.values(err.constraints || {})).flat()
        });
      }

      // Save project
      const savedProject = await this.projectRepository.save(project);

      // Add creator as project owner
      const projectMember = this.projectMemberRepository.create({
        projectId: savedProject.id,
        userId: userId,
        role: ProjectRole.OWNER,
        addedById: userId,
        joinedDate: new Date().toISOString().split('T')[0]
      });

      await this.projectMemberRepository.save(projectMember);

      // Fetch the complete project with relations
      const completeProject = await this.projectRepository
        .createQueryBuilder('project')
        .leftJoinAndSelect('project.createdBy', 'createdBy')
        .leftJoinAndSelect('project.manager', 'manager')
        .leftJoinAndSelect('project.members', 'members')
        .leftJoinAndSelect('members.user', 'memberUser')
        .where('project.id = :id', { id: savedProject.id })
        .getOne();

      res.status(201).json(completeProject);
    } catch (error) {
      console.error('Error creating project:', error);
      res.status(500).json({ error: 'Failed to create project' });
    }
  }

  // Update project
  async updateProject(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;
      const updateData = req.body;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const project = await this.projectRepository.findOne({
        where: { id: Number(id), isActive: true },
        relations: ['members', 'members.user']
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      // Check if user has permission to update project
      const isOwnerOrManager = project.createdById === userId || 
                              project.managerId === userId ||
                              project.members.some(member => 
                                member.userId === userId && 
                                [ProjectRole.OWNER, ProjectRole.MANAGER].includes(member.role)
                              );

      if (!isOwnerOrManager) {
        return res.status(403).json({ error: 'Insufficient permissions to update project' });
      }

      // Update project
      await this.projectRepository.update(id, updateData);

      // Fetch updated project
      const updatedProject = await this.projectRepository
        .createQueryBuilder('project')
        .leftJoinAndSelect('project.createdBy', 'createdBy')
        .leftJoinAndSelect('project.manager', 'manager')
        .leftJoinAndSelect('project.members', 'members')
        .leftJoinAndSelect('members.user', 'memberUser')
        .where('project.id = :id', { id })
        .getOne();

      res.json(updatedProject);
    } catch (error) {
      console.error('Error updating project:', error);
      res.status(500).json({ error: 'Failed to update project' });
    }
  }

  // Delete project (soft delete)
  async deleteProject(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const project = await this.projectRepository.findOne({
        where: { id: Number(id), isActive: true },
        relations: ['members']
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      // Check if user has permission to delete project
      const isOwner = project.createdById === userId ||
                     project.members.some(member =>
                       member.userId === userId && member.role === ProjectRole.OWNER
                     );

      if (!isOwner) {
        return res.status(403).json({ error: 'Only project owners can delete projects' });
      }

      // Soft delete project
      await this.projectRepository.update(id, { isActive: false });

      res.json({ message: 'Project deleted successfully' });
    } catch (error) {
      console.error('Error deleting project:', error);
      res.status(500).json({ error: 'Failed to delete project' });
    }
  }

  // Get project statistics
  async getProjectStats(req: Request, res: Response) {
    try {
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Get total projects
      const totalProjects = await this.projectRepository.count({
        where: { isActive: true }
      });

      // Get active projects
      const activeProjects = await this.projectRepository.count({
        where: { isActive: true, status: ProjectStatus.ACTIVE }
      });

      // Get completed projects
      const completedProjects = await this.projectRepository.count({
        where: { isActive: true, status: ProjectStatus.COMPLETED }
      });

      // Get total tasks
      const totalTasks = await this.taskRepository.count({
        where: { isActive: true }
      });

      // Get completed tasks
      const completedTasks = await this.taskRepository.count({
        where: { isActive: true, status: 'done' }
      });

      // Get overdue tasks
      const today = new Date().toISOString().split('T')[0];
      const overdueTasks = await this.taskRepository
        .createQueryBuilder('task')
        .where('task.isActive = :isActive', { isActive: true })
        .andWhere('task.dueDate < :today', { today })
        .andWhere('task.status != :status', { status: 'done' })
        .getCount();

      // Get user's projects
      const myProjects = await this.projectMemberRepository.count({
        where: { userId, isActive: true }
      });

      // Get user's tasks
      const myTasks = await this.taskRepository.count({
        where: { assignedToId: userId, isActive: true }
      });

      const stats = {
        totalProjects,
        activeProjects,
        completedProjects,
        totalTasks,
        completedTasks,
        overdueTasks,
        myProjects,
        myTasks
      };

      res.json(stats);
    } catch (error) {
      console.error('Error fetching project stats:', error);
      res.status(500).json({ error: 'Failed to fetch project statistics' });
    }
  }

  // Get user's projects
  async getMyProjects(req: Request, res: Response) {
    try {
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const projects = await this.projectRepository
        .createQueryBuilder('project')
        .leftJoinAndSelect('project.createdBy', 'createdBy')
        .leftJoinAndSelect('project.manager', 'manager')
        .leftJoinAndSelect('project.members', 'members')
        .leftJoinAndSelect('members.user', 'memberUser')
        .leftJoin('project.members', 'userMember')
        .where('project.isActive = :isActive', { isActive: true })
        .andWhere('(project.createdById = :userId OR userMember.userId = :userId)', { userId })
        .orderBy('project.createdAt', 'DESC')
        .getMany();

      res.json(projects);
    } catch (error) {
      console.error('Error fetching user projects:', error);
      res.status(500).json({ error: 'Failed to fetch user projects' });
    }
  }
}
