import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { dashboardController } from '../controllers/dashboardController';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();

// Protected route - requires authentication
router.get('/stats', authMiddleware.verify as <PERSON>quest<PERSON><PERSON><PERSON>, dashboardController.getStats as RequestHandler);

export default router;
