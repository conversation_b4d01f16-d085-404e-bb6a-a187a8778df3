import { User } from '../types/user';
import bcrypt from 'bcryptjs';

type UserRole = 'IT_ADMIN' | 'IT_STAFF' | 'EMPLOYEE' | 'CEO' | 'FINANCE_MANAGER' | 'DEPT_HEAD' | 'VIEW';

export class MemoryStore {
  private tickets: Map<string, any> = new Map();
  private users: Map<string, User> = new Map();

  constructor() {
    this.initializeData();
  }

  private async initializeData() {
    const hashedPassword = await bcrypt.hash('admin123', 10);
    this.users.set('user-1', {
      id: 'user-1',
      name: 'Test Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'IT_ADMIN',
      department: 'IT',
      isActive: true,
      permissions: ['VIEW_ALL_TICKETS', 'CREATE_TICKETS', 'MANAGE_USERS'],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    const hashedPassword2 = await bcrypt.hash('user123', 10);
    this.users.set('user-2', {
      id: 'user-2',
      name: 'Test User',
      email: '<EMAIL>',
      password: hashedPassword2,
      role: 'EMPLOYEE',
      department: 'Support',
      isActive: true,
      permissions: ['CREATE_TICKETS'],
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }

  // Add methods for ticket operations
  createTicket(ticket: any) {
    this.tickets.set(ticket.id, ticket);
    return ticket;
  }

  getTickets() {
    return Array.from(this.tickets.values());
  }

  getTicketById(id: string) {
    return this.tickets.get(id);
  }

  updateTicket(id: string, data: any) {
    const ticket = this.tickets.get(id);
    if (!ticket) return null;
    const updated = { ...ticket, ...data };
    this.tickets.set(id, updated);
    return updated;
  }

  deleteTicket(id: string) {
    return this.tickets.delete(id);
  }
}

export const memoryStore = new MemoryStore();

