import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  FileText, 
  PlusCircle, 
  Edit, 
  Check, 
  Search, 
  ArrowLeft,
  ArrowRight,
  Filter
} from 'lucide-react';
import { 
  hrPrimaryButtonStyle, 
  hrSecondaryButtonStyle, 
  hrCardStyle,
  hrInputStyle,
  hrSectionTitleStyle,
  hrSubsectionTitleStyle,
  hrInfoAlertStyle,
  hrSuccessAlertStyle
} from '../../../styles/hrWorkflow';
import { useAuth } from '../../../contexts/AuthContext';
import employeePortalService from '../../../services/EmployeePortalService';

// Interface for leave request data
interface LeaveRequest {
  id: number;
  type: string;
  startDate: string;
  endDate: string;
  days: number;
  status: string;
  reason: string;
  approvedBy: string | null;
  appliedOn: string;
  rejectionReason?: string;
}

// Interface for leave balance data
interface LeaveBalance {
  leaveType: string;
  total: number;
  used: number;
  pending: number;
  remaining: number;
  carryForward?: number;
  expiryDate?: string;
}

const EmployeeLeaveView: React.FC = () => {
  const { user } = useAuth();
  const [currentView, setCurrentView] = useState<'summary' | 'details' | 'apply'>('summary');
  const [leaveRecords, setLeaveRecords] = useState<LeaveRequest[]>([]);
  const [leaveBalances, setLeaveBalances] = useState<LeaveBalance[]>([]);
  const [selectedLeave, setSelectedLeave] = useState<LeaveRequest | null>(null);
  const [filterYear, setFilterYear] = useState<string>(new Date().getFullYear().toString());
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [leaveForm, setLeaveForm] = useState({
    type: 'ANNUAL_LEAVE',
    startDate: '',
    endDate: '',
    reason: '',
    contactInfo: ''
  });

  // Load leave data on component mount
  useEffect(() => {
    const loadLeaveData = async () => {
      if (!user?.id) return;
      
      try {
        setLoading(true);
        const leaveData = await employeePortalService.getEmployeeLeaveData(parseInt(user.id));
        
        if (leaveData) {
          setLeaveBalances(leaveData.balances);
          
          // Transform recent requests to match the expected format
          const transformedRecords: LeaveRequest[] = leaveData.recentRequests.map(req => ({
            id: req.id,
            type: req.type,
            startDate: req.from,
            endDate: req.to,
            days: req.days,
            status: req.status,
            reason: req.reason || '',
            approvedBy: null,
            appliedOn: new Date().toISOString()
          }));
          
          setLeaveRecords(transformedRecords);
        }
      } catch (error) {
        console.error('Error loading leave data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadLeaveData();
  }, [user?.id]);

  // Helper function to get leave balance by type
  const getLeaveBalance = (leaveType: string) => {
    const balance = leaveBalances.find(b => b.leaveType.toLowerCase().includes(leaveType.toLowerCase()));
    return balance || { 
      leaveType, 
      total: 0, 
      used: 0, 
      pending: 0, 
      remaining: 0 
    };
  };

  // Filter leave records by year
  const filteredRecords = leaveRecords.filter((record: LeaveRequest) => 
    record.startDate.startsWith(filterYear)
  );

  // Format date for display
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status badge based on leave status
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Approved
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <AlertCircle className="h-3 w-3 mr-1" />
            Rejected
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  // Handle leave application form submission
  const handleLeaveSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!leaveForm.reason.trim()) {
      alert('Please provide a reason for your leave');
      return;
    }
    
    if (!leaveForm.startDate || !leaveForm.endDate) {
      alert('Please select start and end dates');
      return;
    }

    // Create new request
    const startDate = new Date(leaveForm.startDate);
    const endDate = new Date(leaveForm.endDate);
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
    
    const newLeave = {
      id: Math.max(...leaveRecords.map(r => r.id), 0) + 1,
      type: leaveForm.type,
      startDate: leaveForm.startDate,
      endDate: leaveForm.endDate,
      days: diffDays,
      status: 'pending',
      reason: leaveForm.reason,
      approvedBy: null,
      appliedOn: new Date().toISOString()
    };
    
    setLeaveRecords([...leaveRecords, newLeave]);
    setSuccessMessage('Your leave application has been submitted successfully.');
    setCurrentView('summary');
    
    // Clear success message after 5 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 5000);
  };

  // Render summary view
  const renderSummaryView = () => (
    <>
      <div className="flex justify-between items-center mb-6">
        <h2 className={hrSectionTitleStyle}>
          <Calendar className="h-6 w-6 mr-2 text-blue-500" />
          My Leave
        </h2>
        <div className="flex items-center space-x-3">
          <div className="relative">
            <select
              className={hrInputStyle}
              value={filterYear}
              onChange={(e) => setFilterYear(e.target.value)}
            >
              <option value="2023">2023</option>
              <option value="2022">2022</option>
              <option value="2021">2021</option>
            </select>
            <Filter className="h-4 w-4 text-gray-400 absolute right-2 top-2.5" />
          </div>
          <button
            className={hrPrimaryButtonStyle}
            onClick={() => setCurrentView('apply')}
          >
            <PlusCircle className="h-4 w-4 mr-2" />
            Apply Leave
          </button>
        </div>
      </div>

      {/* Success message */}
      {successMessage && (
        <div className={hrSuccessAlertStyle}>
          <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
          <p>{successMessage}</p>
        </div>
      )}

      {/* Leave Balance */}
      <div className="bg-white rounded-lg shadow-sm p-5 mb-6">
        <h3 className={hrSubsectionTitleStyle + " mb-4"}>Leave Balance</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="border rounded-md p-4 bg-blue-50 border-blue-100">
            <div className="text-sm font-medium text-gray-500 mb-1">Annual Leave</div>
            <div className="flex justify-between">
              <div className="text-2xl font-bold text-blue-800">{getLeaveBalance('annual').remaining}</div>
              <div className="text-sm text-gray-500">
                <span className="block">Used: {getLeaveBalance('annual').used}</span>
                <span className="block">Pending: {getLeaveBalance('annual').pending}</span>
              </div>
            </div>
          </div>
          
          <div className="border rounded-md p-4 bg-green-50 border-green-100">
            <div className="text-sm font-medium text-gray-500 mb-1">Sick Leave</div>
            <div className="flex justify-between">
              <div className="text-2xl font-bold text-green-800">{getLeaveBalance('sick').remaining}</div>
              <div className="text-sm text-gray-500">
                <span className="block">Used: {getLeaveBalance('sick').used}</span>
                <span className="block">Pending: {getLeaveBalance('sick').pending}</span>
              </div>
            </div>
          </div>
          
          <div className="border rounded-md p-4 bg-purple-50 border-purple-100">
            <div className="text-sm font-medium text-gray-500 mb-1">Casual Leave</div>
            <div className="flex justify-between">
              <div className="text-2xl font-bold text-purple-800">{getLeaveBalance('casual').remaining}</div>
              <div className="text-sm text-gray-500">
                <span className="block">Used: {getLeaveBalance('casual').used}</span>
                <span className="block">Pending: {getLeaveBalance('casual').pending}</span>
              </div>
            </div>
          </div>
          
          <div className="border rounded-md p-4 bg-gray-50 border-gray-100">
            <div className="text-sm font-medium text-gray-500 mb-1">Total Allocation</div>
            <div className="flex justify-between">
              <div className="text-2xl font-bold text-gray-800">{leaveBalances.reduce((sum, b) => sum + b.total, 0)}</div>
              <div className="text-sm text-gray-500">
                <span className="block">Used: {leaveBalances.reduce((sum, b) => sum + b.used, 0)}</span>
                <span className="block">Pending: {leaveBalances.reduce((sum, b) => sum + b.pending, 0)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Leave History */}
      <div className="border rounded-md overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Days</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applied On</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredRecords.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                  No leave records found for this year
                </td>
              </tr>
            ) : (
              filteredRecords.map(record => (
                <tr 
                  key={record.id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    setSelectedLeave(record);
                    setCurrentView('details');
                  }}
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {record.type}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(record.startDate)} - {formatDate(record.endDate)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {record.days}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(record.appliedOn).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(record.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button
                      className="text-blue-600 hover:text-blue-800 font-medium text-sm inline-flex items-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedLeave(record);
                        setCurrentView('details');
                      }}
                    >
                      <FileText className="h-3 w-3 mr-1" />
                      View
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </>
  );

  // Render details view for a specific leave
  const renderDetailsView = () => {
    if (!selectedLeave) {
      return (
        <div className="text-center py-8">
          <p className="text-gray-500">No leave record selected.</p>
          <button
            className={hrSecondaryButtonStyle + " mt-4"}
            onClick={() => setCurrentView('summary')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Summary
          </button>
        </div>
      );
    }
    
    return (
      <>
        <div className="flex items-center mb-6">
          <button
            className={hrSecondaryButtonStyle + " mr-4"}
            onClick={() => setCurrentView('summary')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </button>
          <h2 className={hrSectionTitleStyle}>
            <Calendar className="h-6 w-6 mr-2 text-blue-500" />
            Leave Details
          </h2>
        </div>
        
        <div className={hrCardStyle}>
          <div className="flex justify-between items-start mb-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900">{selectedLeave.type}</h3>
              <p className="text-sm text-gray-500">
                Applied on {new Date(selectedLeave.appliedOn).toLocaleDateString()}
              </p>
            </div>
            <div>
              {getStatusBadge(selectedLeave.status)}
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-6 mb-6">
            <div className="border rounded-md p-4 bg-gray-50">
              <div className="text-sm font-medium text-gray-500 mb-1">Start Date</div>
              <div className="text-xl font-bold text-gray-900 flex items-center">
                <Calendar className="h-5 w-5 mr-2 text-blue-500" />
                {formatDate(selectedLeave.startDate)}
              </div>
            </div>
            
            <div className="border rounded-md p-4 bg-gray-50">
              <div className="text-sm font-medium text-gray-500 mb-1">End Date</div>
              <div className="text-xl font-bold text-gray-900 flex items-center">
                <Calendar className="h-5 w-5 mr-2 text-blue-500" />
                {formatDate(selectedLeave.endDate)}
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <div className="text-sm font-medium text-gray-500 mb-1">Total Days</div>
            <div className="text-xl font-bold text-gray-900">
              {selectedLeave.days} day{selectedLeave.days !== 1 ? 's' : ''}
            </div>
          </div>
          
          <div className="mb-6">
            <div className="text-sm font-medium text-gray-500 mb-1">Reason for Leave</div>
            <div className="border rounded-md p-4 bg-gray-50 text-gray-900">
              {selectedLeave.reason}
            </div>
          </div>
          
          {selectedLeave.approvedBy && (
            <div className="mb-6">
              <div className="text-sm font-medium text-gray-500 mb-1">Approved By</div>
              <div className="text-gray-900">
                {selectedLeave.approvedBy}
              </div>
            </div>
          )}

          {selectedLeave.rejectionReason && (
            <div className="mb-6">
              <div className="text-sm font-medium text-gray-500 mb-1">Rejection Reason</div>
              <div className="border rounded-md p-4 bg-red-50 text-red-800">
                {selectedLeave.rejectionReason}
              </div>
            </div>
          )}
          
          {selectedLeave.status === 'pending' && (
            <div className="border-t pt-4 mt-4 flex justify-end">
              <button
                className={hrSecondaryButtonStyle + " mr-3"}
                onClick={() => {
                  if (window.confirm('Are you sure you want to cancel this leave request?')) {
                    setLeaveRecords(leaveRecords.filter(record => record.id !== selectedLeave.id));
                    setSuccessMessage('Your leave request has been cancelled.');
                    setCurrentView('summary');
                  }
                }}
              >
                Cancel Request
              </button>
            </div>
          )}
        </div>
      </>
    );
  };

  // Render apply leave form
  const renderApplyView = () => (
    <>
      <div className="flex items-center mb-6">
        <button
          className={hrSecondaryButtonStyle + " mr-4"}
          onClick={() => setCurrentView('summary')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </button>
        <h2 className={hrSectionTitleStyle}>
          <PlusCircle className="h-6 w-6 mr-2 text-blue-500" />
          Apply for Leave
        </h2>
      </div>
      
      <div className={hrCardStyle}>
        <form onSubmit={handleLeaveSubmit}>
          <div className="mb-6">
            <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
              Leave Type <span className="text-red-500">*</span>
            </label>
            <select
              id="type"
              className={hrInputStyle}
              value={leaveForm.type}
              onChange={(e) => setLeaveForm({...leaveForm, type: e.target.value})}
              required
            >
              <option value="ANNUAL_LEAVE">Annual Leave</option>
              <option value="SICK_LEAVE">Sick Leave</option>
              <option value="CASUAL_LEAVE">Casual Leave</option>
            </select>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                Start Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                id="startDate"
                className={hrInputStyle}
                value={leaveForm.startDate}
                onChange={(e) => setLeaveForm({...leaveForm, startDate: e.target.value})}
                min={new Date().toISOString().split('T')[0]}
                required
              />
            </div>
            
            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                End Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                id="endDate"
                className={hrInputStyle}
                value={leaveForm.endDate}
                onChange={(e) => setLeaveForm({...leaveForm, endDate: e.target.value})}
                min={leaveForm.startDate || new Date().toISOString().split('T')[0]}
                required
              />
            </div>
          </div>
          
          <div className="mb-6">
            <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-1">
              Reason for Leave <span className="text-red-500">*</span>
            </label>
            <textarea
              id="reason"
              rows={4}
              className={hrInputStyle}
              value={leaveForm.reason}
              onChange={(e) => setLeaveForm({...leaveForm, reason: e.target.value})}
              placeholder="Please provide a detailed reason for your leave request..."
              required
            />
          </div>
          
          <div className="mb-6">
            <label htmlFor="contactInfo" className="block text-sm font-medium text-gray-700 mb-1">
              Emergency Contact Information
            </label>
            <input
              type="text"
              id="contactInfo"
              className={hrInputStyle}
              value={leaveForm.contactInfo}
              onChange={(e) => setLeaveForm({...leaveForm, contactInfo: e.target.value})}
              placeholder="Phone number or email where you can be reached during leave..."
            />
          </div>
          
          <div className={hrInfoAlertStyle + " mb-6"}>
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
              <div>
                <p className="text-sm text-blue-700">
                  Your request will be sent to your manager for approval. Please ensure all details are accurate.
                </p>
                {leaveForm.type === 'ANNUAL_LEAVE' && (
                  <p className="text-sm text-blue-700 mt-1">
                    You currently have {getLeaveBalance('annual').remaining} days of annual leave available.
                  </p>
                )}
                {leaveForm.type === 'SICK_LEAVE' && (
                  <p className="text-sm text-blue-700 mt-1">
                    You currently have {getLeaveBalance('sick').remaining} days of sick leave available.
                  </p>
                )}
                {leaveForm.type === 'CASUAL_LEAVE' && (
                  <p className="text-sm text-blue-700 mt-1">
                    You currently have {getLeaveBalance('casual').remaining} days of casual leave available.
                  </p>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              className={hrSecondaryButtonStyle}
              onClick={() => setCurrentView('summary')}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={hrPrimaryButtonStyle}
            >
              <Check className="h-4 w-4 mr-2" />
              Submit Request
            </button>
          </div>
        </form>
      </div>
    </>
  );

  // Render the appropriate view based on current state
  if (loading) {
    return (
      <div className="p-4 flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <p className="text-gray-600">Loading leave data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      {currentView === 'summary' && renderSummaryView()}
      {currentView === 'details' && renderDetailsView()}
      {currentView === 'apply' && renderApplyView()}
    </div>
  );
};

export default EmployeeLeaveView; 