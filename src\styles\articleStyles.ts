export const articleStyles = `
  .article-content {
    color: #374151;
    line-height: 1.6;
    font-size: 1.0625rem;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  .article-content p {
    margin-bottom: 1rem;
    color: #4b5563;
    min-height: 1em;
  }

  .article-content p + p {
    margin-top: 0.5em;
  }

  .article-content br {
    content: "";
    margin-top: 0.25em;
    display: block;
  }

  .article-content h1 {
    font-size: 2.25rem;
    font-weight: 700;
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #111827;
    line-height: 1.2;
    letter-spacing: -0.025em;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.5rem;
  }

  .article-content h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-top: 1.75rem;
    margin-bottom: 1rem;
    color: #1f2937;
    line-height: 1.3;
    letter-spacing: -0.025em;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.5rem;
  }

  .article-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    color: #1f2937;
    line-height: 1.4;
    letter-spacing: -0.025em;
    background-color: #f9fafb;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    border-left: 4px solid #3b82f6;
  }

  .article-content h2 + p,
  .article-content h3 + p {
    margin-top: 0.75rem;
  }

  .article-content strong {
    color: #111827;
    font-weight: 700;
  }

  .article-content h3 + p {
    margin-left: 1rem;
    margin-top: 0.5rem;
  }

  .article-content p strong:first-child {
    display: inline-block;
    margin-right: 0.5rem;
    color: #4b5563;
    font-weight: 700;
  }

  .article-content a {
    color: #2563eb;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    border-bottom: 1px solid transparent;
  }

  .article-content a:hover {
    color: #1d4ed8;
    border-bottom-color: #1d4ed8;
  }

  .article-content ul, .article-content ol {
    margin: 1rem 0;
    padding-left: 1.25rem;
    color: #4b5563;
  }

  .article-content li {
    margin: 0.5rem 0;
    padding-left: 0.5rem;
  }

  .article-content blockquote {
    border-left: 4px solid #3b82f6;
    padding: 1rem 1.25rem;
    margin: 1.5rem 0;
    background: linear-gradient(to right, #f3f4f6, #ffffff);
    border-radius: 0.75rem;
    font-style: italic;
    color: #374151;
  }

  .article-content code {
    background: linear-gradient(to right, #f3f4f6, #f8fafc);
    padding: 0.2rem 0.4rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #1f2937;
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
    border: 1px solid #e5e7eb;
  }

  .article-content pre {
    background: linear-gradient(to bottom right, #f8fafc, #ffffff);
    padding: 1.25rem;
    border-radius: 0.75rem;
    overflow-x: auto;
    margin: 1.5rem 0;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    white-space: pre;
  }

  .article-content pre code {
    background: transparent;
    padding: 0;
    border: none;
    color: #1f2937;
    font-size: 0.875rem;
    line-height: 1.7;
  }

  .article-content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.75rem;
    margin: 1.5rem 0;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  .article-content table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: 1.5rem 0;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    overflow: hidden;
  }

  .article-content th {
    background: linear-gradient(to right, #f9fafb, #ffffff);
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 600;
    color: #111827;
    border-bottom: 1px solid #e5e7eb;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .article-content td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
    color: #4b5563;
    font-size: 0.9375rem;
  }

  .article-content tr:last-child td {
    border-bottom: none;
  }

  .article-content tr:nth-child(even) {
    background: linear-gradient(to right, #f9fafb, #ffffff);
  }

  .article-content hr {
    border: 0;
    height: 1px;
    background: linear-gradient(to right, #e5e7eb, transparent);
    margin: 2rem 0;
  }

  .article-content details {
    margin: 1.5rem 0;
    padding: 1rem;
    background: linear-gradient(to bottom right, #f9fafb, #ffffff);
    border-radius: 0.75rem;
    border: 1px solid #e5e7eb;
  }

  .article-content summary {
    font-weight: 600;
    cursor: pointer;
    color: #111827;
    user-select: none;
  }

  .article-content details[open] summary {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .article-content .note {
    background: linear-gradient(to right, #dbeafe, #eff6ff);
    border-radius: 0.75rem;
    padding: 1rem 1.25rem;
    margin: 1.5rem 0;
    border: 1px solid #bfdbfe;
  }

  .article-content .warning {
    background: linear-gradient(to right, #fef3c7, #fffbeb);
    border-radius: 0.75rem;
    padding: 1rem 1.25rem;
    margin: 1.5rem 0;
    border: 1px solid #fcd34d;
  }

  .article-content .tip {
    background: linear-gradient(to right, #d1fae5, #ecfdf5);
    border-radius: 0.75rem;
    padding: 1rem 1.25rem;
    margin: 1.5rem 0;
    border: 1px solid #6ee7b7;
  }

  .article-content h3 + p strong:first-child {
    color: #4b5563;
    font-weight: 600;
    display: inline-block;
    min-width: 200px;
  }

  .article-content p strong:first-child:after {
    content: " — ";
    font-weight: normal;
    color: #6b7280;
  }

  .article-content h3 {
    color: #1f2937;
    font-weight: 700;
    margin-top: 1.75rem;
    margin-bottom: 0.75rem;
    background-color: #f9fafb;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    border-left: 4px solid #3b82f6;
  }

  .article-content h3 + p {
    padding-left: 1rem;
    border-left: 1px solid #e5e7eb;
    margin-left: 0.5rem;
    margin-bottom: 0.75rem;
  }

  .article-content > blockquote:first-of-type {
    border-left: 4px solid #3b82f6;
    background-color: #f9fafb;
    padding: 1rem 1.25rem;
    margin: 1rem 0 1.5rem 0;
    font-style: normal;
    color: #1f2937;
    font-size: 1.125rem;
    line-height: 1.6;
  }

  /* File reference styling */
  .article-content .file-reference {
    display: inline-block;
    color: #2563eb;
    font-weight: 500;
    margin: 0.5rem 0;
    padding: 0.25rem 0.5rem;
    background-color: #f3f4f6;
    border-radius: 0.375rem;
    border-left: 3px solid #3b82f6;
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    word-break: break-all;
  }

  .article-content ol {
    counter-reset: steps;
    list-style-type: none;
    padding-left: 0;
  }

  .article-content ol li {
    position: relative;
    padding-left: 2.5rem;
    margin-bottom: 0.75rem;
  }

  .article-content ol li:before {
    content: counter(steps);
    counter-increment: steps;
    position: absolute;
    left: 0;
    top: 0;
    width: 1.75rem;
    height: 1.75rem;
    background-color: #3b82f6;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
  }

  /* Numbered list styling for steps */
  .article-content ol.steps {
    counter-reset: steps;
    list-style-type: none;
    padding-left: 0;
    margin: 1.5rem 0;
  }
  
  .article-content ol.steps li.step {
    position: relative;
    padding-left: 2.5rem;
    margin-bottom: 0.75rem;
    min-height: 1.75rem;
    display: flex;
    align-items: center;
  }
  
  .article-content ol.steps li.step:before {
    content: counter(steps);
    counter-increment: steps;
    position: absolute;
    left: 0;
    top: 0;
    width: 1.75rem;
    height: 1.75rem;
    background-color: #3b82f6;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
  }
`; 