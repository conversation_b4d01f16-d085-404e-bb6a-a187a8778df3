import { Request, Response } from 'express';
import { AppDataSource } from '../../config/database';
import { User } from '../../entities/User';
import { UserRole, UserPermissions } from '../../types/common';
import bcrypt from 'bcryptjs';
import { Like } from 'typeorm';
import { Role } from '../../entities/Role';
import { UserRoleAssignment } from '../../entities/UserRoleAssignment';

const userRepository = AppDataSource.getRepository(User);

export const userController = {
  // Helper methods for fallback role data
  getFallbackRolePermissions(roleId: string): string[] {
    const rolePermissionsMap: Record<string, string[]> = {
      'role-1': [
        'canAccessAllModules', 'canConfigureSystem', 'canManageRoles', 
        'canAddUsers', 'canEditUsers', 'canDeleteUsers', 'canViewReports', 
        'canExportData', 'canImportData', 'canViewAllDepartments',
        'canCreateTickets', 'canEditTickets', 'canDeleteTickets', 'canCloseTickets',
        'canCreateEmployee', 'canEditEmployee', 'canDeleteEmployee', 'canViewEmployees',
        'canViewDashboards', 'canViewAllDashboards', 'canCreateDashboards', 'canEditDashboards'
      ],
      'role-2': ['canCreateTickets', 'canEditTickets', 'canCloseTickets'],
      'role-3': ['canViewDepartmentTickets', 'canApproveRequests'],
      'role-4': ['canCreateTickets'],
      'role-5': [
        'canViewDashboards', 'canViewAllDashboards', 'canCreateDashboards', 
        'canEditDashboards', 'canShareDashboards', 'canExportDashboardData'
      ]
    };
    return rolePermissionsMap[roleId] || [];
  },

  getFallbackRoleDescription(roleId: string): string {
    const roleDescriptionMap: Record<string, string> = {
      'role-1': 'Full system access with all permissions across all modules',
      'role-2': 'Manage IT resources and tickets',
      'role-3': 'Manage department resources and approve requests',
      'role-4': 'Basic access to create tickets and view own data',
      'role-5': 'Full access to create and manage dashboards'
    };
    return roleDescriptionMap[roleId] || 'Custom role';
  },

  getFallbackRoleCategory(roleId: string): any {
    const roleCategoryMap: Record<string, string> = {
      'role-1': 'system',
      'role-2': 'system',
      'role-3': 'system',
      'role-4': 'system',
      'role-5': 'dashboard'
    };
    return roleCategoryMap[roleId] || 'custom';
  },

  async createUser(req: Request, res: Response) {
    try {
      const { name, email, password, department, role, oversightPermissions } = req.body;

      // Validate required fields
      if (!name || !email || !password || !department || !role) {
        return res.status(400).json({ error: 'Name, email, password, department, and role are required' });
      }

      const existingUser = await userRepository.findOne({ where: { email: String(email) } });
      if (existingUser) {
        return res.status(400).json({ error: 'User already exists' });
      }

      const hashedPassword = await bcrypt.hash(password, 10);

      // Set default permissions based on role
      const defaultPermissions: UserPermissions = {
        canCreateTickets: true,
        canCreateTicketsForOthers: role === 'IT_ADMIN' || role === 'IT_STAFF',
        canEditTickets: role === 'IT_ADMIN' || role === 'IT_STAFF',
        canDeleteTickets: role === 'IT_ADMIN',
        canCloseTickets: role === 'IT_ADMIN' || role === 'IT_STAFF',
        canLockTickets: role === 'IT_ADMIN',
        canAssignTickets: role === 'IT_ADMIN' || role === 'IT_STAFF',
        canEscalateTickets: role === 'IT_ADMIN' || role === 'IT_STAFF',
        canViewAllTickets: role === 'IT_ADMIN' || role === 'IT_STAFF',
        // HR Permissions with default values
        canCreateEmployee: role === 'IT_ADMIN' || role === 'HR_ADMIN',
        canEditEmployee: role === 'IT_ADMIN' || role === 'HR_ADMIN',
        canDeleteEmployee: role === 'IT_ADMIN',
        canViewEmployees: role === 'IT_ADMIN' || role === 'HR_ADMIN' || role === 'HR_STAFF',
        canManageAttendance: role === 'IT_ADMIN' || role === 'HR_ADMIN',
        canManageLeave: role === 'IT_ADMIN' || role === 'HR_ADMIN',
        canManagePayroll: role === 'IT_ADMIN' || role === 'HR_ADMIN',
        canManagePerformance: role === 'IT_ADMIN' || role === 'HR_ADMIN',
        department: department,
        isAdmin: role === 'IT_ADMIN'
      };

      // Create user
      const user = userRepository.create({
        name: String(name),
        email: String(email),
        password: hashedPassword,
        department: String(department),
        role: role as UserRole,
        permissions: defaultPermissions,
        project: req.body.project || null,
        location: req.body.location || null,
        oversightPermissions: oversightPermissions || {
          departments: [],
          projects: [],
          locations: [],
          hasFullAccess: false
        },
        isActive: true
      });

      const savedUser = await userRepository.save(user);

      // Return user without password
      const { password: _, ...userWithoutPassword } = savedUser;
      return res.status(201).json(userWithoutPassword);
    } catch (error) {
      console.error('Create user error:', error);
      return res.status(500).json({ error: 'Failed to create user' });
    }
  },

  async getUsers(req: Request, res: Response) {
    try {
      const { 
        page = '1',
        limit = '15',
        search = '',
        department = '',
        role = '',
        status = '',
        project = '',
        location = ''
      } = req.query;
      
      // Parse pagination parameters
      const currentPage = Math.max(1, parseInt(String(page)));
      const itemsPerPage = Math.max(1, Math.min(50, parseInt(String(limit))));
      const skip = (currentPage - 1) * itemsPerPage;

      // Build where conditions
      const whereConditions: any = {};
      
      if (search) {
        whereConditions.name = Like(`%${search}%`);
      }
      
      if (department && department !== 'all') {
        whereConditions.department = department;
      }
      
      if (role && role !== 'all') {
        whereConditions.role = role;
      }
      
      if (status) {
        whereConditions.isActive = status === 'active';
      }
      
      // Add project and location filters
      if (project) {
        whereConditions.project = project;
      }
      
      if (location) {
        whereConditions.location = location;
      }

      console.log('User query with filters:', whereConditions);

      // Execute query with findAndCount for better performance
      const [users, total] = await userRepository.findAndCount({
        where: whereConditions,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          department: true,
          project: true,
          location: true,
          isActive: true,
          permissions: true,
          oversightPermissions: {
            departments: true,
            projects: true,
            locations: true,
            hasFullAccess: true
          },
          createdAt: true,
          updatedAt: true
        },
        skip,
        take: itemsPerPage,
        order: {
          name: 'ASC'
        }
      });

      // Get role assignments for each user
      const userRoleRepo = AppDataSource.getRepository(UserRoleAssignment);
      const usersWithRoles = await Promise.all(users.map(async (user) => {
        try {
          console.log(`🔍 Fetching roles for user: ${user.name} (ID: ${user.id})`);
          
          const roleAssignments = await userRoleRepo.find({
            where: { userId: user.id },
            relations: ['role']
          });
          
          console.log(`📋 Found ${roleAssignments.length} role assignments for user ${user.name}:`, 
            roleAssignments.map(ra => ({
              roleId: ra.roleId,
              roleName: ra.role?.name,
              assignedBy: ra.assignedBy
            }))
          );
          
          // Convert role assignments to role IDs for compatibility with frontend
          // Map database numeric IDs back to frontend string IDs if possible
          const roleIds = roleAssignments.map(assignment => {
            const dbRoleId = assignment.roleId;
            const dbRole = assignment.role;
            
            console.log(`🎭 Processing assignment: DB Role ID ${dbRoleId}, Role Object:`, dbRole);
            
            // Try to map back to frontend string IDs based on role name
            if (dbRole) {
              // Updated mapping to match actual database role names (based on current DB state)
              const nameToStringIdMap: Record<string, string> = {
                'System Administrator': 'role-1',    // DB ID: 1
                'HR-Users': 'role-2',               // DB ID: 2  
                'IT-Staff': 'role-3',               // DB ID: 4
                'IT Administrator': 'role-4',       // DB ID: 6
                'Department Head': 'role-5',        // DB ID: 8
                'Employee': 'role-6',               // DB ID: 9
                'Dashboard Manager': 'role-7'       // DB ID: 30
              };
              
              const frontendId = nameToStringIdMap[dbRole.name];
              if (frontendId) {
                console.log(`✅ Mapped role "${dbRole.name}" (DB ID: ${dbRoleId}) to frontend ID: ${frontendId}`);
                return frontendId;
              } else {
                console.log(`❌ No mapping found for role "${dbRole.name}" (DB ID: ${dbRoleId}), using numeric ID`);
              }
            } else {
              console.log(`⚠️ No role object found for assignment with roleId: ${dbRoleId}`);
            }
            
            // Fallback to string representation of numeric ID
            console.log(`🔄 Using fallback string ID: ${String(dbRoleId)}`);
            return String(dbRoleId);
          });
          
          // Remove duplicates from roleIds array
          const uniqueRoleIds = [...new Set(roleIds)];
          
          console.log(`🎯 Final mapped role IDs for user ${user.name}:`, uniqueRoleIds);
          
          return {
            ...user,
            roles: uniqueRoleIds, // Add unique roles array for RolesAndPermissions component
            roleAssignments: roleAssignments // Keep detailed assignments if needed
          };
        } catch (error) {
          console.error(`❌ Error fetching roles for user ${user.id}:`, error);
          return {
            ...user,
            roles: [], // Empty roles if error
            roleAssignments: []
          };
        }
      }));

      return res.json({
        users: usersWithRoles,
        pagination: {
          total,
          currentPage,
          itemsPerPage,
          totalPages: Math.ceil(total / itemsPerPage)
        }
      });

    } catch (error) {
      console.error('Get users error:', error);
      return res.status(500).json({ error: 'Failed to fetch users' });
    }
  },

  async getUserById(req: Request, res: Response) {
    try {
      const userId = req.params.id;
      if (!userId) {
        return res.status(400).json({ error: "Invalid user ID" });
      }

      const user = await userRepository.findOne({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          department: true,
          isActive: true,
          permissions: {
            canCreateTickets: true,
            canCreateTicketsForOthers: true,
            canEditTickets: true,
            canDeleteTickets: true,
            canCloseTickets: true,
            canLockTickets: true,
            canAssignTickets: true,
            canEscalateTickets: true,
            canViewAllTickets: true
          },
          createdAt: true,
          updatedAt: true,
          project: true,
          location: true
        }
      });

      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      return res.json(user);
    } catch (error) {
      console.error("❌ Fetch user error:", error);
      return res.status(500).json({ error: "Failed to get user" });
    }
  },

  async updateUser(req: Request, res: Response) {
    try {
      const userId = req.params.id;
      if (!userId) {
        return res.status(400).json({ error: "Invalid user ID" });
      }

      // Handle role assignment requests (from RolesAndPermissions component)
      if (req.body.role && typeof req.body.role === 'string' && req.body.role.includes(',')) {
        console.log('HANDLING ROLE ASSIGNMENT REQUEST for user:', userId);
        console.log('Roles to assign:', req.body.role);
        
        try {
          // Get repositories
          const userRepo = AppDataSource.getRepository(User);
          const roleRepo = AppDataSource.getRepository(Role);
          const userRoleRepo = AppDataSource.getRepository(UserRoleAssignment);
          
          // Check database connection
          if (!AppDataSource.isInitialized) {
            console.error('❌ Database not initialized');
            return res.status(500).json({
              error: "Database connection not available",
              details: "AppDataSource is not initialized",
              fallback: true,
              timestamp: new Date().toISOString()
            });
          }
          
          console.log('✅ Database connection verified');
          
          // Remove auto-creation of default roles - they should exist from database migration
          // await this.ensureDefaultRoles();
          
          // Check if user exists
          const user = await userRepo.findOne({ where: { id: userId } });
          if (!user) {
            console.warn(`User not found: ${userId}`);
            // Return fallback response
            return res.status(200).json({
              success: true,
              id: userId,
              roles: req.body.role.split(',').filter(Boolean),
              message: 'User roles updated (user not found in database - fallback mode)',
              fallback: true,
              savedToDatabase: false,
              timestamp: new Date().toISOString()
            });
          }
          
          // Parse role IDs from comma-separated string
          const roleIds = req.body.role.split(',').filter(Boolean);
          console.log('Parsed role IDs:', roleIds);
          
          // Create a mapping from frontend string IDs to database roles by name
          const roleStringToDbMapping: Record<string, number | null> = {
            'role-1': null, // Will be populated dynamically
            'role-2': null,
            'role-3': null,
            'role-4': null,
            'role-5': null
          };
          
          // Map role names to find corresponding database IDs
          const roleNameMapping: Record<string, string> = {
            'role-1': 'System Administrator',    // Maps to DB ID: 1
            'role-2': 'HR-Users',               // Maps to DB ID: 2
            'role-3': 'IT-Staff',               // Maps to DB ID: 4  
            'role-4': 'IT Administrator',       // Maps to DB ID: 6
            'role-5': 'Department Head',        // Maps to DB ID: 8
            'role-6': 'Employee',               // Maps to DB ID: 9
            'role-7': 'Dashboard Manager'       // Maps to DB ID: 30
          };
          
          // Clear existing role assignments for this user
          // First, decrement user counts for existing roles
          const existingAssignments = await userRoleRepo.find({ 
            where: { userId },
            relations: ['role']
          });
          
          for (const existingAssignment of existingAssignments) {
            if (existingAssignment.role) {
              const role = existingAssignment.role;
              role.userCount = Math.max(0, (role.userCount || 0) - 1);
              await roleRepo.save(role);
              console.log(`📉 Decremented user count for role ${role.name}: ${role.userCount}`);
            }
          }
          
          // Now delete the assignments
          await userRoleRepo.delete({ userId });
          console.log('✅ Cleared existing role assignments for user:', userId);
          
          // Find or create roles and create assignments
          const assignments = [];
          const validRoles = [];
          const invalidRoles = [];
          const processedRoleIds = new Set(); // Prevent duplicate processing
          
          for (const roleIdStr of roleIds) {
            // Skip if we've already processed this role
            if (processedRoleIds.has(roleIdStr)) {
              console.log(`⚠️ Skipping duplicate role ID: ${roleIdStr}`);
              continue;
            }
            processedRoleIds.add(roleIdStr);
            
            let dbRole = null;
            
            // First, try to parse as numeric ID (if it's already a number)
            if (!isNaN(parseInt(roleIdStr, 10))) {
              const numericId = parseInt(roleIdStr, 10);
              dbRole = await roleRepo.findOne({ where: { id: numericId } });
              console.log(`Found role by numeric ID ${numericId}:`, dbRole?.name);
            }
            
            // If not found and it's a string ID, try to find by name mapping
            if (!dbRole && roleNameMapping[roleIdStr]) {
              const roleName = roleNameMapping[roleIdStr];
              dbRole = await roleRepo.findOne({ where: { name: roleName } });
              console.log(`Found role by name mapping ${roleIdStr} -> ${roleName}:`, dbRole?.name);
              
              // If role doesn't exist, create it as a fallback
              if (!dbRole) {
                console.warn(`Role not found in database: ${roleName}. Creating as fallback for role ID: ${roleIdStr}`);
                
                try {
                  // Create the role with basic permissions
                  const fallbackRole = roleRepo.create({
                  name: roleName,
                  description: this.getFallbackRoleDescription(roleIdStr),
                    permissions: this.getFallbackRolePermissions(roleIdStr),
                  category: this.getFallbackRoleCategory(roleIdStr),
                    userCount: 0,
                    createdAt: new Date(),
                    updatedAt: new Date()
                  });
                  
                  dbRole = await roleRepo.save(fallbackRole);
                  console.log(`✅ Created fallback role: ${roleName} (ID: ${dbRole.id})`);
                } catch (createError) {
                  console.error(`❌ Failed to create fallback role ${roleName}:`, createError);
                  invalidRoles.push(`${roleIdStr} (${roleName} - creation failed)`);
                }
              }
            } else if (!dbRole) {
              // No mapping found for this role ID
              console.warn(`No mapping found for role ID: ${roleIdStr}`);
              invalidRoles.push(`${roleIdStr} (no mapping found)`);
            }
            
            // If still no role found, skip this assignment
            if (!dbRole) {
              console.warn(`Could not find or create role for ID: ${roleIdStr}`);
              continue;
            }
            
            // Create role assignment
            const assignment = userRoleRepo.create({
              userId: userId,
              roleId: dbRole.id, // Use the numeric ID from database
              assignedBy: 'system',
              projectId: req.body.projectId || 'default',
              locationId: req.body.locationId || null,
              departmentId: req.body.departmentId || null,
              notes: `Assigned via role management interface`,
            });
            
            assignments.push(assignment);
            validRoles.push(dbRole.name);
            
            console.log(`✅ Created assignment for role ${dbRole.name} (ID: ${dbRole.id})`);
            
            // UPDATE ROLE USER COUNT
            dbRole.userCount = (dbRole.userCount || 0) + 1;
            await roleRepo.save(dbRole);
            console.log(`📊 Updated user count for role ${dbRole.name}: ${dbRole.userCount}`);
          }
          
          // Save all assignments (if any)
          if (assignments.length > 0) {
            await userRoleRepo.save(assignments);
            console.log(`Saved ${assignments.length} role assignments to database`);
            
            // Return success with detailed info about valid and invalid roles
            return res.status(200).json({
              success: true,
              id: userId,
              roles: roleIds, // Return original role IDs for frontend compatibility
              roleNames: validRoles,
              message: `Successfully assigned ${validRoles.length} roles to user`,
              assignmentsCount: assignments.length,
              savedToDatabase: true,
              validRoles: validRoles,
              invalidRoles: invalidRoles.length > 0 ? invalidRoles : undefined,
              warning: invalidRoles.length > 0 ? `Some roles could not be assigned: ${invalidRoles.join(', ')}` : undefined,
              timestamp: new Date().toISOString()
            });
          } else {
            // FIXED: Handle role unselection (empty assignments) as SUCCESS
            // When all roles are unselected, we have zero assignments - this is intentional and successful
            if (roleIds.length === 0) {
              console.log(`✅ Successfully cleared all role assignments for user ${userId}`);
              return res.status(200).json({
                success: true,
                id: userId,
                roles: [], // Empty array for unselected roles
                roleNames: [],
                message: 'Successfully removed all roles from user',
                assignmentsCount: 0,
                savedToDatabase: true,
                operation: 'role_unselection',
                timestamp: new Date().toISOString()
              });
            } else {
              // Only return error if we attempted to assign roles but all failed
              return res.status(400).json({
                error: "No valid roles found to assign",
                attemptedRoles: roleIds,
                invalidRoles: invalidRoles,
                details: "All requested roles either don't exist in the database or have no mapping defined",
                availableRoles: "Check database migration or contact system administrator",
                troubleshooting: {
                  step1: "Verify database migration has been run",
                  step2: "Check if roles exist in database",
                  step3: "Verify role name mapping in backend code"
                }
              });
            }
          }
          
        } catch (dbError) {
          console.error('Database error during role assignment:', dbError);
          
          // If database fails, return the fallback response but indicate the failure
          return res.status(200).json({
            success: true,
            id: userId,
            roles: req.body.role.split(',').filter(Boolean),
            message: 'Role assignment processed (database unavailable)',
            fallback: true,
            savedToDatabase: false,
            error: 'Database connection failed',
            timestamp: new Date().toISOString()
          });
        }
      }

      const user = await userRepository.findOne({ where: { id: userId } });
      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      const { name, email, password, role, department, isActive, project, location, oversightPermissions } = req.body;
      
      // If this is a status toggle request, only update isActive
      if (isActive !== undefined && Object.keys(req.body).length === 1) {
        user.isActive = isActive;
        const updatedUser = await userRepository.save(user);
        return res.json({ 
          message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
          user: updatedUser
        });
      }

      // For regular update requests, validate required fields
      if (!name || !email || !role || !department) {
        return res.status(400).json({ error: "Missing required fields" });
      }

      // Set permissions based on role
      const permissions: UserPermissions = {
        canCreateTickets: true,
        canCreateTicketsForOthers: role === 'IT_ADMIN' || role === 'IT_STAFF',
        canEditTickets: role === 'IT_ADMIN' || role === 'IT_STAFF',
        canDeleteTickets: role === 'IT_ADMIN',
        canCloseTickets: role === 'IT_ADMIN' || role === 'IT_STAFF',
        canLockTickets: role === 'IT_ADMIN',
        canAssignTickets: role === 'IT_ADMIN' || role === 'IT_STAFF',
        canEscalateTickets: role === 'IT_ADMIN' || role === 'IT_STAFF',
        canViewAllTickets: role === 'IT_ADMIN' || role === 'IT_STAFF',
        // HR Permissions with default values
        canCreateEmployee: role === 'IT_ADMIN' || role === 'HR_ADMIN',
        canEditEmployee: role === 'IT_ADMIN' || role === 'HR_ADMIN',
        canDeleteEmployee: role === 'IT_ADMIN',
        canViewEmployees: role === 'IT_ADMIN' || role === 'HR_ADMIN' || role === 'HR_STAFF',
        canManageAttendance: role === 'IT_ADMIN' || role === 'HR_ADMIN',
        canManageLeave: role === 'IT_ADMIN' || role === 'HR_ADMIN',
        canManagePayroll: role === 'IT_ADMIN' || role === 'HR_ADMIN',
        canManagePerformance: role === 'IT_ADMIN' || role === 'HR_ADMIN',
        department: department,
        isAdmin: role === 'IT_ADMIN'
      };

      // Update user fields
      user.name = String(name);
      user.email = String(email);
      user.department = String(department);
      user.role = role as UserRole;
      user.permissions = permissions;
      user.project = project || null;
      user.location = location || null;
      
      // Update oversightPermissions if provided
      if (oversightPermissions) {
        user.oversightPermissions = oversightPermissions;
      }

      // Update password if provided
      if (password) {
        user.password = await bcrypt.hash(password, 10);
      }

      const updatedUser = await userRepository.save(user);
      const { password: _, ...userWithoutPassword } = updatedUser;
      return res.json(userWithoutPassword);
    } catch (error: any) {
      console.error("❌ Update user error:", {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      
      if (error.code === 'ER_DUP_ENTRY') {
        return res.status(400).json({ error: 'Email already in use' });
      }
      
      return res.status(500).json({ 
        error: "Failed to update user",
        message: error.message
      });
    }
  },

  async deleteUser(req: Request, res: Response) {
    try {
      const userId = req.params.id;
      if (!userId) {
        return res.status(400).json({ error: "Invalid user ID" });
      }

      const user = await userRepository.findOne({ where: { id: userId } });
      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      await userRepository.remove(user);

      return res.status(200).json({ 
        message: "User deleted successfully",
        deletedUserId: userId 
      });
    } catch (error: any) {
      console.error("❌ Delete user error:", {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      
      return res.status(500).json({ 
        error: "Failed to delete user",
        message: error.message
      });
    }
  },

  // Method to ensure default roles exist in database
  async ensureDefaultRoles(): Promise<void> {
    try {
      const roleRepo = AppDataSource.getRepository(Role);
      
      const defaultRoles = [
        {
          name: 'IT Administrator',
          description: 'Full system access with all permissions across all modules',
          permissions: [
            'canAccessAllModules', 'canConfigureSystem', 'canManageRoles', 
            'canAddUsers', 'canEditUsers', 'canDeleteUsers', 'canViewReports', 
            'canExportData', 'canImportData', 'canViewAllDepartments',
            'canCreateTickets', 'canEditTickets', 'canDeleteTickets', 'canCloseTickets',
            'canCreateEmployee', 'canEditEmployee', 'canDeleteEmployee', 'canViewEmployees',
            'canViewDashboards', 'canViewAllDashboards', 'canCreateDashboards', 'canEditDashboards'
          ],
          category: 'system' as any,
          userCount: 0
        },
        {
          name: 'IT Staff',
          description: 'Manage IT resources and tickets',
          permissions: ['canCreateTickets', 'canEditTickets', 'canCloseTickets'],
          category: 'system' as any,
          userCount: 0
        },
        {
          name: 'Department Head',
          description: 'Manage department resources and approve requests',
          permissions: ['canViewDepartmentTickets', 'canApproveRequests'],
          category: 'system' as any,
          userCount: 0
        },
        {
          name: 'Employee',
          description: 'Basic access to create tickets and view own data',
          permissions: ['canCreateTickets'],
          category: 'system' as any,
          userCount: 0
        },
        {
          name: 'Dashboard Manager',
          description: 'Full access to create and manage dashboards',
          permissions: [
            'canViewDashboards', 'canViewAllDashboards', 'canCreateDashboards', 
            'canEditDashboards', 'canShareDashboards', 'canExportDashboardData'
          ],
          category: 'dashboard' as any,
          userCount: 0
        }
      ];
      
      for (const roleData of defaultRoles) {
        const existingRole = await roleRepo.findOne({ where: { name: roleData.name } });
        if (!existingRole) {
          const newRole = roleRepo.create(roleData);
          await roleRepo.save(newRole);
          console.log(`Created default role: ${roleData.name}`);
        }
      }
    } catch (error) {
      console.error('Error seeding default roles:', error);
    }
  },
};