import express, { Request, Response, NextFunction } from 'express';
import { AppDataSource } from '../../config/database';
import { EmailAccount } from '../../entities/EmailAccount';

const router = express.Router();
const emailAccountRepository = AppDataSource.getRepository(EmailAccount);

// Custom validation middleware for email accounts
const validateEmailAccount = (req: Request, res: Response, next: NextFunction) => {
  // Define required fields
  const requiredFields = [
    { field: 'emailAddress', message: 'Email address is required' },
    { field: 'assignedToType', message: 'Assigned to type is required', 
      allowedValues: ['User', 'Department'], valueMessage: 'Assigned to type must be either "User" or "Department"' },
    { field: 'assignedToName', message: 'Assigned to name is required' },
    { field: 'department', message: 'Department is required' },
    { field: 'accountType', message: 'Account type is required' },
    { field: 'platform', message: 'Platform is required' },
    { field: 'status', message: 'Status is required' },
    { field: 'primaryUser', message: 'Primary user is required' },
    { field: 'createdBy', message: 'Created by is required' }
  ];
  
  // Validation errors array
  const errors = [];
  
  // Check for empty request body
  if (!req.body || Object.keys(req.body).length === 0) {
    return res.status(400).json({ 
      error: 'Empty request body received',
      message: 'Request body cannot be empty'
    });
  }
  
  // Validate each required field
  requiredFields.forEach(({ field, message, allowedValues, valueMessage }) => {
    const value = req.body[field];
    
    // Check if field exists and is not empty
    if (value === undefined || value === null || (typeof value === 'string' && value.trim() === '')) {
      errors.push({ field, message });
    } 
    // If field has allowed values, validate against them
    else if (allowedValues && !allowedValues.includes(value)) {
      errors.push({ field, message: valueMessage || `Invalid value for ${field}` });
    }
  });
  
  // Validate email format
  if (req.body.emailAddress && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(req.body.emailAddress)) {
    errors.push({ field: 'emailAddress', message: 'Invalid email format' });
  }
  
  // Handle twoFactorEnabled default
  if (req.body.twoFactorEnabled === undefined || req.body.twoFactorEnabled === null) {
    req.body.twoFactorEnabled = false;
  }
  
  // If there are validation errors, return them
  if (errors.length > 0) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors
    });
  }
  
  // If validation passes, continue to the route handler
  next();
};

// Get all email accounts
router.get('/', async (req, res) => {
  try {
    console.log('GET /email-accounts - Returning all email accounts');
    const accounts = await emailAccountRepository.find();
    res.json(accounts);
  } catch (error) {
    console.error('Error fetching email accounts:', error);
    res.status(500).json({ error: 'Failed to fetch email accounts' });
  }
});

// Get a single email account by ID
router.get('/:id', async (req, res) => {
  try {
    const id = req.params.id;
    console.log(`GET /email-accounts/${id} - Fetching email account`);
    
    const account = await emailAccountRepository.findOneBy({ id });
    
    if (!account) {
      return res.status(404).json({ error: 'Email account not found' });
    }
    
    res.json(account);
  } catch (error) {
    console.error(`Error fetching email account ${req.params.id}:`, error);
    res.status(500).json({ error: 'Failed to fetch email account' });
  }
});

// Create a new email account
router.post('/', validateEmailAccount, async (req, res) => {
  try {
    console.log('POST /email-accounts - Creating new email account. Body:', JSON.stringify(req.body, null, 2));
    
    // At this point, validation has passed via middleware
    // Process the request body
    try {
      // Handle date fields explicitly to ensure compatibility with the database
      const processedBody = { ...req.body };
      
      // Log data types of all fields
      console.log('Data types of request fields:');
      Object.entries(processedBody).forEach(([key, value]) => {
        console.log(`${key}: ${typeof value} - ${value === null ? 'null' : value === undefined ? 'undefined' : value}`);
      });
      
      // Format date fields to ensure they match yyyy-MM-dd format
      if (processedBody.creationDate) {
        if (typeof processedBody.creationDate === 'string') {
          if (processedBody.creationDate.includes('T')) {
            // Convert ISO date string to yyyy-MM-dd
            const date = new Date(processedBody.creationDate);
            if (!isNaN(date.getTime())) {
              processedBody.creationDate = date.toISOString().split('T')[0]; // yyyy-MM-dd format
              console.log('Converted creationDate to:', processedBody.creationDate);
            } else {
              // Invalid date, remove it
              console.log('Invalid creationDate received, removing field');
              delete processedBody.creationDate;
            }
          }
        }
      } else if (processedBody.creationDate === null || processedBody.creationDate === '') {
        delete processedBody.creationDate;
      }
      
      if (processedBody.lastAccessDate) {
        if (typeof processedBody.lastAccessDate === 'string') {
          if (processedBody.lastAccessDate.includes('T')) {
            // Convert ISO date string to yyyy-MM-dd
            const date = new Date(processedBody.lastAccessDate);
            if (!isNaN(date.getTime())) {
              processedBody.lastAccessDate = date.toISOString().split('T')[0]; // yyyy-MM-dd format
              console.log('Converted lastAccessDate to:', processedBody.lastAccessDate);
            } else {
              // Invalid date, remove it
              console.log('Invalid lastAccessDate received, removing field');
              delete processedBody.lastAccessDate;
            }
          }
        }
      } else if (processedBody.lastAccessDate === null || processedBody.lastAccessDate === '') {
        delete processedBody.lastAccessDate;
      }
      
      // Ensure twoFactorEnabled has a default value
      if (processedBody.twoFactorEnabled === undefined) {
        processedBody.twoFactorEnabled = false;
      }
      
      console.log('Creating entity with processed body:', JSON.stringify(processedBody, null, 2));
      const newAccount = emailAccountRepository.create(processedBody);
      
      console.log('Entity created, about to save:', JSON.stringify(newAccount, null, 2));
      const savedAccount = await emailAccountRepository.save(newAccount);
      
      if (savedAccount && 'id' in savedAccount) {
        console.log('Email account created successfully:', savedAccount.id);
        res.status(201).json(savedAccount);
      } else {
        throw new Error('Failed to save email account properly');
      }
    } catch (saveError: any) {
      console.error('Database error saving email account. Error type:', typeof saveError);
      console.error('Error name:', saveError.name);
      console.error('Error message:', saveError.message);
      console.error('Error stack:', saveError.stack);
      
      // Extract more specific error information from TypeORM/MySQL error
      let errorMessage = 'Failed to save email account to database';
      let errorDetails = saveError.message || 'Unknown database error';
      
      // Handle common database errors more specifically
      if (saveError.code) {
        console.error('SQL Error code:', saveError.code);
        
        if (saveError.code === 'ER_DUP_ENTRY') {
          errorMessage = 'Email account already exists';
          errorDetails = 'A duplicate entry was found. The email address may already be in use.';
        } else if (saveError.code === 'ER_NO_DEFAULT_FOR_FIELD') {
          errorMessage = 'Missing required field';
          errorDetails = saveError.sqlMessage || 'A required field was not provided';
        }
      }
      
      if (saveError.sqlMessage) {
        console.error('SQL Message:', saveError.sqlMessage);
      }
      
      res.status(500).json({ 
        error: errorMessage,
        details: errorDetails,
        fieldError: saveError.sqlMessage ? extractFieldFromSqlError(saveError.sqlMessage) : null,
        errorType: saveError.name || typeof saveError
      });
    }
  } catch (error: any) {
    console.error('Error creating email account:', error);
    res.status(500).json({ 
      error: 'Failed to create email account',
      details: error.message || 'Unknown error'
    });
  }
});

// Update an existing email account
router.put('/:id', validateEmailAccount, async (req, res) => {
  try {
    const id = req.params.id;
    console.log(`PUT /email-accounts/${id} - Updating email account:`, req.body);
    
    const account = await emailAccountRepository.findOneBy({ id });
    
    if (!account) {
      return res.status(404).json({ error: 'Email account not found' });
    }
    
    // Update account data
    try {
      // Process the request body
      const processedBody = { ...req.body };
      
      // Log what we received from client
      console.log('Received update data:', JSON.stringify(processedBody, null, 2));
      
      // Format date fields to ensure they match yyyy-MM-dd format
      if (processedBody.creationDate) {
        if (typeof processedBody.creationDate === 'string') {
          if (processedBody.creationDate.includes('T')) {
            // Convert ISO date string to yyyy-MM-dd
            const date = new Date(processedBody.creationDate);
            if (!isNaN(date.getTime())) {
              processedBody.creationDate = date.toISOString().split('T')[0]; // yyyy-MM-dd format
              console.log('Converted creationDate to:', processedBody.creationDate);
            } else {
              // Invalid date, remove it
              console.log('Invalid creationDate received, removing field');
              delete processedBody.creationDate;
            }
          }
        }
      } else if (processedBody.creationDate === null || processedBody.creationDate === '') {
        delete processedBody.creationDate;
      }
      
      if (processedBody.lastAccessDate) {
        if (typeof processedBody.lastAccessDate === 'string') {
          if (processedBody.lastAccessDate.includes('T')) {
            // Convert ISO date string to yyyy-MM-dd
            const date = new Date(processedBody.lastAccessDate);
            if (!isNaN(date.getTime())) {
              processedBody.lastAccessDate = date.toISOString().split('T')[0]; // yyyy-MM-dd format
              console.log('Converted lastAccessDate to:', processedBody.lastAccessDate);
            } else {
              // Invalid date, remove it
              console.log('Invalid lastAccessDate received, removing field');
              delete processedBody.lastAccessDate;
            }
          }
        }
      } else if (processedBody.lastAccessDate === null || processedBody.lastAccessDate === '') {
        delete processedBody.lastAccessDate;
      }
      
      // Ensure twoFactorEnabled has a default value if not provided
      if (processedBody.twoFactorEnabled === undefined) {
        processedBody.twoFactorEnabled = account.twoFactorEnabled; // Preserve existing value
      }
      
      console.log('Processed update data:', JSON.stringify(processedBody, null, 2));
      
      emailAccountRepository.merge(account, processedBody);
      const updatedAccount = await emailAccountRepository.save(account);
      
      console.log('Account updated successfully:', updatedAccount.id);
      res.json(updatedAccount);
    } catch (saveError: any) {
      console.error(`Database error updating email account ${id}:`, saveError);
      console.error('Error name:', saveError.name);
      console.error('Error message:', saveError.message);
      
      // Extract more specific error information
      let errorMessage = 'Failed to update email account';
      let errorDetails = saveError.message || 'Unknown database error';
      
      if (saveError.code) {
        console.error('SQL Error code:', saveError.code);
        
        if (saveError.code === 'ER_DUP_ENTRY') {
          errorMessage = 'Email account with this address already exists';
          errorDetails = 'A duplicate entry was found. The email address may already be in use.';
        }
      }
      
      if (saveError.sqlMessage) {
        console.error('SQL Message:', saveError.sqlMessage);
      }
      
      res.status(500).json({ 
        error: errorMessage,
        details: errorDetails,
        fieldError: saveError.sqlMessage ? extractFieldFromSqlError(saveError.sqlMessage) : null,
        errorType: saveError.name || typeof saveError
      });
    }
  } catch (error: any) {
    console.error(`Error updating email account ${req.params.id}:`, error);
    res.status(500).json({ 
      error: 'Failed to update email account',
      details: error.message || 'Unknown error'
    });
  }
});

// Delete an email account
router.delete('/:id', async (req, res) => {
  try {
    const id = req.params.id;
    console.log(`DELETE /email-accounts/${id} - Deleting email account`);
    
    const account = await emailAccountRepository.findOneBy({ id });
    
    if (!account) {
      return res.status(404).json({ error: 'Email account not found' });
    }
    
    // Remove the account
    try {
      await emailAccountRepository.remove(account);
      res.json({ message: 'Email account deleted successfully', account });
    } catch (deleteError: any) {
      console.error(`Database error deleting email account ${id}:`, deleteError);
      res.status(500).json({ 
        error: 'Failed to delete email account',
        details: deleteError.message || 'Unknown database error'
      });
    }
  } catch (error: any) {
    console.error(`Error deleting email account ${req.params.id}:`, error);
    res.status(500).json({ 
      error: 'Failed to delete email account',
      details: error.message || 'Unknown error'
    });
  }
});

// Helper function to extract field name from SQL error message
function extractFieldFromSqlError(sqlMessage: string): string | null {
  // Common patterns in SQL error messages
  const columnPattern = /'([^']+)'/;
  const match = sqlMessage.match(columnPattern);
  
  if (match && match[1]) {
    // Convert DB column name to camelCase field name
    const dbColumnName = match[1];
    // Basic conversion from snake_case to camelCase if needed
    return dbColumnName.replace(/_([a-z])/g, (g) => g[1].toUpperCase());
  }
  
  return null;
}

export default router; 