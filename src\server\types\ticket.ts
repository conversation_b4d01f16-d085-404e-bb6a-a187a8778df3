export enum UserRole {
  IT_ADMIN = 'IT_ADMIN',
  IT_SUPPORT = 'IT_SUPPORT',
  EMPLOYEE = 'EMPLOYEE',
  MANAGER = 'MANAGER'
}

export enum TicketStatus {
  OPEN = 'OPEN',
  IN_PROGRESS = 'IN_PROGRESS',
  RESOLVED = 'RESOLVED',
  CLOSED = 'CLOSED'
}

export enum Priority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export interface User {
  id: string;
  name: string;
  email: string;
  password: string;
  role: UserRole;
  department: string;
  phone?: string;
  position?: string;
  isActive: boolean;
  permissions?: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface Ticket {
  id: string;
  ticketNumber: string;
  title: string;
  description: string;
  status: TicketStatus;
  priority: Priority;
  category: string;
  createdById: string;
  visibleTo: string[];
  departmentChain: string[];
  comments: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Comment {
  id: string;
  content: string;
  ticketId: string;
  createdById: string;
  createdAt: Date;
  updatedAt: Date;
} 