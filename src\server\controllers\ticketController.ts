import { Request, Response } from 'express';
import { AuthRequest } from '../middleware/authMiddleware';
import { AppDataSource } from '../../config/database';
import { Ticket } from '../../entities/Ticket';
import { User } from '../../entities/User';
import { Comment } from '../../entities/Comment';
import { FindOptionsWhere, Repository, Equal, DeepPartial, Brackets, In } from 'typeorm';
import { UserRole, TicketStatus, TicketPriority } from '../../types/common';
import { UploadedFile } from 'express-fileupload';
import fs from 'fs';
import path from 'path';
import { Attachment } from '../../entities/Attachment';
import { 
  sendTicketCreatedNotification, 
  sendTicketAssignedNotification,
  sendTicketEscalatedNotification,
  sendSystemNotification,
  sendTicketInProgressNotification
} from '../utils/notificationUtils';

interface TicketAttachment {
  id: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
  uploadedBy: {
    id: string;
    name: string;
    role: UserRole;
  };
  uploadedAt: string;
}

// IT team roles that can see all tickets
const IT_ROLES: UserRole[] = [UserRole.IT_ADMIN, UserRole.IT_STAFF];

const ticketRepository: Repository<Ticket> = AppDataSource.getRepository(Ticket);
const userRepository: Repository<User> = AppDataSource.getRepository(User);
const commentRepository: Repository<Comment> = AppDataSource.getRepository(Comment);
const attachmentRepository: Repository<Attachment> = AppDataSource.getRepository(Attachment);

// Add function to generate next ticket number
const generateNextTicketNumber = async (): Promise<string> => {
  try {
    // Get all tickets and sort by ticket number in descending order
    const tickets = await ticketRepository.find({
      select: ['ticketNumber'],
      order: {
        ticketNumber: 'DESC'
      }
    });

    // Find the highest ticket number
    let highestNumber = 100; // Start from 100 so first ticket will be 101
    
    for (const ticket of tickets) {
      if (ticket.ticketNumber) {
        // Extract number from format T101
        const numberPart = parseInt(ticket.ticketNumber.substring(1));
        if (!isNaN(numberPart) && numberPart > highestNumber) {
          highestNumber = numberPart;
        }
      }
    }
    
    // Increment the highest number by 1
    const nextNumber = highestNumber + 1;
    
    // Format as T101 (no leading zeros)
    return `T${nextNumber}`;
  } catch (error) {
    console.error('Error generating ticket number:', error);
    throw new Error('Failed to generate ticket number');
  }
};

// Define type-safe where clause creators
const createTicketWhere = (id: string): FindOptionsWhere<Ticket> => ({
  id: parseInt(id)
});

const createUserWhere = (id: string): FindOptionsWhere<User> => ({
  id
});

// Define allowed status transitions with type safety
const ALLOWED_TRANSITIONS: Record<TicketStatus, TicketStatus[]> = {
  [TicketStatus.OPEN]: [TicketStatus.IN_PROGRESS],
  [TicketStatus.IN_PROGRESS]: [TicketStatus.RESOLVED],
  [TicketStatus.RESOLVED]: []
};

// Helper function to check if status transition is allowed
const isStatusTransitionAllowed = (from: TicketStatus, to: TicketStatus): boolean => {
  const allowedTransitions = ALLOWED_TRANSITIONS[from] || [];
  return allowedTransitions.includes(to);
};

// Constants for file validation
const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/plain'
];
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB max for files

// Define upload directory
const uploadDir = path.join(__dirname, '../../../public/uploads');

export const ticketController = {
  async createTicket(req: AuthRequest, res: Response) {
    try {
      if (!req.user) {
        console.error('User not authenticated when creating ticket');
        return res.status(401).json({ error: 'Unauthorized' });
      }

      // Find the creator
      const creator = await userRepository.findOne({ where: { id: req.user.id } });
      if (!creator) {
        return res.status(404).json({ error: 'Creator not found' });
      }

      // Generate next ticket number
      const ticketNumber = await generateNextTicketNumber();

      // Create the ticket first without attachments
      const ticket = ticketRepository.create({
        title: req.body.title,
        description: req.body.description,
        category: req.body.category,
        priority: req.body.priority?.toUpperCase() || TicketPriority.MEDIUM,
        status: TicketStatus.OPEN,
        createdBy: creator,
        createdById: creator.id,
        department: req.body.department || creator.department || 'GENERAL',
        ticketNumber,
        visibleTo: [
          UserRole.IT_ADMIN,
          UserRole.IT_STAFF,
          creator.department || 'GENERAL',
          creator.role
        ].filter((value, index, self) => self.indexOf(value) === index),
        departmentChain: [
          'IT',
          creator.department || 'GENERAL'
        ].filter((value, index, self) => self.indexOf(value) === index)
      });

      // Save the ticket first
      const savedTicket = await ticketRepository.save(ticket);

      // Send notification to the ticket creator
      if (req.app.locals.io && savedTicket.createdById) {
        sendTicketCreatedNotification(
          req.app.locals.io,
          savedTicket.createdById,
          savedTicket.id.toString(),
          savedTicket.title,
          savedTicket.ticketNumber
        );
      }

      // If the ticket is assigned to someone, send them a notification
      if (req.app.locals.io && savedTicket.assignedToId) {
        sendTicketAssignedNotification(
          req.app.locals.io,
          savedTicket.assignedToId,
          savedTicket.ticketNumber,
          savedTicket.title,
          req.user.name
        );
      }

      // Only notify IT staff about the new ticket if they are not the creator
      // and only if the creator is not already an IT staff member
      if (req.app.locals.io && !IT_ROLES.includes(req.user.role as UserRole)) {
        try {
          // Find IT staff and admins
          const itStaff = await userRepository.find({
            where: [
              { role: UserRole.IT_ADMIN },
              { role: UserRole.IT_STAFF }
            ]
          });
          
          // Send notifications to all IT staff except the creator
          for (const staff of itStaff) {
            if (staff.id !== savedTicket.createdById) {
              sendSystemNotification(
                req.app.locals.io,
                staff.id,
                'New Ticket Created',
                `Ticket #${savedTicket.ticketNumber}: ${savedTicket.title} has been created by ${req.user.name}`,
                savedTicket.priority === TicketPriority.HIGH || savedTicket.priority === TicketPriority.CRITICAL 
                  ? 'high' 
                  : 'medium'
              );
            }
          }
        } catch (error) {
          console.error('Error notifying IT staff about new ticket:', error);
        }
      }

      // Handle file uploads if present
      if (req.files && 'files' in req.files) {
        const files = req.files.files;
        const uploadedFiles = Array.isArray(files) 
          ? (files as unknown as UploadedFile[])
          : [(files as unknown as UploadedFile)];

        // Create uploads directory if it doesn't exist
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }

        // Validate file types and sizes
        const invalidFiles = uploadedFiles.filter(
          file => !ALLOWED_FILE_TYPES.includes(file.mimetype)
        );
        if (invalidFiles.length > 0) {
          return res.status(400).json({ 
            error: `Invalid file type(s): ${invalidFiles.map(f => f.name).join(', ')}. Only images (JPG, PNG, GIF, WebP) and documents (PDF, Word, Excel) are allowed.` 
          });
        }

        const oversizedFiles = uploadedFiles.filter(file => file.size > MAX_FILE_SIZE);
        if (oversizedFiles.length > 0) {
          return res.status(400).json({ 
            error: `Files must be under 10MB: ${oversizedFiles.map(f => f.name).join(', ')}` 
          });
        }

        // Create and save attachments
        const attachments = await Promise.all(uploadedFiles.map(async (file: UploadedFile) => {
          const uniqueFilename = `${Date.now()}-${Math.random().toString(36).substring(2)}-${file.name}`;
          const filePath = path.join(uploadDir, uniqueFilename);
          
          try {
            await file.mv(filePath);
            
            const attachment = attachmentRepository.create({
              fileName: file.name,
              fileUrl: `/uploads/${uniqueFilename}`,
              fileType: file.mimetype,
              ticket: savedTicket,
              ticketId: savedTicket.id,
              uploadedBy: creator,
              uploadedById: creator.id,
              role: creator.role
            });

            return attachmentRepository.save(attachment);
          } catch (error) {
            console.error('Error moving file:', error);
            throw new Error(`Failed to save file: ${file.name}`);
          }
        }));

        console.log('Created attachments:', attachments.map(a => ({
          id: a.id,
          fileName: a.fileName,
          fileUrl: a.fileUrl
        })));
      }

      // Fetch the complete ticket with all relations
      const completeTicket = await ticketRepository.findOne({
        where: { id: savedTicket.id },
        relations: {
          createdBy: true,
          assignedTo: true,
          comments: {
            createdBy: true
          },
          attachments: true
        }
      });

      if (!completeTicket) {
        throw new Error('Failed to fetch complete ticket');
      }

      res.status(201).json(completeTicket);
    } catch (error: any) {
      console.error('Create ticket error:', error);
      console.error('Error stack:', error.stack);
      res.status(500).json({ error: error.message || 'Failed to create ticket' });
    }
  },

  async getTickets(req: AuthRequest, res: Response) {
    try {
      const { role, department, id } = req.user || {};
      
      if (!req.user || !role) {
        console.error('User not authenticated when fetching tickets');
        return res.status(401).json({ error: 'Unauthorized' });
      }

      console.log('Fetching tickets for user:', {
        userId: id,
        role,
        department
      });

      let ticketsQuery = ticketRepository
        .createQueryBuilder('ticket')
        .leftJoinAndSelect('ticket.createdBy', 'createdBy')
        .leftJoinAndSelect('ticket.assignedTo', 'assignedTo')
        .leftJoinAndSelect('ticket.lockedBy', 'lockedBy')
        .leftJoinAndSelect('ticket.comments', 'comments')
        .leftJoinAndSelect('comments.createdBy', 'commentCreator')
        .leftJoinAndSelect('ticket.attachments', 'attachments')
        .leftJoinAndSelect('comments.attachments', 'commentAttachments')
        .orderBy('ticket.createdAt', 'ASC')
        .addOrderBy('comments.createdAt', 'ASC');

      // IT Admin and IT Staff can see all tickets
      if (role === UserRole.IT_ADMIN || role === UserRole.IT_STAFF) {
        // No additional conditions needed - they see all tickets
      }
      // Department Head can see their department's tickets
      else if (role === UserRole.DEPT_HEAD && department) {
        ticketsQuery = ticketsQuery
          .where('createdBy.department = :department', { department })
          .orWhere('ticket.departmentChain LIKE :deptPattern', { deptPattern: `%${department}%` });
      }
      // Regular employees can only see their own tickets and tickets where they are in visibleTo
      else {
        ticketsQuery = ticketsQuery
          .where(new Brackets(qb => {
            qb.where('ticket.createdById = :userId', { userId: id })
              .orWhere('ticket.assignedToId = :userId', { userId: id })
              .orWhere(
                new Brackets(qb2 => {
                  qb2.where('ticket.visibleTo LIKE :rolePattern', { rolePattern: `%${role}%` })
                    .andWhere('createdBy.department = :department', { department })
                })
              );
          }));
      }

      const tickets = await ticketsQuery.getMany();

      // Log ticket counts for debugging
      console.log('Fetched tickets:', {
        userRole: role,
        userDepartment: department,
        userId: id,
        totalCount: tickets.length,
        statusCounts: {
          open: tickets.filter(t => t.status === TicketStatus.OPEN).length,
          inProgress: tickets.filter(t => t.status === TicketStatus.IN_PROGRESS).length,
          resolved: tickets.filter(t => t.status === TicketStatus.RESOLVED).length
        },
        ticketNumbers: tickets.map(t => t.ticketNumber)
      });

      res.json(tickets);
    } catch (error: any) {
      console.error('Get tickets error:', error);
      res.status(500).json({ error: 'Failed to fetch tickets' });
    }
  },

  async updateTicket(req: Request, res: Response) {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const user = (req as AuthRequest).user;

        if (!user) {
            return res.status(401).json({ error: 'Unauthorized' });
        }

        // Log incoming request data
        console.log('Update ticket request:', {
            ticketId: id,
            updateData,
            userId: user.id
        });

        // Find the ticket with all necessary relations
        const ticket = await ticketRepository
            .createQueryBuilder('ticket')
            .leftJoinAndSelect('ticket.createdBy', 'createdBy')
            .leftJoinAndSelect('ticket.assignedTo', 'assignedTo')
            .leftJoinAndSelect('ticket.comments', 'comments')
            .leftJoinAndSelect('comments.createdBy', 'commentCreator')
            .leftJoinAndSelect('ticket.attachments', 'attachments')
            .leftJoinAndSelect('comments.attachments', 'commentAttachments')
            .where('ticket.id = :id', { id: parseInt(id) })
            .getOne();

        if (!ticket) {
            return res.status(404).json({ error: 'Ticket not found' });
        }

        // Store previous values for comparison
        const previousStatus = ticket.status;
        const previousAssignedToId = ticket.assignedToId;

        // Update allowed ticket fields
        if (updateData.title) ticket.title = updateData.title;
        if (updateData.description) ticket.description = updateData.description;
        if (updateData.priority) ticket.priority = updateData.priority;
        if (updateData.category) ticket.category = updateData.category;
        if (updateData.status) ticket.status = updateData.status;

        // Start transaction
        await AppDataSource.manager.transaction(async transactionalEntityManager => {
            // First save the ticket to ensure it exists
            await transactionalEntityManager.save(ticket);

            // Handle assignment changes
            if (updateData.assignedToId && updateData.assignedToId !== previousAssignedToId) {
                const assignedTo = await userRepository.findOne({
                    where: { id: updateData.assignedToId }
                });

                if (!assignedTo) {
                    throw new Error('Assigned user not found');
                }

                ticket.assignedTo = assignedTo;
                ticket.assignedToId = assignedTo.id;
                ticket.lockedById = null; // Clear the lock when reassigning
                ticket.lockedBy = null;
                ticket.lockedAt = null;

                // Save the updated ticket with new assignment before creating the comment
                await transactionalEntityManager.save(ticket);

                // Create system comment for assignment
                let assignmentContent = `Ticket reassigned to ${assignedTo.name}`;
                
                // Add referral note if provided
                if (updateData.comment) {
                    assignmentContent += `\nNote: ${updateData.comment}`;
                }
                
                // Create the comment with explicit ticketId
                const assignmentComment = new Comment();
                assignmentComment.content = assignmentContent;
                assignmentComment.ticket = ticket;
                assignmentComment.ticketId = ticket.id;
                assignmentComment.createdBy = user;
                assignmentComment.createdById = user.id;
                assignmentComment.isSystemComment = true;

                // Log the comment data before saving
                console.log('Assignment comment before save:', {
                    content: assignmentComment.content,
                    ticketId: assignmentComment.ticketId,
                    createdById: assignmentComment.createdById
                });

                try {
                    // Save the comment within the transaction
                    await transactionalEntityManager.save(assignmentComment);
                    console.log('Assignment comment saved successfully');
                } catch (error) {
                    console.error('Error saving assignment comment:', error);
                    throw error;
                }

                // Send notification
                if (req.app.locals.io) {
                    // Notify the new assignee
                    sendTicketAssignedNotification(
                        req.app.locals.io,
                        assignedTo.id,
                        ticket.ticketNumber,
                        ticket.title,
                        user.name
                    );
                    
                    // Collect all unique user IDs involved in the ticket (except the new assignee and current user)
                    const involvedUserIds = new Set<string>();
                    
                    // Add ticket creator
                    if (ticket.createdById && ticket.createdById !== user.id && ticket.createdById !== assignedTo.id) {
                        involvedUserIds.add(ticket.createdById);
                    }
                    
                    // Add previous assignee if different
                    if (previousAssignedToId && 
                        previousAssignedToId !== user.id && 
                        previousAssignedToId !== assignedTo.id) {
                        involvedUserIds.add(previousAssignedToId);
                    }
                    
                    // Add all commenters
                    if (ticket.comments && ticket.comments.length > 0) {
                        ticket.comments.forEach(comment => {
                            if (comment.createdById && 
                                comment.createdById !== user.id && 
                                comment.createdById !== assignedTo.id) {
                                involvedUserIds.add(comment.createdById);
                            }
                        });
                    }
                    
                    // Log the notification recipients
                    console.log('Sending reassignment notifications to users:', Array.from(involvedUserIds));
                    
                    // Notify all involved users
                    involvedUserIds.forEach(userId => {
                        sendSystemNotification(
                            req.app.locals.io,
                            userId,
                            'Ticket Reassigned',
                            `Ticket #${ticket.ticketNumber}: ${ticket.title} has been reassigned to ${assignedTo.name} by ${user.name}`,
                            'medium'
                        );
                    });
                    
                    console.log('Reassignment notifications sent to all users in the ticket chain');
                }
            }
            // Add a standalone comment if there's a comment but no assignment change
            else if (updateData.comment) {
                // Create the comment with explicit ticketId
                const comment = new Comment();
                comment.content = updateData.comment;
                comment.ticket = ticket;
                comment.ticketId = ticket.id;
                comment.createdBy = user;
                comment.createdById = user.id;
                comment.isSystemComment = false;

                // Log the comment data before saving
                console.log('Standalone comment before save:', {
                    content: comment.content,
                    ticketId: comment.ticketId,
                    createdById: comment.createdById
                });

                try {
                    await transactionalEntityManager.save(comment);
                    console.log('Standalone comment saved successfully');
                } catch (error) {
                    console.error('Error saving standalone comment:', error);
                    throw error;
                }
            }
        });

        // Fetch fresh ticket data with all relations
        const updatedTicket = await ticketRepository
            .createQueryBuilder('ticket')
            .leftJoinAndSelect('ticket.createdBy', 'createdBy')
            .leftJoinAndSelect('ticket.assignedTo', 'assignedTo')
            .leftJoinAndSelect('ticket.lockedBy', 'lockedBy')
            .leftJoinAndSelect('ticket.comments', 'comments')
            .leftJoinAndSelect('comments.createdBy', 'commentCreator')
            .leftJoinAndSelect('ticket.attachments', 'attachments')
            .leftJoinAndSelect('comments.attachments', 'commentAttachments')
            .where('ticket.id = :id', { id: parseInt(id) })
            .getOne();

        if (!updatedTicket) {
            throw new Error('Failed to verify ticket update');
        }

        console.log('Ticket updated successfully:', {
            id: updatedTicket.id,
            status: updatedTicket.status,
            assignedTo: updatedTicket.assignedTo?.name,
            commentsCount: updatedTicket.comments?.length
        });

        res.json(updatedTicket);
    } catch (error: unknown) {
        console.error('Update ticket error:', error);
        if (error instanceof Error) {
            console.error('Error stack:', error.stack);
            res.status(500).json({ error: error.message || 'Failed to update ticket' });
        } else {
            res.status(500).json({ error: 'Failed to update ticket' });
        }
    }
  },

  async deleteTicket(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const user = (req as AuthRequest).user;

      if (!user) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      // Only IT Admin can delete tickets
      if (user.role !== UserRole.IT_ADMIN) {
        return res.status(403).json({ 
          error: 'Only IT Administrators can delete tickets' 
        });
      }

      const ticket = await ticketRepository.findOne({
        where: createTicketWhere(id),
        relations: ['createdBy', 'assignedTo', 'comments']
      });

      if (!ticket) {
        return res.status(404).json({ error: 'Ticket not found' });
      }

      console.log('IT Admin deleting ticket:', {
        ticketId: ticket.id,
        ticketNumber: ticket.ticketNumber,
        deletedBy: user.name,
        status: ticket.status
      });

      // Create a system comment before deletion
      const deleteComment = commentRepository.create({
        content: `Ticket deleted by IT Admin: ${user.name}`,
        ticket,
        createdBy: user,
        createdById: user.id,
        ticketId: ticket.id,
        isSystemComment: true
      });
      await commentRepository.save(deleteComment);

      await ticketRepository.remove(ticket);
      res.json({ 
        message: 'Ticket deleted successfully',
        ticketNumber: ticket.ticketNumber
      });
    } catch (error) {
      console.error('Delete ticket error:', error);
      res.status(500).json({ error: 'Failed to delete ticket' });
    }
  },

  async updateStatus(req: AuthRequest, res: Response) {
    try {
      const { id } = req.params;
      const { status, assignedToId } = req.body;
      const user = req.user;

      if (!user) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      // Validate the requested status
      if (!Object.values(TicketStatus).includes(status)) {
        return res.status(400).json({ error: 'Invalid status' });
      }

      const ticket = await ticketRepository.findOne({
        where: createTicketWhere(id),
        relations: ['createdBy', 'assignedTo']
      });

      if (!ticket) {
        return res.status(404).json({ error: 'Ticket not found' });
      }

      // Store previous status
      const previousStatus = ticket.status;

      // Check permissions based on status transition
      if (status === TicketStatus.IN_PROGRESS && !IT_ROLES.includes(user.role)) {
        return res.status(403).json({ 
          error: 'Only IT staff can set tickets to IN_PROGRESS' 
        });
      }

      if (status === TicketStatus.RESOLVED && !IT_ROLES.includes(user.role)) {
        return res.status(403).json({ 
          error: 'Only IT staff can resolve tickets' 
        });
      }

      // Check if the status transition is allowed
      if (!isStatusTransitionAllowed(previousStatus, status)) {
        return res.status(400).json({ 
          error: `Cannot transition ticket from ${previousStatus} to ${status}. Ticket must follow the workflow: OPEN → IN_PROGRESS → RESOLVED` 
        });
      }

      // Update the status
      ticket.status = status as TicketStatus;

      // Handle assignment if provided
      if (assignedToId) {
        const assignedTo = await userRepository.findOne({
          where: createUserWhere(assignedToId)
        });

        if (!assignedTo) {
          return res.status(404).json({ error: 'Assigned user not found' });
        }

        ticket.assignedTo = assignedTo;
        ticket.assignedToId = assignedTo.id;
      }

      // Send notification to all users involved in the ticket
      if (req.app.locals.io && ticket.status !== previousStatus) {
        // Collect all unique user IDs involved in the ticket (except the current user)
        const involvedUserIds = new Set<string>();
        
        // Add ticket creator
        if (ticket.createdById && ticket.createdById !== user.id) {
          involvedUserIds.add(ticket.createdById);
        }
        
        // Add current assignee
        if (ticket.assignedToId && ticket.assignedToId !== user.id) {
          involvedUserIds.add(ticket.assignedToId);
        }
        
        // Get all comments to find all users involved
        const ticketWithComments = await ticketRepository
          .createQueryBuilder('ticket')
          .leftJoinAndSelect('ticket.comments', 'comments')
          .leftJoinAndSelect('comments.createdBy', 'commentCreator')
          .where('ticket.id = :id', { id: parseInt(id) })
          .getOne();
          
        // Add all commenters
        if (ticketWithComments && ticketWithComments.comments && ticketWithComments.comments.length > 0) {
          ticketWithComments.comments.forEach(comment => {
            if (comment.createdById && comment.createdById !== user.id) {
              involvedUserIds.add(comment.createdById);
            }
          });
        }
        
        // Log the notification recipients
        console.log('Sending status update notifications to users:', Array.from(involvedUserIds));
        
        // Notify all involved users
        involvedUserIds.forEach(userId => {
          sendSystemNotification(
            req.app.locals.io,
            userId,
            'Ticket Status Updated',
            `Ticket #${ticket.ticketNumber}: ${ticket.title} status changed from ${previousStatus} to ${ticket.status} by ${user.name}`,
            'medium'
          );
        });
        
        // Send a specific notification to the ticket creator when status changes to IN_PROGRESS
        if (status === TicketStatus.IN_PROGRESS && previousStatus !== TicketStatus.IN_PROGRESS && ticket.createdById) {
          console.log('Sending IN_PROGRESS notification to ticket creator:', ticket.createdById);
          
          sendTicketInProgressNotification(
            req.app.locals.io,
            ticket.createdById,
            ticket.id.toString(),
            ticket.title,
            ticket.ticketNumber || `#${ticket.id}`,
            user.name || 'An agent'
          );
        }
        
        console.log('Status update notifications sent to all users in the ticket chain');
      }

      await ticketRepository.save(ticket);

      // Add system comment for status change
      const comment = commentRepository.create({
        content: `Ticket status changed from ${previousStatus} to ${status}`,
        ticket,
        createdBy: user,
        createdById: user.id,
        ticketId: ticket.id,
        isSystemComment: true
      });

      // Ensure ticketId is not null before saving
      if (!comment.ticketId) {
        comment.ticketId = ticket.id;
      }

      await commentRepository.save(comment);

      // Fetch updated ticket with all relations
      const updatedTicket = await ticketRepository.findOne({
        where: createTicketWhere(id),
        relations: {
          createdBy: true,
          assignedTo: true,
          comments: {
            createdBy: true
          }
        }
      });

      if (!updatedTicket) {
        throw new Error('Failed to verify ticket update');
      }

      console.log('Ticket status updated:', {
        ticketId: updatedTicket.id,
        previousStatus,
        newStatus: updatedTicket.status,
        updatedBy: user.name,
        assignedTo: updatedTicket.assignedTo?.name
      });

      res.json(updatedTicket);
    } catch (error) {
      console.error('Update ticket status error:', error);
      res.status(500).json({ error: 'Failed to update ticket status' });
    }
  },

  async lockTicket(req: AuthRequest, res: Response) {
    try {
      const { id } = req.params;
      const user = req.user;

      if (!user) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      // Only IT staff can lock tickets
      if (!IT_ROLES.includes(user.role)) {
        return res.status(403).json({ 
          error: 'Only IT staff can lock tickets' 
        });
      }

      const ticket = await ticketRepository.findOne({
        where: createTicketWhere(id),
        relations: ['createdBy', 'assignedTo', 'lockedBy']
      });

      if (!ticket) {
        return res.status(404).json({ error: 'Ticket not found' });
      }

      // Check if ticket is already locked by someone else
      if (ticket.lockedById && ticket.lockedById !== user.id) {
        return res.status(400).json({ 
          error: `Ticket is already locked by ${ticket.lockedBy?.name || 'another user'}` 
        });
      }

      // For IN_PROGRESS tickets, check if the user is assigned to the ticket or is an admin
      if (ticket.status === TicketStatus.IN_PROGRESS && 
          ticket.assignedToId !== user.id && 
          user.role !== UserRole.IT_ADMIN) {
        return res.status(403).json({ 
          error: 'You can only lock tickets assigned to you' 
        });
      }

      // Lock the ticket
      ticket.lockedBy = user;
      ticket.lockedById = user.id;
      ticket.lockedAt = new Date();

      // If ticket is OPEN, change status to IN_PROGRESS
      if (ticket.status === TicketStatus.OPEN) {
        ticket.status = TicketStatus.IN_PROGRESS;
        
        // If not assigned, assign to the user locking it
        if (!ticket.assignedToId) {
          ticket.assignedTo = user;
          ticket.assignedToId = user.id;
        }
      }

      await ticketRepository.save(ticket);

      // Add system comment for locking
      const comment = commentRepository.create({
        content: `Ticket locked by ${user.name}`,
        ticket,
        ticketId: ticket.id,
        createdBy: user,
        createdById: user.id,
        isSystemComment: true
      });

      await commentRepository.save(comment);

      // Fetch updated ticket with all relations
      const updatedTicket = await ticketRepository.findOne({
        where: createTicketWhere(id),
        relations: {
          createdBy: true,
          assignedTo: true,
          lockedBy: true,
          comments: {
            createdBy: true
          }
        }
      });

      if (!updatedTicket) {
        throw new Error('Failed to verify ticket lock');
      }

      console.log('Ticket locked successfully:', {
        id: updatedTicket.id,
        status: updatedTicket.status,
        lockedBy: updatedTicket.lockedBy?.name,
        assignedTo: updatedTicket.assignedTo?.name
      });

      res.json(updatedTicket);
    } catch (error) {
      console.error('Lock ticket error:', error);
      res.status(500).json({ error: 'Failed to lock ticket' });
    }
  },

  async unlockTicket(req: AuthRequest, res: Response) {
    try {
      const { id } = req.params;
      const user = req.user;

      if (!user) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      const ticket = await ticketRepository.findOne({
        where: createTicketWhere(id),
        relations: ['createdBy', 'assignedTo', 'lockedBy']
      });

      if (!ticket) {
        return res.status(404).json({ error: 'Ticket not found' });
      }

      // Check if ticket is locked by the current user or if user is an admin
      if (ticket.lockedById && ticket.lockedById !== user.id && user.role !== UserRole.IT_ADMIN) {
        return res.status(403).json({ 
          error: `Only the user who locked the ticket or an admin can unlock it` 
        });
      }

      // Store who unlocked it for the comment
      const unlockedBy = user.name;
      const previouslyLockedBy = ticket.lockedBy?.name || 'Unknown';

      // Unlock the ticket
      ticket.lockedById = null;
      ticket.lockedBy = null;
      ticket.lockedAt = null;

      await ticketRepository.save(ticket);

      // Create system comment for unlocking
      const unlockComment = commentRepository.create({
        content: ticket.lockedById === user.id 
          ? `Ticket unlocked by ${unlockedBy} - Finished working on this ticket`
          : `Ticket unlocked by ${unlockedBy} - Previously locked by ${previouslyLockedBy}`,
        ticket,
        createdBy: user,
        createdById: user.id,
        ticketId: ticket.id,
        isSystemComment: true
      });
      await commentRepository.save(unlockComment);

      // Fetch updated ticket with all relations
      const updatedTicket = await ticketRepository.findOne({
        where: createTicketWhere(id),
        relations: {
          createdBy: true,
          assignedTo: true,
          comments: {
            createdBy: true
          }
        }
      });

      if (!updatedTicket) {
        throw new Error('Failed to verify ticket update');
      }

      console.log('Ticket unlocked:', {
        ticketId: updatedTicket.id,
        unlockedBy: user.name
      });

      res.json(updatedTicket);
    } catch (error) {
      console.error('Unlock ticket error:', error);
      res.status(500).json({ error: 'Failed to unlock ticket' });
    }
  },

  async addComment(req: AuthRequest, res: Response) {
    try {
      const user = req.user as User;
      if (!user) {
        return res.status(401).json({ message: 'Unauthorized' });
      }

      const { ticketId } = req.params;
      const { content, attachments } = req.body;

      // Get the ticket with all necessary relations to identify everyone in the chain
      const ticket = await ticketRepository
        .createQueryBuilder('ticket')
        .leftJoinAndSelect('ticket.createdBy', 'createdBy')
        .leftJoinAndSelect('ticket.assignedTo', 'assignedTo')
        .leftJoinAndSelect('ticket.lockedBy', 'lockedBy')
        .leftJoinAndSelect('ticket.comments', 'comments')
        .leftJoinAndSelect('comments.createdBy', 'commentCreator')
        .where('ticket.id = :id', { id: parseInt(ticketId) })
        .getOne();

      if (!ticket) {
        return res.status(404).json({ message: 'Ticket not found' });
      }

      if (!['OPEN', 'IN_PROGRESS'].includes(ticket.status)) {
        return res.status(400).json({ 
          message: 'Can only add comments to open or in-progress tickets' 
        });
      }

      // Check if ticket is locked by another user
      if (ticket.lockedById && ticket.lockedById !== user.id && ticket.createdBy.id !== user.id) {
        return res.status(403).json({ 
          message: `This ticket is currently being worked on by ${ticket.lockedBy?.name}. Only they can add comments at this time.` 
        });
      }

      const comment = commentRepository.create({
        content,
        ticket,
        createdBy: user,
        createdById: user.id,
        ticketId: ticket.id
      });

      // Ensure ticketId is not null before saving
      if (!comment.ticketId) {
        comment.ticketId = ticket.id;
      }

      // Save the comment first to get its ID
      await commentRepository.save(comment);

      // Link attachments to the comment if any were provided
      if (attachments && Array.isArray(attachments) && attachments.length > 0) {
        // Update each attachment to link it to this comment
        await attachmentRepository
          .createQueryBuilder()
          .update(Attachment)
          .set({ commentId: comment.id })
          .where("id IN (:...ids)", { ids: attachments })
          .andWhere("ticketId = :ticketId", { ticketId: ticket.id })
          .execute();
          
        // Fetch the updated attachments to include in the response
        const updatedAttachments = await attachmentRepository.find({
          where: {
            id: In(attachments),
            ticketId: ticket.id
          }
        });
        
        // Assign the attachments to the comment object for the response
        comment.attachments = updatedAttachments;
      }

      // Send notifications to all users in the ticket chain
      if (req.app.locals.io) {
        const io = req.app.locals.io;
        const commentPreview = content.length > 50 ? `${content.substring(0, 50)}...` : content;
        
        // Collect all unique user IDs involved in the ticket
        const involvedUserIds = new Set<string>();
        
        // Add ticket creator
        if (ticket.createdById && ticket.createdById !== user.id) {
          involvedUserIds.add(ticket.createdById);
        }
        
        // Add current assignee
        if (ticket.assignedToId && ticket.assignedToId !== user.id) {
          involvedUserIds.add(ticket.assignedToId);
        }
        
        // Add all previous commenters
        if (ticket.comments && ticket.comments.length > 0) {
          ticket.comments.forEach(existingComment => {
            if (existingComment.createdById && existingComment.createdById !== user.id) {
              involvedUserIds.add(existingComment.createdById);
            }
          });
        }
        
        // Log the notification recipients
        console.log('Sending comment notifications to users:', Array.from(involvedUserIds));
        
        // Send notifications to all involved users
        involvedUserIds.forEach(userId => {
          sendSystemNotification(
            io,
            userId,
            'New Comment on Ticket',
            `${user.name} commented on ticket #${ticket.ticketNumber}: "${commentPreview}"`,
            'medium'
          );
        });
        
        // Special notification for reassignment
        if (ticket.assignedToId && 
            ticket.assignedToId !== user.id && 
            (content.toLowerCase().includes('reassign') || content.toLowerCase().includes('refer'))) {
          sendSystemNotification(
            io,
            ticket.assignedToId,
            'Ticket Reassigned to You',
            `Ticket #${ticket.ticketNumber} has been reassigned to you with comment: "${commentPreview}"`,
            'high'
          );
        }
        
        console.log('Comment notifications sent to all users in the ticket chain');
      }

      return res.json({ 
        message: 'Comment added successfully',
        comment
      });

    } catch (error) {
      console.error('Error adding comment:', error);
      return res.status(500).json({ message: 'Error adding comment' });
    }
  },

  async getIds(req: AuthRequest, res: Response) {
    try {
      const user = req.user;
      if (!user) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      // Only IT staff and admins can get all IDs
      if (!IT_ROLES.includes(user.role)) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      // Get all users with basic info
      const users = await userRepository.find({
        select: ['id', 'name', 'email', 'role', 'department'],
        order: {
          name: 'ASC'
        }
      });

      // Get all tickets with basic info
      const tickets = await ticketRepository.find({
        select: ['id', 'ticketNumber', 'title', 'status', 'priority', 'department'],
        relations: {
          createdBy: true,
          assignedTo: true
        },
        order: {
          createdAt: 'DESC'
        }
      });

      // Format the response
      const response = {
        users: users.map(user => ({
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          department: user.department
        })),
        tickets: tickets.map(ticket => ({
          id: ticket.id,
          ticketNumber: ticket.ticketNumber,
          title: ticket.title,
          status: ticket.status,
          priority: ticket.priority,
          department: ticket.department,
          createdBy: ticket.createdBy ? {
            id: ticket.createdBy.id,
            name: ticket.createdBy.name
          } : null,
          assignedTo: ticket.assignedTo ? {
            id: ticket.assignedTo.id,
            name: ticket.assignedTo.name
          } : null
        }))
      };

      console.log('Retrieved IDs:', {
        userCount: response.users.length,
        ticketCount: response.tickets.length
      });

      res.json(response);
    } catch (error) {
      console.error('Error fetching IDs:', error);
      res.status(500).json({ error: 'Failed to fetch IDs' });
    }
  },

  async getTicket(req: AuthRequest, res: Response) {
    try {
      const { id } = req.params;
      const { role, department, id: userId } = req.user || {};
      
      if (!req.user || !role) {
        console.error('User not authenticated when fetching ticket');
        return res.status(401).json({ error: 'Unauthorized' });
      }

      console.log('Fetching ticket details:', {
        ticketId: id,
        userId,
        role,
        department
      });

      // Find the ticket with all necessary relations
      const ticket = await ticketRepository
        .createQueryBuilder('ticket')
        .leftJoinAndSelect('ticket.createdBy', 'createdBy')
        .leftJoinAndSelect('ticket.assignedTo', 'assignedTo')
        .leftJoinAndSelect('ticket.lockedBy', 'lockedBy')
        .leftJoinAndSelect('ticket.comments', 'comments')
        .leftJoinAndSelect('comments.createdBy', 'commentCreator')
        .leftJoinAndSelect('ticket.attachments', 'attachments')
        .leftJoinAndSelect('attachments.uploadedBy', 'attachmentUploader')
        .leftJoinAndSelect('comments.attachments', 'commentAttachments')
        .where('ticket.id = :id', { id: parseInt(id) })
        .orderBy('comments.createdAt', 'ASC')
        .getOne();

      if (!ticket) {
        return res.status(404).json({ error: 'Ticket not found' });
      }

      // Check permissions
      const isITRole = role === UserRole.IT_ADMIN || role === UserRole.IT_STAFF;
      const isDeptHead = role === UserRole.DEPT_HEAD && department === ticket.department;
      const isCreator = ticket.createdBy.id === req.user.id;
      const isAssigned = ticket.assignedTo && ticket.assignedTo.id === req.user.id;
      const isVisibleToRole = role ? ticket.visibleTo.includes(role) : false;
      const isVisibleToDept = department ? ticket.visibleTo.includes(department) : false;

      if (!isITRole && !isDeptHead && !isCreator && !isAssigned && !isVisibleToRole && !isVisibleToDept) {
        return res.status(403).json({ error: 'You do not have permission to view this ticket' });
      }

      console.log('Ticket found with attachments:', {
        ticketId: ticket.id,
        attachmentsCount: ticket.attachments.length,
        attachments: ticket.attachments.map(a => ({
          id: a.id,
          fileName: a.fileName,
          fileType: a.fileType
        }))
      });

      res.json(ticket);
    } catch (error: any) {
      console.error('Get ticket error:', error);
      res.status(500).json({ error: 'Failed to fetch ticket' });
    }
  },

  async uploadAttachments(req: AuthRequest, res: Response) {
    try {
      const { ticketId } = req.params;
      
      if (!req.user) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      if (!req.files || !('files' in req.files)) {
        return res.status(400).json({ error: 'No files uploaded' });
      }

      const files = req.files.files;
      const uploadedFiles = Array.isArray(files) 
        ? (files as unknown as UploadedFile[])
        : [(files as unknown as UploadedFile)];

      // Create uploads directory if it doesn't exist
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      // Find the ticket
      const ticket = await ticketRepository.findOne({
        where: { id: parseInt(ticketId) }
      });

      if (!ticket) {
        return res.status(404).json({ error: 'Ticket not found' });
      }

      // Create attachment records
      const attachments = await Promise.all(uploadedFiles.map(async (file: UploadedFile) => {
        // Generate unique filename to prevent collisions
        const uniqueFilename = `${Date.now()}-${Math.random().toString(36).substring(2)}-${file.name}`;
        const filePath = path.join(uploadDir, uniqueFilename);
        
        // Move file to uploads directory
        await file.mv(filePath);
        
        // Create attachment record
        const attachment = attachmentRepository.create({
          fileName: file.name,
          fileUrl: `/uploads/${uniqueFilename}`,
          fileType: file.mimetype,
          ticket,
          ticketId: ticket.id,
          uploadedBy: req.user!,
          uploadedById: req.user!.id,
          role: req.user!.role
        });

        return attachmentRepository.save(attachment);
      }));

      // Return the saved attachments
      res.json(attachments);
    } catch (error) {
      console.error('Error uploading attachments:', error);
      res.status(500).json({ error: 'Failed to upload attachments' });
    }
  }
};

