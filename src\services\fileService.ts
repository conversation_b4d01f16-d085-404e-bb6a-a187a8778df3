import axios from 'axios';
import { toast } from 'react-hot-toast';

/**
 * Service for handling secure file operations
 */
export const fileService = {
  /**
   * Gets a secure URL for viewing a file
   * @param filePath Path to the file to view
   * @returns Object with success status, file URL, and optional error message
   */
  getSecureFileUrl: async (filePath: string): Promise<{ 
    success: boolean; 
    fileUrl: string; 
    message?: string; 
  }> => {
    try {
      // Handle empty paths
      if (!filePath) {
        return { 
          success: false, 
          fileUrl: '', 
          message: 'No file path provided' 
        };
      }

      // For local development file paths that start with 'blob:'
      if (filePath.startsWith('blob:')) {
        return { success: true, fileUrl: filePath };
      }

      // For placeholder URLs, return them directly
      if (filePath.includes('placeholder.com')) {
        return { success: true, fileUrl: filePath };
      }
      
      // Check if file is a PDF for special handling
      const isPdf = filePath.toLowerCase().endsWith('.pdf');

      // For uploaded files, verify access and get secure URL
      // If the path is already a full URL using our base URL, just use it
      if (filePath.startsWith(window.location.origin)) {
        // Add view parameter to force inline viewing
        const url = new URL(filePath);
        url.searchParams.set('view', 'inline');
        
        // Add specific disposition for PDFs to force inline display
        if (isPdf) {
          url.searchParams.set('disposition', 'inline');
        }
        
        // Add timestamp to prevent caching
        url.searchParams.set('t', Date.now().toString());
        return { success: true, fileUrl: url.toString() };
      }

      // For relative paths to uploaded files in our system
      if (filePath.startsWith('/')) {
        const fullPath = `${window.location.origin}${filePath}`;
        // Add view parameter to force inline viewing
        const url = new URL(fullPath);
        url.searchParams.set('view', 'inline');
        
        // Add specific disposition for PDFs to force inline display
        if (isPdf) {
          url.searchParams.set('disposition', 'inline');
        }
        
        // Add timestamp to prevent caching
        url.searchParams.set('t', Date.now().toString());
        return { success: true, fileUrl: url.toString() };
      }

      // For all other paths, request a secure access token
      try {
        const response = await axios.post('/api/documents/secure-access', { 
          filePath,
          forceInline: true // Tell server to force inline viewing
        });
        
        if (response.data.success) {
          // The server returns a signed URL or token for secure access
          let secureUrl = response.data.secureUrl;
          
          // Add view=inline parameter if it's not already there
          if (!secureUrl.includes('view=inline')) {
            secureUrl = secureUrl.includes('?') 
              ? `${secureUrl}&view=inline` 
              : `${secureUrl}?view=inline`;
          }
          
          // For PDFs, add disposition=inline parameter if not already there
          if (isPdf && !secureUrl.includes('disposition=inline')) {
            secureUrl = `${secureUrl}&disposition=inline`;
          }
          
          // Add timestamp to prevent caching
          secureUrl = `${secureUrl}&t=${Date.now()}`;
          
          return { 
            success: true, 
            fileUrl: secureUrl
          };
        } else {
          return { 
            success: false, 
            fileUrl: '',
            message: response.data.message || 'Could not access document'
          };
        }
      } catch (error: any) {
        // Handle specific axios errors
        if (error.response) {
          // The request was made and the server responded with a status code outside of 2xx
          const status = error.response.status;
          if (status === 404) {
            return { 
              success: false, 
              fileUrl: '',
              message: 'Document not found (404)'
            };
          } else {
            return { 
              success: false, 
              fileUrl: '',
              message: `Server error (${status}): ${error.response.data?.message || 'Unknown error'}`
            };
          }
        } else if (error.request) {
          // The request was made but no response was received
          return { 
            success: false, 
            fileUrl: '',
            message: 'No response from server. Please check your connection.'
          };
        } else {
          // Something happened in setting up the request
          return { 
            success: false, 
            fileUrl: '',
            message: 'Request configuration error'
          };
        }
      }
    } catch (error) {
      console.error('Error getting secure file URL:', error);
      return { 
        success: false, 
        fileUrl: '',
        message: 'Error processing file access request'
      };
    }
  },

  /**
   * Downloads a file securely
   * @param filePath Path to the file to download
   * @param fileName Suggested filename for the download
   */
  downloadFile: async (filePath: string, fileName: string): Promise<boolean> => {
    try {
      const { success, fileUrl, message } = await fileService.getSecureFileUrl(filePath);
      
      if (!success) {
        toast.error(message || 'Could not download file');
        return false;
      }

      // Create a temporary link element to trigger the download
      const link = document.createElement('a');
      link.href = fileUrl;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      return true;
    } catch (error) {
      console.error('Error downloading file:', error);
      toast.error('Error downloading file');
      return false;
    }
  }
}; 