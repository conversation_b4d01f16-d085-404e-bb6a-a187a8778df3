import { SystemLog } from '../types';

export interface UserSession {
  loginTime: string;
  logoutTime: string | null;
  duration: number | null; // in minutes
  ipAddress: string | null;
}

export interface UserLoginInfo {
  lastLogin: string | null;
  loginCount: number;
  loginTimes: string[];
  failedLoginCount: number;
  failedLoginTimes: string[];
  averageSessionDuration?: number; // in minutes
  timeZone?: string; // The time zone recorded at login time
  sessions: UserSession[]; // New field to track complete sessions
}

/**
 * Extract login information for all users from system logs
 */
export const extractUserLoginInfo = (logs: SystemLog[]): Map<string, UserLoginInfo> => {
  const userLoginMap = new Map<string, UserLoginInfo>();
  
  // Sort logs by timestamp
  const sortedLogs = [...logs].sort((a, b) => 
    new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );
  
  // First, initialize the map for each user
  const uniqueUsers = new Set(logs.map(log => log.user));
  
  uniqueUsers.forEach(user => {
    userLoginMap.set(user, {
      lastLogin: null,
      loginCount: 0,
      loginTimes: [],
      failedLoginCount: 0,
      failedLoginTimes: [],
      sessions: []
    });
  });
  
  // Process logs to extract login information
  sortedLogs.forEach(log => {
    const userInfo = userLoginMap.get(log.user);
    if (!userInfo) return;
    
    // Check for successful logins
    if (isSuccessfulLogin(log)) {
      userInfo.loginCount++;
      userInfo.loginTimes.push(log.timestamp);
      userInfo.lastLogin = log.timestamp;
      
      // Create a new session
      const loginDetails = extractLoginTimeDetails(log);
      userInfo.sessions.push({
        loginTime: log.timestamp,
        logoutTime: null,
        duration: null,
        ipAddress: loginDetails.ipAddress
      });
    }
    
    // Check for failed logins
    if (isFailedLogin(log)) {
      userInfo.failedLoginCount++;
      userInfo.failedLoginTimes.push(log.timestamp);
    }
    
    // Check for logouts and update the most recent session
    if (isLogout(log) && userInfo.sessions.length > 0) {
      // Find the most recent session without a logout time
      for (let i = userInfo.sessions.length - 1; i >= 0; i--) {
        const session = userInfo.sessions[i];
        if (!session.logoutTime) {
          session.logoutTime = log.timestamp;
          
          // Calculate duration in minutes
          const loginTime = new Date(session.loginTime).getTime();
          const logoutTime = new Date(session.logoutTime).getTime();
          session.duration = Math.round((logoutTime - loginTime) / (1000 * 60));
          
          break;
        }
      }
    }
  });
  
  // Calculate average session durations
  calculateSessionDurations(userLoginMap);
  
  return userLoginMap;
};

/**
 * Check if a log entry is a successful login
 */
export const isSuccessfulLogin = (log: SystemLog): boolean => {
  const actionLower = log.action.toLowerCase();
  const detailsLower = log.details.toLowerCase();
  
  return (
    // Check action matches login
    (actionLower.includes('login') || actionLower.includes('logged in')) &&
    
    // And check for success indicators
    (log.type === 'success' || 
     detailsLower.includes('successful') ||
     !detailsLower.includes('fail'))
  );
};

/**
 * Check if a log entry is a failed login attempt
 */
export const isFailedLogin = (log: SystemLog): boolean => {
  const actionLower = log.action.toLowerCase();
  const detailsLower = log.details.toLowerCase();
  
  return (
    // Check that action matches login
    (actionLower.includes('login') || actionLower.includes('logged in')) &&
    
    // And check for failure indicators
    (log.type === 'error' || 
     detailsLower.includes('fail') || 
     detailsLower.includes('invalid'))
  );
};

/**
 * Check if a log entry is a logout
 */
export const isLogout = (log: SystemLog): boolean => {
  const actionLower = log.action.toLowerCase();
  return actionLower.includes('logout') || actionLower.includes('logged out');
};

/**
 * Extract login time details from a log entry
 */
export const extractLoginTimeDetails = (log: SystemLog): { 
  timestamp: string;
  ipAddress: string | null;
  userAgent: string | null;
  timeZone: string | null;
} => {
  const details = log.details;
  const result = {
    timestamp: log.timestamp,
    ipAddress: null as string | null,
    userAgent: null as string | null,
    timeZone: null as string | null
  };
  
  // Look for IP address
  const ipMatch = details.match(/ip:\s*([^\s|]+)/i);
  if (ipMatch && ipMatch[1]) {
    result.ipAddress = ipMatch[1];
  }
  
  // Look for user agent
  const uaMatch = details.match(/user agent:\s*([^|]+)/i);
  if (uaMatch && uaMatch[1]) {
    result.userAgent = uaMatch[1].trim();
  }
  
  // Look for system login time
  const systemLoginTimeMatch = details.match(/system login time:\s*([^|]+)/i);
  if (systemLoginTimeMatch && systemLoginTimeMatch[1]) {
    // Just use the exact timestamp string as found in the log
    result.timestamp = systemLoginTimeMatch[1].trim();
  } 
  // Fall back to regular login time if system login time isn't found
  else {
    const loginTimeMatch = details.match(/login time:\s*([^|]+)/i);
    if (loginTimeMatch && loginTimeMatch[1]) {
      result.timestamp = loginTimeMatch[1].trim();
    }
  }
  
  // Look for system time zone
  const timeZoneMatch = details.match(/system time zone:\s*([^|]+)/i);
  if (timeZoneMatch && timeZoneMatch[1]) {
    result.timeZone = timeZoneMatch[1].trim();
  } else {
    // Default to PKT if no timezone information is found
    result.timeZone = "PKT (+05:00)";
  }
  
  return result;
};

/**
 * Calculate average session durations for users
 */
const calculateSessionDurations = (userLoginMap: Map<string, UserLoginInfo>): void => {
  userLoginMap.forEach((userInfo) => {
    // Filter out sessions with null durations
    const completedSessions = userInfo.sessions.filter(session => session.duration !== null);
    
    if (completedSessions.length > 0) {
      const totalDuration = completedSessions.reduce((sum, session) => sum + (session.duration || 0), 0);
      userInfo.averageSessionDuration = Math.round(totalDuration / completedSessions.length);
    }
  });
}; 