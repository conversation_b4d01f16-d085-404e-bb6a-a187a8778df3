import React, { useEffect, memo, useCallback } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = memo(({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  // Memoized function to reset access error flag
  const resetAccessErrorFlag = useCallback(() => {
    if (window.hasOwnProperty('accessErrorShown')) {
      (window as any).accessErrorShown = false;
    }
  }, []);

  // Reset the access error flag when successfully accessing a protected route
  useEffect(() => {
    if (isAuthenticated && !loading) {
      resetAccessErrorFlag();
    }
    
    // Cleanup function to ensure we reset the flag when component unmounts
    return resetAccessErrorFlag;
  }, [isAuthenticated, loading, resetAccessErrorFlag]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
      </div>
    );
  }

  if (!isAuthenticated) {
    // Reset the global access error flag before redirecting
    resetAccessErrorFlag();
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
});

export default ProtectedRoute; 