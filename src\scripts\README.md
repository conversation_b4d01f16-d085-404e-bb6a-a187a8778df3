# Scripts Directory

This directory contains essential scripts for the InfraSpine IT Management System.

## 📁 Organization

### Database Scripts
- Migration scripts for database schema updates
- Database maintenance and backup utilities

### Development Scripts  
- Build and deployment scripts
- Development utilities

### Data Management Scripts
- Data import/export utilities
- Sample data generation

## 🚀 Usage

Most scripts should be run through npm commands defined in package.json:

```bash
# Database operations
npm run migrate
npm run db:backup
npm run db:restore

# Development
npm run dev
npm run build
npm start

# Data operations
npm run import:employees
npm run import:attendance
```

## 📝 Note

- All scripts in this directory are essential for system operation
- Redundant and obsolete scripts have been removed
- For debugging, use the development tools in the main application

## 🔧 Maintenance

When adding new scripts:
1. Place them in appropriate subdirectories
2. Add corresponding npm commands in package.json
3. Document their purpose and usage
4. Remove any temporary or debug scripts after use
