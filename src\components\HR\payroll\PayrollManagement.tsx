import React, { useState, useEffect } from 'react';
import { Tab } from '@headlessui/react';
import { 
  DollarSign, 
  Calendar, 
  Users, 
  FileText, 
  Settings,
  BarChart2, 
  Clock,
  Briefcase,
  CreditCard,
  Calculator
} from 'lucide-react';
import { PayrollPeriod, PayrollStatus } from '../../../types/payroll';
import PayrollPeriods from './PayrollPeriods';
import PayrollSettings from './PayrollSettings';
import PayrollReports from './PayrollReports';
import LoanAdvanceManagement from './LoanAdvanceManagement';
import TaxManagement from './TaxManagement';
import SalaryComponents from './SalaryComponents';
import EmployeePayroll from './EmployeePayroll';

interface PayrollManagementProps {
  isAdmin?: boolean;
}

const PayrollManagement: React.FC<PayrollManagementProps> = ({ isAdmin = false }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="flex items-center justify-between px-6 py-4 border-b">
        <div>
          <h2 className="text-xl font-semibold text-gray-800">Payroll Management</h2>
          <p className="text-sm text-gray-500">Manage payroll, salary components, and employee payments</p>
        </div>
        <div className="flex items-center space-x-2">
          <span className="px-3 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
            Current Month: {new Date().toLocaleString('default', { month: 'long' })} {new Date().getFullYear()}
          </span>
        </div>
      </div>

      <Tab.Group selectedIndex={activeTab} onChange={setActiveTab}>
        <Tab.List className="flex border-b border-gray-200 px-4 overflow-x-auto">
          <Tab className={({ selected }) =>
            `py-3 px-4 text-sm font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
            ${selected 
              ? 'border-blue-600 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`
          }>
            <Calendar className="h-4 w-4 mr-2" />
            Payroll Periods
          </Tab>
          
          <Tab className={({ selected }) =>
            `py-3 px-4 text-sm font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
            ${selected 
              ? 'border-blue-600 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`
          }>
            <DollarSign className="h-4 w-4 mr-2" />
            Salary Components
          </Tab>
          
          <Tab className={({ selected }) =>
            `py-3 px-4 text-sm font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
            ${selected 
              ? 'border-blue-600 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`
          }>
            <Users className="h-4 w-4 mr-2" />
            Employee Payroll
          </Tab>
          
          <Tab className={({ selected }) =>
            `py-3 px-4 text-sm font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
            ${selected 
              ? 'border-blue-600 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`
          }>
            <CreditCard className="h-4 w-4 mr-2" />
            Loans & Advances
          </Tab>
          
          <Tab className={({ selected }) =>
            `py-3 px-4 text-sm font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
            ${selected 
              ? 'border-blue-600 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`
          }>
            <Calculator className="h-4 w-4 mr-2" />
            Tax Management
          </Tab>
          
          <Tab className={({ selected }) =>
            `py-3 px-4 text-sm font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
            ${selected 
              ? 'border-blue-600 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`
          }>
            <BarChart2 className="h-4 w-4 mr-2" />
            Reports
          </Tab>
          
          {isAdmin && (
            <Tab className={({ selected }) =>
              `py-3 px-4 text-sm font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
              ${selected 
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`
            }>
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Tab>
          )}
        </Tab.List>
        
        <Tab.Panels>
          {/* Payroll Periods Tab */}
          <Tab.Panel>
            <PayrollPeriods isAdmin={isAdmin} />
          </Tab.Panel>
          
          {/* Salary Components Tab */}
          <Tab.Panel>
            <SalaryComponents isAdmin={isAdmin} />
          </Tab.Panel>
          
          {/* Employee Payroll Tab */}
          <Tab.Panel>
            <EmployeePayroll isAdmin={isAdmin} />
          </Tab.Panel>
          
          {/* Loans & Advances Tab */}
          <Tab.Panel>
            <LoanAdvanceManagement isAdmin={isAdmin} />
          </Tab.Panel>
          
          {/* Tax Management Tab */}
          <Tab.Panel>
            <TaxManagement isAdmin={isAdmin} />
          </Tab.Panel>
          
          {/* Reports Tab */}
          <Tab.Panel>
            <PayrollReports isAdmin={isAdmin} />
          </Tab.Panel>
          
          {/* Settings Tab - Only for admin */}
          {isAdmin && (
            <Tab.Panel>
              <PayrollSettings />
            </Tab.Panel>
          )}
        </Tab.Panels>
      </Tab.Group>
    </div>
  );
};

export default PayrollManagement; 