export interface ActivityItem {
  id: number;
  type: string;
  description: string;
  userId?: number;
  timestamp: string;
  ticketId?: number;
  ticketNumber?: string;
  priority?: string;
  status?: string;
}

export interface ChartDataset {
  label: string;
  data: number[];
  borderColor: string | string[];
  backgroundColor: string | string[];
  borderWidth?: number;
  fill?: boolean;
}

export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface TicketStats {
  total: number;
  open: number;
  inProgress: number;
  resolved: number;
  closed: number;
  byPriority: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
  byDepartment: Record<string, DepartmentInfo>;
  responseTime: {
    lessThanHour: number;
    oneToFourHours: number;
    fourToDay: number;
    moreThanDay: number;
  };
  categoryStatus: Record<string, CategoryStatusInfo>;
  trends: {
    labels: string[];
    newTickets: number[];
    resolvedTickets: number[];
  };
  recentActivity: ActivityItem[];
}

export interface DashboardStats {
  tickets: TicketStats;
  performance: {
    averageResponseTime: string;
    averageResolutionTime: string;
    satisfactionRate: number;
  };
  ticketTrends: ChartData;
  responseTimes: ChartData;
  activityData: ChartData;
}

export interface DepartmentInfo {
  name: string;
  ticketCount: number;
  color: string;
}

export interface CategoryStatusInfo {
  open: number;
  inProgress: number;
  resolved: number;
} 