import { RecurringTaskService } from './RecurringTaskService';

// Global service instances
let recurringTaskService: RecurringTaskService | null = null;

// Service initialization
export const initializeServices = async () => {
  console.log('🚀 Initializing application services...');
  
  try {
    // Initialize RecurringTaskService after database is connected
    recurringTaskService = new RecurringTaskService();
    console.log('✅ Recurring Task Service initialized');
    
    // Add other service initializations here as needed
    
    console.log('✅ All services initialized successfully');
  } catch (error) {
    console.error('❌ Error initializing services:', error);
    throw error;
  }
};

// Service cleanup
export const shutdownServices = async () => {
  console.log('🛑 Shutting down application services...');
  
  try {
    // Cleanup recurring task service
    if (recurringTaskService) {
      recurringTaskService.destroy();
    }
    
    // Add other service cleanups here as needed
    
    console.log('✅ All services shut down successfully');
  } catch (error) {
    console.error('❌ Error shutting down services:', error);
  }
};

// Export getter for services
export const getRecurringTaskService = () => recurringTaskService;
