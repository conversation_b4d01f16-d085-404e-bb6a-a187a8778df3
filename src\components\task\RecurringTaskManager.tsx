import React, { useState, useEffect } from 'react';
import { 
  Clock, 
  Calendar, 
  Repeat, 
  Plus, 
  Settings, 
  Play, 
  Pause, 
  BarChart3,
  <PERSON>ert<PERSON>ircle,
  CheckCircle,
  Edit,
  Trash2
} from 'lucide-react';

interface RecurrenceConfig {
  interval?: number;
  daysOfWeek?: number[];
  dayOfMonth?: number;
  endDate?: string;
  maxOccurrences?: number;
}

interface RecurringTask {
  id: number;
  title: string;
  description?: string;
  recurrenceType: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  recurrenceConfig: RecurrenceConfig;
  projectId: number;
  projectName: string;
  assignedTo?: {
    id: string;
    name: string;
  };
  isActive: boolean;
  createdAt: string;
  nextRun?: string;
}

interface TaskInstance {
  id: number;
  title: string;
  status: 'todo' | 'in_progress' | 'done' | 'cancelled';
  createdAt: string;
  dueDate?: string;
  timeSpentMinutes: number;
}

interface RecurringTaskStats {
  total: number;
  completed: number;
  pending: number;
  completionRate: number;
  totalTimeSpent: number;
  averageTimePerInstance: number;
}

interface RecurringTaskManagerProps {
  projectId?: number;
  onCreateTask?: (task: Partial<RecurringTask>) => void;
}

export function RecurringTaskManager({ projectId, onCreateTask }: RecurringTaskManagerProps) {
  const [recurringTasks, setRecurringTasks] = useState<RecurringTask[]>([]);
  const [selectedTask, setSelectedTask] = useState<RecurringTask | null>(null);
  const [taskInstances, setTaskInstances] = useState<TaskInstance[]>([]);
  const [taskStats, setTaskStats] = useState<RecurringTaskStats | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadRecurringTasks();
  }, [projectId]);

  const loadRecurringTasks = async () => {
    try {
      setLoading(true);
      // In real implementation, this would call the API
      const mockTasks: RecurringTask[] = [
        {
          id: 1,
          title: 'Daily Server Health Check',
          description: 'Monitor server performance and resource usage',
          recurrenceType: 'daily',
          recurrenceConfig: { interval: 1 },
          projectId: 1,
          projectName: 'IT Infrastructure',
          assignedTo: { id: 'user1', name: 'John Doe' },
          isActive: true,
          createdAt: '2024-01-15T00:00:00Z',
          nextRun: '2024-02-01T09:00:00Z'
        },
        {
          id: 2,
          title: 'Weekly Security Scan',
          description: 'Automated security vulnerability assessment',
          recurrenceType: 'weekly',
          recurrenceConfig: { daysOfWeek: [1], interval: 1 }, // Monday
          projectId: 2,
          projectName: 'Security Audit',
          assignedTo: { id: 'user2', name: 'Jane Smith' },
          isActive: true,
          createdAt: '2024-01-10T00:00:00Z',
          nextRun: '2024-02-05T09:00:00Z'
        },
        {
          id: 3,
          title: 'Monthly Backup Verification',
          description: 'Verify integrity of monthly backup archives',
          recurrenceType: 'monthly',
          recurrenceConfig: { dayOfMonth: 1, interval: 1 },
          projectId: 1,
          projectName: 'IT Infrastructure',
          assignedTo: { id: 'user1', name: 'John Doe' },
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          nextRun: '2024-03-01T09:00:00Z'
        }
      ];

      setRecurringTasks(mockTasks);
    } catch (error) {
      console.error('Error loading recurring tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTaskDetails = async (task: RecurringTask) => {
    try {
      setSelectedTask(task);
      
      // Mock task instances
      const mockInstances: TaskInstance[] = [
        {
          id: 101,
          title: `${task.title} - 2024-01-30`,
          status: 'done',
          createdAt: '2024-01-30T09:00:00Z',
          dueDate: '2024-01-30T17:00:00Z',
          timeSpentMinutes: 45
        },
        {
          id: 102,
          title: `${task.title} - 2024-01-31`,
          status: 'done',
          createdAt: '2024-01-31T09:00:00Z',
          dueDate: '2024-01-31T17:00:00Z',
          timeSpentMinutes: 38
        },
        {
          id: 103,
          title: `${task.title} - 2024-02-01`,
          status: 'in_progress',
          createdAt: '2024-02-01T09:00:00Z',
          dueDate: '2024-02-01T17:00:00Z',
          timeSpentMinutes: 15
        }
      ];

      // Mock stats
      const mockStats: RecurringTaskStats = {
        total: 30,
        completed: 28,
        pending: 2,
        completionRate: 93,
        totalTimeSpent: 1260, // minutes
        averageTimePerInstance: 42
      };

      setTaskInstances(mockInstances);
      setTaskStats(mockStats);
    } catch (error) {
      console.error('Error loading task details:', error);
    }
  };

  const getRecurrenceDescription = (type: string, config: RecurrenceConfig) => {
    switch (type) {
      case 'daily':
        return config.interval === 1 ? 'Every day' : `Every ${config.interval} days`;
      case 'weekly':
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        const dayNames = config.daysOfWeek?.map(day => days[day]).join(', ') || 'Monday';
        return `Weekly on ${dayNames}`;
      case 'monthly':
        return `Monthly on day ${config.dayOfMonth || 1}`;
      case 'quarterly':
        return 'Every quarter';
      case 'yearly':
        return 'Yearly';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'done': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'todo': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Recurring Tasks</h2>
          <p className="text-gray-600">Automate repetitive tasks with scheduled recurrence</p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Recurring Task
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recurring Tasks List */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Active Recurring Tasks</h3>
          
          {recurringTasks.length === 0 ? (
            <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
              <Repeat className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No recurring tasks configured</p>
              <button
                onClick={() => setShowCreateForm(true)}
                className="mt-4 text-blue-600 hover:text-blue-700"
              >
                Create your first recurring task
              </button>
            </div>
          ) : (
            <div className="space-y-3">
              {recurringTasks.map(task => (
                <div
                  key={task.id}
                  className={`bg-white rounded-lg border p-4 cursor-pointer transition-all hover:shadow-md ${
                    selectedTask?.id === task.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                  }`}
                  onClick={() => loadTaskDetails(task)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{task.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{task.description}</p>
                      
                      <div className="flex items-center gap-4 mt-3 text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <Repeat className="h-4 w-4" />
                          {getRecurrenceDescription(task.recurrenceType, task.recurrenceConfig)}
                        </span>
                        
                        {task.assignedTo && (
                          <span className="flex items-center gap-1">
                            <span>👤</span>
                            {task.assignedTo.name}
                          </span>
                        )}
                      </div>
                      
                      {task.nextRun && (
                        <div className="mt-2 text-xs text-blue-600">
                          Next run: {new Date(task.nextRun).toLocaleString()}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        task.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {task.isActive ? 'Active' : 'Inactive'}
                      </span>
                      
                      <button className="p-1 hover:bg-gray-100 rounded">
                        <Settings className="h-4 w-4 text-gray-400" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Task Details Panel */}
        <div className="space-y-4">
          {selectedTask ? (
            <>
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Task Details</h3>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900">{selectedTask.title}</h4>
                    <p className="text-gray-600">{selectedTask.description}</p>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Recurrence:</span>
                      <p className="font-medium">
                        {getRecurrenceDescription(selectedTask.recurrenceType, selectedTask.recurrenceConfig)}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-500">Project:</span>
                      <p className="font-medium">{selectedTask.projectName}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Assigned to:</span>
                      <p className="font-medium">{selectedTask.assignedTo?.name || 'Unassigned'}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Status:</span>
                      <p className="font-medium">{selectedTask.isActive ? 'Active' : 'Inactive'}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Statistics */}
              {taskStats && (
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Statistics</h3>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{taskStats.total}</div>
                      <div className="text-sm text-gray-600">Total Instances</div>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{taskStats.completionRate}%</div>
                      <div className="text-sm text-gray-600">Completion Rate</div>
                    </div>
                    <div className="text-center p-3 bg-yellow-50 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">{formatTime(taskStats.totalTimeSpent)}</div>
                      <div className="text-sm text-gray-600">Total Time</div>
                    </div>
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{formatTime(taskStats.averageTimePerInstance)}</div>
                      <div className="text-sm text-gray-600">Avg per Instance</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Recent Instances */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Instances</h3>
                
                <div className="space-y-3">
                  {taskInstances.map(instance => (
                    <div key={instance.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{instance.title}</p>
                        <p className="text-sm text-gray-500">
                          Created: {new Date(instance.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="text-right">
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(instance.status)}`}>
                          {instance.status.replace('_', ' ')}
                        </span>
                        <p className="text-sm text-gray-500 mt-1">
                          {formatTime(instance.timeSpentMinutes)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          ) : (
            <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
              <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Select a recurring task to view details</p>
            </div>
          )}
        </div>
      </div>

      {/* Create Form Modal Placeholder */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Create Recurring Task</h3>
            <p className="text-gray-600 mb-4">Recurring task creation form will be implemented here.</p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowCreateForm(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowCreateForm(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Create
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
