import { Request, Response } from 'express';
import { AppDataSource } from '../config/database';

import { LeavePolicyConfiguration } from '../entities/LeavePolicyConfiguration';
import { LeaveTypePolicy } from '../entities/LeaveTypePolicy';
import { HolidayCalendar } from '../entities/HolidayCalendar';
import { Holiday } from '../entities/Holiday';

import { LeaveAllocation } from '../entities/LeaveAllocation';
import { LeaveBalance } from '../entities/LeaveBalance';
import { Employee } from '../server/entities/Employee';
import { Not } from 'typeorm';
import { proratedLeaveCalculationService } from '../services/ProratedLeaveCalculationService';

export class LeavePolicyController {

  private leavePolicyConfigurationRepository = AppDataSource.getRepository(LeavePolicyConfiguration);
  private leaveTypePolicyRepository = AppDataSource.getRepository(LeaveTypePolicy);
  private holidayCalendarRepository = AppDataSource.getRepository(HolidayCalendar);
  private holidayRepository = AppDataSource.getRepository(Holiday);


  // Policy Configuration Endpoints
  async getCurrentPolicy(req: Request, res: Response) {
    try {
      const currentPolicy = await this.leavePolicyConfigurationRepository.findOne({
        where: { isActive: true },
        relations: ['leaveTypes', 'holidayCalendars', 'holidayCalendars.holidays'],
        order: { createdAt: 'DESC' }
      });

      if (!currentPolicy) {
        // Return default empty policy structure
        return res.json({
          success: true,
          data: {
            id: null,
            leaveTypes: [],
            holidayCalendars: [],
            version: 'v1.0.0',
            effectiveDate: new Date(),
            isActive: true,
            companyId: 1,
            createdBy: 1,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });
      }

      res.json({
        success: true,
        data: currentPolicy
      });
    } catch (error) {
      console.error('Error fetching current policy:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch current policy',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async createPolicy(req: Request, res: Response) {
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const policyData = req.body;
      console.log('Creating policy with data:', JSON.stringify(policyData, null, 2));

      // Get user ID from request (assuming authentication middleware sets this)
      const userId = req.user?.id ? (typeof req.user.id === 'string' ? parseInt(req.user.id, 10) : req.user.id) : 1;

      // Deactivate existing active policies
      await queryRunner.manager.update(LeavePolicyConfiguration, 
        { isActive: true },
        { isActive: false }
      );

      // Create main policy configuration
      const policyConfig = queryRunner.manager.create(LeavePolicyConfiguration, {
        ...policyData,
        isActive: true,
        createdBy: userId,
        companyId: policyData.companyId || 1
      });

      const savedPolicyConfig = await queryRunner.manager.save(LeavePolicyConfiguration, policyConfig);
      console.log('✅ Saved policy configuration with ID:', savedPolicyConfig.id);

      // Save leave types if provided
      const savedLeaveTypes = [];
      if (policyData.leaveTypes && Array.isArray(policyData.leaveTypes)) {
        for (const leaveTypeData of policyData.leaveTypes) {
          console.log('Processing leave type:', leaveTypeData.leaveType);
          
          const leaveTypeWithPolicyId = {
            ...leaveTypeData,
            policyConfigurationId: savedPolicyConfig.id
          };

          const newLeaveType = queryRunner.manager.create(LeaveTypePolicy, leaveTypeWithPolicyId);
          const savedLeaveType = await queryRunner.manager.save(LeaveTypePolicy, newLeaveType);
          savedLeaveTypes.push(savedLeaveType);
          console.log('✅ Saved leave type:', savedLeaveType.leaveType);
        }
      }

      await queryRunner.commitTransaction();
      console.log('✅ Transaction committed successfully');

      // Fetch the complete saved policy with relations
      const completeSavedPolicy = await this.leavePolicyConfigurationRepository.findOne({
        where: { id: savedPolicyConfig.id },
        relations: ['leaveTypes', 'holidayCalendars', 'holidayCalendars.holidays']
      });

      res.status(201).json({
        success: true,
        data: {
          ...completeSavedPolicy,
          leaveTypes: savedLeaveTypes
        },
        message: 'Leave policy created successfully'
      });

    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error('❌ Error creating policy:', error);
      
      res.status(500).json({
        success: false,
        message: 'Failed to create leave policy',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      await queryRunner.release();
    }
  }

  async getAllPolicies(req: Request, res: Response) {
    try {
      const policies = await this.leavePolicyConfigurationRepository.find({
        relations: ['leaveTypes', 'holidayCalendars', 'holidayCalendars.holidays']
      });

        res.json({
          success: true,
        data: policies
      });
    } catch (error) {
      console.error('Error fetching policies:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch policies',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async getPolicyById(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);

      const policy = await this.leavePolicyConfigurationRepository.findOne({
        where: { id },
        relations: ['leaveTypes', 'holidayCalendars', 'holidayCalendars.holidays']
      });

      if (!policy) {
        return res.status(404).json({
          success: false,
          message: 'Policy not found'
        });
      }

      res.json({
        success: true,
        data: policy
      });
    } catch (error) {
      console.error('Error fetching policy:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch policy',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async updatePolicy(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const policyData = req.body;

      const existingPolicy = await this.leavePolicyConfigurationRepository.findOne({
        where: { id }
      });

      if (!existingPolicy) {
        return res.status(404).json({
          success: false,
          message: 'Policy not found'
        });
      }

      await this.leavePolicyConfigurationRepository.update(id, policyData);
      const updatedPolicy = await this.leavePolicyConfigurationRepository.findOne({
        where: { id },
        relations: ['leaveTypes', 'holidayCalendars', 'holidayCalendars.holidays']
      });

      res.json({
        success: true,
        data: updatedPolicy,
        message: 'Policy updated successfully'
      });
    } catch (error) {
      console.error('Error updating policy:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update policy',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async deletePolicy(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);

      const existingPolicy = await this.leavePolicyConfigurationRepository.findOne({
        where: { id }
      });

      if (!existingPolicy) {
        return res.status(404).json({
          success: false,
          message: 'Policy not found'
        });
      }

      await this.leavePolicyConfigurationRepository.delete(id);

      res.json({
        success: true,
        message: 'Policy deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting policy:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete policy',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Leave Types CRUD Methods
  async getAllLeaveTypes(req: Request, res: Response) {
    try {
      const leaveTypes = await this.leaveTypePolicyRepository.find({
        order: { createdAt: 'DESC' }
      });

        res.json({
          success: true,
        data: leaveTypes
      });
    } catch (error) {
      console.error('Error fetching leave types:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch leave types',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async getActiveLeaveTypes(req: Request, res: Response) {
    try {
      const leaveTypes = await this.leaveTypePolicyRepository.find({
        where: { isActive: true },
        order: { createdAt: 'DESC' }
      });

      res.json({
        success: true,
        data: leaveTypes
      });
    } catch (error) {
      console.error('Error fetching active leave types:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch active leave types',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async getLeaveTypeById(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);

      const leaveType = await this.leaveTypePolicyRepository.findOne({
        where: { id }
      });

      if (!leaveType) {
        return res.status(404).json({
          success: false,
          message: 'Leave type not found'
        });
      }

      res.json({
        success: true,
        data: leaveType
      });
    } catch (error) {
      console.error('Error fetching leave type:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch leave type',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async createLeaveType(req: Request, res: Response) {
    try {
      const leaveTypeData = req.body;
      console.log('Creating leave type with data:', leaveTypeData);
      
      // Validation
      if (!leaveTypeData.displayName || !leaveTypeData.displayName.trim()) {
        return res.status(400).json({
          success: false,
          message: 'Leave type name is required',
          error: 'MISSING_DISPLAY_NAME'
        });
      }
      
      if (!leaveTypeData.maxDaysPerYear || leaveTypeData.maxDaysPerYear <= 0) {
        return res.status(400).json({
          success: false,
          message: 'Maximum days per year must be greater than 0',
          error: 'INVALID_MAX_DAYS'
        });
      }
      
      if (!leaveTypeData.leaveType || !leaveTypeData.leaveType.trim()) {
        return res.status(400).json({
          success: false,
          message: 'Leave type identifier is required',
          error: 'MISSING_LEAVE_TYPE'
        });
      }
      
      // Find the active policy configuration
      let activePolicyConfig = await this.leavePolicyConfigurationRepository.findOne({
        where: { isActive: true }
      });

      // If no active policy configuration exists, create a default one
      if (!activePolicyConfig) {
        console.log('No active policy configuration found, creating default one...');
        const userId = req.user?.id ? (typeof req.user.id === 'string' ? parseInt(req.user.id, 10) : req.user.id) : 1;
        
        const defaultPolicyConfig = this.leavePolicyConfigurationRepository.create({
          version: 'v1.0.0',
          effectiveDate: new Date(),
          isActive: true,
          createdBy: userId,
          companyId: 1,
          leaveTypes: [],
          holidayCalendars: []
        });
        
        activePolicyConfig = await this.leavePolicyConfigurationRepository.save(defaultPolicyConfig);
        console.log('✅ Created default policy configuration with ID:', activePolicyConfig.id);
      }
      
      // Check if leave type already exists
      const existingLeaveType = await this.leaveTypePolicyRepository.findOne({
        where: { leaveType: leaveTypeData.leaveType }
      });

      if (existingLeaveType) {
        console.log('Leave type already exists:', existingLeaveType);
        return res.status(400).json({
          success: false,
          message: `Leave type '${leaveTypeData.displayName}' already exists. Please use a different name.`,
          error: 'DUPLICATE_LEAVE_TYPE',
          existingData: existingLeaveType
        });
      }

      // Ensure settings object is properly initialized
      const processedLeaveTypeData = {
        ...leaveTypeData,
        policyConfigurationId: activePolicyConfig.id, // Associate with active policy
        settings: {
          minDaysNotice: 0,
          allowLeaveModification: false,
          enableProratedLeave: false,
          ...leaveTypeData.settings // Override with any provided settings
        }
      };

      const newLeaveType = this.leaveTypePolicyRepository.create(processedLeaveTypeData);
      const savedLeaveType = await this.leaveTypePolicyRepository.save(newLeaveType);

      console.log('✅ Leave type created successfully:', savedLeaveType);
      res.status(201).json({
        success: true,
        data: savedLeaveType,
        message: 'Leave type created successfully'
      });
    } catch (error) {
      console.error('❌ Error creating leave type:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create leave type',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async updateLeaveType(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const leaveTypeData = req.body;

      const existingLeaveType = await this.leaveTypePolicyRepository.findOne({
        where: { id }
      });

      if (!existingLeaveType) {
        return res.status(404).json({
          success: false,
          message: 'Leave type not found'
        });
      }

      // Check if the new leave type identifier conflicts with another record
      if (leaveTypeData.leaveType && leaveTypeData.leaveType !== existingLeaveType.leaveType) {
        const conflictingLeaveType = await this.leaveTypePolicyRepository.findOne({
          where: { leaveType: leaveTypeData.leaveType }
        });

        if (conflictingLeaveType) {
          return res.status(400).json({
            success: false,
            message: `Leave type '${leaveTypeData.leaveType}' already exists. Please use a different leave type identifier.`,
            error: 'DUPLICATE_LEAVE_TYPE'
          });
        }
      }

      // Check if prorated leave is being enabled or disabled
      const isProratedLeaveEnabled = leaveTypeData.settings?.enableProratedLeave === true;
      const wasProratedLeaveEnabled = existingLeaveType.settings?.enableProratedLeave === true;

      await this.leaveTypePolicyRepository.update(id, leaveTypeData);
      const updatedLeaveType = await this.leaveTypePolicyRepository.findOne({
        where: { id }
      });

      // If prorated leave was just enabled, apply it to all eligible employees
      if (isProratedLeaveEnabled && !wasProratedLeaveEnabled) {
        console.log(`🔄 Prorated leave enabled for ${updatedLeaveType?.leaveType}. Applying to eligible employees...`);
        await this.applyProratedLeaveToEligibleEmployees(updatedLeaveType!);
      }
      
      // If prorated leave was just disabled, reset allocations to full amounts
      if (!isProratedLeaveEnabled && wasProratedLeaveEnabled) {
        console.log(`🔄 Prorated leave disabled for ${updatedLeaveType?.leaveType}. Resetting to full allocations...`);
        await this.resetToFullAllocations(updatedLeaveType!);
      }

      res.json({
        success: true,
        data: updatedLeaveType,
        message: 'Leave type updated successfully'
      });
    } catch (error) {
      console.error('Error updating leave type:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update leave type',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async deleteLeaveType(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);

      const existingLeaveType = await this.leaveTypePolicyRepository.findOne({
        where: { id }
      });

      if (!existingLeaveType) {
        return res.status(404).json({
          success: false,
          message: 'Leave type not found'
        });
      }

      await this.leaveTypePolicyRepository.delete(id);

      res.json({
        success: true,
        message: 'Leave type deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting leave type:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete leave type',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Holiday Calendar Methods
  async getAllHolidayCalendars(req: Request, res: Response) {
    try {
      const holidayCalendars = await this.holidayCalendarRepository.find({
        relations: ['holidays'],
        order: { createdAt: 'DESC' }
      });

      res.json({
        success: true,
        data: holidayCalendars
      });
    } catch (error) {
      console.error('Error fetching holiday calendars:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch holiday calendars',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async getHolidayCalendarById(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);

      const holidayCalendar = await this.holidayCalendarRepository.findOne({
        where: { id },
        relations: ['holidays']
      });

      if (!holidayCalendar) {
        return res.status(404).json({
          success: false,
          message: 'Holiday calendar not found'
        });
      }

      res.json({
        success: true,
        data: holidayCalendar
      });
    } catch (error) {
      console.error('Error fetching holiday calendar:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch holiday calendar',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async createHolidayCalendar(req: Request, res: Response) {
    try {
      const holidayCalendarData = req.body;

      const newHolidayCalendar = this.holidayCalendarRepository.create(holidayCalendarData);
      const savedHolidayCalendar = await this.holidayCalendarRepository.save(newHolidayCalendar);

      res.status(201).json({
        success: true,
        data: savedHolidayCalendar,
        message: 'Holiday calendar created successfully'
      });
    } catch (error) {
      console.error('Error creating holiday calendar:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create holiday calendar',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async updateHolidayCalendar(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const holidayCalendarData = req.body;

      const existingHolidayCalendar = await this.holidayCalendarRepository.findOne({
        where: { id }
      });

      if (!existingHolidayCalendar) {
        return res.status(404).json({
          success: false,
          message: 'Holiday calendar not found'
        });
      }

      await this.holidayCalendarRepository.update(id, holidayCalendarData);
      const updatedHolidayCalendar = await this.holidayCalendarRepository.findOne({
        where: { id },
        relations: ['holidays']
      });

      res.json({
        success: true,
        data: updatedHolidayCalendar,
        message: 'Holiday calendar updated successfully'
      });
    } catch (error) {
      console.error('Error updating holiday calendar:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update holiday calendar',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async deleteHolidayCalendar(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);

      const existingHolidayCalendar = await this.holidayCalendarRepository.findOne({
        where: { id }
      });

      if (!existingHolidayCalendar) {
        return res.status(404).json({
          success: false,
          message: 'Holiday calendar not found'
        });
      }

      await this.holidayCalendarRepository.delete(id);

      res.json({
        success: true,
        message: 'Holiday calendar deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting holiday calendar:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete holiday calendar',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Holiday Methods
  async getHolidaysByCalendar(req: Request, res: Response) {
    try {
      const calendarId = parseInt(req.params.calendarId);

      const holidays = await this.holidayRepository.find({
        where: { calendarId },
        order: { date: 'ASC' }
      });

      res.json({
        success: true,
        data: holidays
      });
    } catch (error) {
      console.error('Error fetching holidays:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch holidays',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async createHoliday(req: Request, res: Response) {
    try {
      const holidayData = req.body;

      // Convert date string to Date object if needed
      if (holidayData.date && typeof holidayData.date === 'string') {
        holidayData.date = new Date(holidayData.date);
      }

      const newHoliday = this.holidayRepository.create(holidayData);
      const savedHoliday = await this.holidayRepository.save(newHoliday);

      res.status(201).json({
        success: true,
        data: savedHoliday,
        message: 'Holiday created successfully'
      });
    } catch (error) {
      console.error('Error creating holiday:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create holiday',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async updateHoliday(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const holidayData = req.body;

      const existingHoliday = await this.holidayRepository.findOne({
        where: { id }
      });

      if (!existingHoliday) {
        return res.status(404).json({
          success: false,
          message: 'Holiday not found'
        });
      }

      // Convert date string to Date object if needed
      if (holidayData.date && typeof holidayData.date === 'string') {
        holidayData.date = new Date(holidayData.date);
      }

      await this.holidayRepository.update(id, holidayData);
      const updatedHoliday = await this.holidayRepository.findOne({
        where: { id }
      });

      res.json({
        success: true,
        data: updatedHoliday,
        message: 'Holiday updated successfully'
      });
    } catch (error) {
      console.error('Error updating holiday:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update holiday',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async deleteHoliday(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);

      const existingHoliday = await this.holidayRepository.findOne({
        where: { id }
      });

      if (!existingHoliday) {
        return res.status(404).json({
          success: false,
          message: 'Holiday not found'
        });
      }

      await this.holidayRepository.delete(id);

      res.json({
        success: true,
        message: 'Holiday deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting holiday:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete holiday',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async bulkUpdateHolidays(req: Request, res: Response) {
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const { holidays } = req.body;

      if (!Array.isArray(holidays)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid data format. Expected array of holidays.'
        });
      }

      const updatedHolidays = [];

      for (const holidayData of holidays) {
        // Convert date string to Date object if needed
        if (holidayData.date && typeof holidayData.date === 'string') {
          holidayData.date = new Date(holidayData.date);
        }

        if (holidayData.id) {
          // Update existing holiday
          await queryRunner.manager.update(Holiday, holidayData.id, holidayData);
          const updatedHoliday = await queryRunner.manager.findOne(Holiday, {
            where: { id: holidayData.id }
          });
          if (updatedHoliday) {
            updatedHolidays.push(updatedHoliday);
          }
        } else {
          // Create new holiday
          const newHoliday = queryRunner.manager.create(Holiday, holidayData);
          const savedHoliday = await queryRunner.manager.save(newHoliday);
          updatedHolidays.push(savedHoliday);
        }
      }

      await queryRunner.commitTransaction();

      res.json({
        success: true,
        data: updatedHolidays,
        message: 'Holidays updated successfully'
      });
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error('Error in bulk update holidays:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update holidays',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Auto-allocate leaves to all employees based on current policy configuration
   */
  async autoAllocateFromPolicies(req: Request, res: Response) {
    try {
      const { year, forceUpdate } = req.body;
      const currentYear = year || new Date().getFullYear();

      console.log(`🚀 Starting auto-allocation from policies for year ${currentYear}`);

      // Get current active policy configuration
      const currentPolicy = await this.leavePolicyConfigurationRepository.findOne({
        where: { isActive: true },
        relations: ['leaveTypes']
      });

      if (!currentPolicy || !currentPolicy.leaveTypes || currentPolicy.leaveTypes.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No active leave policy configuration found. Please configure leave types first.'
        });
      }

      // Get all active employees
      const employeeRepository = AppDataSource.getRepository('Employee');
      const employees = await employeeRepository.find({
        where: { status: 'active' },
        relations: ['job']
      });

      if (employees.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No active employees found.'
        });
      }

      const leaveAllocationRepository = AppDataSource.getRepository(LeaveAllocation);
      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);

      let allocatedCount = 0;
      let updatedCount = 0;
      let skippedCount = 0;
      const results: any[] = [];

      console.log(`📋 Processing ${employees.length} employees with ${currentPolicy.leaveTypes.length} leave types`);

      // Process each employee with each leave type
      for (const employee of employees) {
        for (const leaveTypePolicy of currentPolicy.leaveTypes) {
          try {
            const employeeName = `${employee.firstName} ${employee.lastName}`.trim();

            // Check if allocation already exists
            let allocation = await leaveAllocationRepository.findOne({
              where: {
                employeeId: employee.id,
                leaveType: leaveTypePolicy.leaveType,
                year: currentYear
              }
            });

            if (allocation && !forceUpdate) {
              // Skip if allocation exists and not forcing update
              skippedCount++;
              results.push({
                employeeId: employee.id,
                employeeName,
                leaveType: leaveTypePolicy.leaveType,
                action: 'skipped',
                reason: 'Allocation already exists'
              });
              continue;
            }

            if (allocation && forceUpdate) {
              // Update existing allocation - only update policy allocation, keep manual adjustments
              const oldPolicyAllocation = allocation.policyAllocation;
              allocation.policyAllocation = leaveTypePolicy.maxDaysPerYear;
              allocation.notes = `Policy updated: ${oldPolicyAllocation} → ${leaveTypePolicy.maxDaysPerYear} - ${new Date().toISOString()}`;
              updatedCount++;
            } else {
              // Create new allocation
              allocation = leaveAllocationRepository.create({
                employeeId: employee.id,
                leaveType: leaveTypePolicy.leaveType,
                year: currentYear,
                policyAllocation: leaveTypePolicy.maxDaysPerYear,
                manualAdjustment: 0,
                carriedForward: 0,
                source: 'POLICY',
                notes: `Auto-allocated based on ${leaveTypePolicy.displayName} policy - ${new Date().toISOString()}`,
                isActive: true
              });
              allocatedCount++;
            }

            const savedAllocation = await leaveAllocationRepository.save(allocation);

            // Update or create corresponding balance
            let balance = await leaveBalanceRepository.findOne({
              where: {
                employeeId: employee.id,
                leaveType: leaveTypePolicy.leaveType,
                year: currentYear
              }
            });

            if (balance) {
              balance.totalAllocated = savedAllocation.totalAllocated;
              balance.notes = `Updated from policy allocation - ${new Date().toISOString()}`;
            } else {
              balance = leaveBalanceRepository.create({
                employeeId: employee.id,
                leaveType: leaveTypePolicy.leaveType,
                year: currentYear,
                totalAllocated: savedAllocation.totalAllocated,
                used: 0,
                pending: 0,
                carriedForward: 0,
                lapsed: 0,
                notes: `Created from policy allocation - ${new Date().toISOString()}`,
                isActive: true
              });
            }

            await leaveBalanceRepository.save(balance);

            results.push({
              employeeId: employee.id,
              employeeName,
              leaveType: leaveTypePolicy.leaveType,
              allocated: savedAllocation.totalAllocated,
              action: allocation.id ? 'updated' : 'created',
              source: 'policy'
            });

          } catch (error: any) {
            console.error(`Error allocating ${leaveTypePolicy.leaveType} for employee ${employee.id}:`, error);
            results.push({
              employeeId: employee.id,
              employeeName: `${employee.firstName} ${employee.lastName}`.trim(),
              leaveType: leaveTypePolicy.leaveType,
              action: 'error',
              error: error.message
            });
          }
        }
      }

      const totalProcessed = allocatedCount + updatedCount + skippedCount;

      console.log(`✅ Auto-allocation completed: ${allocatedCount} created, ${updatedCount} updated, ${skippedCount} skipped`);

      res.json({
        success: true,
        data: {
          allocatedCount,
          updatedCount,
          skippedCount,
          totalProcessed,
          totalEmployees: employees.length,
          totalLeaveTypes: currentPolicy.leaveTypes.length,
          results
        },
        message: `Auto-allocation completed: ${allocatedCount} new allocations, ${updatedCount} updated, ${skippedCount} skipped`
      });

    } catch (error: any) {
      console.error('Error in auto-allocate from policies:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to auto-allocate from policies',
        error: error.message
      });
    }
  }

  /**
   * Apply prorated leave to all eligible employees for a specific leave type
   */
  private async applyProratedLeaveToEligibleEmployees(leaveType: LeaveTypePolicy) {
    try {
      console.log(`🔄 Applying prorated leave for leave type: ${leaveType.leaveType}`);
      
             // Get all active employees with their job information
       const employeeRepository = AppDataSource.getRepository(Employee);
       const employees = await employeeRepository.find({
         where: { status: 'active' },
         relations: ['job']
       });

       const currentYear = new Date().getFullYear();
       const leaveYearStart = new Date(currentYear, 0, 1); // January 1st
       
       let processedCount = 0;
       let eligibleCount = 0;
       let errorCount = 0;

       for (const employee of employees) {
         try {
           // Check if employee joined mid-year and is eligible for prorated leave
           if (!employee.job?.joinDate) {
             console.log(`⚠️ Employee ${employee.id} has no joining date, skipping`);
             continue;
           }

           const joiningDate = new Date(employee.job.joinDate);
           
           // Only apply prorated leave to employees who joined after the leave year started
           if (joiningDate <= leaveYearStart) {
             console.log(`⏭️ Employee ${employee.id} joined before leave year, not eligible for prorated leave`);
             continue;
           }

          // Calculate and apply prorated leave
          const calculations = await proratedLeaveCalculationService.calculateProratedLeaveForEmployee(employee.id);
          
          // Find calculation for this specific leave type
          const leaveTypeCalculation = calculations.find(calc => calc.leaveType === leaveType.leaveType);
          
          if (!leaveTypeCalculation || leaveTypeCalculation.proratedEntitlement <= 0) {
            console.log(`⏭️ Employee ${employee.id} not eligible or zero entitlement for ${leaveType.leaveType}`);
            continue;
          }

          eligibleCount++;

          // Apply the prorated allocation
          const leaveAllocationRepository = AppDataSource.getRepository(LeaveAllocation);
          
          // Check if allocation already exists
          let existingAllocation = await leaveAllocationRepository.findOne({
            where: {
              employeeId: employee.id,
              leaveType: leaveType.leaveType,
              year: currentYear
            }
          });

          if (existingAllocation) {
            // Update existing allocation
            existingAllocation.policyAllocation = leaveTypeCalculation.proratedEntitlement;
            existingAllocation.notes = `Prorated leave applied: ${leaveTypeCalculation.calculationMethod} - ${new Date().toISOString()}`;
            await leaveAllocationRepository.save(existingAllocation);
            console.log(`✅ Updated prorated allocation for employee ${employee.id}: ${leaveTypeCalculation.proratedEntitlement} days`);
          } else {
                         // Create new allocation
             const newAllocation = leaveAllocationRepository.create({
               employeeId: employee.id,
               leaveType: leaveType.leaveType,
               year: currentYear,
               policyAllocation: leaveTypeCalculation.proratedEntitlement,
               manualAdjustment: 0,
               carriedForward: 0,
               source: 'POLICY',
               notes: `Prorated leave applied: ${leaveTypeCalculation.calculationMethod} - ${new Date().toISOString()}`,
               isActive: true
             });
            await leaveAllocationRepository.save(newAllocation);
            console.log(`✅ Created prorated allocation for employee ${employee.id}: ${leaveTypeCalculation.proratedEntitlement} days`);
          }

          // Update leave balance
          const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
          let balance = await leaveBalanceRepository.findOne({
            where: {
              employeeId: employee.id,
              leaveType: leaveType.leaveType,
              year: currentYear
            }
          });

          if (balance) {
            balance.totalAllocated = leaveTypeCalculation.proratedEntitlement;
            balance.notes = `Prorated leave balance updated - ${new Date().toISOString()}`;
            await leaveBalanceRepository.save(balance);
          } else {
            const newBalance = leaveBalanceRepository.create({
              employeeId: employee.id,
              leaveType: leaveType.leaveType,
              year: currentYear,
              totalAllocated: leaveTypeCalculation.proratedEntitlement,
              used: 0,
              pending: 0,
              carriedForward: 0,
              lapsed: 0,
              notes: `Prorated leave balance created - ${new Date().toISOString()}`,
              isActive: true
            });
            await leaveBalanceRepository.save(newBalance);
          }

          processedCount++;

        } catch (error) {
          errorCount++;
          console.error(`❌ Error applying prorated leave for employee ${employee.id}:`, error);
        }
      }

      console.log(`🎯 Prorated leave application completed for ${leaveType.leaveType}:`);
      console.log(`   - Total employees: ${employees.length}`);
      console.log(`   - Eligible for prorated leave: ${eligibleCount}`);
      console.log(`   - Successfully processed: ${processedCount}`);
      console.log(`   - Errors: ${errorCount}`);

    } catch (error) {
      console.error(`❌ Error in applyProratedLeaveToEligibleEmployees:`, error);
      throw error;
    }
  }

  private async resetToFullAllocations(leaveType: LeaveTypePolicy) {
    try {
      console.log(`🔄 Resetting ${leaveType.leaveType} to full allocations`);
      
      // Use the LeaveAllocationService to recalculate allocations
      const { leaveAllocationService } = await import('../services/LeaveAllocationService');
      await leaveAllocationService.recalculateAllocationsForLeaveType(leaveType.leaveType);

      console.log(`✅ Successfully reset all allocations for ${leaveType.leaveType} to full amounts`);
    } catch (error) {
      console.error(`Error resetting allocations for ${leaveType.leaveType}:`, error);
      throw error;
    }
  }
} 