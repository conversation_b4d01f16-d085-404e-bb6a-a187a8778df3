import { Request, Response, NextFunction } from 'express';
import { PermissionEvaluationService } from '../services/PermissionEvaluationService';
import { PermissionEvaluationContext } from '../types/roles';
import logger from '../utils/logger';

// Extend Request interface to include user
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    userId?: string;
    role: string;
    department?: string;
    location?: string;
    project?: string;
    permissions?: any;
  };
}

/**
 * Dynamic permission middleware that replaces hardcoded role checks
 */
export const requirePermission = (action: string, options?: {
  resourceExtractor?: (req: AuthenticatedRequest) => any;
  errorMessage?: string;
  logAccess?: boolean;
}) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          message: 'Authentication required',
          code: 'UNAUTHENTICATED'
        });
      }

      // Extract resource from request if extractor provided
      let resource = null;
      if (options?.resourceExtractor) {
        try {
          resource = options.resourceExtractor(req);
        } catch (error) {
          logger.warn('Error extracting resource for permission check:', error);
        }
      }

      // Create evaluation context
      const context: PermissionEvaluationContext = {
        user: {
          userId: req.user.id || req.user.userId,
          roles: [], // Will be populated by the service
          department: req.user.department,
          location: req.user.location,
          project: req.user.project
        },
        action,
        resource,
        environment: {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          timestamp: new Date(),
          path: req.path,
          method: req.method
        },
        timestamp: new Date()
      };

      // Evaluate permission
      const result = await PermissionEvaluationService.evaluatePermission(context);

      if (result.granted) {
        // Log successful access if enabled
        if (options?.logAccess) {
          logger.info('Permission granted', {
            userId: req.user.id,
            action,
            resource: resource?.id || 'unknown',
            reason: result.reason,
            conditions: result.conditions
          });
        }

        // Add permission result to request for downstream use
        req.permissionResult = result;
        next();
      } else {
        // Log access denial
        logger.warn('Permission denied', {
          userId: req.user.id,
          action,
          resource: resource?.id || 'unknown',
          reason: result.reason,
          conditions: result.conditions
        });

        return res.status(403).json({
          message: options?.errorMessage || 'Access denied',
          code: 'INSUFFICIENT_PERMISSIONS',
          reason: result.reason,
          requiredAction: action,
          metadata: result.metadata
        });
      }
    } catch (error) {
      logger.error('Error in permission middleware:', error);
      return res.status(500).json({
        message: 'Internal server error during permission check',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
};

/**
 * Middleware for checking multiple permissions (user must have ALL)
 */
export const requireAllPermissions = (actions: string[], options?: {
  resourceExtractor?: (req: AuthenticatedRequest) => any;
  errorMessage?: string;
}) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          message: 'Authentication required',
          code: 'UNAUTHENTICATED'
        });
      }

      const resource = options?.resourceExtractor ? options.resourceExtractor(req) : null;
      const userId = req.user.id || req.user.userId;

      // Check all permissions
      const results = await PermissionEvaluationService.checkMultiplePermissions(
        userId, 
        actions, 
        resource
      );

      const deniedActions = actions.filter(action => !results[action]);

      if (deniedActions.length > 0) {
        logger.warn('Multiple permissions check failed', {
          userId,
          deniedActions,
          resource: resource?.id || 'unknown'
        });

        return res.status(403).json({
          message: options?.errorMessage || 'Insufficient permissions',
          code: 'INSUFFICIENT_PERMISSIONS',
          deniedActions,
          requiredActions: actions
        });
      }

      next();
    } catch (error) {
      logger.error('Error in multiple permissions middleware:', error);
      return res.status(500).json({
        message: 'Internal server error during permission check',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
};

/**
 * Middleware for checking any of multiple permissions (user must have AT LEAST ONE)
 */
export const requireAnyPermission = (actions: string[], options?: {
  resourceExtractor?: (req: AuthenticatedRequest) => any;
  errorMessage?: string;
}) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          message: 'Authentication required',
          code: 'UNAUTHENTICATED'
        });
      }

      const resource = options?.resourceExtractor ? options.resourceExtractor(req) : null;
      const userId = req.user.id || req.user.userId;

      // Check all permissions
      const results = await PermissionEvaluationService.checkMultiplePermissions(
        userId, 
        actions, 
        resource
      );

      const hasAnyPermission = actions.some(action => results[action]);

      if (!hasAnyPermission) {
        logger.warn('Any permission check failed', {
          userId,
          checkedActions: actions,
          resource: resource?.id || 'unknown'
        });

        return res.status(403).json({
          message: options?.errorMessage || 'Insufficient permissions',
          code: 'INSUFFICIENT_PERMISSIONS',
          requiredActions: actions,
          note: 'At least one of the listed permissions is required'
        });
      }

      next();
    } catch (error) {
      logger.error('Error in any permission middleware:', error);
      return res.status(500).json({
        message: 'Internal server error during permission check',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
};

/**
 * Resource extractors for common use cases
 */
export const resourceExtractors = {
  // Extract ticket from request params
  ticket: (req: AuthenticatedRequest) => ({
    id: req.params.ticketId || req.params.id,
    type: 'ticket',
    department: req.body?.department,
    createdBy: req.body?.createdBy
  }),

  // Extract employee from request params
  employee: (req: AuthenticatedRequest) => ({
    id: req.params.employeeId || req.params.id,
    type: 'employee',
    department: req.body?.department,
    createdBy: req.body?.createdBy
  }),

  // Extract user from request params
  user: (req: AuthenticatedRequest) => ({
    id: req.params.userId || req.params.id,
    type: 'user',
    department: req.body?.department,
    role: req.body?.role
  }),

  // Extract from request body
  fromBody: (field: string) => (req: AuthenticatedRequest) => req.body?.[field],

  // Extract from request params
  fromParams: (field: string) => (req: AuthenticatedRequest) => req.params?.[field]
};

/**
 * Common permission patterns
 */
export const commonPermissions = {
  // Ticket permissions
  tickets: {
    create: 'tickets.create',
    read: 'tickets.read',
    update: 'tickets.update',
    delete: 'tickets.delete',
    assign: 'tickets.assign',
    close: 'tickets.close'
  },

  // Employee permissions
  employees: {
    create: 'employees.create',
    read: 'employees.read',
    update: 'employees.update',
    delete: 'employees.delete'
  },

  // System permissions
  system: {
    admin: 'system.admin',
    configure: 'system.configure',
    backup: 'system.backup'
  },

  // User permissions
  users: {
    create: 'users.create',
    read: 'users.read',
    update: 'users.update',
    delete: 'users.delete'
  }
};

// Extend Request interface to include permission result
declare global {
  namespace Express {
    interface Request {
      permissionResult?: {
        granted: boolean;
        reason: string;
        conditions?: string[];
        requiresApproval?: boolean;
        approvers?: string[];
        metadata?: Record<string, any>;
      };
    }
  }
}
