import axios from 'axios';
import { BillingInvoice, ApprovalStatus, BillingFrequency } from '../entities/BillingInvoice';
import { AppDataSource } from '../config/database';
import { format } from 'date-fns';
import { User } from '../entities/User';
import { Asset } from '../entities/Asset';
import { SoftwareLicense } from '../entities/SoftwareLicense';
import { Vendor } from '../entities/Vendor';
import { In } from 'typeorm';

// Create repository instances
const billingInvoiceRepository = AppDataSource.getRepository(BillingInvoice);
const userRepository = AppDataSource.getRepository(User);
const assetRepository = AppDataSource.getRepository(Asset);
const softwareRepository = AppDataSource.getRepository(SoftwareLicense);
const vendorRepository = AppDataSource.getRepository(Vendor);

// Interface for the form data
export interface BillingInvoiceFormData {
  id: string;
  invoiceNumber: string;
  vendorName: string;
  serviceProduct: string;
  department: string;
  billingCategory: string;
  invoiceDate: string | Date;
  dueDate: string | Date;
  amount: number;
  currency: string;
  tax: number;
  totalAmount: number;
  paymentMethod: string;
  accountTitle?: string;
  mobileNumber?: string;
  checkNumber?: string;
  bankName?: string;
  receivedBy?: string;
  receiptNumber?: string;
  paymentNotes?: string;
  accountNumber?: string;
  cardNumber?: string;
  expiryDate?: string;
  cvv?: string;
  invoiceFileUrl?: string;
  notes: string;
  isRecurring: boolean;
  billingFrequency?: string;
  reminderDays?: number;
  linkedAssets?: { id: string; name: string; category?: string; department?: string; assetType?: string; }[];
  linkedSoftware?: { id: string; name: string; category?: string; department?: string; }[];
  assignedUsers?: { id: string; name: string; department?: string; project?: string; }[];
  approvalStatus: string;
  createdBy: string;
  approvedBy?: string;
  lastModifiedBy: string;
  lastModifiedDate: string;
}

// Helper function to format date objects to MySQL-compatible dates
const formatDateForMySQL = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Fix a specific date formatting issue when creating a Date from a parsed form date
const createDateWithoutTime = (dateStr: string): Date => {
  // Parse the YYYY-MM-DD string
  const [year, month, day] = dateStr.split('-').map(num => parseInt(num.trim()));
  // Create date with no time component using UTC to avoid timezone issues
  return new Date(Date.UTC(year, month - 1, day, 0, 0, 0, 0));
};

// Helper to convert form data to entity
const formDataToEntity = async (formData: BillingInvoiceFormData): Promise<BillingInvoice> => {
  try {
    // Log the raw data for debugging
    console.log('RAW FORM DATA:', JSON.stringify({
      invoiceNumber: formData.invoiceNumber,
      vendorName: formData.vendorName,
      invoiceDate: formData.invoiceDate,
      dueDate: formData.dueDate,
      amount: formData.amount,
      tax: formData.tax,
      totalAmount: formData.totalAmount,
      isRecurring: formData.isRecurring,
      approvalStatus: formData.approvalStatus
    }, null, 2));
    
    // Validate required fields
    if (!formData.invoiceNumber) {
      throw new Error('Invoice number is required');
    }

    if (!formData.vendorName) {
      throw new Error('Vendor name is required');
    }
    
    // Create a new entity instance or find existing one
    let entity: BillingInvoice;
    
    if (formData.id) {
      // Edit mode - find existing entity
      const existingEntity = await billingInvoiceRepository.findOne({
        where: { id: formData.id },
        relations: ['linkedAssets', 'linkedSoftware', 'assignedUsers', 'vendor']
      });
      
      if (!existingEntity) {
        throw new Error(`BillingInvoice with ID ${formData.id} not found`);
      }
      
      entity = existingEntity;
    } else {
      // Create mode - new entity
      entity = new BillingInvoice();
    }
    
    // Update basic fields
    entity.invoiceNumber = formData.invoiceNumber;
    entity.vendorName = formData.vendorName;
    entity.serviceProduct = formData.serviceProduct || '';
    entity.department = formData.department || '';
    entity.billingCategory = formData.billingCategory || '';
    
    // Handle date conversion with robust error handling
    try {
      // First try to fix date format if needed
      let invoiceDateStr = formData.invoiceDate || '';
      let dueDateStr = formData.dueDate || '';
      
      // Add extra logging for date debugging
      console.log('Date values received:', {
        invoiceDateStr,
        dueDateStr,
        invoiceDateType: typeof invoiceDateStr,
        dueDateType: typeof dueDateStr
      });
      
      // Handle date strings in various formats
      let invoiceDate: Date;
      let dueDate: Date;
      
      // Try to parse the date strings
      try {
        // Critical fix: For MySQL 'date' type, we need to ensure clean Date objects with no time component
        if (invoiceDateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
          // This is already in the YYYY-MM-DD format
          // Create a date object at UTC midnight to avoid timezone issues
          const parts = invoiceDateStr.split('-');
          invoiceDate = new Date(Date.UTC(
            parseInt(parts[0]), // year
            parseInt(parts[1]) - 1, // month (0-based)
            parseInt(parts[2]) // day
          ));
          console.log('Using direct YYYY-MM-DD format for invoice date:', invoiceDateStr, '→', invoiceDate);
        } else if (invoiceDateStr.includes('T') && invoiceDateStr.includes('Z')) {
          // This is a full ISO string - extract only the date part
          const parts = invoiceDateStr.split('T')[0].split('-');
          invoiceDate = new Date(Date.UTC(
            parseInt(parts[0]), // year
            parseInt(parts[1]) - 1, // month (0-based)
            parseInt(parts[2]) // day
          ));
          console.log('Extracted date part from ISO string:', invoiceDateStr, '→', invoiceDate);
        } else {
          // Try normal parsing
          const parsedDate = new Date(invoiceDateStr);
          if (!isNaN(parsedDate.getTime())) {
            // Create a new date with only the date part, no time
            const formatted = formatDateForMySQL(parsedDate);
            const parts = formatted.split('-');
            invoiceDate = new Date(Date.UTC(
              parseInt(parts[0]), // year
              parseInt(parts[1]) - 1, // month (0-based)
              parseInt(parts[2]) // day
            ));
            console.log('Parsed and normalized invoice date:', parsedDate, '→', invoiceDate);
          } else {
            throw new Error('Invalid date format');
          }
        }
        
        // Double-check the date is valid
        if (isNaN(invoiceDate.getTime())) {
          throw new Error('Invalid date object after parsing');
        }
        
        // Assign the date object to the entity
        entity.invoiceDate = invoiceDate;
        console.log('Final invoice date value stored:', 
          formatDateForMySQL(invoiceDate), 
          invoiceDate.toISOString()
        );
      } catch (error) {
        console.error('Error parsing invoice date:', error);
        // Default to current date, but with no time component
        const today = new Date();
        const utcToday = new Date(Date.UTC(
          today.getUTCFullYear(),
          today.getUTCMonth(),
          today.getUTCDate()
        ));
        entity.invoiceDate = utcToday;
        console.log('Using today as fallback for invoice date:', utcToday);
      }
      
      // Similarly for due date
      try {
        // Critical fix: For MySQL 'date' type, we need to ensure clean Date objects with no time component
        if (dueDateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
          // This is already in the YYYY-MM-DD format
          // Create a date object at UTC midnight to avoid timezone issues
          const parts = dueDateStr.split('-');
          dueDate = new Date(Date.UTC(
            parseInt(parts[0]), // year
            parseInt(parts[1]) - 1, // month (0-based)
            parseInt(parts[2]) // day
          ));
          console.log('Using direct YYYY-MM-DD format for due date:', dueDateStr, '→', dueDate);
        } else if (dueDateStr.includes('T') && dueDateStr.includes('Z')) {
          // This is a full ISO string - extract only the date part
          const parts = dueDateStr.split('T')[0].split('-');
          dueDate = new Date(Date.UTC(
            parseInt(parts[0]), // year
            parseInt(parts[1]) - 1, // month (0-based)
            parseInt(parts[2]) // day
          ));
          console.log('Extracted date part from ISO string:', dueDateStr, '→', dueDate);
        } else {
          // Try normal parsing
          const parsedDate = new Date(dueDateStr);
          if (!isNaN(parsedDate.getTime())) {
            // Create a new date with only the date part, no time
            const formatted = formatDateForMySQL(parsedDate);
            const parts = formatted.split('-');
            dueDate = new Date(Date.UTC(
              parseInt(parts[0]), // year
              parseInt(parts[1]) - 1, // month (0-based)
              parseInt(parts[2]) // day
            ));
            console.log('Parsed and normalized due date:', parsedDate, '→', dueDate);
          } else {
            throw new Error('Invalid date format');
          }
        }
        
        // Double-check the date is valid
        if (isNaN(dueDate.getTime())) {
          throw new Error('Invalid date object after parsing');
        }
        
        // Assign the date object to the entity
        entity.dueDate = dueDate;
        console.log('Final due date value stored:', 
          formatDateForMySQL(dueDate), 
          dueDate.toISOString()
        );
      } catch (error) {
        console.error('Error parsing due date:', error);
        // Default to current date + 30 days
        const defaultDueDate = new Date();
        defaultDueDate.setDate(defaultDueDate.getDate() + 30);
        entity.dueDate = defaultDueDate;
      }
      
      console.log('Parsed dates:', {
        invoiceDate: entity.invoiceDate,
        dueDate: entity.dueDate
      });
    } catch (error) {
      console.error('Date conversion error:', error);
      // Continue with defaults instead of failing
      entity.invoiceDate = new Date();
      const defaultDueDate = new Date();
      defaultDueDate.setDate(defaultDueDate.getDate() + 30);
      entity.dueDate = defaultDueDate;
    }
    
    // Handle numeric values with validation and defaults
    try {
      entity.amount = typeof formData.amount === 'number' ? formData.amount : 
                      parseFloat(String(formData.amount || '0'));
      if (isNaN(entity.amount)) entity.amount = 0;
    } catch (e) {
      console.warn('Error parsing amount, using 0:', e);
      entity.amount = 0;
    }
    
    entity.currency = formData.currency || 'USD';
    
    try {
      entity.tax = typeof formData.tax === 'number' ? formData.tax : 
                  parseFloat(String(formData.tax || '0'));
      if (isNaN(entity.tax)) entity.tax = 0;
    } catch (e) {
      console.warn('Error parsing tax, using 0:', e);
      entity.tax = 0;
    }
    
    try {
      entity.totalAmount = typeof formData.totalAmount === 'number' ? formData.totalAmount : 
                          parseFloat(String(formData.totalAmount || '0'));
      if (isNaN(entity.totalAmount)) entity.totalAmount = 0;
    } catch (e) {
      console.warn('Error parsing totalAmount, using 0:', e);
      entity.totalAmount = 0;
    }
    
    entity.paymentMethod = formData.paymentMethod || '';
    
    // Set payment detail fields
    entity.accountTitle = formData.accountTitle || '';
    entity.mobileNumber = formData.mobileNumber || '';
    entity.checkNumber = formData.checkNumber || '';
    entity.bankName = formData.bankName || '';
    entity.receivedBy = formData.receivedBy || '';
    entity.receiptNumber = formData.receiptNumber || '';
    entity.paymentNotes = formData.paymentNotes || '';
    entity.accountNumber = formData.accountNumber || '';
    entity.cardNumber = formData.cardNumber || '';
    entity.expiryDate = formData.expiryDate || '';
    entity.cvv = formData.cvv || '';
    
    entity.invoiceFileUrl = formData.invoiceFileUrl || '';
    entity.notes = formData.notes || '';
    entity.isRecurring = Boolean(formData.isRecurring);
    
    // Look up vendor by name if vendorName is provided
    if (formData.vendorName) {
      try {
        const vendor = await vendorRepository.findOne({
          where: { vendorName: formData.vendorName }
        });
        
        if (vendor) {
          entity.vendor = vendor;
          console.log(`Found vendor with ID ${vendor.id} for name "${formData.vendorName}"`);
        } else {
          console.warn(`No vendor found with name "${formData.vendorName}"`);
          // Don't set vendor to null/undefined - let it be handled by TypeORM
        }
      } catch (error) {
        console.error(`Error looking up vendor by name "${formData.vendorName}":`, error);
        // Don't set vendor to null/undefined - let it be handled by TypeORM
      }
    }
    
    // Handle enum values with defaults
    try {
      if (formData.billingFrequency) {
        switch (formData.billingFrequency) {
          case 'Monthly':
            entity.billingFrequency = BillingFrequency.MONTHLY;
            break;
          case 'Yearly':
            entity.billingFrequency = BillingFrequency.YEARLY;
            break;
          case 'One-Time':
            entity.billingFrequency = BillingFrequency.ONE_TIME;
            break;
          default:
            // Default to ONE_TIME if value doesn't match
            entity.billingFrequency = BillingFrequency.ONE_TIME;
        }
      } else {
        // Default to ONE_TIME if not provided
        entity.billingFrequency = BillingFrequency.ONE_TIME;
      }
    } catch (e) {
      console.warn('Error parsing billing frequency, using ONE_TIME:', e);
      entity.billingFrequency = BillingFrequency.ONE_TIME;
    }
    
    entity.reminderDays = formData.reminderDays || 0;
    
    // Handle approval status with defaults
    try {
      switch (formData.approvalStatus) {
        case 'Pending':
          entity.approvalStatus = ApprovalStatus.PENDING;
          break;
        case 'Approved':
          entity.approvalStatus = ApprovalStatus.APPROVED;
          break;
        case 'Rejected':
          entity.approvalStatus = ApprovalStatus.REJECTED;
          break;
        default:
          // Default to PENDING if value doesn't match
          entity.approvalStatus = ApprovalStatus.PENDING;
      }
    } catch (e) {
      console.warn('Error parsing approval status, using PENDING:', e);
      entity.approvalStatus = ApprovalStatus.PENDING;
    }
    
    entity.createdBy = formData.createdBy || 'system';
    entity.approvedBy = formData.approvedBy || '';
    entity.lastModifiedBy = formData.lastModifiedBy || 'system';
    
    // Process linked assets
    if (formData.linkedAssets && Array.isArray(formData.linkedAssets)) {
      try {
        console.log(`Processing ${formData.linkedAssets.length} linked assets`);
        
        // Find assets by their IDs
        const assetIds = formData.linkedAssets
          .filter(item => item && item.id)
          .map(item => item.id);
        
        if (assetIds.length > 0) {
          console.log('Looking up assets with IDs:', assetIds);
          
          // Use findBy with In operator
          const assets = await assetRepository.findBy({
            id: In(assetIds)
          });
          
          if (assets.length === 0) {
            // If no assets found, try an alternative approach with raw query
            console.log('No assets found with standard query, trying raw SQL');
            const rawAssets = await AppDataSource.query(
              `SELECT * FROM assets WHERE id IN (?)`,
              [assetIds]
            );
            
            if (rawAssets && rawAssets.length > 0) {
              entity.linkedAssets = rawAssets;
              console.log(`Found ${rawAssets.length} assets using raw query`);
            } else {
              entity.linkedAssets = [];
              console.log('No assets found even with raw query');
            }
          } else {
            entity.linkedAssets = assets;
            console.log(`Found ${assets.length} of ${assetIds.length} requested assets`);
          }
        } else {
          entity.linkedAssets = [];
          console.log('No valid asset IDs provided');
        }
      } catch (error) {
        console.error('Error processing linked assets:', error);
        // Keep existing assets if in edit mode and error occurs
        if (!entity.linkedAssets) {
          entity.linkedAssets = [];
        }
      }
    } else {
      console.log('No linkedAssets array provided');
      entity.linkedAssets = [];
    }
    
    // Process linked software
    if (formData.linkedSoftware && Array.isArray(formData.linkedSoftware)) {
      try {
        console.log(`Processing ${formData.linkedSoftware.length} linked software items`);
        
        // Find software by their IDs
        const softwareIds = formData.linkedSoftware
          .filter(item => item && item.id)
          .map(item => item.id);
        
        if (softwareIds.length > 0) {
          console.log('Looking up software with IDs:', softwareIds);
          
          // Use findBy with In operator
          const software = await softwareRepository.findBy({
            id: In(softwareIds)
          });
          
          if (software.length === 0) {
            // If no software found, try an alternative approach with raw query
            console.log('No software found with standard query, trying raw SQL');
            const rawSoftware = await AppDataSource.query(
              `SELECT * FROM software_licenses WHERE id IN (?)`,
              [softwareIds]
            );
            
            if (rawSoftware && rawSoftware.length > 0) {
              entity.linkedSoftware = rawSoftware;
              console.log(`Found ${rawSoftware.length} software items using raw query`);
            } else {
              entity.linkedSoftware = [];
              console.log('No software found even with raw query');
            }
          } else {
            entity.linkedSoftware = software;
            console.log(`Found ${software.length} of ${softwareIds.length} requested software items`);
          }
        } else {
          entity.linkedSoftware = [];
          console.log('No valid software IDs provided');
        }
      } catch (error) {
        console.error('Error processing linked software:', error);
        // Keep existing software if in edit mode and error occurs
        if (!entity.linkedSoftware) {
          entity.linkedSoftware = [];
        }
      }
    } else {
      console.log('No linkedSoftware array provided');
      entity.linkedSoftware = [];
    }
    
    // Process assigned users
    if (formData.assignedUsers && Array.isArray(formData.assignedUsers)) {
      try {
        console.log(`Processing ${formData.assignedUsers.length} assigned users`);
        
        // Find users by their IDs
        const userIds = formData.assignedUsers
          .filter(item => item && item.id)
          .map(item => item.id);
        
        if (userIds.length > 0) {
          console.log('Looking up users with IDs:', userIds);
          
          // Use findBy with In operator
          const users = await userRepository.findBy({
            id: In(userIds)
          });
          
          if (users.length === 0) {
            // If no users found, try an alternative approach with raw query
            console.log('No users found with standard query, trying raw SQL');
            const rawUsers = await AppDataSource.query(
              `SELECT * FROM users WHERE id IN (?)`,
              [userIds]
            );
            
            if (rawUsers && rawUsers.length > 0) {
              entity.assignedUsers = rawUsers;
              console.log(`Found ${rawUsers.length} users using raw query`);
            } else {
              entity.assignedUsers = [];
              console.log('No users found even with raw query');
            }
          } else {
            entity.assignedUsers = users;
            console.log(`Found ${users.length} of ${userIds.length} requested users`);
          }
        } else {
          entity.assignedUsers = [];
          console.log('No valid user IDs provided');
        }
      } catch (error) {
        console.error('Error processing assigned users:', error);
        // Keep existing users if in edit mode and error occurs
        if (!entity.assignedUsers) {
          entity.assignedUsers = [];
        }
      }
    } else {
      console.log('No assignedUsers array provided');
      entity.assignedUsers = [];
    }
    
    console.log('Final entity prepared for saving:', {
      id: entity.id,
      invoiceNumber: entity.invoiceNumber,
      vendorName: entity.vendorName,
      amount: entity.amount,
      tax: entity.tax,
      totalAmount: entity.totalAmount,
      invoiceDate: entity.invoiceDate,
      dueDate: entity.dueDate
    });
    
    // Format dates properly for MySQL (YYYY-MM-DD format)
    // The entity uses Date type but MySQL expects it without time component
    if (entity.invoiceDate instanceof Date) {
      // Store the Date object as is, TypeORM will handle conversion for MySQL
      // But we need to ensure it has no time component for consistent storage
      const dateStr = formatDateForMySQL(entity.invoiceDate);
      entity.invoiceDate = createDateWithoutTime(dateStr);
    } else if (typeof entity.invoiceDate === 'string') {
      // Handle different string date formats
      if (entity.invoiceDate.includes('T')) {
        // Handle ISO string format
        const dateOnly = entity.invoiceDate.split('T')[0];
        entity.invoiceDate = createDateWithoutTime(dateOnly);
      } else if (entity.invoiceDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
        // Handle YYYY-MM-DD format
        entity.invoiceDate = createDateWithoutTime(entity.invoiceDate);
      } else {
        // Try standard Date parsing as fallback
        const date = new Date(entity.invoiceDate);
        if (!isNaN(date.getTime())) {
          entity.invoiceDate = new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 0, 0, 0, 0);
        } else {
          // If all fails, use current date
          entity.invoiceDate = new Date();
          console.warn('Invalid invoice date format, using current date');
        }
      }
    }
    
    if (entity.dueDate instanceof Date) {
      // Store the Date object as is, TypeORM will handle conversion for MySQL
      // But we need to ensure it has no time component for consistent storage
      const dateStr = formatDateForMySQL(entity.dueDate);
      entity.dueDate = createDateWithoutTime(dateStr);
    } else if (typeof entity.dueDate === 'string') {
      // Handle different string date formats
      if (entity.dueDate.includes('T')) {
        // Handle ISO string format
        const dateOnly = entity.dueDate.split('T')[0];
        entity.dueDate = createDateWithoutTime(dateOnly);
      } else if (entity.dueDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
        // Handle YYYY-MM-DD format
        entity.dueDate = createDateWithoutTime(entity.dueDate);
      } else {
        // Try standard Date parsing as fallback
        const date = new Date(entity.dueDate);
        if (!isNaN(date.getTime())) {
          entity.dueDate = new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 0, 0, 0, 0);
        } else {
          // If all fails, use current date + 30 days
          const date = new Date();
          date.setDate(date.getDate() + 30);
          entity.dueDate = date;
          console.warn('Invalid due date format, using current date + 30 days');
        }
      }
    }
    
    return entity;
  } catch (error) {
    console.error('Error in formDataToEntity:', error);
    throw error;
  }
};

// Helper to convert entity to form data
const entityToFormData = (entity: BillingInvoice): BillingInvoiceFormData => {
  // Convert dates to strings in the expected format for the form
  let invoiceDateStr: string = '';
  let dueDateStr: string = '';
  
  if (entity.invoiceDate instanceof Date) {
    invoiceDateStr = formatDateForMySQL(entity.invoiceDate);
  } else if (entity.invoiceDate) {
    // Handle if it's already a string somehow
    invoiceDateStr = String(entity.invoiceDate);
  }
  
  if (entity.dueDate instanceof Date) {
    dueDateStr = formatDateForMySQL(entity.dueDate);
  } else if (entity.dueDate) {
    // Handle if it's already a string somehow
    dueDateStr = String(entity.dueDate);
  }
  
  return {
    id: entity.id,
    invoiceNumber: entity.invoiceNumber,
    vendorName: entity.vendorName,
    serviceProduct: entity.serviceProduct,
    department: entity.department,
    billingCategory: entity.billingCategory,
    invoiceDate: invoiceDateStr,
    dueDate: dueDateStr,
    amount: entity.amount as number,
    currency: entity.currency,
    tax: entity.tax as number,
    totalAmount: entity.totalAmount as number,
    paymentMethod: entity.paymentMethod,
    accountTitle: entity.accountTitle || '',
    mobileNumber: entity.mobileNumber || '',
    checkNumber: entity.checkNumber || '',
    bankName: entity.bankName || '',
    receivedBy: entity.receivedBy || '',
    receiptNumber: entity.receiptNumber || '',
    paymentNotes: entity.paymentNotes || '',
    accountNumber: entity.accountNumber || '',
    cardNumber: entity.cardNumber || '',
    expiryDate: entity.expiryDate || '',
    cvv: entity.cvv || '',
    invoiceFileUrl: entity.invoiceFileUrl,
    notes: entity.notes,
    isRecurring: entity.isRecurring,
    billingFrequency: entity.billingFrequency,
    reminderDays: entity.reminderDays,
    linkedAssets: entity.linkedAssets?.map(asset => ({
      id: asset.id.toString(),
      name: asset.assetTag || 'Unknown Asset',
      category: asset.category || '',
      department: asset.department || '',
      assetType: asset.assetType?.toString() || ''
    })),
    linkedSoftware: entity.linkedSoftware?.map(sw => ({
      id: sw.id,
      name: sw.name || 'Unknown Software',
      category: sw.category || '',
      department: sw.department || ''
    })),
    assignedUsers: entity.assignedUsers?.map(user => ({
      id: user.id,
      name: user.name || 'Unknown User',
      department: user.department || '',
      project: user.project || ''
    })),
    approvalStatus: entity.approvalStatus,
    createdBy: entity.createdBy,
    approvedBy: entity.approvedBy,
    lastModifiedBy: entity.lastModifiedBy,
    lastModifiedDate: entity.updatedAt.toISOString()
  };
};

export const BillingInvoiceService = {
  // Get all invoices with pagination
  async getAllInvoices(
    page: number = 1, 
    limit: number = 10, 
    search: string = '', 
    filters: Record<string, any> = {}
  ): Promise<{ data: BillingInvoiceFormData[]; total: number }> {
    try {
      // Build the query with relations
      let queryBuilder = billingInvoiceRepository.createQueryBuilder('invoice')
        .leftJoinAndSelect('invoice.linkedAssets', 'assets')
        .leftJoinAndSelect('invoice.linkedSoftware', 'software')
        .leftJoinAndSelect('invoice.assignedUsers', 'users')
        .leftJoinAndSelect('invoice.vendor', 'vendor');

      // Apply search if provided
      if (search) {
        queryBuilder = queryBuilder.andWhere(
          '(invoice.invoiceNumber LIKE :search OR invoice.vendorName LIKE :search OR invoice.serviceProduct LIKE :search)',
          { search: `%${search}%` }
        );
      }

      // Apply filters if provided
      if (filters.department) {
        queryBuilder = queryBuilder.andWhere('invoice.department = :department', { department: filters.department });
      }
      
      if (filters.billingCategory) {
        queryBuilder = queryBuilder.andWhere('invoice.billingCategory = :billingCategory', { billingCategory: filters.billingCategory });
      }
      
      if (filters.approvalStatus) {
        queryBuilder = queryBuilder.andWhere('invoice.approvalStatus = :approvalStatus', { approvalStatus: filters.approvalStatus });
      }
      
      if (filters.isRecurring !== undefined) {
        queryBuilder = queryBuilder.andWhere('invoice.isRecurring = :isRecurring', { isRecurring: filters.isRecurring });
      }
      
      // Get total count
      const total = await queryBuilder.getCount();
      
      // Apply pagination
      queryBuilder = queryBuilder
        .orderBy('invoice.createdAt', 'DESC')
        .skip((page - 1) * limit)
        .take(limit);
        
      // Execute the query
      const invoices = await queryBuilder.getMany();
      
      // Convert entities to form data
      const formattedInvoices = invoices.map(invoice => entityToFormData(invoice));
      
      return {
        data: formattedInvoices,
        total
      };
    } catch (error) {
      console.error('Error fetching billing invoices:', error);
      throw error;
    }
  },
  
  // Get invoice by ID
  async getInvoiceById(id: string): Promise<BillingInvoiceFormData> {
    try {
      console.log(`Fetching invoice with ID ${id} with all relations`);
      
      // First attempt - use findOne with relations
      let invoice = await billingInvoiceRepository.findOne({
        where: { id },
        relations: ['linkedAssets', 'linkedSoftware', 'assignedUsers', 'vendor']
      });
      
      // If relations are empty, try a different approach with queryBuilder
      if (invoice && 
         (!invoice.linkedAssets?.length && 
          !invoice.linkedSoftware?.length && 
          !invoice.assignedUsers?.length)) {
        
        console.log('Relations are empty, trying alternative query approach');
        
        const invoiceWithRelations = await billingInvoiceRepository
          .createQueryBuilder('invoice')
          .leftJoinAndSelect('invoice.linkedAssets', 'assets')
          .leftJoinAndSelect('invoice.linkedSoftware', 'software')
          .leftJoinAndSelect('invoice.assignedUsers', 'users')
          .leftJoinAndSelect('invoice.vendor', 'vendor')
          .where('invoice.id = :id', { id })
          .getOne();
        
        if (invoiceWithRelations) {
          invoice = invoiceWithRelations;
          console.log('Alternative query returned relations:', {
            hasAssets: !!invoice.linkedAssets?.length,
            hasSoftware: !!invoice.linkedSoftware?.length,
            hasUsers: !!invoice.assignedUsers?.length
          });
        }
      }
      
      if (!invoice) {
        throw new Error(`Invoice with ID ${id} not found`);
      }
      
      // Ensure the relations arrays are initialized
      invoice.linkedAssets = invoice.linkedAssets || [];
      invoice.linkedSoftware = invoice.linkedSoftware || [];
      invoice.assignedUsers = invoice.assignedUsers || [];
      
      console.log('Invoice relations data:', {
        assetCount: invoice.linkedAssets.length,
        softwareCount: invoice.linkedSoftware.length,
        userCount: invoice.assignedUsers.length
      });
      
      // If all relations are still empty, we could try a direct database query
      if (!invoice.linkedAssets.length && !invoice.linkedSoftware.length && !invoice.assignedUsers.length) {
        console.log('Attempting direct relation lookups as a fallback');
        
        try {
          // Try to directly query the join tables
          const assetJoinQuery = AppDataSource.query(`
            SELECT a.* FROM assets a
            JOIN billing_invoice_assets bia ON a.id = bia.asset_id
            WHERE bia.invoice_id = ?
          `, [id]);
          
          const softwareJoinQuery = AppDataSource.query(`
            SELECT s.* FROM software_licenses s
            JOIN billing_invoice_software bis ON s.id = bis.software_id
            WHERE bis.invoice_id = ?
          `, [id]);
          
          const userJoinQuery = AppDataSource.query(`
            SELECT u.* FROM users u
            JOIN billing_invoice_users biu ON u.id = biu.user_id
            WHERE biu.invoice_id = ?
          `, [id]);
          
          const [assets, software, users] = await Promise.all([
            assetJoinQuery,
            softwareJoinQuery,
            userJoinQuery
          ]);
          
          console.log('Direct database queries returned:', {
            assetCount: assets?.length || 0,
            softwareCount: software?.length || 0,
            userCount: users?.length || 0
          });
          
          if (assets?.length) invoice.linkedAssets = assets;
          if (software?.length) invoice.linkedSoftware = software;
          if (users?.length) invoice.assignedUsers = users;
        } catch (directQueryError) {
          console.error('Error with direct relation lookups:', directQueryError);
          // Continue with whatever data we have
        }
      }
      
      const formData = entityToFormData(invoice);
      
      // Log the final form data that will be returned to the client
      console.log('Returning invoice form data with relations:', {
        assetCount: formData.linkedAssets?.length || 0,
        softwareCount: formData.linkedSoftware?.length || 0,
        userCount: formData.assignedUsers?.length || 0
      });
      
      return formData;
    } catch (error) {
      console.error(`Error fetching invoice with ID ${id}:`, error);
      throw error;
    }
  },
  
  // Create a new invoice
  async createInvoice(formData: BillingInvoiceFormData): Promise<BillingInvoiceFormData> {
    try {
      console.log('Creating invoice with data:', {
        invoiceNumber: formData.invoiceNumber,
        vendorName: formData.vendorName,
        department: formData.department,
        hasFileUrl: !!formData.invoiceFileUrl,
        hasAssets: !!formData.linkedAssets?.length,
        hasSoftware: !!formData.linkedSoftware?.length,
        hasUsers: !!formData.assignedUsers?.length,
        dateTypes: {
          invoiceDate: typeof formData.invoiceDate,
          dueDate: typeof formData.dueDate
        }
      });
      
      // Clean up any JSON stringified data that shouldn't be stringified
      if (typeof formData.amount === 'string' && String(formData.amount).includes('"')) {
        try {
          formData.amount = parseFloat(String(formData.amount).replace(/"/g, ''));
        } catch (e) {
          console.warn('Error cleaning amount:', e);
        }
      }
      
      if (typeof formData.tax === 'string' && String(formData.tax).includes('"')) {
        try {
          formData.tax = parseFloat(String(formData.tax).replace(/"/g, ''));
        } catch (e) {
          console.warn('Error cleaning tax:', e);
        }
      }
      
      if (typeof formData.totalAmount === 'string' && String(formData.totalAmount).includes('"')) {
        try {
          formData.totalAmount = parseFloat(String(formData.totalAmount).replace(/"/g, ''));
        } catch (e) {
          console.warn('Error cleaning totalAmount:', e);
        }
      }
      
      let entity: BillingInvoice;
      try {
        entity = await formDataToEntity(formData);
      } catch (error) {
        console.error('Error converting form data to entity:', error);
        // Create a minimal entity with essential fields to avoid database errors
        entity = new BillingInvoice();
        entity.invoiceNumber = formData.invoiceNumber || `INV-${Date.now()}`;
        entity.vendorName = formData.vendorName || 'Unknown Vendor';
        entity.serviceProduct = formData.serviceProduct || '';
        entity.department = formData.department || '';
        entity.billingCategory = formData.billingCategory || '';
        entity.invoiceDate = new Date();
        entity.dueDate = new Date();
        entity.dueDate.setDate(entity.dueDate.getDate() + 30);
        entity.amount = 0;
        entity.currency = 'USD';
        entity.tax = 0;
        entity.totalAmount = 0;
        entity.paymentMethod = '';
        entity.invoiceFileUrl = '';
        entity.notes = '';
        entity.isRecurring = false;
        entity.approvalStatus = ApprovalStatus.PENDING;
        entity.createdBy = formData.createdBy || 'system';
        entity.approvedBy = '';
        entity.lastModifiedBy = formData.lastModifiedBy || 'system';
        entity.linkedAssets = [];
        entity.linkedSoftware = [];
        entity.assignedUsers = [];
      }
      
      // Validate the invoice number is unique for new invoices
      try {
        if (!formData.id) {
          const existingInvoice = await billingInvoiceRepository.findOne({
            where: { invoiceNumber: formData.invoiceNumber }
          });
          
          if (existingInvoice) {
            // Make invoice number unique by appending timestamp if duplicate
            console.warn(`Invoice number '${formData.invoiceNumber}' already exists, making it unique`);
            entity.invoiceNumber = `${entity.invoiceNumber}-${Date.now()}`;
          }
        }
      } catch (error) {
        console.error('Error checking invoice number uniqueness:', error);
        // Make invoice number unique by appending timestamp
        entity.invoiceNumber = `${entity.invoiceNumber}-${Date.now()}`;
      }
      
      // Log the entity before saving, including relation counts
      console.log('Saving invoice entity with values:', {
        invoiceNumber: entity.invoiceNumber,
        amount: entity.amount,
        totalAmount: entity.totalAmount, 
        invoiceDate: entity.invoiceDate,
        dueDate: entity.dueDate,
        assetsCount: entity.linkedAssets?.length || 0,
        softwareCount: entity.linkedSoftware?.length || 0,
        usersCount: entity.assignedUsers?.length || 0
      });
      
      // Capture whether we have relations in the form data but not in the entity
      const relationsMismatch = {
        assets: formData.linkedAssets?.length && (!entity.linkedAssets || !entity.linkedAssets.length),
        software: formData.linkedSoftware?.length && (!entity.linkedSoftware || !entity.linkedSoftware.length),
        users: formData.assignedUsers?.length && (!entity.assignedUsers || !entity.assignedUsers.length)
      };
      
      console.log('Relations mismatch check:', relationsMismatch);
      
      // Attempt to save the entity with detailed error handling
      let savedInvoice: BillingInvoice;
      try {
        savedInvoice = await billingInvoiceRepository.save(entity);
        console.log('Invoice saved successfully with ID:', savedInvoice.id);
      } catch (saveError) {
        console.error('DATABASE ERROR during invoice save:', saveError);
        // Try to identify the specific error for better feedback
        const errorMessage = saveError instanceof Error ? saveError.message : 'Unknown error';
        
        if (errorMessage.includes('duplicate') || errorMessage.includes('unique constraint')) {
          throw new Error(`Database constraint error: Duplicate invoice number '${entity.invoiceNumber}'`);
        } else if (errorMessage.includes('not null') || errorMessage.includes('NULL')) {
          throw new Error(`Database error: Required field missing - ${errorMessage}`);
        } else {
          throw new Error(`Database error saving invoice: ${errorMessage}`);
        }
      }
      
      // Handle manual relation creation if needed
      if (relationsMismatch.assets || relationsMismatch.software || relationsMismatch.users) {
        console.log('Detected relations mismatch, will try to manually create relationships');
        
        // For any relations that are missing but should be present, try to save them directly
        if (relationsMismatch.assets) {
          try {
            console.log('Saving assets relationship manually via query builder');
            // Extract asset IDs
            const assetIds = formData.linkedAssets!
              .filter(item => item && item.id)
              .map(item => item.id);
              
            if (assetIds.length > 0) {
              // Insert relationships
              for (const assetId of assetIds) {
                await AppDataSource.query(
                  `INSERT INTO billing_invoice_assets (invoice_id, asset_id) VALUES (?, ?)`,
                  [savedInvoice.id, assetId]
                );
              }
              console.log(`Manually saved ${assetIds.length} asset relationships`);
            }
          } catch (error) {
            console.error('Error saving asset relationships manually:', error);
          }
        }
        
        if (relationsMismatch.software) {
          try {
            console.log('Saving software relationship manually via query builder');
            // Extract software IDs
            const softwareIds = formData.linkedSoftware!
              .filter(item => item && item.id)
              .map(item => item.id);
              
            if (softwareIds.length > 0) {
              // Insert relationships
              for (const softwareId of softwareIds) {
                await AppDataSource.query(
                  `INSERT INTO billing_invoice_software (invoice_id, software_id) VALUES (?, ?)`,
                  [savedInvoice.id, softwareId]
                );
              }
              console.log(`Manually saved ${softwareIds.length} software relationships`);
            }
          } catch (error) {
            console.error('Error saving software relationships manually:', error);
          }
        }
        
        if (relationsMismatch.users) {
          try {
            console.log('Saving users relationship manually via query builder');
            // Extract user IDs
            const userIds = formData.assignedUsers!
              .filter(item => item && item.id)
              .map(item => item.id);
              
            if (userIds.length > 0) {
              // Insert relationships
              for (const userId of userIds) {
                await AppDataSource.query(
                  `INSERT INTO billing_invoice_users (invoice_id, user_id) VALUES (?, ?)`,
                  [savedInvoice.id, userId]
                );
              }
              console.log(`Manually saved ${userIds.length} user relationships`);
            }
          } catch (error) {
            console.error('Error saving user relationships manually:', error);
          }
        }
      }
      
      // Fetch the saved invoice with all relations
      let fullInvoice: BillingInvoice | null = null;
      try {
        fullInvoice = await billingInvoiceRepository.findOne({
          where: { id: savedInvoice.id },
          relations: ['linkedAssets', 'linkedSoftware', 'assignedUsers', 'vendor']
        });
        
        // If relations are still missing but we have them in formData, try to fetch directly
        if (fullInvoice) {
          // Initialize relations to avoid null
          fullInvoice.linkedAssets = fullInvoice.linkedAssets || [];
          fullInvoice.linkedSoftware = fullInvoice.linkedSoftware || [];
          fullInvoice.assignedUsers = fullInvoice.assignedUsers || [];
          
          // Log what we got back from the database
          console.log('Fetched relations after save:', {
            assetCount: fullInvoice.linkedAssets.length,
            softwareCount: fullInvoice.linkedSoftware.length,
            userCount: fullInvoice.assignedUsers.length
          });
          
          // If we expect relations but they're missing, try to fetch them directly
          if (relationsMismatch.assets && fullInvoice.linkedAssets.length === 0) {
            try {
              console.log('Direct query for assets as a last resort');
              const assets = await AppDataSource.query(`
                SELECT a.* FROM assets a
                JOIN billing_invoice_assets bia ON a.id = bia.asset_id
                WHERE bia.invoice_id = ?
              `, [savedInvoice.id]);
              
              if (assets && assets.length > 0) {
                fullInvoice.linkedAssets = assets;
                console.log(`Found ${assets.length} assets with direct query`);
              }
            } catch (assetQueryError) {
              console.error('Error in direct asset query:', assetQueryError);
            }
          }
          
          if (relationsMismatch.software && fullInvoice.linkedSoftware.length === 0) {
            try {
              console.log('Direct query for software as a last resort');
              const software = await AppDataSource.query(`
                SELECT s.* FROM software_licenses s
                JOIN billing_invoice_software bis ON s.id = bis.software_id
                WHERE bis.invoice_id = ?
              `, [savedInvoice.id]);
              
              if (software && software.length > 0) {
                fullInvoice.linkedSoftware = software;
                console.log(`Found ${software.length} software items with direct query`);
              }
            } catch (softwareQueryError) {
              console.error('Error in direct software query:', softwareQueryError);
            }
          }
          
          if (relationsMismatch.users && fullInvoice.assignedUsers.length === 0) {
            try {
              console.log('Direct query for users as a last resort');
              const users = await AppDataSource.query(`
                SELECT u.* FROM users u
                JOIN billing_invoice_users biu ON u.id = biu.user_id
                WHERE biu.invoice_id = ?
              `, [savedInvoice.id]);
              
              if (users && users.length > 0) {
                fullInvoice.assignedUsers = users;
                console.log(`Found ${users.length} users with direct query`);
              }
            } catch (userQueryError) {
              console.error('Error in direct user query:', userQueryError);
            }
          }
        }
      } catch (fetchError) {
        console.error('Error fetching saved invoice:', fetchError);
      }
      
      if (!fullInvoice) {
        console.warn('Could not fetch saved invoice with relations, using basic invoice data');
        return {
          id: savedInvoice.id,
          invoiceNumber: savedInvoice.invoiceNumber,
          vendorName: savedInvoice.vendorName,
          serviceProduct: savedInvoice.serviceProduct,
          department: savedInvoice.department,
          billingCategory: savedInvoice.billingCategory,
          invoiceDate: savedInvoice.invoiceDate,
          dueDate: savedInvoice.dueDate,
          amount: savedInvoice.amount,
          currency: savedInvoice.currency,
          tax: savedInvoice.tax,
          totalAmount: savedInvoice.totalAmount,
          paymentMethod: savedInvoice.paymentMethod,
          invoiceFileUrl: savedInvoice.invoiceFileUrl,
          notes: savedInvoice.notes || '',
          isRecurring: savedInvoice.isRecurring,
          billingFrequency: savedInvoice.billingFrequency,
          reminderDays: savedInvoice.reminderDays,
          linkedAssets: formData.linkedAssets || [], // Use the form data as a fallback
          linkedSoftware: formData.linkedSoftware || [], // Use the form data as a fallback
          assignedUsers: formData.assignedUsers || [], // Use the form data as a fallback
          approvalStatus: savedInvoice.approvalStatus,
          createdBy: savedInvoice.createdBy,
          approvedBy: savedInvoice.approvedBy,
          lastModifiedBy: savedInvoice.lastModifiedBy,
          lastModifiedDate: savedInvoice.updatedAt.toISOString()
        };
      }
      
      const responseData = entityToFormData(fullInvoice);
      
      // Final check: if the form data had relations but our response doesn't,
      // manually include them in the response
      if (formData.linkedAssets?.length && !responseData.linkedAssets?.length) {
        responseData.linkedAssets = formData.linkedAssets;
        console.log('Adding linkedAssets from form data to response');
      }
      
      if (formData.linkedSoftware?.length && !responseData.linkedSoftware?.length) {
        responseData.linkedSoftware = formData.linkedSoftware;
        console.log('Adding linkedSoftware from form data to response');
      }
      
      if (formData.assignedUsers?.length && !responseData.assignedUsers?.length) {
        responseData.assignedUsers = formData.assignedUsers;
        console.log('Adding assignedUsers from form data to response');
      }
      
      return responseData;
    } catch (error) {
      console.error('Error creating invoice:', error);
      throw error;
    }
  },
  
  // Update an existing invoice
  async updateInvoice(id: string, formData: BillingInvoiceFormData): Promise<BillingInvoiceFormData> {
    try {
      console.log('Updating invoice with data:', {
        id,
        invoiceNumber: formData.invoiceNumber,
        hasFileUrl: !!formData.invoiceFileUrl,
        hasAssets: !!formData.linkedAssets?.length,
        hasSoftware: !!formData.linkedSoftware?.length,
        hasUsers: !!formData.assignedUsers?.length,
        dateTypes: {
          invoiceDate: typeof formData.invoiceDate,
          dueDate: typeof formData.dueDate
        }
      });
      
      // Ensure we're updating the correct invoice
      formData.id = id;
      
      // Check if the invoice exists
      const existingInvoice = await billingInvoiceRepository.findOne({
        where: { id },
        relations: ['linkedAssets', 'linkedSoftware', 'assignedUsers', 'vendor']
      });
      
      if (!existingInvoice) {
        throw new Error(`Invoice with ID ${id} not found`);
      }
      
      // Check if invoice number is changed and if it's unique
      if (formData.invoiceNumber !== existingInvoice.invoiceNumber) {
        const duplicateInvoice = await billingInvoiceRepository.findOne({
          where: { invoiceNumber: formData.invoiceNumber }
        });
        
        if (duplicateInvoice && duplicateInvoice.id !== id) {
          throw new Error(`Invoice number '${formData.invoiceNumber}' already exists`);
        }
      }
      
      // Convert formData to entity
      const entity = await formDataToEntity(formData);
      
      // If relations are empty in the entity but the formData had them, log a warning
      if (
        (!entity.linkedAssets?.length && formData.linkedAssets?.length) ||
        (!entity.linkedSoftware?.length && formData.linkedSoftware?.length) ||
        (!entity.assignedUsers?.length && formData.assignedUsers?.length)
      ) {
        console.warn('Warning: Relations were lost during entity conversion', {
          formData: {
            assetsCount: formData.linkedAssets?.length || 0,
            softwareCount: formData.linkedSoftware?.length || 0,
            usersCount: formData.assignedUsers?.length || 0
          },
          entity: {
            assetsCount: entity.linkedAssets?.length || 0,
            softwareCount: entity.linkedSoftware?.length || 0,
            usersCount: entity.assignedUsers?.length || 0
          }
        });
        
        // In this case, try to use the original relations if they're not empty
        if (!entity.linkedAssets?.length && existingInvoice.linkedAssets?.length) {
          console.log('Using existing linkedAssets as fallback');
          entity.linkedAssets = existingInvoice.linkedAssets;
        }
        
        if (!entity.linkedSoftware?.length && existingInvoice.linkedSoftware?.length) {
          console.log('Using existing linkedSoftware as fallback');
          entity.linkedSoftware = existingInvoice.linkedSoftware;
        }
        
        if (!entity.assignedUsers?.length && existingInvoice.assignedUsers?.length) {
          console.log('Using existing assignedUsers as fallback');
          entity.assignedUsers = existingInvoice.assignedUsers;
        }
      }
      
      // Log the entity before saving
      console.log('Saving updated invoice entity with values:', {
        invoiceNumber: entity.invoiceNumber,
        amount: entity.amount,
        totalAmount: entity.totalAmount, 
        invoiceDate: entity.invoiceDate,
        dueDate: entity.dueDate,
        assetsCount: entity.linkedAssets?.length || 0,
        softwareCount: entity.linkedSoftware?.length || 0,
        usersCount: entity.assignedUsers?.length || 0
      });
      
      const savedInvoice = await billingInvoiceRepository.save(entity);
      
      // For any relations that are missing but should be present, try to save them directly
      if (formData.linkedAssets?.length && !entity.linkedAssets?.length) {
        try {
          console.log('Saving assets relationship manually via query builder');
          // Extract asset IDs
          const assetIds = formData.linkedAssets
            .filter(item => item && item.id)
            .map(item => item.id);
            
          if (assetIds.length > 0) {
            // First delete existing relationships
            await AppDataSource.query(
              `DELETE FROM billing_invoice_assets WHERE invoice_id = ?`,
              [id]
            );
            
            // Then insert new relationships
            for (const assetId of assetIds) {
              await AppDataSource.query(
                `INSERT INTO billing_invoice_assets (invoice_id, asset_id) VALUES (?, ?)`,
                [id, assetId]
              );
            }
            console.log(`Manually saved ${assetIds.length} asset relationships`);
          }
        } catch (error) {
          console.error('Error saving asset relationships manually:', error);
        }
      }
      
      if (formData.linkedSoftware?.length && !entity.linkedSoftware?.length) {
        try {
          console.log('Saving software relationship manually via query builder');
          // Extract software IDs
          const softwareIds = formData.linkedSoftware
            .filter(item => item && item.id)
            .map(item => item.id);
            
          if (softwareIds.length > 0) {
            // First delete existing relationships
            await AppDataSource.query(
              `DELETE FROM billing_invoice_software WHERE invoice_id = ?`,
              [id]
            );
            
            // Then insert new relationships
            for (const softwareId of softwareIds) {
              await AppDataSource.query(
                `INSERT INTO billing_invoice_software (invoice_id, software_id) VALUES (?, ?)`,
                [id, softwareId]
              );
            }
            console.log(`Manually saved ${softwareIds.length} software relationships`);
          }
        } catch (error) {
          console.error('Error saving software relationships manually:', error);
        }
      }
      
      if (formData.assignedUsers?.length && !entity.assignedUsers?.length) {
        try {
          console.log('Saving users relationship manually via query builder');
          // Extract user IDs
          const userIds = formData.assignedUsers
            .filter(item => item && item.id)
            .map(item => item.id);
            
          if (userIds.length > 0) {
            // First delete existing relationships
            await AppDataSource.query(
              `DELETE FROM billing_invoice_users WHERE invoice_id = ?`,
              [id]
            );
            
            // Then insert new relationships
            for (const userId of userIds) {
              await AppDataSource.query(
                `INSERT INTO billing_invoice_users (invoice_id, user_id) VALUES (?, ?)`,
                [id, userId]
              );
            }
            console.log(`Manually saved ${userIds.length} user relationships`);
          }
        } catch (error) {
          console.error('Error saving user relationships manually:', error);
        }
      }
      
      // Fetch the saved invoice with all relations
      const fullInvoice = await billingInvoiceRepository.findOne({
        where: { id: savedInvoice.id },
        relations: ['linkedAssets', 'linkedSoftware', 'assignedUsers', 'vendor']
      });
      
      if (!fullInvoice) {
        throw new Error('Failed to fetch updated invoice');
      }
      
      // If relations are still missing, try a direct lookup
      if (formData.linkedAssets?.length && !fullInvoice.linkedAssets?.length) {
        try {
          console.log('Directly fetching assets for response');
          const assets = await AppDataSource.query(`
            SELECT a.* FROM assets a
            JOIN billing_invoice_assets bia ON a.id = bia.asset_id
            WHERE bia.invoice_id = ?
          `, [id]);
          fullInvoice.linkedAssets = assets;
        } catch (error) {
          console.error('Error fetching assets for response:', error);
        }
      }
      
      return entityToFormData(fullInvoice);
    } catch (error) {
      console.error(`Error updating invoice with ID ${id}:`, error);
      throw error;
    }
  },
  
  // Delete an invoice
  async deleteInvoice(id: string): Promise<boolean> {
    try {
      const result = await billingInvoiceRepository.delete(id);
      return result.affected !== null && result.affected !== undefined && result.affected > 0;
    } catch (error) {
      console.error(`Error deleting invoice with ID ${id}:`, error);
      throw error;
    }
  },
  
  // Change invoice approval status
  async updateApprovalStatus(
    id: string, 
    status: ApprovalStatus, 
    approvedBy?: string
  ): Promise<BillingInvoiceFormData> {
    try {
      const invoice = await billingInvoiceRepository.findOne({
        where: { id },
        relations: ['linkedAssets', 'linkedSoftware', 'assignedUsers', 'vendor']
      });
      
      if (!invoice) {
        throw new Error(`Invoice with ID ${id} not found`);
      }
      
      invoice.approvalStatus = status;
      
      if (status === ApprovalStatus.APPROVED && approvedBy) {
        invoice.approvedBy = approvedBy;
      }
      
      const savedInvoice = await billingInvoiceRepository.save(invoice);
      return entityToFormData(savedInvoice);
    } catch (error) {
      console.error(`Error updating approval status for invoice with ID ${id}:`, error);
      throw error;
    }
  }
};

export default BillingInvoiceService; 