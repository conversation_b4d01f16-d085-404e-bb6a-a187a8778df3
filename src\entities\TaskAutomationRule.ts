import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  ManyToOne,
  JoinColum<PERSON>
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsEnum, Length } from 'class-validator';
import { User } from './User';
import { Project } from './Project';

export enum AutomationTrigger {
  TASK_CREATED = 'task_created',
  TASK_STATUS_CHANGED = 'task_status_changed',
  TASK_ASSIGNED = 'task_assigned',
  TASK_DUE_SOON = 'task_due_soon',
  TASK_OVERDUE = 'task_overdue',
  PROJECT_CREATED = 'project_created',
  USER_WORKLOAD_LOW = 'user_workload_low',
  SKILL_MATCH = 'skill_match'
}

export enum AutomationAction {
  ASSIGN_TO_USER = 'assign_to_user',
  ASSIGN_BY_SKILL = 'assign_by_skill',
  ASSIGN_BY_WORKLOAD = 'assign_by_workload',
  SEND_NOTIFICATION = 'send_notification',
  CREATE_SUBTASK = 'create_subtask',
  UPDATE_STATUS = 'update_status',
  SET_PRIORITY = 'set_priority',
  ADD_TAG = 'add_tag',
  SEND_EMAIL = 'send_email'
}

@Entity('task_automation_rules')
export class TaskAutomationRule {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Rule name is required' })
  @Length(2, 255, { message: 'Rule name must be between 2 and 255 characters' })
  name: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  description: string;

  @Column({
    type: 'enum',
    enum: AutomationTrigger
  })
  @IsEnum(AutomationTrigger, { message: 'Invalid automation trigger' })
  trigger: AutomationTrigger;

  @Column({ type: 'json' })
  triggerConditions: {
    projectIds?: number[];
    taskTypes?: string[];
    priorities?: string[];
    tags?: string[];
    assigneeIds?: string[];
    customConditions?: {
      field: string;
      operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
      value: any;
    }[];
  };

  @Column({
    type: 'enum',
    enum: AutomationAction
  })
  @IsEnum(AutomationAction, { message: 'Invalid automation action' })
  action: AutomationAction;

  @Column({ type: 'json' })
  actionConfig: {
    userId?: string;
    skillTags?: string[];
    statusValue?: string;
    priorityValue?: string;
    tagValue?: string;
    notificationMessage?: string;
    emailTemplate?: string;
    subtaskTemplate?: {
      title: string;
      description?: string;
      estimatedHours?: number;
    };
  };

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'int', default: 0 })
  executionCount: number;

  @Column({ type: 'timestamp', nullable: true })
  lastExecutedAt: Date;

  @Column({ type: 'int', default: 0 })
  priority: number; // Higher number = higher priority

  // Relations
  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @Column({ type: 'int', nullable: true })
  projectId: number;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @Column({ type: 'uuid' })
  createdById: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;
}
