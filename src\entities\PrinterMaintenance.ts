import { <PERSON><PERSON><PERSON>, <PERSON>umn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, PrimaryColumn, BeforeInsert } from 'typeorm';
import { Asset } from './Asset';
import { User } from './User';
import { Vendor } from './Vendor';
import { v4 as uuidv4 } from 'uuid';

@Entity('printer_maintenance')
export class PrinterMaintenance {
  @PrimaryColumn({ type: 'varchar', length: 36 })
  id: string;

  @BeforeInsert()
  generateId() {
    // Only generate an ID if:
    // 1. This is not an update operation (explicitly marked)
    // 2. AND The ID is not already set
    if (this._isUpdate) {
      console.log(`📋 SKIPPING ID GENERATION - entity marked as update operation: ${this.id}`);
      return;
    }
    
    if (this.id) {
      console.log(`📋 SKIPPING ID GENERATION - entity already has ID: ${this.id}`);
      return;
    }
    
    // Additional check for explicit flags from the controller that might be passed from frontend
    if ((this as any)._preventNewRecord === true) {
      console.log(`📋 SKIPPING ID GENERATION - _preventNewRecord flag is set`);
      return;
    }
    
    // If we get here, this is a new record, so generate an ID
    console.log('📋 GENERATING NEW ID - entity is new');
    this.id = uuidv4();
  }

  @ManyToOne(() => Asset, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'assetId' })
  asset: Asset;

  @Column({ type: 'int' })
  assetId: number;

  @ManyToOne(() => Vendor)
  @JoinColumn({ name: 'vendorId' })
  vendor: Vendor;

  @Column({ type: 'varchar', length: 36 })
  vendorId: string;

  @Column({ type: 'date' })
  serviceDate: Date;

  @Column({ type: 'text' })
  serviceDescription: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  invoiceAmount: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  invoiceNumber: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  invoiceFilePath: string;

  @Column({ type: 'json', nullable: true })
  partsReplaced: {
    partName: string;
    partNumber?: string;
    quantity: number;
    unitCost?: number;
  }[];

  @Column({ type: 'json', nullable: true })
  tonerReplaced: {
    type: string;
    color?: string;
    modelNumber?: string;
    quantity: number;
  }[];

  @ManyToOne(() => User)
  @JoinColumn({ name: 'assigneeId' })
  assignee: User;

  @Column({ type: 'varchar', length: 36 })
  assigneeId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'approvedById' })
  approvedBy: User;

  @Column({ type: 'varchar', length: 36, nullable: true })
  approvedById: string;

  @Column({ type: 'date', nullable: true })
  approvalDate: Date;

  @Column({ type: 'varchar', length: 50, default: 'Pending' })
  approvalStatus: 'Pending' | 'Approved' | 'Rejected';

  @Column({ type: 'boolean', default: false })
  submittedToFinance: boolean;

  @Column({ type: 'date', nullable: true })
  submittedToFinanceDate: Date;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'varchar', length: 50, default: 'Completed' })
  serviceStatus: 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled';

  @Column({ type: 'date', nullable: true })
  nextServiceDate: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  private _isUpdate: boolean = false;

  markAsUpdate() {
    console.log(`📋 ENTITY DEBUG - markAsUpdate called on record ${this.id}`);
    this._isUpdate = true;
    return this;
  }
} 