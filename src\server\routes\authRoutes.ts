import express, { Request, Response, NextFunction } from 'express';
import { authController } from '../controllers/authController';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();

// Enhanced error handling wrapper
const asyncHandler = (fn: Function) => async (req: Request, res: Response, next: NextFunction) => {
  try {
    await fn(req, res);
  } catch (error) {
    console.error('Route error:', error);
    // Send a properly formatted error response
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Public routes with better error handling
router.post('/register', asyncHandler(authController.registerUser));
router.post('/login', asyncHandler(authController.login));
router.post('/create-test-user', async<PERSON>and<PERSON>(authController.createTestUser));

// Protected routes
router.get('/me', 
  authMiddleware.verify,
  asyncHandler(authController.getCurrentUser)
);

router.post('/change-password', authMiddleware.verify, asyncHandler(authController.changePassword));

export default router;
