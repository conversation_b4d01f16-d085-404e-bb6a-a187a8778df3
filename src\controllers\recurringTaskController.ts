import { Request, Response } from 'express';
import { AppDataSource } from '../config/database';
import { Task, TaskRecurrenceType } from '../entities/Task';
import { Project } from '../entities/Project';
import { User } from '../entities/User';
import { recurringTaskService } from '../services/RecurringTaskService';
import { validate } from 'class-validator';

export class RecurringTaskController {
  private taskRepository = AppDataSource.getRepository(Task);
  private projectRepository = AppDataSource.getRepository(Project);
  private userRepository = AppDataSource.getRepository(User);

  // Create a new recurring task
  async createRecurringTask(req: Request, res: Response) {
    try {
      const userId = (req as any).user?.id;
      const taskData = req.body;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Validate required fields
      if (!taskData.title || !taskData.projectId || !taskData.recurrenceType) {
        return res.status(400).json({ 
          error: 'Title, project ID, and recurrence type are required' 
        });
      }

      // Validate recurrence type
      if (!Object.values(TaskRecurrenceType).includes(taskData.recurrenceType)) {
        return res.status(400).json({ error: 'Invalid recurrence type' });
      }

      // Validate project exists and user has access
      const project = await this.projectRepository.findOne({
        where: { id: taskData.projectId, isActive: true },
        relations: ['members']
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      const isMember = project.createdById === userId ||
                      project.managerId === userId ||
                      project.members.some(member => member.userId === userId && member.isActive);

      if (!isMember) {
        return res.status(403).json({ error: 'You are not a member of this project' });
      }

      // Set default recurrence config if not provided
      if (!taskData.recurrenceConfig) {
        taskData.recurrenceConfig = this.getDefaultRecurrenceConfig(taskData.recurrenceType);
      }

      // Create recurring task using the service
      const recurringTask = await recurringTaskService.createRecurringTask({
        ...taskData,
        createdById: userId,
        status: 'todo',
        progress: 0,
        timeSpentMinutes: 0,
        isBlocked: false,
        isActive: true
      });

      // Fetch complete task with relations
      const completeTask = await this.taskRepository.findOne({
        where: { id: recurringTask.id },
        relations: ['project', 'createdBy', 'assignedTo']
      });

      res.status(201).json(completeTask);
    } catch (error) {
      console.error('Error creating recurring task:', error);
      res.status(500).json({ error: 'Failed to create recurring task' });
    }
  }

  // Get all recurring tasks
  async getRecurringTasks(req: Request, res: Response) {
    try {
      const userId = (req as any).user?.id;
      const { projectId, status } = req.query;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const queryBuilder = this.taskRepository
        .createQueryBuilder('task')
        .leftJoinAndSelect('task.project', 'project')
        .leftJoinAndSelect('task.createdBy', 'createdBy')
        .leftJoinAndSelect('task.assignedTo', 'assignedTo')
        .where('task.recurrenceType != :none', { none: TaskRecurrenceType.NONE })
        .andWhere('task.isActive = :isActive', { isActive: true })
        .andWhere('task.originalTaskId IS NULL'); // Only original recurring tasks, not instances

      // Filter by project if specified
      if (projectId) {
        queryBuilder.andWhere('task.projectId = :projectId', { projectId });
      }

      // Filter by status if specified
      if (status) {
        queryBuilder.andWhere('task.status = :status', { status });
      }

      // Only show tasks user has access to
      queryBuilder.andWhere(
        '(task.createdById = :userId OR task.assignedToId = :userId OR project.createdById = :userId OR project.managerId = :userId)',
        { userId }
      );

      const recurringTasks = await queryBuilder
        .orderBy('task.createdAt', 'DESC')
        .getMany();

      res.json(recurringTasks);
    } catch (error) {
      console.error('Error fetching recurring tasks:', error);
      res.status(500).json({ error: 'Failed to fetch recurring tasks' });
    }
  }

  // Get recurring task by ID with instances
  async getRecurringTaskById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const recurringTask = await this.taskRepository.findOne({
        where: { 
          id: Number(id), 
          isActive: true,
          recurrenceType: { $ne: TaskRecurrenceType.NONE } as any
        },
        relations: ['project', 'createdBy', 'assignedTo']
      });

      if (!recurringTask) {
        return res.status(404).json({ error: 'Recurring task not found' });
      }

      // Check if user has access
      const hasAccess = recurringTask.createdById === userId ||
                       recurringTask.assignedToId === userId ||
                       recurringTask.project.createdById === userId ||
                       recurringTask.project.managerId === userId;

      if (!hasAccess) {
        return res.status(403).json({ error: 'You do not have access to this recurring task' });
      }

      // Get task instances
      const instances = await recurringTaskService.getRecurringTaskInstances(Number(id), 20);
      
      // Get statistics
      const stats = await recurringTaskService.getRecurringTaskStats(Number(id));

      res.json({
        recurringTask,
        instances,
        stats
      });
    } catch (error) {
      console.error('Error fetching recurring task:', error);
      res.status(500).json({ error: 'Failed to fetch recurring task' });
    }
  }

  // Update recurring task
  async updateRecurringTask(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;
      const updates = req.body;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const recurringTask = await this.taskRepository.findOne({
        where: { 
          id: Number(id), 
          isActive: true,
          recurrenceType: { $ne: TaskRecurrenceType.NONE } as any
        },
        relations: ['project']
      });

      if (!recurringTask) {
        return res.status(404).json({ error: 'Recurring task not found' });
      }

      // Check if user has permission to update
      const hasPermission = recurringTask.createdById === userId ||
                           recurringTask.project.createdById === userId ||
                           recurringTask.project.managerId === userId;

      if (!hasPermission) {
        return res.status(403).json({ error: 'You do not have permission to update this recurring task' });
      }

      // Update using the service (handles rescheduling if needed)
      await recurringTaskService.updateRecurringTask(Number(id), updates);

      // Fetch updated task
      const updatedTask = await this.taskRepository.findOne({
        where: { id: Number(id) },
        relations: ['project', 'createdBy', 'assignedTo']
      });

      res.json(updatedTask);
    } catch (error) {
      console.error('Error updating recurring task:', error);
      res.status(500).json({ error: 'Failed to update recurring task' });
    }
  }

  // Stop/Delete recurring task
  async stopRecurringTask(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const recurringTask = await this.taskRepository.findOne({
        where: { 
          id: Number(id), 
          isActive: true,
          recurrenceType: { $ne: TaskRecurrenceType.NONE } as any
        },
        relations: ['project']
      });

      if (!recurringTask) {
        return res.status(404).json({ error: 'Recurring task not found' });
      }

      // Check if user has permission to stop
      const hasPermission = recurringTask.createdById === userId ||
                           recurringTask.project.createdById === userId ||
                           recurringTask.project.managerId === userId;

      if (!hasPermission) {
        return res.status(403).json({ error: 'You do not have permission to stop this recurring task' });
      }

      // Stop the recurring task
      await recurringTaskService.stopRecurringTask(Number(id));

      res.json({ message: 'Recurring task stopped successfully' });
    } catch (error) {
      console.error('Error stopping recurring task:', error);
      res.status(500).json({ error: 'Failed to stop recurring task' });
    }
  }

  // Get task instances for a recurring task
  async getTaskInstances(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { limit = 20, offset = 0 } = req.query;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Verify user has access to the recurring task
      const recurringTask = await this.taskRepository.findOne({
        where: { 
          id: Number(id), 
          isActive: true,
          recurrenceType: { $ne: TaskRecurrenceType.NONE } as any
        },
        relations: ['project']
      });

      if (!recurringTask) {
        return res.status(404).json({ error: 'Recurring task not found' });
      }

      const hasAccess = recurringTask.createdById === userId ||
                       recurringTask.assignedToId === userId ||
                       recurringTask.project.createdById === userId ||
                       recurringTask.project.managerId === userId;

      if (!hasAccess) {
        return res.status(403).json({ error: 'You do not have access to this recurring task' });
      }

      // Get instances with pagination
      const instances = await this.taskRepository.find({
        where: { originalTaskId: Number(id) },
        relations: ['assignedTo', 'project'],
        order: { createdAt: 'DESC' },
        take: Number(limit),
        skip: Number(offset)
      });

      const totalCount = await this.taskRepository.count({
        where: { originalTaskId: Number(id) }
      });

      res.json({
        instances,
        pagination: {
          total: totalCount,
          limit: Number(limit),
          offset: Number(offset),
          hasMore: Number(offset) + instances.length < totalCount
        }
      });
    } catch (error) {
      console.error('Error fetching task instances:', error);
      res.status(500).json({ error: 'Failed to fetch task instances' });
    }
  }

  // Get recurrence types and their descriptions
  async getRecurrenceTypes(req: Request, res: Response) {
    try {
      const recurrenceTypes = [
        {
          value: TaskRecurrenceType.DAILY,
          label: 'Daily',
          description: 'Repeat every day or every N days',
          configFields: ['interval']
        },
        {
          value: TaskRecurrenceType.WEEKLY,
          label: 'Weekly',
          description: 'Repeat on specific days of the week',
          configFields: ['daysOfWeek', 'interval']
        },
        {
          value: TaskRecurrenceType.MONTHLY,
          label: 'Monthly',
          description: 'Repeat on a specific day of the month',
          configFields: ['dayOfMonth', 'interval']
        },
        {
          value: TaskRecurrenceType.QUARTERLY,
          label: 'Quarterly',
          description: 'Repeat every quarter (every 3 months)',
          configFields: []
        },
        {
          value: TaskRecurrenceType.YEARLY,
          label: 'Yearly',
          description: 'Repeat once per year',
          configFields: ['interval']
        }
      ];

      res.json(recurrenceTypes);
    } catch (error) {
      console.error('Error fetching recurrence types:', error);
      res.status(500).json({ error: 'Failed to fetch recurrence types' });
    }
  }

  // Helper method to get default recurrence config
  private getDefaultRecurrenceConfig(recurrenceType: TaskRecurrenceType) {
    switch (recurrenceType) {
      case TaskRecurrenceType.DAILY:
        return { interval: 1 };
      case TaskRecurrenceType.WEEKLY:
        return { daysOfWeek: [1], interval: 1 }; // Monday
      case TaskRecurrenceType.MONTHLY:
        return { dayOfMonth: 1, interval: 1 }; // 1st of month
      case TaskRecurrenceType.QUARTERLY:
        return {};
      case TaskRecurrenceType.YEARLY:
        return { interval: 1 };
      default:
        return {};
    }
  }
}
