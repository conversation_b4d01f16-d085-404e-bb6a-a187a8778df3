import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { FileSpreadsheet, Upload, FileType, AlertCircle, CheckCircle2, X } from 'lucide-react';
import * as XLSX from 'xlsx';
import { Attendance, AttendanceStatus, AttendanceImportData } from '../../../types/attendance';

interface AttendanceImportProps {
  onImport: (data: AttendanceImportData[]) => void;
  onClose: () => void;
}

const AttendanceImport: React.FC<AttendanceImportProps> = ({ onImport, onClose }) => {
  const [file, setFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [importedData, setImportedData] = useState<AttendanceImportData[]>([]);
  const [isTimeCardFormat, setIsTimeCardFormat] = useState(false);

  // Log when component mounts
  React.useEffect(() => {
    console.log('AttendanceImport component mounted');
    return () => {
      console.log('AttendanceImport component unmounted');
    };
  }, []);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    console.log('File dropped:', file.name);
    setFile(file);
    setError(null);

    // Display file preview for supported types
    if (file.type === 'application/pdf') {
      setFilePreview('/pdf-preview.png'); // You would need an actual PDF preview
    } else {
      const reader = new FileReader();
      reader.onload = () => {
        setFilePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv'],
      'application/pdf': ['.pdf']
    },
    maxFiles: 1
  });

  const handleImport = async () => {
    if (!file) {
      setError('Please select a file to import');
      return;
    }

    setLoading(true);
    setError(null);
    console.log('Processing file:', file.name);

    try {
      let data: any[] = [];

      if (file.type === 'text/csv' || file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        const reader = new FileReader();
        
        reader.onload = (e) => {
          try {
            console.log('File read successfully');
            const result = e.target?.result;
            
            if (typeof result === 'string' && file.type === 'text/csv') {
              // First, log the raw first few lines of the CSV for debugging
              const lines = result.split('\n');
              console.log('CSV Header:', lines[0]);
              console.log('First data row:', lines[1]);
              if (lines.length > 2) console.log('Second data row:', lines[2]);
              
              // Process CSV
              const rows = result.split('\n');
              const headers = rows[0].split(',');
              
              // Check if this is a time card format
              const isTimeCardFormat = headers.includes('Employee ID') && 
                                    (headers.includes('First Name') || headers.includes('Clock In'));
              
              setIsTimeCardFormat(isTimeCardFormat);

              // Helper function to safely parse CSV rows 
              const parseCSVRow = (row: string) => {
                const values: string[] = [];
                let inQuotes = false;
                let currentValue = '';
                
                // Handle CSV with potential quotes and commas inside quotes
                for (let i = 0; i < row.length; i++) {
                  const char = row[i];
                  
                  if (char === '"' && (i === 0 || row[i-1] !== '\\')) {
                    inQuotes = !inQuotes;
                  } else if (char === ',' && !inQuotes) {
                    values.push(currentValue.trim());
                    currentValue = '';
                  } else {
                    currentValue += char;
                  }
                }
                
                // Add the last value
                values.push(currentValue.trim());
                return values;
              };

              if (isTimeCardFormat) {
                console.log('Detected time card format, processing accordingly');
                
                // Get index of each relevant column
                const empIdIdx = headers.indexOf('Employee ID');
                console.log('Headers:', headers);
                console.log('Employee ID column index:', empIdIdx);
                
                const firstNameIdx = headers.indexOf('First Name');
                const lastNameIdx = headers.indexOf('Last Name');
                const departmentIdx = headers.indexOf('Department');
                const positionIdx = headers.indexOf('Position');
                const dateIdx = headers.indexOf('Date');
                const clockInIdx = headers.indexOf('Clock In');
                const clockOutIdx = headers.indexOf('Clock Out');
                const totalHoursIdx = headers.indexOf('Total Hours');
                const workedHoursIdx = headers.indexOf('Worked Hours');
                const absenceIdx = headers.indexOf('Absence');
                
                console.log('Column indices:', { 
                  empIdIdx, firstNameIdx, lastNameIdx, dateIdx, clockInIdx, clockOutIdx 
                });
                
                // Debug: Print first few rows of CSV data
                console.log('First row values:', parseCSVRow(rows[1]));
                if (rows.length > 2) console.log('Second row values:', parseCSVRow(rows[2]));
                
                for (let i = 1; i < rows.length; i++) {
                  if (rows[i].trim() === '') continue;
                  
                  // Use more robust CSV parsing
                  const values = parseCSVRow(rows[i]);
                  
                  // Skip rows with no employee ID
                  if (!values[empIdIdx]?.trim()) {
                    console.log(`Skipping row ${i}: No employee ID found`);
                    continue;
                  }
                  
                  // Combine first and last name
                  const firstName = values[firstNameIdx]?.trim() || '';
                  const lastName = values[lastNameIdx]?.trim() || '';
                  const fullName = [firstName, lastName].filter(Boolean).join(' ');
                  
                  // Extract the employee ID and ensure it's not empty
                  const empId = values[empIdIdx]?.trim() || '';
                  console.log(`Raw employee ID from CSV: '${empId}', row ${i}, fullName: ${fullName}`);
                  
                  // Skip rows with no employee ID
                  if (!empId) {
                    console.log(`Skipping row ${i}: No employee ID found`);
                    continue;
                  }
                  
                  // Determine status based on absence and clock in/out
                  let status = 'present';
                  if (values[absenceIdx]?.trim()) {
                    status = 'absent';
                  } else if (!values[clockInIdx]?.trim() && !values[clockOutIdx]?.trim()) {
                    status = 'absent';
                  } else if (values[clockInIdx]?.trim() && !values[clockOutIdx]?.trim()) {
                    status = 'present'; // Could be still working
                  }
                  
                  // Extract just the HH:MM from the clock times (which may be in HH:MM:SS format)
                  const clockIn = values[clockInIdx]?.trim() 
                    ? values[clockInIdx].trim().substring(0, 5) 
                    : '';
                    
                  const clockOut = values[clockOutIdx]?.trim() 
                    ? values[clockOutIdx].trim().substring(0, 5) 
                    : '';
                  
                  // Use worked hours if available, otherwise total hours
                  const hours = values[workedHoursIdx]?.trim() || values[totalHoursIdx]?.trim() || '0';
                  
                  // Create record using the time card format - KEEP EMPLOYEE ID AS STRING
                  data.push({
                    employeeCode: empId, // Keep as string for proper matching
                    employeeName: fullName,
                    date: values[dateIdx].trim(),
                    checkInTime: clockIn,
                    checkOutTime: clockOut || null,
                    status: status === 'present' ? AttendanceStatus.PRESENT : AttendanceStatus.ABSENT,
                    workHours: parseFloat(hours) || 0,
                    isRemote: false,
                    location: 'Office',
                    department: values[departmentIdx]?.trim() || '',
                    notes: `Position: ${values[positionIdx]?.trim() || 'N/A'}`,
                    shift: 1,
                  });
                }
              } else {
                // Original CSV processing code for standard format
                for (let i = 1; i < rows.length; i++) {
                  if (rows[i].trim() === '') continue;
                  
                  const values = rows[i].split(',');
                  const rowData: Record<string, any> = {};
                  
                  headers.forEach((header, index) => {
                    rowData[header.trim()] = values[index]?.trim() || '';
                  });
                  
                  data.push(rowData);
                }
              }
            } else if (result) {
              // Process Excel
              const workbook = XLSX.read(result, { type: 'binary' });
              const sheetName = workbook.SheetNames[0];
              const worksheet = workbook.Sheets[sheetName];
              data = XLSX.utils.sheet_to_json(worksheet);
            }
            
            console.log('Extracted data:', data.length, 'records');
            
            // Convert imported data to AttendanceImportData format
            const attendanceData = data.map((item, index) => {
              // For employee codes, preserve the original string format
              let employeeCode = item.employeeId || item.employeeCode || item.EmployeeId || item.EmployeeCode || '';
              
              // Convert to string if it's not already
              if (typeof employeeCode !== 'string') {
                employeeCode = String(employeeCode);
              }
              
              // Skip records with no employee code
              if (!employeeCode || employeeCode === 'undefined' || employeeCode === 'null') {
                console.log(`Skipping record ${index}: No employee code found`);
                return null;
              }
              
              // Map status string to enum value
              let statusValue = item.status || item.Status || 'present';
              let mappedStatus: AttendanceStatus;
              
              switch(statusValue.toLowerCase()) {
                case 'present':
                  mappedStatus = AttendanceStatus.PRESENT;
                  break;
                case 'absent':
                  mappedStatus = AttendanceStatus.ABSENT;
                  break;
                case 'late':
                  mappedStatus = AttendanceStatus.LATE;
                  break;
                case 'leave':
                  mappedStatus = AttendanceStatus.LEAVE;
                  break;
                case 'half_day':
                case 'half day':
                  mappedStatus = AttendanceStatus.HALF_DAY;
                  break;
                case 'work_from_home':
                case 'work from home':
                case 'wfh':
                  mappedStatus = AttendanceStatus.WORK_FROM_HOME;
                  break;
                default:
                  mappedStatus = AttendanceStatus.PRESENT;
              }
              
              return {
                id: Date.now() + index,
                employeeCode: employeeCode,
                employeeName: item.employeeName || item.EmployeeName || item['Employee Name'] || '',
                date: item.date || item.Date || new Date().toISOString().split('T')[0],
                checkInTime: item.checkInTime || item.CheckInTime || item['Check In Time'] || '',
                checkOutTime: item.checkOutTime || item.CheckOutTime || item['Check Out Time'] || null,
                status: mappedStatus,
                notes: item.notes || item.Notes || null,
                location: item.location || item.Location || 'Office',
                workHours: Number(item.workHours || item.WorkHours || item['Work Hours'] || 0),
                isRemote: item.isRemote === 'true' || item.isRemote === true || item.IsRemote === 'true' || item.IsRemote === true || false,
                department: item.department || item.Department || '',
                isImported: true,
                shift: 1,
              } as AttendanceImportData;
            });
            
            // Filter out null values (skipped records)
            const validAttendanceData = attendanceData.filter((item): item is AttendanceImportData => item !== null);
            
            console.log('Converted data to AttendanceImportData format:', validAttendanceData.length, 'records');
            setImportedData(validAttendanceData);
            setSuccess(true);
            onImport(validAttendanceData);
            
            setTimeout(() => {
              onClose();
            }, 2000);
            
            setLoading(false);
          } catch (error) {
            console.error('Error processing file:', error);
            setError('Error processing file. Please check the format and try again.');
            setLoading(false);
          }
        };
        
        reader.onerror = () => {
          console.error('Error reading file');
          setError('Error reading file');
          setLoading(false);
        };
        
        if (file.type === 'text/csv') {
          reader.readAsText(file);
        } else {
          reader.readAsBinaryString(file);
        }
      } else if (file.type === 'application/pdf') {
        // For PDF, you would need a PDF parsing library
        // This is a placeholder for PDF parsing logic
        console.warn('PDF parsing not implemented');
        setError('PDF parsing is not implemented in this example');
        setLoading(false);
      } else {
        console.error('Unsupported file type:', file.type);
        setError('Unsupported file type');
        setLoading(false);
      }
    } catch (error) {
      console.error('Import error:', error);
      setError('Failed to import file. Please try again.');
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]" style={{position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, zIndex: 9999}}>
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-auto">
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-800">Import Attendance Data</h2>
          <button 
            onClick={() => {
              console.log('Close button clicked');
              onClose();
            }}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-6">
          {!file ? (
            <div 
              {...getRootProps()} 
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
                ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50'}`}
            >
              <input {...getInputProps()} />
              <FileSpreadsheet className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-700 font-medium mb-1">Drag & drop a file here, or click to select</p>
              <p className="text-gray-500 text-sm mb-4">Supports Excel, CSV, and PDF formats</p>
              
              <div className="flex justify-center items-center text-xs text-gray-500 space-x-2">
                <span className="px-2 py-1 bg-gray-100 rounded">.xlsx</span>
                <span className="px-2 py-1 bg-gray-100 rounded">.xls</span>
                <span className="px-2 py-1 bg-gray-100 rounded">.csv</span>
                <span className="px-2 py-1 bg-gray-100 rounded">.pdf</span>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center p-4 border border-gray-200 rounded-lg bg-gray-50">
                <FileType className="h-10 w-10 text-blue-500 mr-4" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">{file.name}</p>
                  <p className="text-xs text-gray-500">{(file.size / 1024).toFixed(2)} KB</p>
                </div>
                <button 
                  onClick={() => {
                    console.log('Remove file button clicked');
                    setFile(null);
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              
              {loading ? (
                <div className="text-center py-4">
                  <div className="inline-block animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
                  <p className="mt-2 text-gray-600">Processing file...</p>
                </div>
              ) : error ? (
                <div className="bg-red-50 border border-red-200 rounded-md p-4 flex">
                  <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" />
                  <p className="text-sm text-red-600">{error}</p>
                </div>
              ) : success ? (
                <div className="bg-green-50 border border-green-200 rounded-md p-4 flex">
                  <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-sm text-green-600">
                      {isTimeCardFormat ? 
                        `Successfully imported ${importedData.length} attendance records from Time Card format!` :
                        `Successfully imported ${importedData.length} attendance records!`
                      }
                    </p>
                    {isTimeCardFormat && (
                      <p className="text-xs text-green-500 mt-1">
                        Time Card format detected and automatically converted
                      </p>
                    )}
                  </div>
                </div>
              ) : null}
              
              {!loading && !success && (
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => {
                      console.log('Cancel button clicked');
                      setFile(null);
                    }}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      console.log('Import file button clicked');
                      handleImport();
                    }}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Import File
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AttendanceImport; 