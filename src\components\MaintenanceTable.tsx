import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import api from '../services/api';
import { PrinterMaintenanceRecord, MaintenanceTableFilters } from '../types';
import { X } from 'lucide-react';
import PrinterMaintenanceForm from './PrinterMaintenanceForm';
import { Menu, ChevronDown, MoreVertical, FileText, Eye, Trash2, Edit, AlertCircle, ChevronLeft, ChevronRight, Printer, Users, DollarSign, Wrench } from 'lucide-react';
import { format } from 'date-fns';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';

// Modal component for viewing record details
const ViewRecordModal = ({ record, onClose, onDownloadInvoice }: { 
  record: PrinterMaintenanceRecord, 
  onClose: () => void, 
  onDownloadInvoice: (record: PrinterMaintenanceRecord) => void 
}) => {
  if (!record) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Maintenance Record Details</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="col-span-2 bg-blue-50 p-3 rounded-lg">
            <h3 className="font-bold text-blue-700 mb-1">Printer Information</h3>
            <p className="text-sm"><span className="font-semibold">Model:</span> {record.manufacturer} {record.model || record.printer_name || `Asset #${record.asset_id || record.printer_id}`}</p>
            <p className="text-sm"><span className="font-semibold">Asset ID:</span> {record.asset_id || record.printer_id || 'Not specified'}</p>
            <p className="text-sm"><span className="font-semibold">Department:</span> {record.department || 'Not specified'}</p>
          </div>
          
          <div className="bg-green-50 p-3 rounded-lg">
            <h3 className="font-bold text-green-700 mb-1">Service Details</h3>
            <p className="text-sm"><span className="font-semibold">Service Date:</span> {
              (record.visit_date || record.service_date) 
                ? new Date(record.visit_date || record.service_date || '').toLocaleDateString() 
                : 'Not specified'
            }</p>
            <p className="text-sm"><span className="font-semibold">Service Type:</span> {record.service_type || record.action_taken || 'Maintenance'}</p>
            <p className="text-sm"><span className="font-semibold">Issue Description:</span> {record.issue_description || 'Not specified'}</p>
          </div>
          
          <div className="bg-yellow-50 p-3 rounded-lg">
            <h3 className="font-bold text-yellow-700 mb-1">Vendor Information</h3>
            <p className="text-sm"><span className="font-semibold">Vendor:</span> {record.vendor_name || 'Not specified'}</p>
            <p className="text-sm"><span className="font-semibold">Technician:</span> {record.technician_name || 'Not specified'}</p>
            <p className="text-sm"><span className="font-semibold">Assignee:</span> {record.assignee_name || 'Not specified'}</p>
          </div>
          
          <div className="col-span-2 bg-purple-50 p-3 rounded-lg">
            <h3 className="font-bold text-purple-700 mb-1">Invoice Details</h3>
            <div className="grid grid-cols-2 gap-2">
              <p className="text-sm"><span className="font-semibold">Invoice #:</span> {record.invoice_number || 'Not specified'}</p>
              <p className="text-sm"><span className="font-semibold">Amount:</span> {record.invoice_amount || record.cost ? `PKR ${parseFloat(String(record.invoice_amount || record.cost)).toLocaleString('en-PK')}` : 'Not specified'}</p>
              <p className="text-sm"><span className="font-semibold">Status:</span> {record.status || record.serviceStatus || 'Not specified'}</p>
              <p className="text-sm"><span className="font-semibold">Invoice File:</span> {record.invoice_file ? 
                <button onClick={() => onDownloadInvoice(record)} 
                  className="text-blue-600 underline">Download</button> : 'Not available'}</p>
            </div>
          </div>
          
          <div className="col-span-2 bg-gray-50 p-3 rounded-lg">
            <h3 className="font-bold text-gray-700 mb-1">Additional Information</h3>
            <p className="text-sm"><span className="font-semibold">Remarks:</span> {record.remarks || 'None'}</p>
            <p className="text-sm"><span className="font-semibold">Created:</span> {new Date(record.created_at).toLocaleString()}</p>
          </div>
        </div>
        
        <div className="mt-6 flex justify-end">
          <button onClick={onClose} className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded">
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

// Wrapper for ViewRecordModal that handles the null check
const ViewRecordModalWrapper = ({ 
  record, 
  onClose, 
  onDownloadInvoice 
}: { 
  record: PrinterMaintenanceRecord | null, 
  onClose: () => void, 
  onDownloadInvoice: (record: PrinterMaintenanceRecord) => void 
}) => {
  if (!record) return null;
  
  return (
    <ViewRecordModal 
      record={record} 
      onClose={onClose} 
      onDownloadInvoice={onDownloadInvoice} 
    />
  );
};

// Wrapper for EditRecordForm that handles the null check  
const EditRecordFormWrapper = ({
  record,
  onComplete,
  onCancel
}: {
  record: PrinterMaintenanceRecord | null,
  onComplete: () => void,
  onCancel: () => void
}) => {
  if (!record) return null;
  
  console.log('EditRecordFormWrapper rendering with record:', record);
  console.log('Record details - ID:', record.id);
  console.log('Record details - issue_description:', record.issue_description);
  console.log('Record details - technician_name:', record.technician_name);
  console.log('Record details - service_type:', record.service_type);
  console.log('Record details - all record data:', JSON.stringify(record, null, 2));
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-3xl w-full max-h-[90vh] overflow-y-auto">
        <div className="bg-[#4a76cf] text-white p-4 rounded-t-lg flex items-center justify-between">
          <h2 className="text-xl font-semibold">Edit Maintenance Record</h2>
          <button 
            onClick={onCancel}
            className="text-white hover:bg-[#3a66bf] rounded-full w-7 h-7 flex items-center justify-center"
            aria-label="Close"
          >
            ✕
          </button>
        </div>
        <div className="p-4">
          <PrinterMaintenanceForm 
            onComplete={onComplete} 
            inPage={true} 
            isEditMode={true}
            initialData={record}
          />
        </div>
      </div>
    </div>
  );
};

// Export Modal Component
const ExportModal = ({ 
  isOpen, 
  onClose, 
  onExport 
}: { 
  isOpen: boolean, 
  onClose: () => void, 
  onExport: (format: string, scope: 'filtered' | 'all') => Promise<void> 
}) => {
  const [exportFormat, setExportFormat] = useState('xlsx');
  const [exportScope, setExportScope] = useState<'filtered' | 'all'>('filtered');

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <h2 className="text-xl font-bold mb-4">Export Assets</h2>
        
        <div className="mb-6">
          <h3 className="font-medium mb-3 text-gray-700">Export Scope</h3>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                className="h-4 w-4 text-blue-600"
                name="exportScope"
                value="filtered"
                checked={exportScope === 'filtered'}
                onChange={() => setExportScope('filtered')}
              />
              <span className="ml-2">Current View (Filtered)</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                className="h-4 w-4 text-blue-600"
                name="exportScope"
                value="all"
                checked={exportScope === 'all'}
                onChange={() => setExportScope('all')}
              />
              <span className="ml-2">All Assets</span>
            </label>
          </div>
        </div>
        
        <div className="mb-6">
          <h3 className="font-medium mb-3 text-gray-700">Export Format</h3>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                className="h-4 w-4 text-blue-600"
                name="exportFormat"
                value="xlsx"
                checked={exportFormat === 'xlsx'}
                onChange={() => setExportFormat('xlsx')}
              />
              <span className="ml-2">Excel (.xlsx)</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                className="h-4 w-4 text-blue-600"
                name="exportFormat"
                value="csv"
                checked={exportFormat === 'csv'}
                onChange={() => setExportFormat('csv')}
              />
              <span className="ml-2">CSV (.csv)</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                className="h-4 w-4 text-blue-600"
                name="exportFormat"
                value="pdf"
                checked={exportFormat === 'pdf'}
                onChange={() => setExportFormat('pdf')}
              />
              <span className="ml-2">PDF (.pdf)</span>
            </label>
          </div>
        </div>
        
        <div className="flex justify-end space-x-3 mt-4">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={() => onExport(exportFormat, exportScope)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Export
          </button>
        </div>
      </div>
    </div>
  );
};

interface MaintenanceTableProps {
  onNewClick?: () => void;
  hideHeader?: boolean;
  noPadding?: boolean;
  onEditRecord?: (record: PrinterMaintenanceRecord) => void;
  onViewRecord?: (record: PrinterMaintenanceRecord) => void;
  onDeleteRecord?: (record: PrinterMaintenanceRecord) => void;
  renderStatsOnly?: boolean; 
  hideStats?: boolean;
  headerComponent?: React.ReactNode; // Add this prop to inject a component between stats and table
  suppressOuterMessage?: boolean; // Add this prop to prevent duplicate "No records" messages
}

const MaintenanceTable: React.FC<MaintenanceTableProps> = ({ 
  onNewClick,
  hideHeader = false,
  noPadding = false,
  onEditRecord,
  onViewRecord,
  onDeleteRecord,
  renderStatsOnly = false,
  hideStats = false,
  headerComponent,
  suppressOuterMessage = false
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();

  const [records, setRecords] = useState<PrinterMaintenanceRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [viewingRecord, setViewingRecord] = useState<PrinterMaintenanceRecord | null>(null);
  const [editingRecord, setEditingRecord] = useState<PrinterMaintenanceRecord | null>(null);
  const [filters, setFilters] = useState<MaintenanceTableFilters>({
    start_date: '',
    end_date: '',
    printer_id: undefined,
    vendor_name: '',
    department: '',
    search: '',
  });
  
  // Debug mode state
  const [debugMode, setDebugMode] = useState(false);
  const [apiResponse, setApiResponse] = useState<any>(null);
  
  // Export modal state
  const [showExportModal, setShowExportModal] = useState(false);
  
  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [totalRecords, setTotalRecords] = useState(0);
  
  // Lists for filter dropdowns
  const [printers, setPrinters] = useState<Array<{id: number, name: string}>>([]);
  const [vendors, setVendors] = useState<string[]>([]);
  const [departments, setDepartments] = useState<string[]>([]);

  // Stats for dashboard
  const [stats, setStats] = useState({
    totalPrinters: 0,
    assignedPrinters: 0,
    totalCost: 0,
    maintenanceCount: 0,
    requireAttention: 0,
    upcoming: 0,
    warrantyExpiring: 0,
    tonerCost: 0,
    hardwareRepairCost: 0,
    regularServiceCost: 0,
    drumCost: 0,
    fuserUnitCost: 0,
    pickupRollerCost: 0
  });

  // Load URL params on mount and when URL changes
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    
    const newFilters = {
      start_date: params.get('start_date') ?? '',
      end_date: params.get('end_date') ?? '',
      printer_id: params.get('printer_id') ? parseInt(params.get('printer_id') ?? '') : undefined,
      vendor_name: params.get('vendor_name') ?? '',
      department: params.get('department') ?? '',
      search: params.get('search') ?? '',
    };

    // Set pagination from URL
    const page = parseInt(params.get('page') ?? '1');
    const limit = parseInt(params.get('limit') ?? '10');
    
    // Only update state if values have changed
    if (JSON.stringify(filters) !== JSON.stringify(newFilters)) {
      setFilters(newFilters);
    }
    if (currentPage !== page) {
      setCurrentPage(page || 1);
    }
    if (recordsPerPage !== limit) {
      setRecordsPerPage(limit || 10);
    }
  }, [location.search]); // Only depend on URL changes

  // Update URL when filters or pagination change
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    let hasChanges = false;

    // Update only changed parameters
    Object.entries(filters).forEach(([key, value]) => {
      const currentValue = params.get(key);
      if (value && value.toString() !== currentValue) {
        params.set(key, value.toString());
        hasChanges = true;
      } else if (!value && currentValue) {
        params.delete(key);
        hasChanges = true;
      }
    });

    // Update pagination params only if they've changed
    const urlPage = params.get('page');
    const urlLimit = params.get('limit');
    
    // Check if page parameter needs updating
    if (!urlPage || parseInt(urlPage) !== currentPage) {
      params.set('page', currentPage.toString());
      hasChanges = true;
    }
    
    // Check if limit parameter needs updating
    if (!urlLimit || parseInt(urlLimit) !== recordsPerPage) {
      params.set('limit', recordsPerPage.toString());
      hasChanges = true;
    }

    // Only update URL if there are actual changes
    if (hasChanges) {
      setSearchParams(params, { replace: true });
    }
  }, [filters, currentPage, recordsPerPage]);

  // Single effect for data fetching
  useEffect(() => {
    const fetchData = async () => {
      await fetchMaintenanceRecords();
      loadStats();
    };
    fetchData();
  }, [location.search]); // Only fetch when URL changes

  // Load data on component mount and when filters or pagination changes
  useEffect(() => {
    console.log('MaintenanceTable useEffect triggered - loading records with pagination:', { 
      page: currentPage, 
      limit: recordsPerPage,
      filters
    });
    fetchMaintenanceRecords();
  }, [currentPage, recordsPerPage, 
      filters.printer_id, 
      filters.vendor_name, 
      filters.department, 
      filters.search,
      filters.start_date,
      filters.end_date
  ]);

  // Add a separate effect to calculate stats whenever records change
  useEffect(() => {
    console.log('Records changed, recalculating stats. Records count:', records.length);
    loadStats();
  }, [records]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setActiveDropdown(null);
    };
    
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // Toggle dropdown for a specific record
  const toggleDropdown = (e: React.MouseEvent, recordId: string) => {
    e.stopPropagation(); // Prevent event from bubbling up
    setActiveDropdown(activeDropdown === recordId ? null : recordId);
  };

  // Handle viewing a record
  const handleViewRecord = (record: PrinterMaintenanceRecord) => {
    setViewingRecord(record);
    if (onViewRecord) {
      onViewRecord(record);
    }
  };

  // Handle editing a record
  const handleEditRecord = async (record: PrinterMaintenanceRecord) => {
    if (onEditRecord) {
      onEditRecord(record);
      return;
    }
    
    try {
      console.log('Starting edit for record ID:', record.id);
      
      // Format the parts_replaced field to work with the form
      let formattedPartsReplaced = {
        toner: false,
        drum: false,
        fuser_unit: false,
        pickup_roller: false
      };
      
      // Process the parts_replaced field if it exists
      if (record.parts_replaced) {
        try {
          let partsData;
          
          // Handle string format
          if (typeof record.parts_replaced === 'string') {
            try {
              partsData = JSON.parse(record.parts_replaced);
            } catch (e) {
              console.log('Failed to parse parts_replaced string:', record.parts_replaced);
              partsData = null;
            }
          } else {
            partsData = record.parts_replaced;
          }
          
          // Process into checkbox format the form expects
          if (partsData) {
            if (Array.isArray(partsData)) {
              // Format: [{partName: "Toner", quantity: 1}, ...]
              partsData.forEach(part => {
                const partName = (part.partName || part.name || '').toLowerCase().replace(/\s+/g, '_');
                if (partName === 'toner') formattedPartsReplaced.toner = true;
                if (partName === 'drum') formattedPartsReplaced.drum = true;
                if (partName === 'fuser_unit' || partName === 'fuser unit') formattedPartsReplaced.fuser_unit = true;
                if (partName === 'pickup_roller' || partName === 'pickup roller') formattedPartsReplaced.pickup_roller = true;
              });
            } else if (typeof partsData === 'object') {
              // Format: {toner: true, drum: false, ...}
              if ('toner' in partsData) formattedPartsReplaced.toner = Boolean(partsData.toner);
              if ('drum' in partsData) formattedPartsReplaced.drum = Boolean(partsData.drum);
              if ('fuser_unit' in partsData) formattedPartsReplaced.fuser_unit = Boolean(partsData.fuser_unit);
              if ('pickup_roller' in partsData) formattedPartsReplaced.pickup_roller = Boolean(partsData.pickup_roller);
            }
          }
        } catch (error) {
          console.error('Error formatting parts_replaced:', error);
        }
      }
      
      console.log('Formatted parts_replaced:', formattedPartsReplaced);
      
      // Create a copy of the record to ensure all properties are properly formatted
      const preparedRecord = {
        ...record,
        // Ensure date fields are properly formatted as strings (not undefined)
        visit_date: record.visit_date ? new Date(record.visit_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        service_date: record.service_date ? new Date(record.service_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        // Ensure invoice amount is properly formatted
        invoice_amount: record.invoice_amount ? String(record.invoice_amount) : 
                        record.cost ? String(record.cost) : '',
        // Convert the boolean object to an array of objects that matches the expected type
        parts_replaced: Object.entries(formattedPartsReplaced)
          .filter(([_, isReplaced]) => isReplaced)
          .map(([part]) => ({
            partName: part.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
            quantity: 1
          }))
      };
      
      // Set the record for editing directly without localStorage
      setEditingRecord(preparedRecord);
      console.log('Record set for editing:', preparedRecord);
    } catch (error) {
      console.error('Error preparing record for editing:', error);
      toast.error('Could not prepare record for editing');
    }
  };

  // Handle cancelling edit
  const handleCancelEdit = () => {
    setEditingRecord(null);
  };

  // Handle edit completion
  const handleEditComplete = () => {
    setEditingRecord(null);
    fetchMaintenanceRecords(); // Refresh records after edit
  };

  // Handle deleting a record
  const handleDeleteRecord = async (record: PrinterMaintenanceRecord) => {
    if (onDeleteRecord) {
      onDeleteRecord(record);
      return;
    }
    
    // Default delete behavior
    try {
      const confirmMessage = `Are you sure you want to delete the maintenance record for ${
        record.manufacturer 
          ? `${record.manufacturer} ${record.model || ''}` 
          : record.printer_name 
            ? record.printer_name 
            : `Asset #${record.asset_id || record.printer_id || 'Unknown'}`
      }?`;
      
      const confirmed = window.confirm(confirmMessage);
      
      if (!confirmed) return;
      
      setLoading(true);
      
      // First check which ID format to use (numeric or string)
      const recordId = record.id;
      console.log('Attempting to delete record with ID:', recordId, 'of type:', typeof recordId);
      
      // Add a delay to ensure UI updates before the API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      let deleted = false;
      let errorMessage = '';
      
      // Try multiple deletion approaches with better error handling
      try {
        console.log('Trying standard endpoint deletion...');
        const response = await api.delete(`/printer-maintenance/${recordId}`);
        console.log('Standard endpoint response:', response);
        deleted = true;
      } catch (firstError: any) {
        errorMessage = firstError?.response?.data?.message || firstError?.message || 'Standard delete failed';
        console.error('Standard endpoint deletion failed:', firstError);
        console.error('Error details:', {
          status: firstError?.response?.status,
          data: firstError?.response?.data
        });
        
        try {
          console.log('Trying record endpoint deletion...');
          const response = await api.delete(`/printer-maintenance/record/${recordId}`);
          console.log('Record endpoint response:', response);
          deleted = true;
        } catch (secondError: any) {
          errorMessage += ' | ' + (secondError?.response?.data?.message || secondError?.message || 'Record delete failed');
          console.error('Record endpoint deletion failed:', secondError);
          console.error('Error details:', {
            status: secondError?.response?.status,
            data: secondError?.response?.data
          });
          
          try {
            console.log('Trying direct fetch DELETE as last resort...');
            const token = localStorage.getItem('authToken') || '';
            const response = await fetch(`/api/printer-maintenance/${recordId}`, {
              method: 'DELETE',
              headers: {
                'Authorization': token ? `Bearer ${token}` : '',
                'Content-Type': 'application/json'
              }
            });
            
            console.log('Fetch DELETE response status:', response.status);
            
            if (!response.ok) {
              errorMessage += ' | Fetch delete failed with status ' + response.status;
              try {
                const errorData = await response.json();
                console.error('Fetch delete error details:', errorData);
              } catch (e) {
                console.error('Could not parse error response');
              }
              throw new Error(`Delete failed with status ${response.status}`);
            }
            
            console.log('Record deleted successfully with fetch DELETE');
            deleted = true;
          } catch (thirdError: any) {
            errorMessage += ' | ' + (thirdError?.message || 'Fetch delete failed');
            console.error('All delete attempts failed:', thirdError);
            throw thirdError;
          }
        }
      }
      
      if (deleted) {
        toast.success('Maintenance record deleted successfully');
      // Refresh the records
      fetchMaintenanceRecords();
      } else {
        throw new Error('All delete attempts failed: ' + errorMessage);
      }
    } catch (error: any) {
      console.error('Error deleting maintenance record:', error);
      const errorMsg = error?.response?.data?.message || error?.message || 'Failed to delete maintenance record. Please try again.';
      toast.error(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  // Load stats for dashboard
  const loadStats = async () => {
    console.log('loadStats called with records:', records.length);
    
    try {
      // Initialize default stats
      const defaultStats = {
        totalPrinters: 0,
        assignedPrinters: 0,
        totalCost: 0,
        maintenanceCount: 0,
        requireAttention: 0,
        upcoming: 0,
        warrantyExpiring: 0,
        tonerCost: 0,
        hardwareRepairCost: 0,
        regularServiceCost: 0,
        drumCost: 0,
        fuserUnitCost: 0,
        pickupRollerCost: 0
      };

      // If no records, set default stats
      if (!records || records.length === 0) {
        console.log('No records available, setting default stats');
        setStats(defaultStats);
        return;
      }

      // Count records with attention required
      const requireAttention = records.filter(record => 
        record.status?.toLowerCase().includes('pending') || 
        record.status?.toLowerCase().includes('scheduled') ||
        record.status?.toLowerCase().includes('in progress')
      ).length;
      
      // Count upcoming maintenance records (scheduled in the future)
      const upcoming = records.filter(record => {
        if (!record.visit_date && !record.service_date) return false;
        const serviceDate = new Date(record.visit_date || record.service_date || '');
        const now = new Date();
        return serviceDate > now && !record.status?.toLowerCase().includes('completed');
      }).length;

      // Calculate service type costs from records
      const tonerCost = records
        .filter(record => {
          if (typeof record.service_type === 'string' && record.service_type.toLowerCase().includes('toner')) {
            return true;
          }
          if (typeof record.parts_replaced === 'string') {
            try {
              const parts = JSON.parse(record.parts_replaced);
              return parts.some((part: any) => 
                (part.partName || part.name || '').toLowerCase().includes('toner')
              );
            } catch {
              return false;
            }
          }
          return false;
        })
        .reduce((sum: number, record) => {
          let cost = 0;
          if (record.invoice_amount) {
            const parsed = parseFloat(String(record.invoice_amount));
            if (!isNaN(parsed)) cost = parsed;
          } else if (record.cost) {
            const parsed = parseFloat(String(record.cost));
            if (!isNaN(parsed)) cost = parsed;
          }
          return sum + cost;
        }, 0);
      
      // Calculate hardware repair costs
      const hardwareRepairCost = records
        .filter(record => {
          if (typeof record.service_type === 'string' && (
            record.service_type.toLowerCase().includes('repair') || 
            record.service_type.toLowerCase().includes('fuser') ||
            record.service_type.toLowerCase().includes('drum') ||
            record.service_type.toLowerCase().includes('roller')
          )) {
            return true;
          }
          return false;
        })
        .reduce((sum: number, record) => {
          let cost = 0;
          if (record.invoice_amount) {
            const parsed = parseFloat(String(record.invoice_amount));
            if (!isNaN(parsed)) cost = parsed;
          } else if (record.cost) {
            const parsed = parseFloat(String(record.cost));
            if (!isNaN(parsed)) cost = parsed;
          }
          return sum + cost;
        }, 0);
      
      // Calculate regular service costs
      const regularServiceCost = records
        .filter(record => {
          if (typeof record.service_type === 'string' && (
            record.service_type.toLowerCase().includes('maintenance') || 
            record.service_type.toLowerCase().includes('routine') ||
            record.service_type.toLowerCase().includes('visit') ||
            record.service_type.toLowerCase().includes('preventive')
          )) {
            return true;
          }
          return false;
        })
        .reduce((sum: number, record) => {
          let cost = 0;
          if (record.invoice_amount) {
            const parsed = parseFloat(String(record.invoice_amount));
            if (!isNaN(parsed)) cost = parsed;
          } else if (record.cost) {
            const parsed = parseFloat(String(record.cost));
            if (!isNaN(parsed)) cost = parsed;
          }
          return sum + cost;
        }, 0);
      
      // Get actual total cost from all records - ensure numeric values
      const actualTotalCost = records.reduce((sum: number, record) => {
        let cost = 0;
        if (record.invoice_amount) {
          const parsed = parseFloat(String(record.invoice_amount));
          if (!isNaN(parsed)) cost = parsed;
        } else if (record.cost) {
          const parsed = parseFloat(String(record.cost));
          if (!isNaN(parsed)) cost = parsed;
        }
        console.log(`Record ID: ${record.id}, Invoice Amount: ${record.invoice_amount}, Cost: ${record.cost}, Parsed Cost: ${cost}`);
        return sum + cost;
      }, 0);
      
      console.log('Total cost calculated:', actualTotalCost);
      
      // Ensure the actualTotalCost is a valid number
      const validTotalCost = isNaN(actualTotalCost) ? 0 : actualTotalCost;
      
      // Set stats with calculations from records
      const newStats = {
        totalPrinters: records.length,
        assignedPrinters: records.length,
        totalCost: validTotalCost,
        maintenanceCount: records.length,
        requireAttention,
        upcoming,
        warrantyExpiring: 0,
        tonerCost,
        hardwareRepairCost,
        regularServiceCost,
        drumCost: tonerCost, // Simplified for now
        fuserUnitCost: 0,
        pickupRollerCost: 0
      };

      console.log('Setting new stats:', newStats);
      setStats(newStats);
    } catch (error) {
      console.error('Error calculating stats:', error);
      
      // Fallback if calculation fails - ensure safe value for totalCost
      setStats({
        totalPrinters: records.length,
        assignedPrinters: records.length,
        totalCost: 0,
        maintenanceCount: records.length,
        requireAttention: 0,
        upcoming: 0,
        warrantyExpiring: 0,
        tonerCost: 0,
        hardwareRepairCost: 0,
        regularServiceCost: 0,
        drumCost: 0,
        fuserUnitCost: 0,
        pickupRollerCost: 0
      });
    }
  };

  // Fetch records with filters applied
  const fetchMaintenanceRecords = async () => {
    try {
      setLoading(true);
      setError(null);
      setApiResponse(null);
      
      console.log('Fetching printer maintenance records with filters:', filters);
      console.log('Pagination parameters:', { page: currentPage, limit: recordsPerPage });

      // First, fetch all printers to have their information available
      const printersResponse = await api.get('/assets');
      console.log('Printers response:', printersResponse.data);
      const printersMap = new Map();
      
      if (Array.isArray(printersResponse.data)) {
        printersResponse.data.forEach((printer: any) => {
          printersMap.set(printer.id, {
            manufacturer: printer.manufacturer || 'HP',
            model: printer.model || printer.assetModel,
            department: printer.department || 'IT'
          });
        });
      }

      // Build query parameters from filters
      const queryParams = new URLSearchParams();
      
      // Add pagination parameters
      queryParams.append('page', currentPage.toString());
      queryParams.append('limit', recordsPerPage.toString());
      
      // Add other filters - only add if they have actual values
      if (filters.start_date) queryParams.append('start_date', filters.start_date);
      if (filters.end_date) queryParams.append('end_date', filters.end_date);
      if (filters.printer_id !== undefined && filters.printer_id !== null) {
        queryParams.append('printer_id', String(filters.printer_id));
      }
      if (filters.vendor_name && filters.vendor_name.trim() !== '') {
        queryParams.append('vendor_name', filters.vendor_name.trim());
      }
      if (filters.department && filters.department.trim() !== '') {
        queryParams.append('department', filters.department.trim());
      }
      if (filters.search && filters.search.trim() !== '') {
        queryParams.append('search', filters.search.trim());
      }

      console.log('Final query parameters:', queryParams.toString());

      // Fetch maintenance records
      const response = await api.get(`/printer-maintenance?${queryParams.toString()}`);
      console.log('API Response:', response.data);
      
      let recordsData: PrinterMaintenanceRecord[] = [];
      let totalCount = 0;

      if (response.data && response.data.records && Array.isArray(response.data.records)) {
        recordsData = response.data.records;
        totalCount = response.data.totalCount || response.data.total || response.data.count || recordsData.length;
      } else if (Array.isArray(response.data)) {
        recordsData = response.data;
        totalCount = recordsData.length;
      }

      // Merge printer information with maintenance records
      recordsData = recordsData.map(record => {
        const printerId = record.printer_id || record.asset_id;
        const printerInfo = printersMap.get(printerId);
        
        if (printerInfo) {
          return {
            ...record,
            manufacturer: printerInfo.manufacturer,
            model: printerInfo.model || record.model,
            department: printerInfo.department || record.department
          };
        }
        
        return record;
      });

      console.log('Processed records with printer information:', recordsData);
      
      setRecords(recordsData);
      setTotalRecords(totalCount);

      if (recordsData.length > 0 && vendors.length === 0) {
        loadFilterOptions();
      }

      loadStats();
    } catch (error) {
      console.error('Error fetching maintenance records:', error);
      setError('Failed to load maintenance records. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Load options for filter dropdowns
  const loadFilterOptions = async () => {
    try {
      // Fetch printers
      const printersRes = await api.get('/assets');
      console.log('Raw printers data:', printersRes.data);
      
      const printersList = Array.isArray(printersRes.data) 
        ? printersRes.data
            .filter((asset: any) => 
              asset.type?.toLowerCase().includes('print') ||
              asset.category?.toLowerCase().includes('print') ||
              asset.name?.toLowerCase().includes('print')
            )
            .map((printer: any) => ({
              id: Number(printer.id),
              name: `${printer.manufacturer || ''} ${printer.model || ''}`
            }))
        : [];
      console.log('Processed printers list:', printersList);
      setPrinters(printersList);
      
      // Fetch unique vendors from maintenance records
      const maintenanceRes = await api.get('/printer-maintenance');
      console.log('Raw maintenance data:', maintenanceRes.data);
      
      // Extract maintenance records from the response
      let maintenanceRecords = [];
      if (maintenanceRes.data && maintenanceRes.data.records && Array.isArray(maintenanceRes.data.records)) {
        maintenanceRecords = maintenanceRes.data.records;
      } else if (Array.isArray(maintenanceRes.data)) {
        maintenanceRecords = maintenanceRes.data;
      }
      
      if (maintenanceRecords.length > 0) {
        // Extract unique departments
        const uniqueDepartments = [...new Set(
          maintenanceRecords
            .map((record: any) => record.department)
            .filter(Boolean)
        )] as string[];
        console.log('Extracted unique departments:', uniqueDepartments);
        setDepartments(uniqueDepartments);
      }
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  };

  // Handle filter changes
  const handleFilterChange = (field: keyof MaintenanceTableFilters, value: any) => {
    console.log(`Filter changed: ${field} = ${value}`);
    
    setFilters(prev => {
      const newFilters = {
        ...prev,
        [field]: value
      };
      console.log('New filters state:', newFilters);
      return newFilters;
    });
    
    // Reset to first page when filters change
    setCurrentPage(1);
    
    // Automatically fetch records when filter changes
    setTimeout(() => {
      fetchMaintenanceRecords();
    }, 0);
  };

  // Add useEffect to watch for filter changes
  useEffect(() => {
    console.log('Filters or pagination changed, fetching records...');
    fetchMaintenanceRecords();
  }, [
    filters.search,
    filters.vendor_name,
    filters.printer_id,
    filters.department,
    filters.start_date,
    filters.end_date,
    currentPage,
    recordsPerPage
  ]);

  // Apply filters button handler
  const applyFilters = () => {
    console.log('Applying filters:', filters);
    console.log('Current printers:', printers);
    console.log('Current departments:', departments);
    fetchMaintenanceRecords();
  };

  // Reset filters
  const resetFilters = () => {
    console.log('Resetting all filters');
    
    setFilters({
      start_date: '',
      end_date: '',
      printer_id: undefined,
      vendor_name: '',
      department: '',
      search: '',
    });
    
    setCurrentPage(1);
    setRecordsPerPage(10);
    setSearchParams(new URLSearchParams());
  };

  // Pagination handlers
  const goToPage = (page: number) => {
    if (page < 1 || page > Math.ceil(totalRecords / recordsPerPage)) return;
    setCurrentPage(page);
  };

  const handleRecordsPerPageChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newValue = parseInt(event.target.value);
    if (isNaN(newValue)) return;
    
    console.log(`Changing records per page from ${recordsPerPage} to ${newValue}`);
    setRecordsPerPage(newValue);
    setCurrentPage(1);
  };

  // Extract assignee name from department field if present
  const extractAssigneeInfo = (department: string | null | undefined) => {
    if (!department) return { department: null, assignee: null };
    
    // Check if department contains "Assignee:" pattern
    const assigneePattern = department.match(/(HR|IT) Assignee: (.*?)($|\n|Original filename:)/);
    if (assigneePattern) {
      const actualDepartment = assigneePattern[1];
      const assigneeName = assigneePattern[2].trim();
      
      return {
        department: actualDepartment || 'IT Department',
        assignee: assigneeName
      };
    }
    
    // If there's an "Original filename:" pattern but no assignee pattern, remove it
    if (department.includes('Original filename:')) {
      const deptOnly = department.split('Original filename:')[0].trim();
      return { department: deptOnly, assignee: null };
    }
    
    return { department, assignee: null };
  };

  // Format parts replaced array for display
  const formatPartsReplaced = (parts: any) => {
    // Log what we're receiving to debug
    console.log('Parts replaced data:', JSON.stringify(parts));
    
    // If null, undefined, empty string or empty array
    if (!parts || 
        (Array.isArray(parts) && parts.length === 0) || 
        (typeof parts === 'string' && parts.trim() === '') ||
        (typeof parts === 'object' && Object.keys(parts).length === 0)) {
      return <span className="text-gray-400">-</span>;
    }
    
    // Handle string input (might be JSON string)
    if (typeof parts === 'string') {
      try {
        // Try to parse as JSON
        const parsedParts = JSON.parse(parts);
        // Call ourselves recursively with the parsed data
        return formatPartsReplaced(parsedParts);
      } catch (e) {
        // If not valid JSON, just return the string as long as it's not "Unknown part"
        if (parts.trim() === "Unknown part") {
          return <span className="text-gray-400">-</span>;
        }
        return <span>{parts}</span>;
      }
    }
    
    // If it's an array
    if (Array.isArray(parts)) {
      // Filter out "Unknown part" entries and check if all remaining parts are empty
      const filteredParts = parts.filter(part => 
        !(typeof part === 'string' && part.trim() === "Unknown part") &&
        !(typeof part === 'object' && part !== null && part.partName === "Unknown part")
      );
      
      if (filteredParts.length === 0) {
        return <span className="text-gray-400">-</span>;
      }
      
      const allEmpty = filteredParts.every(part => 
        (typeof part === 'object' && (!part.partName || part.partName.trim() === '')) ||
        (typeof part === 'string' && part.trim() === '')
      );
      
      if (allEmpty) {
        return <span className="text-gray-400">-</span>;
      }
      
      return (
        <ul className="list-disc pl-4 my-1">
          {filteredParts.map((part: any, index: number) => {
            // If it's just a string
            if (typeof part === 'string' && part.trim() !== '') {
              return (
                <li key={index} className="text-sm">
                  <span className="text-blue-800 font-medium">{part.trim()}</span>
                </li>
              );
            }
            
            // If it's an object with partName property
            if (typeof part === 'object' && part !== null && 'partName' in part && part.partName && part.partName !== "Unknown part") {
              return (
                <li key={index} className="text-sm">
                  <span className="text-blue-800 font-medium">
                    {part.partName.trim()}
                    {part.quantity > 1 ? ` (${part.quantity})` : ''}
                  </span>
                </li>
              );
            }
            
            // If it's an object with name property
            if (typeof part === 'object' && part !== null && 'name' in part && part.name && part.name !== "Unknown part") {
              return (
                <li key={index} className="text-sm">
                  <span className="text-blue-800 font-medium">
                    {part.name.trim()}
                    {part.quantity > 1 ? ` (${part.quantity})` : ''}
                  </span>
                </li>
              );
            }
            
            return null;
          }).filter(Boolean)} {/* Filter out null items */}
        </ul>
      );
    }
    
    // If it's a single object (not in an array)
    if (typeof parts === 'object' && parts !== null) {
      // Convert to array if it's a single object with partName
      if ('partName' in parts || 'name' in parts) {
        return formatPartsReplaced([parts]);
      }
      
      // If it has numbered keys like {0: {...}, 1: {...}}
      const numericalKeys = Object.keys(parts).filter(key => !isNaN(Number(key)));
      if (numericalKeys.length > 0) {
        const partsArray = numericalKeys.map(key => parts[key]);
        return formatPartsReplaced(partsArray);
      }
      
      // If all else fails, try to display the object keys and values
      return (
        <ul className="list-disc pl-4 my-1">
          {Object.entries(parts).map(([key, value]: [string, any], index: number) => {
            if (value && typeof value === 'object') {
              return null; // Skip nested objects
            }
            
            if (value && String(value).trim() !== '') {
              return (
                <li key={index} className="text-sm">
                  <span className="text-blue-800 font-medium">
                    {key}: {String(value)}
                  </span>
                </li>
              );
            }
            
            return null;
          }).filter(Boolean)}
        </ul>
      );
    }
    
    // Fallback
    return <span className="text-gray-400">-</span>;
  };

  // Format status field with appropriate styling
  const formatStatus = (status: string | null | undefined) => {
    if (!status) return <span className="text-gray-400">Not specified</span>;
    
    const statusText = status.toLowerCase();
    let statusClass = "px-2 py-1 text-xs font-medium rounded-full whitespace-nowrap ";
    let displayText = status;
    
    if (statusText === 'completed') {
      statusClass += "bg-green-100 text-green-800";
      displayText = "Completed";
    } else if (statusText.includes('pending') || statusText === 'in progress') {
      statusClass += "bg-yellow-100 text-yellow-800";
      displayText = "In Progress";
    } else if (statusText.includes('scheduled')) {
      statusClass += "bg-blue-100 text-blue-800";
      displayText = "Scheduled";
    } else if (statusText.includes('cancel')) {
      statusClass += "bg-red-100 text-red-800";
      displayText = "Cancelled";
    } else {
      statusClass += "bg-gray-100 text-gray-800";
      displayText = status.charAt(0).toUpperCase() + status.slice(1);
    }
    
    return <span className={statusClass}>{displayText}</span>;
  };

  // Safely format date to prevent "Invalid Date" display
  const formatDate = (dateString: string | Date | null | undefined) => {
    if (!dateString) return <span className="text-gray-400">Not specified</span>;
    
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return <span className="text-gray-400">Invalid date</span>;
      }
      return date.toLocaleDateString();
    } catch (error) {
      return <span className="text-gray-400">Invalid date</span>;
    }
  };

  // Format datetime with time for created_at fields
  const formatDateTime = (dateString: string | Date | null | undefined) => {
    if (!dateString) return <span className="text-gray-400">Not specified</span>;
    
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return <span className="text-gray-400">Invalid date</span>;
      }
      return date.toLocaleString();
    } catch (error) {
      return <span className="text-gray-400">Invalid date</span>;
    }
  };

  // Format text fields to handle null/undefined/empty values
  const formatField = (value: string | null | undefined, fallback: string = '-') => {
    if (!value || value.trim() === '') {
      return <span className="text-gray-400">{fallback}</span>;
    }
    return value;
  };

  // Handle downloading an invoice
  const handleDownloadInvoice = async (record: PrinterMaintenanceRecord) => {
    try {
      if (!record.invoice_file && !record.invoiceFilePath) {
        toast.error('No invoice file available');
        return;
      }

      // Validate record ID
      if (!record.id) {
        console.error('Missing record ID for download', record);
        toast.error('Cannot download: Missing record ID');
        return;
      }

      // Show loading toast
      const loadingToast = toast.loading('Downloading file...');

      // Try to get the auth token
      const token = localStorage.getItem('authToken');
      
      // Construct the URL with optional token as query parameter for auth
      let downloadUrl = `/api/printer-maintenance/${record.id}/download`;
      if (token) {
        // Add token as query parameter for better compatibility
        downloadUrl += `?token=${encodeURIComponent(token)}`;
      }
      
      console.log('Download URL:', downloadUrl);

      // For direct download, just use window.location
      // This works better than window.open as it doesn't trigger popup blockers
      // and preserves the auth token from the current session
      window.location.href = downloadUrl;
      
      // Dismiss loading toast after a short delay
      setTimeout(() => {
        toast.dismiss(loadingToast);
        toast.success('Download started');
      }, 1000);
    } catch (error: any) {
      console.error('Download error:', error);
      toast.error(error.message || 'Failed to download file. Please try again.');
    }
  };

  // Format currency for display
  const formatCurrency = (amount: number | string | undefined) => {
    if (amount === undefined) return 'Rs 0';
    // Convert amount to number if it's a string
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) || 0 : amount;
    return `Rs ${numericAmount.toLocaleString('en-PK')}`;
  };

  // Handle export functionality
  const handleExport = async (format: string, scope: 'filtered' | 'all') => {
    let loadingToast = '';
    
    try {
      setShowExportModal(false);
      
      // Show loading toast
      loadingToast = toast.loading(`Generating ${format.toUpperCase()} report...`);
      
      // Prepare the data to be exported
      const exportData = {
        format,
        scope,
        filters: scope === 'filtered' ? {
          start_date: filters.start_date || undefined,
          end_date: filters.end_date || undefined,
          printer_id: filters.printer_id,
          vendor_name: filters.vendor_name || undefined,
          department: filters.department || undefined,
          search: filters.search || undefined
        } : undefined,
        records: scope === 'filtered' ? records : undefined
      };

      // For PDF format, use window.open approach
      if (format === 'pdf') {
        try {
          // Instead of using window.open, directly use api to get the PDF blob
          const response = await api({
            url: '/printer-maintenance/export',
            method: 'POST',
            data: exportData,
            responseType: 'blob',
            headers: {
              'Accept': 'application/pdf',
              'Content-Type': 'application/json'
            }
          });

          // Get the blob from response
          const blob = response.data;
          
          // Verify the blob is not empty
          if (!blob || blob.size === 0) {
            throw new Error('Generated PDF is empty');
          }

          // Create object URL for the PDF
          const pdfUrl = window.URL.createObjectURL(blob);
          
          // Dismiss loading toast
          toast.dismiss(loadingToast);

          // Open PDF in new window using object URL
          const newWindow = window.open(pdfUrl, '_blank');
          
          if (!newWindow) {
            // If popup blocked, try downloading instead
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = pdfUrl;
            a.download = `maintenance-report.pdf`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            
            toast.success('PDF report downloaded');
          } else {
            toast.success('PDF report opened in new tab');
          }
          
          // Clean up the object URL after a delay
          setTimeout(() => {
            window.URL.revokeObjectURL(pdfUrl);
          }, 5000);

          return;
        } catch (pdfError) {
          console.error('PDF export error:', pdfError);
          throw pdfError;
        }
      }

      // For non-PDF formats (xlsx, csv), use the blob download approach
      try {
        const response = await api({
          url: '/printer-maintenance/export',
          method: 'POST',
          data: exportData,
          responseType: 'blob',
          headers: {
            'Accept': format === 'xlsx' ? 
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' : 
              'text/csv'
          },
          // Ignore certain network errors that occur after download starts
          validateStatus: (status) => {
            return status === 200 || status === 0;
          }
        });

        // Get the blob from response
        const blob = response.data;
        
        // Verify the blob is not empty
        if (!blob || blob.size === 0) {
          throw new Error('Generated file is empty');
        }

        // Get filename from content disposition or use default
        let filename = `maintenance-export-${format}.${format}`;
        const disposition = response.headers['content-disposition'];
        if (disposition) {
          const matches = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(disposition);
          if (matches?.[1]) {
            filename = matches[1].replace(/['"]/g, '');
          }
        }
        
        // Create download link and trigger download
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        
        // Dismiss loading toast before starting download
        toast.dismiss(loadingToast);
        
        // Trigger download
        a.click();
        
        // Cleanup
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        // Show success message
        toast.success(`${format.toUpperCase()} report downloaded successfully`);
      } catch (exportError: unknown) {
        // Check if the download might have started despite the error
        if (exportError && typeof exportError === 'object' && 'response' in exportError) {
          const response = (exportError as any).response;
          
          // If we got a blob response, the download probably worked despite the error
          if (response?.data instanceof Blob && response.data.size > 0) {
            // Don't show an error message since the download likely succeeded
            return;
          }

          // Try to parse error message from blob if present
          if (response?.data instanceof Blob) {
            try {
              const errorText = await response.data.text();
              const errorJson = JSON.parse(errorText);
              throw new Error(errorJson.message || 'Export failed');
            } catch (e) {
              // If we can't parse the error, check if it's a network error after download started
              if (exportError instanceof Error && 
                  exportError.message.includes('Network Error') && 
                  response?.status === 0) {
                // Ignore network errors that happen after download starts
                return;
              }
              throw new Error('Failed to generate report. Please try again.');
            }
          }
        }
        throw new Error('Failed to generate report. Please try again.');
      }
    } catch (error: unknown) {
      // Ensure loading toast is dismissed in case of error
      toast.dismiss();
      
      // Don't show error for network errors that happen after download starts
      if (error instanceof Error && 
          error.message.includes('Network Error') && 
          document.querySelector('a[download]')) {
        return;
      }
      
      // Type-safe error logging
      if (error instanceof Error) {
        console.error('Export error:', error.message);
      } else {
        console.error('Export error:', String(error));
      }
      
      let errorMessage = 'Failed to export data. Please try again.';
      
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null && 'message' in error) {
        errorMessage = String(error.message);
      }
      
      toast.error(errorMessage);
    }
  };

  return (
    <div className={`${noPadding ? 'pt-2' : 'p-4'}`}>
      {/* Stats cards in grid layout */}
      {!hideStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-5 mb-5">
          {/* RECORDS card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-5 h-full min-h-[170px] flex flex-col">
            <h3 className="text-sm font-semibold text-gray-700 uppercase mb-2">RECORDS</h3>
            <div className="mb-1">
              <span className="text-4xl font-bold text-gray-800">{stats.maintenanceCount}</span>
            </div>
            <p className="text-sm text-gray-500 mt-1">Total printer maintenance records</p>
            <div className="mt-auto pt-2 border-t border-gray-100">
              <p className="text-xs text-blue-600 flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-1.5"></span>
                {stats.requireAttention} records require attention
              </p>
            </div>
          </div>
          
          {/* COST SUMMARY card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-5 h-full min-h-[170px] flex flex-col">
            <h3 className="text-sm font-semibold text-gray-700 uppercase mb-2">COST SUMMARY</h3>
            <div className="mb-1">
              <span className="text-4xl font-bold text-green-600">Rs {typeof stats.totalCost === 'number' ? stats.totalCost.toLocaleString() : '0'}</span>
            </div>
            <p className="text-sm text-gray-500 mt-1">Total maintenance expense</p>
            
            <div className="mt-2 pt-1 flex items-center">
              <span className="text-blue-600 font-semibold text-lg">Rs {typeof stats.totalCost === 'number' && stats.maintenanceCount ? Math.round(stats.totalCost / stats.maintenanceCount).toLocaleString() : '0'}</span>
              <span className="text-sm text-gray-500 ml-2">Avg. per service</span>
            </div>
            
            <div className="pt-1 mt-1">
              <div className="flex justify-between text-xs text-gray-500 mb-1">
                <span>Budget Utilized</span>
                <span>{typeof stats.totalCost === 'number' ? Math.round((stats.totalCost / 250000) * 100) : 0}% of Rs 250,000</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="h-2 rounded-full bg-green-500"
                  style={{ width: `${typeof stats.totalCost === 'number' ? Math.min(100, Math.round((stats.totalCost / 250000) * 100)) : 0}%` }}
                ></div>
              </div>
            </div>
            <div className="mt-auto pt-2 border-t border-gray-100">
              <p className="text-xs text-blue-600 flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-1.5"></span>
                Budget: Rs 250,000
              </p>
            </div>
          </div>

          {/* TOP PRINTER MODELS card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-5 h-full min-h-[170px] flex flex-col">
            <h3 className="text-sm font-semibold text-gray-700 uppercase mb-2">TOP PRINTER MODELS</h3>
            <div className="space-y-2 flex-grow">
              {/* Calculate most expensive models */}
              {(() => {
                // Group records by printer model and calculate costs
                const modelCosts: Record<string, {cost: number, count: number}> = {};
                
                records.forEach(record => {
                  let printerName = "";
                  
                  // Only use records with actual manufacturer/model or printer_name
                  if (record.manufacturer && record.model) {
                    printerName = `${record.manufacturer} ${record.model}`;
                  } else if (record.printer_name && record.printer_name.trim() !== '') {
                    printerName = record.printer_name.trim();
                  } else if (record.asset_name && record.asset_name.trim() !== '') {
                    printerName = record.asset_name.trim();
                  } else {
                    // Skip records without proper name/model
                    return;
                  }
                  
                  const cost = parseFloat(String(record.invoice_amount || record.cost || 0)) || 0;
                  
                  if (!modelCosts[printerName]) {
                    modelCosts[printerName] = {cost: 0, count: 0};
                  }
                  modelCosts[printerName].cost += cost;
                  modelCosts[printerName].count += 1;
                });
                
                // Sort models by cost and get top 5
                const topModels = Object.entries(modelCosts)
                  .sort(([, a], [, b]) => b.cost - a.cost)
                  .slice(0, 5);
                
                // If no records or costs, show placeholder
                if (topModels.length === 0) {
                  return (
                    <div className="text-center py-3 text-gray-500 text-sm">
                      No cost data available
                    </div>
                  );
                }
                
                // Find the maximum cost for scaling
                const maxCost = Math.max(...topModels.map(([, {cost}]) => cost));
                
                return topModels.map(([model, {cost, count}], index) => (
                  <div key={index} className="flex items-center">
                    <div className="flex-grow">
                      <div className="flex justify-between mb-1">
                        <div className="mr-2">
                          <div className="text-xs font-medium text-gray-700" title={model}>
                            {model}
                          </div>
                          <div className="text-xs text-gray-500">
                            {count} record{count !== 1 ? 's' : ''}
                          </div>
                        </div>
                        <div className="text-right">
                          <span className="text-xs font-medium text-green-600">
                            Rs {cost.toLocaleString()}
                          </span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-100 rounded-full h-1.5">
                        <div 
                          className="h-1.5 rounded-full bg-indigo-500"
                          style={{ width: `${Math.round((cost / maxCost) * 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ));
              })()}
            </div>
            <div className="mt-auto pt-2 border-t border-gray-100">
              <p className="text-xs text-indigo-600 flex items-center">
                <span className="w-2 h-2 bg-indigo-500 rounded-full mr-1.5"></span>
                Shows maintenance cost by printer model
              </p>
            </div>
          </div>
          
          {/* VENDORS card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-5 h-full min-h-[170px] flex flex-col">
            <h3 className="text-sm font-semibold text-gray-700 uppercase mb-2">VENDORS</h3>
            <div className="mb-1">
              <span className="text-4xl font-bold text-purple-600">{vendors.length || 1}</span>
            </div>
            <p className="text-sm text-gray-500 mt-1">Service provider partners</p>
            <div className="mt-auto pt-2 border-t border-gray-100">
              <p className="text-xs text-purple-600 flex items-center">
                <span className="w-2 h-2 bg-purple-500 rounded-full mr-1.5"></span>
                Primary: {vendors.length > 0 ? vendors[0] : 'Abdullah Printers'}
              </p>
            </div>
          </div>
        </div>
      )}
      
      {/* Debug Info Panel - only shown when debug mode is active */}
      {debugMode && (
        <div className="bg-gray-100 border border-gray-300 rounded-md p-4 mb-4 text-sm">
          <h3 className="font-bold mb-2 text-gray-800">Debug Information</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p><strong>Current Page:</strong> {currentPage}</p>
              <p><strong>Records Per Page:</strong> {recordsPerPage}</p>
              <p><strong>Total Records (UI):</strong> {totalRecords}</p>
              <p><strong>Records Displayed:</strong> {records.length}</p>
              <p><strong>API Request Parameters:</strong></p>
              <pre className="bg-gray-700 text-white p-2 rounded text-xs mt-1 overflow-auto max-h-20">
                page={currentPage}, limit={recordsPerPage}
                {filters.start_date ? `, start_date=${filters.start_date}` : ''}
                {filters.end_date ? `, end_date=${filters.end_date}` : ''}
                {filters.printer_id ? `, printer_id=${filters.printer_id}` : ''}
                {filters.vendor_name ? `, vendor_name=${filters.vendor_name}` : ''}
                {filters.department ? `, department=${filters.department}` : ''}
                {filters.search ? `, search=${filters.search}` : ''}
              </pre>
            </div>
            <div>
              <p><strong>API Response Structure:</strong></p>
              <div className="bg-gray-700 text-white p-2 rounded text-xs mt-1 overflow-auto max-h-40">
                {apiResponse ? (
                  <pre>{JSON.stringify(apiResponse, null, 2)}</pre>
                ) : (
                  <span>No data available</span>
                )}
              </div>
              <div className="mt-2 flex gap-2">
                <button 
                  onClick={() => {
                    // Force fetch all records to count total
                    const getAllRecords = async () => {
                      try {
                        const response = await api.get('/printer-maintenance');
                        const allRecords = Array.isArray(response.data) ? response.data : 
                                          (response.data && response.data.records) ? response.data.records : [];
                        alert(`Total records in database: ${allRecords.length}`);
                      } catch (err) {
                        console.error('Error getting all records:', err);
                        alert('Could not retrieve all records');
                      }
                    };
                    getAllRecords();
                  }}
                  className="bg-blue-500 hover:bg-blue-600 text-white text-xs px-2 py-1 rounded"
                >
                  Check Total Records
                </button>
                <button 
                  onClick={fetchMaintenanceRecords}
                  className="bg-green-500 hover:bg-green-600 text-white text-xs px-2 py-1 rounded"
                >
                  Refresh Data
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Only render the filters and table if not in renderStatsOnly mode */}
      {!renderStatsOnly && (
        <>
          {/* Error message with retry button */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-center justify-between">
              <div className="flex items-center">
                <AlertCircle className="text-red-500 mr-2" size={20} />
                <p className="text-red-700">{error}</p>
              </div>
              <button 
                onClick={() => {
                  setRetryCount(0);
                  fetchMaintenanceRecords();
                }}
                className="px-4 py-2 bg-red-100 hover:bg-red-200 text-red-800 rounded-md flex items-center"
              >
                Retry
              </button>
            </div>
          )}
          
          {/* No records message (only shown when not loading and no records) */}
          {!suppressOuterMessage && !loading && records.length === 0 && !error && (
            <div className="text-center py-10">
              <p className="text-gray-500 text-lg">No maintenance records found</p>
              <button 
                onClick={onNewClick || (() => {})}
                className="mt-4 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md"
              >
                Create New Maintenance Record
              </button>
            </div>
          )}
          
          {/* Table with integrated filters - styled like Asset Management */}
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            {/* Filter bar now inside the table */}
            <div className="border-b border-gray-200 bg-gray-50 p-3">
              {/* Page title and button moved here */}
              <div className="mb-2 flex justify-between items-center">
                <div>
                  <h1 className="text-xl font-bold text-gray-800">Printer Maintenance</h1>
                  <p className="text-sm text-gray-600">Manage and track your printer maintenance records</p>
                </div>
                <div className="flex space-x-3">
                  <button 
                    onClick={() => setShowExportModal(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center text-sm"
                  >
                    <FileText className="h-4 w-4 mr-1.5" /> Download Sheet
                  </button>
                  {onNewClick && (
                    <button 
                      onClick={onNewClick}
                      className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center text-sm"
                    >
                      <span className="mr-1">+</span> New Maintenance Log
                    </button>
                  )}
                </div>
              </div>
              
              {/* Filter controls */}
              <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-4">
                {/* Search bar */}
                <div className="relative w-full md:w-64 lg:w-80">
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    value={filters.search || ''}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    placeholder="Search by model, service type, or invoice number..."
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                
                {/* Vendor filter */}
                <div className="w-full md:w-40">
                  <select
                    className="w-full border border-gray-300 rounded-md py-2 px-3 text-sm bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    value={filters.vendor_name || ''}
                    onChange={(e) => handleFilterChange('vendor_name', e.target.value)}
                  >
                    <option value="">All Vendors</option>
                    {vendors.map((vendor) => (
                      <option key={vendor} value={vendor}>
                        {vendor}
                      </option>
                    ))}
                  </select>
                </div>
                
                {/* Printer filter */}
                <div className="w-full md:w-40">
                  <select
                    className="w-full border border-gray-300 rounded-md py-2 px-3 text-sm bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    value={filters.printer_id || ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      // Set to undefined when "All Printers" is selected (empty value)
                      handleFilterChange('printer_id', value === '' ? undefined : parseInt(value));
                    }}
                  >
                    <option value="">All Printers</option>
                    {printers.map((printer) => (
                      <option key={printer.id} value={printer.id}>
                        {printer.name || `Printer ${printer.id}`}
                      </option>
                    ))}
                  </select>
                </div>
                
                {/* Department filter */}
                <div className="w-full md:w-40">
                  <select
                    className="w-full border border-gray-300 rounded-md py-2 px-3 text-sm bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    value={filters.department || ''}
                    onChange={(e) => handleFilterChange('department', e.target.value)}
                  >
                    <option value="">All Departments</option>
                    {departments.map((dept) => (
                      <option key={dept} value={dept}>
                        {dept}
                      </option>
                    ))}
                  </select>
                </div>
                
                {/* Date range */}
                <div className="flex space-x-2 w-full md:w-auto">
                  <input
                    type="date"
                    className="w-full border border-gray-300 rounded-md py-2 px-3 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    value={filters.start_date || ''}
                    onChange={(e) => handleFilterChange('start_date', e.target.value)}
                    placeholder="From"
                  />
                  <input
                    type="date"
                    className="w-full border border-gray-300 rounded-md py-2 px-3 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    value={filters.end_date || ''}
                    onChange={(e) => handleFilterChange('end_date', e.target.value)}
                    placeholder="To"
                  />
                </div>
                
                {/* Filter buttons */}
                <div className="flex space-x-2 w-full md:w-auto">
                  <button
                    onClick={applyFilters}
                    className="bg-blue-600 text-white px-4 py-2 text-sm rounded-md hover:bg-blue-700 flex items-center"
                  >
                    Apply Filters
                  </button>
                  <button 
                    onClick={resetFilters}
                    className="bg-gray-200 text-gray-700 px-4 py-2 text-sm rounded-md hover:bg-gray-300"
                  >
                    Reset
                  </button>
                </div>
              </div>
            </div>
            
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-pulse">Loading maintenance records...</div>
              </div>
            ) : records.length === 0 && !error ? (
              <div className="text-center py-8">
                <p className="text-gray-500 text-lg">No maintenance records found</p>
                {onNewClick && (
                  <button 
                    onClick={onNewClick}
                    className="mt-4 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md"
                  >
                    Create New Maintenance Record
                  </button>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">S.No</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Printer Model</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Service Date</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Department</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Vendor</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Service Type</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Amount</th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-bold text-gray-900 uppercase tracking-wider">Download</th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-bold text-gray-900 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {records.map((record, index) => {
                      // Extract proper department and assignee info
                      const { department, assignee } = extractAssigneeInfo(record.department);
                      
                      // Calculate the serial number
                      const serialNumber = (currentPage - 1) * recordsPerPage + index + 1;
                      
                      // Add a way to display both manufacturer and model properly, even if they come from test data
                      const getPrinterInfo = () => {
                        // Ensure manufacturer is set
                        let displayManufacturer = record.manufacturer;
                        let displayModel = record.model;

                        if (!displayManufacturer && displayModel) {
                          // Set default manufacturer for numeric models
                          if (/^\d+$/.test(displayModel)) {
                            displayManufacturer = 'HP';
                          }
                        }

                        if (displayModel && displayManufacturer) {
                          return (
                            <div className="font-medium">
                              {displayManufacturer} - {displayModel}
                            </div>
                          );
                        }

                        if (record.printer_name) {
                          return (
                            <div className="font-medium">
                              {record.printer_name}
                            </div>
                          );
                        }

                        if (record.printer_id || record.asset_id) {
                          const id = record.printer_id || record.asset_id;
                          return (
                            <div className="font-medium">
                              Printer #{id}
                            </div>
                          );
                        }

                        return (
                          <div className="font-medium">
                            Unknown Printer
                          </div>
                        );
                      };
                      
                      return (
                        <tr key={record.id} className="hover:bg-gray-50">
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600 font-medium">
                            {serialNumber}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {getPrinterInfo()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {formatDate(record.visit_date || record.service_date)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="inline-block bg-gray-100 text-gray-800 px-2 py-1 text-sm rounded">
                              {department || formatField(record.department_name, 'IT')}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap font-medium">
                            {formatField(record.vendor_name, 'Service Provider')}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="inline-block bg-blue-100 text-blue-700 px-2 py-1 text-sm rounded-full">
                              {formatField(record.service_type || record.action_taken, 'Maintenance')}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap font-medium text-green-600">
                            {record.invoice_amount || record.cost
                              ? <span>Rs {parseFloat(String(record.invoice_amount || record.cost)).toLocaleString('en-PK')}</span>
                              : <span className="text-gray-400">-</span>}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center">
                            {record.invoice_file ? (
                              <button
                                onClick={() => handleDownloadInvoice(record)}
                                className="bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-md text-xs font-medium inline-flex items-center"
                              >
                                <FileText className="h-3.5 w-3.5 mr-1" />
                                Invoice
                              </button>
                            ) : (
                              <span className="text-gray-400 text-xs">No file</span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center relative">
                            <button
                              onClick={(e) => toggleDropdown(e, String(record.id))}
                              className="text-gray-500 hover:text-gray-700"
                              title="Actions"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                              </svg>
                            </button>
                            
                            {/* Dropdown menu */}
                            {activeDropdown === record.id && (
                              <div 
                                className="absolute right-0 top-full mt-1 w-48 bg-white rounded-md shadow-lg py-1 z-10 border" 
                                onClick={(e) => e.stopPropagation()}
                              >
                                {/* View option */}
                                <button
                                  onClick={() => {
                                    setActiveDropdown(null);
                                    handleViewRecord(record);
                                  }}
                                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 w-full text-left"
                                >
                                  <Eye className="h-4 w-4 mr-3 text-blue-600" />
                                  <span>View Details</span>
                                </button>
                                
                                {/* Edit option */}
                                <button
                                  onClick={() => {
                                    setActiveDropdown(null);
                                    handleEditRecord(record);
                                  }}
                                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-green-50 w-full text-left"
                                >
                                  <Edit className="h-4 w-4 mr-3 text-green-600" />
                                  <span>Edit Record</span>
                                </button>
                                
                                {/* Delete option */}
                                <button
                                  onClick={() => {
                                    setActiveDropdown(null);
                                    handleDeleteRecord(record);
                                  }}
                                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-red-50 w-full text-left"
                                >
                                  <Trash2 className="h-4 w-4 mr-3 text-red-600" />
                                  <span>Delete Record</span>
                                </button>
                              </div>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>
          
          {/* Pagination - styled to match Asset Management */}
          {!loading && records.length > 0 && (
            <div className="mt-4 flex items-center justify-between">
              <div className="flex items-center text-sm text-gray-700">
                <span>
                  Showing{' '}
                  <span className="font-medium">{((currentPage - 1) * recordsPerPage) + 1}</span>
                  {' '}to{' '}
                  <span className="font-medium">
                    {Math.min(currentPage * recordsPerPage, totalRecords)}
                  </span>
                  {' '}of{' '}
                  <span className="font-medium">{totalRecords}</span>
                  {' '}records
                </span>
                <div className="ml-4">
                  <label htmlFor="recordsPerPage" className="text-sm text-gray-600 mr-2">
                    Records per page:
                  </label>
                  <select
                    id="recordsPerPage"
                    className="border rounded px-2 py-1 text-sm bg-white"
                    value={recordsPerPage}
                    onChange={handleRecordsPerPageChange}
                  >
                    <option value={5}>5</option>
                    <option value={10}>10</option>
                    <option value={25}>25</option>
                    <option value={50}>50</option>
                    <option value={100}>100</option>
                  </select>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => goToPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`flex items-center justify-center w-8 h-8 rounded ${
                    currentPage === 1
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-gray-700 hover:bg-blue-50'
                  }`}
                >
                  <ChevronLeft size={16} />
                </button>
                
                {/* Page numbers */}
                {Array.from({ length: Math.min(5, Math.ceil(totalRecords / recordsPerPage)) }, (_, i) => {
                  // Determine which page numbers to show
                  let pageNum;
                  const totalPages = Math.ceil(totalRecords / recordsPerPage);
                  
                  if (totalPages <= 5) {
                    // Show all pages if there are 5 or fewer
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    // If current page is near the beginning
                    pageNum = i + 1;
                    if (i === 4) {
                      return (
                        <div key="last" className="flex items-center space-x-1">
                          <span className="text-gray-500">...</span>
                          <button
                            onClick={() => goToPage(totalPages)}
                            className="flex items-center justify-center w-8 h-8 rounded text-gray-700 hover:bg-blue-50"
                          >
                            {totalPages}
                          </button>
                        </div>
                      );
                    }
                  } else if (currentPage >= totalPages - 2) {
                    // If current page is near the end
                    pageNum = totalPages - 4 + i;
                    if (i === 0) {
                      return (
                        <div key="first" className="flex items-center space-x-1">
                          <button
                            onClick={() => goToPage(1)}
                            className="flex items-center justify-center w-8 h-8 rounded text-gray-700 hover:bg-blue-50"
                          >
                            1
                          </button>
                          <span className="text-gray-500">...</span>
                        </div>
                      );
                    }
                  } else {
                    // If current page is in the middle
                    pageNum = currentPage - 2 + i;
                    
                    if (i === 0) {
                      return (
                        <div key="first" className="flex items-center space-x-1">
                          <button
                            onClick={() => goToPage(1)}
                            className="flex items-center justify-center w-8 h-8 rounded text-gray-700 hover:bg-blue-50"
                          >
                            1
                          </button>
                          <span className="text-gray-500">...</span>
                        </div>
                      );
                    }
                    
                    if (i === 4) {
                      return (
                        <div key="last" className="flex items-center space-x-1">
                          <span className="text-gray-500">...</span>
                          <button
                            onClick={() => goToPage(totalPages)}
                            className="flex items-center justify-center w-8 h-8 rounded text-gray-700 hover:bg-blue-50"
                          >
                            {totalPages}
                          </button>
                        </div>
                      );
                    }
                  }
                  
                  // Return regular page buttons
                  return (
                    <button
                      key={pageNum}
                      onClick={() => goToPage(pageNum)}
                      className={`flex items-center justify-center w-8 h-8 rounded ${
                        pageNum === currentPage
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-700 hover:bg-blue-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
                
                <button
                  onClick={() => goToPage(currentPage + 1)}
                  disabled={currentPage >= Math.ceil(totalRecords / recordsPerPage)}
                  className={`flex items-center justify-center w-8 h-8 rounded ${
                    currentPage >= Math.ceil(totalRecords / recordsPerPage)
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-gray-700 hover:bg-blue-50'
                  }`}
                >
                  <ChevronRight size={16} />
                </button>
              </div>
            </div>
          )}
          
          {/* View Record Modal */}
          {viewingRecord && (
            <ViewRecordModalWrapper 
              record={viewingRecord} 
              onClose={() => setViewingRecord(null)} 
              onDownloadInvoice={handleDownloadInvoice} 
            />
          )}
          
          {/* Edit Record Form Modal */}
          {editingRecord && (
            <EditRecordFormWrapper 
              record={editingRecord} 
              onComplete={handleEditComplete} 
              onCancel={handleCancelEdit} 
            />
          )}
          
          {/* Export Modal */}
          <ExportModal 
            isOpen={showExportModal}
            onClose={() => setShowExportModal(false)}
            onExport={handleExport}
          />
        </>
      )}
    </div>
  );
};

export default MaintenanceTable; 