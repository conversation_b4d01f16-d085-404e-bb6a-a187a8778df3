import { AppDataSource } from '../config/database';
import { Role } from '../server/entities/Role';
import { Permission } from '../server/entities/Permission';
import { UserRoleAssignment } from '../server/entities/UserRoleAssignment';
import { User } from '../server/entities/User';
import {
  PermissionEvaluationContext,
  PermissionEvaluationResult,
  RuleCondition,
  RuleAction,
  UserRoleContext
} from '../types/roles';
import logger from '../utils/logger';

export class PermissionEvaluationService {
  private static roleRepository = AppDataSource.getRepository(Role);
  private static permissionRepository = AppDataSource.getRepository(Permission);
  private static userRoleRepository = AppDataSource.getRepository(UserRoleAssignment);
  private static userRepository = AppDataSource.getRepository(User);

  /**
   * Main permission evaluation method
   */
  static async evaluatePermission(context: PermissionEvaluationContext): Promise<PermissionEvaluationResult> {
    try {
      const { user, action, resource, environment } = context;

      // Get user's roles and permissions
      const userRoles = await this.getUserRoles(user.userId);
      const userPermissions = await this.getUserPermissions(user.userId);

      // Check direct permission
      const hasDirectPermission = userPermissions.some(permission => 
        this.matchesPermission(permission, action, resource)
      );

      if (hasDirectPermission) {
        // Evaluate conditions for the permission
        const conditionResult = await this.evaluateConditions(context, userRoles);
        
        if (conditionResult.granted) {
          return {
            granted: true,
            reason: 'Direct permission granted',
            conditions: conditionResult.conditions,
            metadata: {
              evaluatedAt: new Date(),
              method: 'direct_permission',
              roles: userRoles.map(r => r.name)
            }
          };
        } else {
          return {
            granted: false,
            reason: conditionResult.reason,
            conditions: conditionResult.conditions
          };
        }
      }

      // Check role-based rules
      const ruleResult = await this.evaluateRoleRules(context, userRoles);
      if (ruleResult.granted) {
        return ruleResult;
      }

      // Default deny
      return {
        granted: false,
        reason: 'No matching permissions or rules found',
        metadata: {
          evaluatedAt: new Date(),
          method: 'default_deny',
          checkedRoles: userRoles.map(r => r.name),
          checkedPermissions: userPermissions.map(p => p.name)
        }
      };

    } catch (error) {
      logger.error('Error evaluating permission:', error);
      return {
        granted: false,
        reason: 'Error during permission evaluation',
        metadata: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  /**
   * Get user's roles
   */
  private static async getUserRoles(userId: string): Promise<Role[]> {
    const assignments = await this.userRoleRepository.find({
      where: { userId },
      relations: ['role']
    });

    return assignments
      .filter(assignment => assignment.role)
      .map(assignment => assignment.role);
  }

  /**
   * Get user's permissions
   */
  private static async getUserPermissions(userId: string): Promise<Permission[]> {
    const roles = await this.getUserRoles(userId);
    const permissions = new Set<Permission>();

    for (const role of roles) {
      const roleWithPermissions = await this.roleRepository.findOne({
        where: { id: role.id },
        relations: ['permissions']
      });

      if (roleWithPermissions?.permissions) {
        roleWithPermissions.permissions.forEach(permission => {
          permissions.add(permission);
        });
      }
    }

    return Array.from(permissions);
  }

  /**
   * Check if permission matches the requested action and resource
   */
  private static matchesPermission(permission: Permission, action: string, resource?: any): boolean {
    // Extract resource and action from permission name (e.g., "tickets.create")
    const [permissionResource, permissionAction] = permission.name.split('.');
    
    // Extract resource and action from requested action
    const [requestedResource, requestedAction] = action.split('.');

    // Check if permission matches
    if (permissionResource === requestedResource && permissionAction === requestedAction) {
      return true;
    }

    // Check for wildcard permissions
    if (permissionAction === '*' && permissionResource === requestedResource) {
      return true;
    }

    if (permissionResource === '*') {
      return true;
    }

    return false;
  }

  /**
   * Evaluate conditions for permission
   */
  private static async evaluateConditions(
    context: PermissionEvaluationContext, 
    userRoles: Role[]
  ): Promise<{ granted: boolean; reason: string; conditions?: string[] }> {
    const { user, resource, environment } = context;
    const conditions: string[] = [];

    // Department-based access control
    if (resource?.department && user.department) {
      if (resource.department !== user.department) {
        // Check if user has cross-department access
        const hasCrossDepartmentAccess = userRoles.some(role => 
          role.name.includes('ADMIN') || role.name.includes('SYSTEM')
        );
        
        if (!hasCrossDepartmentAccess) {
          return {
            granted: false,
            reason: 'Access denied: Different department',
            conditions: ['department_mismatch']
          };
        }
      }
      conditions.push('department_checked');
    }

    // Resource ownership check
    if (resource?.createdBy && resource.createdBy !== user.userId) {
      const canAccessOthersResources = userRoles.some(role => 
        role.name.includes('ADMIN') || role.name.includes('MANAGER')
      );
      
      if (!canAccessOthersResources) {
        return {
          granted: false,
          reason: 'Access denied: Not resource owner',
          conditions: ['ownership_check_failed']
        };
      }
      conditions.push('ownership_override');
    }

    // Time-based access (if applicable)
    if (environment?.timeRestricted) {
      const currentHour = new Date().getHours();
      if (currentHour < 8 || currentHour > 18) {
        const hasAfterHoursAccess = userRoles.some(role => 
          role.name.includes('ADMIN') || role.name.includes('EMERGENCY')
        );
        
        if (!hasAfterHoursAccess) {
          return {
            granted: false,
            reason: 'Access denied: Outside business hours',
            conditions: ['time_restriction']
          };
        }
      }
      conditions.push('time_checked');
    }

    return {
      granted: true,
      reason: 'All conditions satisfied',
      conditions
    };
  }

  /**
   * Evaluate role-based rules
   */
  private static async evaluateRoleRules(
    context: PermissionEvaluationContext, 
    userRoles: Role[]
  ): Promise<PermissionEvaluationResult> {
    // This would evaluate custom rules defined in roles
    // For now, implementing basic role hierarchy
    
    const { action } = context;
    const [resource, actionType] = action.split('.');

    // System admin has access to everything
    if (userRoles.some(role => role.name === 'System Administrator')) {
      return {
        granted: true,
        reason: 'System administrator access',
        metadata: { method: 'system_admin_override' }
      };
    }

    // Resource-specific admin access
    if (resource === 'tickets' && userRoles.some(role => role.name === 'IT Administrator')) {
      return {
        granted: true,
        reason: 'IT administrator access to tickets',
        metadata: { method: 'resource_admin' }
      };
    }

    if (resource === 'employees' && userRoles.some(role => role.name === 'HR Administrator')) {
      return {
        granted: true,
        reason: 'HR administrator access to employees',
        metadata: { method: 'resource_admin' }
      };
    }

    return {
      granted: false,
      reason: 'No applicable role rules found'
    };
  }

  /**
   * Check if user has specific permission (simplified interface)
   */
  static async hasPermission(userId: string, action: string, resource?: any): Promise<boolean> {
    const context: PermissionEvaluationContext = {
      user: { userId, roles: [] },
      action,
      resource,
      timestamp: new Date()
    };

    const result = await this.evaluatePermission(context);
    return result.granted;
  }

  /**
   * Get all effective permissions for a user
   */
  static async getUserEffectivePermissions(userId: string): Promise<string[]> {
    const permissions = await this.getUserPermissions(userId);
    return permissions.map(p => p.name);
  }

  /**
   * Bulk permission check for multiple actions
   */
  static async checkMultiplePermissions(
    userId: string, 
    actions: string[], 
    resource?: any
  ): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};
    
    for (const action of actions) {
      results[action] = await this.hasPermission(userId, action, resource);
    }
    
    return results;
  }
}
