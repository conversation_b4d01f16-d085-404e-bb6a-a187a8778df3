import api, { safelyHandleResponse } from './api';

export interface AssetListItem {
  id: number;
  assetTag: string;
  manufacturer: string;
  model: string;
  status: string;
  category: string;
  assetType: string;
  department: string;
  serialNumber?: string;
  location?: string;
}

export interface SoftwareListItem {
  id: string;
  name: string;
  vendor: {
    vendorName: string;
  };
  status: string;
  category: string;
  type: string;
  department: string;
  totalSeats?: number;
  usedSeats?: number;
}

class AssetSoftwareService {
  // Fetch all assets
  async getAllAssets() {
    console.log('Fetching all assets from API');
    return safelyHandleResponse(api.get('/assets'));
  }

  // Fetch all software licenses
  async getAllSoftware() {
    console.log('Fetching all software licenses from API');
    return safelyHandleResponse(api.get('/software-licenses'));
  }

  // Get a formatted list of assets for dropdown selection
  async getAssetOptions() {
    try {
      const { data, error } = await this.getAllAssets();
      
      if (error || !data) {
        console.error('Error fetching assets:', error);
        return [];
      }
      
      // Group assets by category for better organization
      const groupedAssets = data.reduce((acc: Record<string, AssetListItem[]>, asset: AssetListItem) => {
        const category = asset.category || 'Uncategorized';
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(asset);
        return acc;
      }, {});
      
      // Create an array of options with category headers and assets
      const options: any[] = [];
      
      // Add a default "Select Asset" option
      options.push({ value: '', label: 'Select Asset', disabled: true });
      
      // Add each category and its assets
      Object.entries(groupedAssets).forEach(([category, assets]) => {
        // Add a category header (as a disabled option)
        options.push({ value: `category-${category}`, label: `---- ${category} ----`, disabled: true });
        
        // Add assets in this category
        (assets as AssetListItem[]).forEach((asset: AssetListItem) => {
          // Include department in the display text
          const status = asset.status === 'Active' ? '' : ` [${asset.status}]`; // Only show status if not active
          const department = asset.department ? ` | ${asset.department}` : '';
          const displayText = `${asset.manufacturer} ${asset.model} (${asset.assetTag})${status}${department}`;
          
          // Determine the icon based on asset type
          let icon = '💻'; // Default laptop icon
          
          // Map asset types to appropriate icons
          switch(asset.assetType) {
            case 'Computing':
              icon = '💻'; // Laptop/Desktop
              break;
            case 'Mobile & Tablet':
              icon = '📱'; // Mobile phone
              break;
            case 'Networking & Communication':
              icon = '🌐'; // Network globe
              break;
            case 'Printing':
              icon = '🖨️'; // Printer
              break;
            case 'Display & Multimedia':
              icon = '🖥️'; // Display monitor
              break;
            case 'Security & Surveillance':
              icon = '🔐'; // Security lock
              break;
            case 'Peripherals & Accessories':
              icon = '🖱️'; // Mouse
              break;
            case 'Other Equipment':
              icon = '📦'; // Box/package
              break;
            default:
              icon = '💻'; // Default
          }
          
          options.push({
            value: asset.id.toString(),
            label: displayText,
            // Add the icon to be used in the dropdown
            icon: icon,
            // Additional data that could be used in tooltips or enhanced displays
            metadata: {
              assetType: asset.assetType,
              category: asset.category,
              department: asset.department,
              serialNumber: asset.serialNumber,
              location: asset.location,
              icon: icon // Store icon for easy access
            }
          });
        });
      });
      
      // Add "None" option at the end
      options.push({ value: 'None', label: 'None' });
      
      return options;
    } catch (error) {
      console.error('Error formatting asset options:', error);
      return [
        { value: '', label: 'Select Asset', disabled: true },
        { value: 'None', label: 'None' }
      ];
    }
  }

  // Get a formatted list of software for dropdown selection
  async getSoftwareOptions() {
    try {
      const { data, error } = await this.getAllSoftware();
      
      if (error || !data) {
        console.error('Error fetching software:', error);
        return [];
      }
      
      // Group software by category for better organization
      const groupedSoftware = data.reduce((acc: Record<string, SoftwareListItem[]>, software: SoftwareListItem) => {
        const category = software.category || 'Uncategorized';
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(software);
        return acc;
      }, {});
      
      // Create an array of options with category headers and software items
      const options: any[] = [];
      
      // Add a default "Select Software" option
      options.push({ value: '', label: 'Select Software', disabled: true });
      
      // Add each category and its software items
      Object.entries(groupedSoftware).forEach(([category, softwareItems]) => {
        // Add a category header (as a disabled option)
        options.push({ value: `category-${category}`, label: `---- ${category} ----`, disabled: true });
        
        // Add software items in this category
        (softwareItems as SoftwareListItem[]).forEach((software: SoftwareListItem) => {
          const vendorName = software.vendor?.vendorName || '';
          const status = software.status === 'Active' ? '' : ` [${software.status}]`; // Only show status if not active
          const department = software.department ? ` | ${software.department}` : '';
          
          // Create a display text that includes department
          const displayText = `${software.name} (${vendorName})${status}${department}`;
          
          // Determine the icon based on software type or category
          let icon = '📊'; // Default software icon
          
          // Map software types or categories to appropriate icons
          if (software.type.includes('Subscription')) {
            icon = '🔄'; // Recurring subscription
          } else if (software.type === 'Free' || software.type === 'Open Source') {
            icon = '🆓'; // Free software
          } else if (software.type === 'One-Time (Lifetime)') {
            icon = '🔑'; // License key
          } else if (software.type === 'Trial') {
            icon = '⏱️'; // Timer for trial
          } else if (software.category.includes('Office') || software.name.includes('Office')) {
            icon = '📝'; // Document/Office
          } else if (software.category.includes('Design') || software.name.includes('Adobe')) {
            icon = '🎨'; // Design/Creative
          } else if (software.category.includes('Security') || software.name.includes('Antivirus')) {
            icon = '🛡️'; // Security shield
          } else if (software.category.includes('Utility') || software.category.includes('Tool')) {
            icon = '🔧'; // Utility/Tool
          } else if (software.category.includes('Communication') || software.name.includes('Teams') || software.name.includes('Zoom')) {
            icon = '💬'; // Communication
          } else if (software.category.includes('Development') || software.category.includes('Programming')) {
            icon = '💻'; // Development/Code
          }
          
          options.push({
            value: software.id,
            label: displayText,
            // Add the icon to be used in the dropdown
            icon: icon,
            // Additional data that could be used in tooltips or enhanced displays
            metadata: {
              category: software.category,
              type: software.type,
              department: software.department,
              totalSeats: software.totalSeats,
              usedSeats: software.usedSeats,
              icon: icon // Store icon for easy access
            }
          });
        });
      });
      
      // Add "None" option at the end
      options.push({ value: 'None', label: 'None' });
      
      return options;
    } catch (error) {
      console.error('Error formatting software options:', error);
      return [
        { value: '', label: 'Select Software', disabled: true },
        { value: 'None', label: 'None' }
      ];
    }
  }
}

export default new AssetSoftwareService(); 