import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Users, Calendar, Clock, AlertCircle, X, Eye, TrendingUp, TrendingDown, BarChart3 } from 'lucide-react';

interface EmployeeLeaveData {
  employeeId: number;
  employeeName: string;
  employeeCode: string;
  department: string;
  position: string;
  leaveBalances: any[];
  recentRequests: any[];
  totalDaysUsed: number;
  totalDaysRemaining: number;
}

interface EmployeeLeaveBalancesProps {
  paginatedEmployees: EmployeeLeaveData[];
  onAllocateLeave: () => void;
  onViewDetails?: (employee: EmployeeLeaveData) => void;
  currentPage: number;
  totalPages: number;
  totalEmployees: number;
  employeesPerPage: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (itemsPerPage: number) => void;
  allEmployees: EmployeeLeaveData[];
  configuredLeaveTypes?: any[];
}

const EmployeeLeaveBalances: React.FC<EmployeeLeaveBalancesProps> = ({
  paginatedEmployees,
  onViewDetails,
  currentPage,
  totalPages,
  totalEmployees,
  employeesPerPage,
  onPageChange,
  onItemsPerPageChange,
  configuredLeaveTypes = []
}) => {
  const [notification, setNotification] = useState<{type: 'success' | 'error' | 'warning', message: string} | null>(null);

  const getLeaveTypeLabel = (leaveType: string): string => {
    const configuredType = configuredLeaveTypes.find(lt => lt.leaveType === leaveType);
    if (configuredType && configuredType.displayName) {
      return configuredType.displayName;
    }

    const labels: { [key: string]: string } = {
      'ANNUAL': 'Annual',
      'SICK': 'Sick',
      'PERSONAL': 'Personal',
      'UNPAID': 'Unpaid',
      'MATERNITY': 'Maternity',
      'PATERNITY': 'Paternity',
      'BEREAVEMENT': 'Bereavement',
      'COMP_OFF': 'Comp Off',
      'OTHER': 'Other'
    };
    
    return labels[leaveType] || leaveType;
  };

  const getLeaveTypeColor = (leaveType: string): string => {
    const colors: { [key: string]: string } = {
      'ANNUAL': 'bg-blue-100 text-blue-800 border-blue-200',
      'SICK': 'bg-red-100 text-red-800 border-red-200',
      'PERSONAL': 'bg-green-100 text-green-800 border-green-200',
      'UNPAID': 'bg-gray-100 text-gray-800 border-gray-200',
      'MATERNITY': 'bg-pink-100 text-pink-800 border-pink-200',
      'PATERNITY': 'bg-cyan-100 text-cyan-800 border-cyan-200',
      'BEREAVEMENT': 'bg-purple-100 text-purple-800 border-purple-200',
      'COMP_OFF': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'OTHER': 'bg-indigo-100 text-indigo-800 border-indigo-200'
    };
    
    return colors[leaveType] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getBalanceStatus = (balance: any) => {
    const percentage = (balance.remaining / balance.total) * 100;
    if (percentage === 0) return { status: 'critical', color: 'text-red-600', bgColor: 'bg-red-50', icon: AlertCircle };
    if (percentage <= 25) return { status: 'low', color: 'text-orange-600', bgColor: 'bg-orange-50', icon: Clock };
    if (percentage <= 50) return { status: 'medium', color: 'text-yellow-600', bgColor: 'bg-yellow-50', icon: Calendar };
    return { status: 'good', color: 'text-green-600', bgColor: 'bg-green-50', icon: Calendar };
  };

  const getOverallStatus = (employee: EmployeeLeaveData) => {
    if (employee.totalDaysRemaining === 0) return { status: 'critical', color: 'text-red-600', bgColor: 'bg-red-50' };
    if (employee.totalDaysRemaining <= 5) return { status: 'low', color: 'text-orange-600', bgColor: 'bg-orange-50' };
    if (employee.totalDaysRemaining <= 10) return { status: 'medium', color: 'text-yellow-600', bgColor: 'bg-yellow-50' };
    return { status: 'good', color: 'text-green-600', bgColor: 'bg-green-50' };
  };

  const departmentNames: Record<string, string> = {
    'IT': 'Information Technology',
    'HR': 'Human Resources',
    'FINANCE': 'Finance & Accounting',
    'MARKETING': 'Marketing & Communications',
    'SALES': 'Sales',
    'DIGITAL SALES': 'Digital Sales',
    'OPERATIONS': 'Operations',
    'CSD': 'Customer Service Department',
    'LAND': 'Land Department',
    'LEGAL': 'Legal',
    'MANAGEMENT': 'Management',
    'PND': 'Planning and Development'
  };

  // Calculate summary statistics
  const summaryStats = {
    totalEmployees,
    criticalBalance: paginatedEmployees.filter(emp => emp.totalDaysRemaining === 0).length,
    lowBalance: paginatedEmployees.filter(emp => emp.totalDaysRemaining > 0 && emp.totalDaysRemaining <= 5).length,
    goodBalance: paginatedEmployees.filter(emp => emp.totalDaysRemaining > 10).length
  };

  return (
    <>
      {/* Notification */}
      {notification && (
        <div className={`fixed top-4 right-4 z-50 max-w-md p-4 rounded-lg shadow-lg ${
          notification.type === 'success' ? 'bg-green-50 border border-green-200 text-green-800' :
          notification.type === 'error' ? 'bg-red-50 border border-red-200 text-red-800' :
          'bg-yellow-50 border border-yellow-200 text-yellow-800'
        }`}>
          <div className="flex items-start">
            <div className="ml-3 flex-1">
              <p className="text-sm font-medium">{notification.message}</p>
            </div>
            <button
              onClick={() => setNotification(null)}
              className="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Main Table */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        {/* Professional Header */}
        <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 px-6 py-4 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                <BarChart3 className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold">Employee Leave Balances</h3>
                <p className="text-blue-100 text-sm opacity-90">Real-time leave allocation tracking</p>
              </div>
            </div>
            <div className="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg px-3 py-2 border border-white border-opacity-30">
              <div className="text-center">
                <div className="text-xl font-bold text-white">{totalEmployees}</div>
                <div className="text-xs text-blue-100 uppercase tracking-wide">Employees</div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  <span>Employee</span>
                </th>
                <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  <span>Department</span>
                </th>
                {configuredLeaveTypes.map((leaveType) => (
                  <th key={leaveType.leaveType} className="px-3 py-3 text-center text-xs font-bold text-gray-700 uppercase tracking-wider">
                    <div className="flex flex-col items-center">
                      <span className="text-blue-600 font-semibold">{leaveType.displayName || leaveType.leaveType}</span>
                      <span className="text-xs text-gray-500 normal-case font-normal">Days</span>
                    </div>
                  </th>
                ))}
                <th className="px-3 py-3 text-center text-xs font-bold text-gray-700 uppercase tracking-wider">
                  <div className="flex flex-col items-center">
                    <span className="text-green-600 font-semibold">Total</span>
                    <span className="text-xs text-gray-500 normal-case font-normal">Remaining</span>
                  </div>
                </th>
                <th className="px-3 py-3 text-center text-xs font-bold text-gray-700 uppercase tracking-wider">
                  <div className="flex flex-col items-center">
                    <span className="text-purple-600 font-semibold">Usage</span>
                    <span className="text-xs text-gray-500 normal-case font-normal">Rate</span>
                  </div>
                </th>
                <th className="px-4 py-3 text-center text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {paginatedEmployees.map((employee, index) => {
                const overallStatus = getOverallStatus(employee);
                const totalAllocated = employee.leaveBalances.reduce((sum, balance) => sum + balance.total, 0);
                const usageRate = totalAllocated > 0 ? Math.round((employee.totalDaysUsed / totalAllocated) * 100) : 0;
                
                return (
                  <tr key={employee.employeeId} className={`hover:bg-blue-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/30'}`}>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-sm">
                            <span className="text-xs font-bold text-white">
                              {employee.employeeName.split(' ').map(n => n[0]).join('').toUpperCase()}
                            </span>
                          </div>
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="text-sm font-semibold text-gray-900 truncate">{employee.employeeName}</div>
                          <div className="text-xs text-gray-500">{employee.employeeCode}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{departmentNames[employee.department] || employee.department}</div>
                        <div className="text-xs text-gray-500">{employee.position}</div>
                      </div>
                    </td>
                    {configuredLeaveTypes.map((leaveType) => {
                      const balance = employee.leaveBalances.find(b => b.leaveType === leaveType.leaveType);
                      const remaining = Math.round(balance?.remaining || 0);
                      const total = Math.round(balance?.total || 0);
                      const percentage = total > 0 ? Math.round((remaining / total) * 100) : 0;
                      
                      return (
                        <td key={leaveType.leaveType} className="px-3 py-3 whitespace-nowrap text-center">
                          <div className="flex flex-col items-center space-y-1">
                            <div className={`px-2 py-1 rounded-lg text-sm font-bold min-w-[40px] ${
                              percentage >= 80 ? 'bg-green-100 text-green-700' :
                              percentage >= 60 ? 'bg-yellow-100 text-yellow-700' :
                              percentage >= 40 ? 'bg-orange-100 text-orange-700' :
                              percentage > 0 ? 'bg-red-100 text-red-700' : 'bg-gray-100 text-gray-500'
                            }`}>
                              {remaining}
                            </div>
                            <div className="text-xs text-gray-400">of {total}</div>
                            {total > 0 && (
                              <div className="w-12 bg-gray-200 rounded-full h-1">
                                <div 
                                  className={`h-1 rounded-full ${
                                    percentage >= 80 ? 'bg-green-500' :
                                    percentage >= 60 ? 'bg-yellow-500' :
                                    percentage >= 40 ? 'bg-orange-500' :
                                    'bg-red-500'
                                  }`}
                                  style={{ width: `${percentage}%` }}
                                ></div>
                              </div>
                            )}
                          </div>
                        </td>
                      );
                    })}
                    <td className="px-3 py-3 whitespace-nowrap text-center">
                      <div className="flex flex-col items-center space-y-1">
                        <div className={`px-3 py-1 rounded-lg font-bold text-sm ${
                          employee.totalDaysRemaining > 20 ? 'bg-green-100 text-green-700' :
                          employee.totalDaysRemaining > 10 ? 'bg-yellow-100 text-yellow-700' :
                          employee.totalDaysRemaining > 0 ? 'bg-orange-100 text-orange-700' :
                          'bg-red-100 text-red-700'
                        }`}>
                          {Math.round(employee.totalDaysRemaining)}
                        </div>
                        <div className={`text-xs px-2 py-0.5 rounded-full font-medium ${
                          employee.totalDaysRemaining > 20 ? 'bg-green-50 text-green-600' :
                          employee.totalDaysRemaining > 10 ? 'bg-yellow-50 text-yellow-600' :
                          employee.totalDaysRemaining > 0 ? 'bg-orange-50 text-orange-600' :
                          'bg-red-50 text-red-600'
                        }`}>
                          {employee.totalDaysRemaining > 20 ? 'Excellent' :
                           employee.totalDaysRemaining > 10 ? 'Good' :
                           employee.totalDaysRemaining > 0 ? 'Low' : 'Critical'}
                        </div>
                      </div>
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-center">
                      <div className="flex flex-col items-center space-y-1">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold border-2 ${
                          usageRate >= 80 ? 'bg-red-50 border-red-200 text-red-600' :
                          usageRate >= 60 ? 'bg-orange-50 border-orange-200 text-orange-600' :
                          usageRate >= 40 ? 'bg-yellow-50 border-yellow-200 text-yellow-600' :
                          'bg-green-50 border-green-200 text-green-600'
                        }`}>
                          {usageRate}%
                        </div>
                        <div className={`text-xs px-1.5 py-0.5 rounded font-medium ${
                          usageRate >= 80 ? 'bg-red-100 text-red-700' :
                          usageRate >= 60 ? 'bg-orange-100 text-orange-700' :
                          usageRate >= 40 ? 'bg-yellow-100 text-yellow-700' :
                          'bg-green-100 text-green-700'
                        }`}>
                          {usageRate >= 80 ? 'High' :
                           usageRate >= 60 ? 'Med' :
                           usageRate >= 40 ? 'Low' : 'Min'}
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-center">
                      <button 
                        onClick={() => onViewDetails?.(employee)}
                        className="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white hover:bg-blue-700 rounded-lg text-xs font-medium transition-colors shadow-sm hover:shadow-md"
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <p className="text-sm text-gray-700">
                Show
                <select
                  value={employeesPerPage}
                  onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
                  className="mx-2 border border-gray-300 rounded px-3 py-1 text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={10}>10</option>
                  <option value={25}>25</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
                per page
              </p>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{(currentPage - 1) * employeesPerPage + 1}</span> to{' '}
                  <span className="font-medium">{Math.min(currentPage * employeesPerPage, totalEmployees)}</span> of{' '}
                  <span className="font-medium">{totalEmployees}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    onClick={() => onPageChange(Math.max(currentPage - 1, 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </button>
                  
                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNumber;
                    if (totalPages <= 5) {
                      pageNumber = i + 1;
                    } else if (currentPage <= 3) {
                      pageNumber = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNumber = totalPages - 4 + i;
                    } else {
                      pageNumber = currentPage - 2 + i;
                    }
                    
                    return (
                      <button
                        key={pageNumber}
                        onClick={() => onPageChange(pageNumber)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          currentPage === pageNumber
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {pageNumber}
                      </button>
                    );
                  })}
                  
                  <button
                    onClick={() => onPageChange(Math.min(currentPage + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronRight className="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EmployeeLeaveBalances; 