import { Request, Response } from 'express';
import { AppDataSource } from '../config/database';
import { TaskTemplate, TemplateCategory } from '../entities/TaskTemplate';
import { Task } from '../entities/Task';
import { Project } from '../entities/Project';
import { User } from '../entities/User';
import { validate } from 'class-validator';

export class TaskTemplateController {
  private taskTemplateRepository = AppDataSource.getRepository(TaskTemplate);
  private taskRepository = AppDataSource.getRepository(Task);
  private projectRepository = AppDataSource.getRepository(Project);
  private userRepository = AppDataSource.getRepository(User);

  // Get all task templates
  async getAllTemplates(req: Request, res: Response) {
    try {
      const { category, isPublic, search } = req.query;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const queryBuilder = this.taskTemplateRepository
        .createQueryBuilder('template')
        .leftJoinAndSelect('template.createdBy', 'createdBy')
        .where('template.isActive = :isActive', { isActive: true });

      // Show public templates or user's own templates
      queryBuilder.andWhere(
        '(template.isPublic = :isPublic OR template.createdById = :userId)',
        { isPublic: true, userId }
      );

      if (category) {
        queryBuilder.andWhere('template.category = :category', { category });
      }

      if (isPublic !== undefined) {
        queryBuilder.andWhere('template.isPublic = :isPublicFilter', { isPublicFilter: isPublic === 'true' });
      }

      if (search) {
        queryBuilder.andWhere(
          '(template.name LIKE :search OR template.description LIKE :search)',
          { search: `%${search}%` }
        );
      }

      const templates = await queryBuilder
        .orderBy('template.usageCount', 'DESC')
        .addOrderBy('template.createdAt', 'DESC')
        .getMany();

      res.json(templates);
    } catch (error) {
      console.error('Error fetching templates:', error);
      res.status(500).json({ error: 'Failed to fetch templates' });
    }
  }

  // Get template by ID
  async getTemplateById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const template = await this.taskTemplateRepository.findOne({
        where: { id: Number(id), isActive: true },
        relations: ['createdBy']
      });

      if (!template) {
        return res.status(404).json({ error: 'Template not found' });
      }

      // Check if user can access this template
      if (!template.isPublic && template.createdById !== userId) {
        return res.status(403).json({ error: 'You do not have permission to access this template' });
      }

      res.json(template);
    } catch (error) {
      console.error('Error fetching template:', error);
      res.status(500).json({ error: 'Failed to fetch template' });
    }
  }

  // Create new template
  async createTemplate(req: Request, res: Response) {
    try {
      const userId = (req as any).user?.id;
      const templateData = req.body;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Create template
      const template = this.taskTemplateRepository.create({
        ...templateData,
        createdById: userId
      });

      // Validate
      const errors = await validate(template);
      if (errors.length > 0) {
        return res.status(400).json({ 
          error: 'Validation failed', 
          details: errors.map(err => Object.values(err.constraints || {})).flat()
        });
      }

      const savedTemplate = await this.taskTemplateRepository.save(template);

      const completeTemplate = await this.taskTemplateRepository.findOne({
        where: { id: savedTemplate.id },
        relations: ['createdBy']
      });

      res.status(201).json(completeTemplate);
    } catch (error) {
      console.error('Error creating template:', error);
      res.status(500).json({ error: 'Failed to create template' });
    }
  }

  // Update template
  async updateTemplate(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;
      const updateData = req.body;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const template = await this.taskTemplateRepository.findOne({
        where: { id: Number(id), isActive: true }
      });

      if (!template) {
        return res.status(404).json({ error: 'Template not found' });
      }

      // Check if user owns this template
      if (template.createdById !== userId) {
        return res.status(403).json({ error: 'You can only update your own templates' });
      }

      await this.taskTemplateRepository.update(id, updateData);

      const updatedTemplate = await this.taskTemplateRepository.findOne({
        where: { id: Number(id) },
        relations: ['createdBy']
      });

      res.json(updatedTemplate);
    } catch (error) {
      console.error('Error updating template:', error);
      res.status(500).json({ error: 'Failed to update template' });
    }
  }

  // Delete template
  async deleteTemplate(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const template = await this.taskTemplateRepository.findOne({
        where: { id: Number(id), isActive: true }
      });

      if (!template) {
        return res.status(404).json({ error: 'Template not found' });
      }

      // Check if user owns this template
      if (template.createdById !== userId) {
        return res.status(403).json({ error: 'You can only delete your own templates' });
      }

      // Soft delete
      await this.taskTemplateRepository.update(id, { isActive: false });

      res.json({ message: 'Template deleted successfully' });
    } catch (error) {
      console.error('Error deleting template:', error);
      res.status(500).json({ error: 'Failed to delete template' });
    }
  }

  // Create task from template
  async createTaskFromTemplate(req: Request, res: Response) {
    try {
      const { templateId } = req.params;
      const { projectId, assignedToId, customValues } = req.body;
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Get template
      const template = await this.taskTemplateRepository.findOne({
        where: { id: Number(templateId), isActive: true }
      });

      if (!template) {
        return res.status(404).json({ error: 'Template not found' });
      }

      // Check if user can access this template
      if (!template.isPublic && template.createdById !== userId) {
        return res.status(403).json({ error: 'You do not have permission to use this template' });
      }

      // Verify project exists and user has access
      const project = await this.projectRepository.findOne({
        where: { id: projectId, isActive: true },
        relations: ['members']
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      const isMember = project.createdById === userId ||
                      project.managerId === userId ||
                      project.members.some(member => member.userId === userId && member.isActive);

      if (!isMember) {
        return res.status(403).json({ error: 'You are not a member of this project' });
      }

      // Replace template variables with custom values
      const title = this.replaceTemplateVariables(template.titleTemplate, customValues);
      const description = this.replaceTemplateVariables(template.descriptionTemplate || '', customValues);

      // Create main task
      const taskData = {
        title,
        description,
        projectId,
        assignedToId: assignedToId || null,
        priority: template.defaultPriority,
        type: template.defaultType,
        estimatedHours: template.defaultEstimatedHours,
        tags: template.defaultTags,
        checklist: template.defaultChecklist,
        requiresApproval: template.requiresApproval,
        createdById: userId
      };

      const task = this.taskRepository.create(taskData);
      const savedTask = await this.taskRepository.save(task);

      // Create subtasks if template has them
      if (template.subtaskTemplates && template.subtaskTemplates.length > 0) {
        for (const subtaskTemplate of template.subtaskTemplates) {
          const subtaskData = {
            title: this.replaceTemplateVariables(subtaskTemplate.title, customValues),
            description: this.replaceTemplateVariables(subtaskTemplate.description || '', customValues),
            projectId,
            parentTaskId: savedTask.id,
            priority: subtaskTemplate.priority,
            type: 'subtask',
            estimatedHours: subtaskTemplate.estimatedHours,
            createdById: userId
          };

          const subtask = this.taskRepository.create(subtaskData);
          await this.taskRepository.save(subtask);
        }
      }

      // Increment template usage count
      await this.taskTemplateRepository.update(templateId, {
        usageCount: template.usageCount + 1
      });

      // Fetch complete task with relations
      const completeTask = await this.taskRepository.findOne({
        where: { id: savedTask.id },
        relations: ['project', 'createdBy', 'assignedTo', 'subtasks']
      });

      res.status(201).json(completeTask);
    } catch (error) {
      console.error('Error creating task from template:', error);
      res.status(500).json({ error: 'Failed to create task from template' });
    }
  }

  // Get template categories
  async getTemplateCategories(req: Request, res: Response) {
    try {
      const categories = Object.values(TemplateCategory).map(category => ({
        value: category,
        label: category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      }));

      res.json(categories);
    } catch (error) {
      console.error('Error fetching template categories:', error);
      res.status(500).json({ error: 'Failed to fetch template categories' });
    }
  }

  // Helper method to replace template variables
  private replaceTemplateVariables(template: string, customValues: Record<string, any> = {}): string {
    let result = template;

    // Replace custom variables
    Object.keys(customValues).forEach(key => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, customValues[key]);
    });

    // Replace system variables
    const now = new Date();
    result = result.replace(/{{date}}/g, now.toISOString().split('T')[0]);
    result = result.replace(/{{datetime}}/g, now.toISOString());
    result = result.replace(/{{timestamp}}/g, now.getTime().toString());

    return result;
  }
}
