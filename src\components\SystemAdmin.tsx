import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import {
  Users,
  Search,
  Filter,
  Plus,
  Settings,
  Shield,
  Mail,
  Building2,
  UserPlus,
  Key,
  Lock,
  Unlock,
  MoreVertical,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertCircle,
  Activity,
  Clock,
  RefreshCw,
  FileText,
  Cog,
  Database,
  Bell,
  FileBox,
  Save,
  RotateCw,
  X,
  ChevronDown,
  User
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { UserActivitySummary } from './UserActivitySummary';
import { LogViewer } from './LogViewer/LogViewer';
import SystemLogApiService from '../services/SystemLogApiService';
import { UserManagement } from './UserManagement';
import CompanyProfileEdit from './CompanyProfileEdit';
import { RolesAndPermissions } from './admin/RolesAndPermissions';
import { SystemSettings } from './admin/SystemSettings';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'head' | 'manager' | 'normal' | 'view';
  department?: string;
  status: 'active' | 'inactive' | 'locked';
  lastLogin?: string;
  createdAt: string;
  permissions: string[];
}

interface SystemLog {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  action: string;
  user: string;
  timestamp: string;
  details: string;
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
}

interface Settings {
  id: string;
  category: string;
  key: string;
  value: string;
  description: string;
  lastUpdated: string;
  updatedBy: string;
}

export function SystemAdmin({ defaultTab = 'users' }: { defaultTab?: 'users' | 'roles' | 'logs' | 'settings' | 'company' }) {
  const { user } = useAuth();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState<'users' | 'roles' | 'logs' | 'settings' | 'company'>(defaultTab);
  const [showForm, setShowForm] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [systemLogs, setSystemLogs] = useState<SystemLog[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [settings, setSettings] = useState<Settings[]>([]);
  
  // New states for log functionality
  const [logSearchQuery, setLogSearchQuery] = useState('');
  const [logTypeFilter, setLogTypeFilter] = useState<string>('all');
  const [logUserFilter, setLogUserFilter] = useState<string>('all');
  const [selectedLog, setSelectedLog] = useState<SystemLog | null>(null);
  const [showLogDetails, setShowLogDetails] = useState(false);
  const [currentLogPage, setCurrentLogPage] = useState(1);
  const [logsPerPage] = useState(10);
  const [refreshCounter, setRefreshCounter] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Update activeTab when URL changes
  useEffect(() => {
    const path = location.pathname;
    if (path.includes('users')) {
      setActiveTab('users');
    } else if (path.includes('roles')) {
      setActiveTab('roles');
    } else if (path.includes('system-logs')) {
      setActiveTab('logs');
    } else if (path.includes('system-settings')) {
      setActiveTab('settings');
    } else if (path.includes('company-profile')) {
      setActiveTab('company');
    }
  }, [location.pathname]);

  // Load data from storage and API
  useEffect(() => {
    const storedUsers = localStorage.getItem('users');
    const storedRoles = localStorage.getItem('roles');
    const storedSettings = localStorage.getItem('systemSettings');

    if (storedUsers) setUsers(JSON.parse(storedUsers));
    if (storedRoles) setRoles(JSON.parse(storedRoles));
    if (storedSettings) setSettings(JSON.parse(storedSettings));
    
    // Fetch system logs from API instead of localStorage
    fetchSystemLogs();
  }, []);

  // Fetch system logs from the API
  const fetchSystemLogs = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await SystemLogApiService.getAllLogs({
        page: 1,
        limit: 9999, // Request all logs instead of just 50
        sortBy: 'timestamp',
        sortOrder: 'DESC',
        includeAnonymous: true, // Explicitly include anonymous logs
        onlyValidUsers: false // Explicitly request all users, not just valid ones
      });
      
      if (response.data && response.data.logs) {
        setSystemLogs(response.data.logs);
      } else {
        console.error('Unexpected API response format:', response);
        setError('Failed to fetch system logs: Invalid response format');
      }
    } catch (err) {
      console.error('Error fetching system logs:', err);
      setError('Failed to fetch system logs. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  // Save data to storage (except logs which are now in the database)
  useEffect(() => {
    localStorage.setItem('users', JSON.stringify(users));
    localStorage.setItem('roles', JSON.stringify(roles));
    localStorage.setItem('systemSettings', JSON.stringify(settings));
  }, [users, roles, settings]);

  // Auto-refresh logs every 30 seconds
  useEffect(() => {
    const refreshInterval = setInterval(() => {
      setRefreshCounter(prev => prev + 1);
    }, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(refreshInterval);
  }, []);
  
  // Fetch logs when refreshCounter changes
  useEffect(() => {
    if (refreshCounter > 0 && activeTab === 'logs') {
      fetchSystemLogs();
    }
  }, [refreshCounter, activeTab]);

  // Add log entry using the API
  const addLogEntry = async (entry: Omit<SystemLog, 'id' | 'timestamp'>) => {
    try {
      const response = await SystemLogApiService.createLog(entry);
      if (response.data) {
        // Refresh the logs after adding a new one
        fetchSystemLogs();
      } else {
        console.error('Failed to add log entry:', response.error);
      }
    } catch (err) {
      console.error('Error adding log entry:', err);
    }
  };

  // Handle clearing logs using the API
  const handleClearLogs = async () => {
    try {
      const response = await SystemLogApiService.clearLogs();
      if (response.data) {
        // Refresh logs after clearing
        setSystemLogs([]);
        // Log the clear operation
        addLogEntry({
          type: 'info',
          action: 'Logs Cleared',
          user: user?.name || 'System',
          details: 'System logs were cleared'
        });
      } else {
        console.error('Failed to clear logs:', response.error);
      }
    } catch (err) {
      console.error('Error clearing logs:', err);
    }
  };

  // Handle user actions
  const handleNewUser = async (userData: any) => {
    const newUser = {
      ...userData,
      id: `U-${Date.now()}`,
      status: 'active',
      createdAt: new Date().toISOString(),
      createdBy: user?.name
    };

    setUsers(prev => [newUser, ...prev]);
    addLogEntry({
      type: 'success',
      action: 'User Created',
      user: user?.name || 'System',
      details: `New user ${newUser.name} (${newUser.email}) was created`
    });
    setShowForm(false);
  };

  const handleEditUser = async (userData: any) => {
    setUsers(prev => prev.map(u => u.id === userData.id ? { ...u, ...userData } : u));
    addLogEntry({
      type: 'info',
      action: 'User Updated',
      user: user?.name || 'System',
      details: `User ${userData.name} was updated`
    });
    setSelectedUser(null);
  };

  const handleDeleteUser = () => {
    if (selectedUser) {
      setUsers(prev => prev.filter(u => u.id !== selectedUser.id));
      addLogEntry({
        type: 'warning',
        action: 'User Deleted',
        user: user?.name || 'System',
        details: `User ${selectedUser.name} was deleted`
      });
      setSelectedUser(null);
      setShowDeleteConfirm(false);
    }
  };

  const handleToggleStatus = (userId: string) => {
    setUsers(prev => prev.map(u => {
      if (u.id === userId) {
        const newStatus = u.status === 'active' ? 'inactive' : 'active';
        addLogEntry({
          type: 'info',
          action: 'User Status Changed',
          user: user?.name || 'System',
          details: `User ${u.name} status changed to ${newStatus}`
        });
        return {
          ...u,
          status: newStatus
        };
      }
      return u;
    }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-red-100 text-red-800';
      case 'locked': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'head': return 'bg-purple-100 text-purple-800';
      case 'manager': return 'bg-blue-100 text-blue-800';
      case 'normal': return 'bg-green-100 text-green-800';
      case 'view': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredUsers = users.filter(user => {
    if (selectedRole !== 'all' && user.role !== selectedRole) return false;
    if (selectedDepartment !== 'all' && user.department !== selectedDepartment) return false;
    if (selectedStatus !== 'all' && user.status !== selectedStatus) return false;

    if (searchQuery) {
      const search = searchQuery.toLowerCase();
      return (
        user.name.toLowerCase().includes(search) ||
        user.email.toLowerCase().includes(search) ||
        user.department?.toLowerCase().includes(search)
      );
    }

    return true;
  });

  // Render functions for different tabs
  const renderLogs = () => {
    // Show loading state
    if (isLoading && systemLogs.length === 0) {
      return (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <RotateCw className="w-8 h-8 text-blue-500 animate-spin" />
            <p className="mt-2 text-gray-600">Loading system logs...</p>
          </div>
        </div>
      );
    }
    
    // Show error state
    if (error) {
      return (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <AlertCircle className="w-8 h-8 text-red-500" />
            <p className="mt-2 text-gray-800 font-medium">{error}</p>
            <button 
              onClick={() => fetchSystemLogs()} 
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Retry
            </button>
          </div>
        </div>
      );
    }
    
    return (
      <LogViewer 
        logs={systemLogs}
        onRefresh={() => fetchSystemLogs()}
        onClear={() => handleClearLogs()}
        onAddLog={(log) => addLogEntry(log)}
      />
    );
  };

  return (
    <div className="p-3 space-y-2">
      {/* Content based on active tab */}
      <div className="bg-white rounded-lg shadow-sm p-3">
        {activeTab === 'users' && <UserManagement />}
        {activeTab === 'roles' && <RolesAndPermissions />}
        {activeTab === 'logs' && renderLogs()}
        {activeTab === 'settings' && <SystemSettings />}
        {activeTab === 'company' && (
          <CompanyProfileEdit 
            onCancel={() => setActiveTab('users')} 
            onSave={() => setActiveTab('users')} 
          />
        )}
      </div>

      {/* User Form */}
      {(showForm || selectedUser) && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md m-4 p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold text-gray-900">
                {selectedUser ? 'Edit User' : 'Create User'}
              </h3>
              <button
                onClick={() => {
            setShowForm(false);
            setSelectedUser(null);
          }}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <p className="text-gray-600 mb-4">
              User management has been moved to the User Management section.
              Please use that interface instead.
            </p>
            <div className="flex justify-end">
              <button
                onClick={() => {
                  setShowForm(false);
                  setSelectedUser(null);
                  setActiveTab('users');
                }}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                Go to User Management
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && selectedUser && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md m-4 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-red-100 rounded-lg">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900">Delete User</h3>
            </div>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete the user "{selectedUser.name}"? This action cannot be undone.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setSelectedUser(null);
                }}
                className="px-4 py-2 text-gray-700 bg-gray-100 font-medium rounded-lg hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteUser}
                className="flex items-center gap-2 px-4 py-2 bg-red-500 text-white font-medium rounded-lg hover:bg-red-600"
              >
                <Trash2 className="h-5 w-5" />
                Delete User
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}