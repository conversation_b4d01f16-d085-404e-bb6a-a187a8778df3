import { format } from 'date-fns';

/**
 * Format an ISO date string to a local date format
 * @param dateStr ISO date string to format
 * @param formatStr Optional format string (defaults to 'MMM DD, YYYY')
 * @returns Formatted date string
 */
export const formatDateToLocal = (dateStr: string, formatStr = 'MMM dd, yyyy'): string => {
  if (!dateStr) return '-';
  try {
    const date = new Date(dateStr);
    return format(date, formatStr);
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateStr;
  }
};

/**
 * Format a number as currency
 * @param amount Number amount to format
 * @param currencyCode Optional currency code (defaults to 'PKR')
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number, currencyCode = 'PKR'): string => {
  if (amount === null || amount === undefined) return '-';
  try {
    return `${currencyCode} ${amount.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    })}`;
  } catch (error) {
    console.error('Error formatting currency:', error);
    return `${currencyCode} ${amount}`;
  }
};

/**
 * Format a file size in bytes to human-readable format
 * @param bytes Size in bytes
 * @returns Formatted file size string (e.g., '2.5 MB')
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}; 