import express, { Request, Response } from 'express';
import { uploadSingle } from '../middleware/upload';
import { Re<PERSON>Handler } from 'express';

const router = express.Router();

// Route to handle file upload
router.post('/upload', uploadSingle, (req: Request, res: Response) => {
    try {
        if (!req.file) {
            res.status(400).json({
                status: 'error',
                message: 'No file uploaded'
            });
            return;
        }

        res.json({
            status: 'success',
            filename: req.file.filename,
            path: `/uploads/${req.file.filename}`
        });
    } catch (error: unknown) {
        const message = error instanceof Error ? error.message : 'Unknown error occurred';
        res.status(500).json({
            status: 'error',
            message
        });
    }
});

export default router; 