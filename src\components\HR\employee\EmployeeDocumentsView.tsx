import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  Upload, 
  Download, 
  Trash2, 
  Eye, 
  Search, 
  Filter, 
  Clock, 
  AlertCircle, 
  CheckCircle, 
  Calendar,
  File,
  FileArchive,
  FilePlus,
  FileImage
} from 'lucide-react';
import { useAuth } from '../../../contexts/AuthContext';

// Document type definitions
interface Document {
  id: string;
  name: string;
  type: string;
  category: 'personal' | 'company' | 'payroll' | 'contract' | 'identification' | 'certification';
  uploadDate: Date;
  expiryDate?: Date;
  status: 'active' | 'expired' | 'pending';
  fileSize: string;
  filePath: string;
  isVerified: boolean;
  restricted?: boolean;
}

const EmployeeDocumentsView: React.FC = () => {
  const { user } = useAuth();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showUploadModal, setShowUploadModal] = useState<boolean>(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [showDocumentView, setShowDocumentView] = useState<boolean>(false);

  // Simulated fetch of documents
  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setIsLoading(true);
        // In a real application, this would be an API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        const mockDocuments: Document[] = [
          {
            id: '1',
            name: 'Employment Contract.pdf',
            type: 'application/pdf',
            category: 'contract',
            uploadDate: new Date(2022, 5, 15),
            status: 'active',
            fileSize: '1.2 MB',
            filePath: '/documents/contract.pdf',
            isVerified: true
          },
          {
            id: '2',
            name: 'ID Card.jpg',
            type: 'image/jpeg',
            category: 'identification',
            uploadDate: new Date(2022, 3, 10),
            expiryDate: new Date(2025, 3, 10),
            status: 'active',
            fileSize: '350 KB',
            filePath: '/documents/id_card.jpg',
            isVerified: true
          },
          {
            id: '3',
            name: 'June 2023 Payslip.pdf',
            type: 'application/pdf',
            category: 'payroll',
            uploadDate: new Date(2023, 5, 30),
            status: 'active',
            fileSize: '450 KB',
            filePath: '/documents/june_payslip.pdf',
            isVerified: true
          },
          {
            id: '4',
            name: 'COVID Vaccination Certificate.pdf',
            type: 'application/pdf',
            category: 'certification',
            uploadDate: new Date(2022, 2, 5),
            status: 'active',
            fileSize: '280 KB',
            filePath: '/documents/vaccination.pdf',
            isVerified: true
          },
          {
            id: '5',
            name: 'Company Policy Acknowledgment.pdf',
            type: 'application/pdf',
            category: 'company',
            uploadDate: new Date(2023, 1, 12),
            status: 'active',
            fileSize: '520 KB',
            filePath: '/documents/policy.pdf',
            isVerified: true
          },
          {
            id: '6',
            name: 'Training Certificate - Cybersecurity.pdf',
            type: 'application/pdf',
            category: 'certification',
            uploadDate: new Date(2023, 4, 18),
            expiryDate: new Date(2024, 4, 18),
            status: 'active',
            fileSize: '310 KB',
            filePath: '/documents/cyber_cert.pdf',
            isVerified: true
          },
          {
            id: '7',
            name: 'NDA.pdf',
            type: 'application/pdf',
            category: 'contract',
            uploadDate: new Date(2022, 5, 15),
            status: 'active',
            fileSize: '180 KB',
            filePath: '/documents/nda.pdf',
            isVerified: true
          },
          {
            id: '8',
            name: 'Driving License.jpg',
            type: 'image/jpeg',
            category: 'personal',
            uploadDate: new Date(2022, 7, 25),
            expiryDate: new Date(2023, 7, 25),
            status: 'expired',
            fileSize: '290 KB',
            filePath: '/documents/license.jpg',
            isVerified: true
          }
        ];
        
        setDocuments(mockDocuments);
        setIsLoading(false);
      } catch (error) {
        setError('Failed to load documents. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchDocuments();
  }, []);

  // Filter documents based on search term and category
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || doc.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Get document icon based on file type
  const getDocumentIcon = (document: Document) => {
    if (document.type.includes('pdf')) {
      return <FileText className="h-8 w-8 text-red-500" />;
    } else if (document.type.includes('image')) {
      return <FileImage className="h-8 w-8 text-blue-500" />;
    } else if (document.type.includes('zip') || document.type.includes('rar')) {
      return <FileArchive className="h-8 w-8 text-purple-500" />;
    } else {
      return <File className="h-8 w-8 text-gray-500" />;
    }
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Check if a document is expiring soon (within 30 days)
  const isExpiringSoon = (document: Document) => {
    if (!document.expiryDate) return false;
    
    const today = new Date();
    const expiryDate = new Date(document.expiryDate);
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays > 0 && diffDays <= 30;
  };

  // Simulate document upload
  const handleUpload = (event: React.FormEvent) => {
    event.preventDefault();
    
    // Start a fake upload progress
    setUploadProgress(0);
    const interval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          // Add a new document to the list (would be an API call in a real app)
          const newDoc: Document = {
            id: (documents.length + 1).toString(),
            name: 'New Uploaded Document.pdf',
            type: 'application/pdf',
            category: 'personal',
            uploadDate: new Date(),
            status: 'pending',
            fileSize: '1.1 MB',
            filePath: '/documents/new_doc.pdf',
            isVerified: false
          };
          
          setDocuments(prev => [...prev, newDoc]);
          setShowUploadModal(false);
          return 0;
        }
        return prev + 10;
      });
    }, 300);
  };

  // Handle document view
  const handleViewDocument = (document: Document) => {
    setSelectedDocument(document);
    setShowDocumentView(true);
  };

  // Simulate document download
  const handleDownload = (document: Document) => {
    // In a real app, this would trigger a download
    alert(`Downloading ${document.name}`);
  };

  // Simulating document deletion
  const handleDelete = (documentId: string) => {
    if (window.confirm('Are you sure you want to delete this document?')) {
      setDocuments(prev => prev.filter(doc => doc.id !== documentId));
    }
  };

  // Document Upload Modal
  const renderUploadModal = () => {
    if (!showUploadModal) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-md">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Upload New Document</h3>
          
          <form onSubmit={handleUpload}>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Document Type
              </label>
              <select 
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="personal">Personal Document</option>
                <option value="identification">Identification</option>
                <option value="certification">Certificate</option>
                <option value="contract">Contract</option>
                <option value="payroll">Payroll</option>
              </select>
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Select File
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                <div className="space-y-1 text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="flex text-sm text-gray-600">
                    <label
                      htmlFor="file-upload"
                      className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                    >
                      <span>Upload a file</span>
                      <input id="file-upload" name="file-upload" type="file" className="sr-only" />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                  </div>
                  <p className="text-xs text-gray-500">
                    PDF, PNG, JPG, GIF up to 10MB
                  </p>
                </div>
              </div>
            </div>
            
            {uploadProgress > 0 && (
              <div className="mb-4">
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div 
                    className="bg-blue-600 h-2.5 rounded-full" 
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 text-right mt-1">
                  {uploadProgress}% uploaded
                </p>
              </div>
            )}
            
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowUploadModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Upload
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  // Document View Modal
  const renderDocumentViewModal = () => {
    if (!showDocumentView || !selectedDocument) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-3xl h-5/6 flex flex-col">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">{selectedDocument.name}</h3>
            <button 
              onClick={() => setShowDocumentView(false)}
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
            >
              <span className="sr-only">Close</span>
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div className="bg-gray-100 rounded-md flex-1 p-2 overflow-auto">
            {/* Document preview would go here */}
            <div className="h-full flex items-center justify-center">
              {getDocumentIcon(selectedDocument)}
              <p className="ml-2 text-gray-500">Document preview not available in this demo</p>
            </div>
          </div>
          
          <div className="mt-4 bg-gray-50 p-4 rounded-md">
            <h4 className="font-medium text-gray-900 mb-2">Document Details</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-500">Category</p>
                <p className="font-medium text-gray-900 capitalize">{selectedDocument.category}</p>
              </div>
              <div>
                <p className="text-gray-500">Upload Date</p>
                <p className="font-medium text-gray-900">{formatDate(selectedDocument.uploadDate)}</p>
              </div>
              {selectedDocument.expiryDate && (
                <div>
                  <p className="text-gray-500">Expiry Date</p>
                  <p className="font-medium text-gray-900">{formatDate(selectedDocument.expiryDate)}</p>
                </div>
              )}
              <div>
                <p className="text-gray-500">File Size</p>
                <p className="font-medium text-gray-900">{selectedDocument.fileSize}</p>
              </div>
              <div>
                <p className="text-gray-500">Status</p>
                <p className={`font-medium ${
                  selectedDocument.status === 'active' ? 'text-green-600' : 
                  selectedDocument.status === 'expired' ? 'text-red-600' : 
                  'text-yellow-600'
                } capitalize`}>{selectedDocument.status}</p>
              </div>
              <div>
                <p className="text-gray-500">Verified</p>
                <p className="font-medium text-gray-900">{selectedDocument.isVerified ? 'Yes' : 'No'}</p>
              </div>
            </div>
          </div>
          
          <div className="mt-4 flex justify-end space-x-3">
            <button
              onClick={() => handleDownload(selectedDocument)}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </button>
          </div>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="p-4 flex justify-center items-center h-64">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
          <p className="text-gray-600">Loading documents...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
          <div className="flex items-center">
            <AlertCircle className="h-6 w-6 text-red-500 mr-2" />
            <p className="text-red-700">{error}</p>
          </div>
          <button 
            className="mt-2 bg-red-100 text-red-700 px-4 py-2 rounded hover:bg-red-200"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Categories for the filter dropdown
  const categories = [
    { value: 'all', label: 'All Documents' },
    { value: 'personal', label: 'Personal' },
    { value: 'identification', label: 'Identification' },
    { value: 'contract', label: 'Contracts' },
    { value: 'payroll', label: 'Payroll' },
    { value: 'certification', label: 'Certifications' },
    { value: 'company', label: 'Company Documents' },
  ];

  // Stat cards data
  const documentStats = [
    {
      title: 'Total Documents',
      value: documents.length,
      icon: <FileText className="h-6 w-6 text-blue-500" />,
      color: 'bg-blue-50 text-blue-700'
    },
    {
      title: 'Certifications',
      value: documents.filter(doc => doc.category === 'certification').length,
      icon: <CheckCircle className="h-6 w-6 text-green-500" />,
      color: 'bg-green-50 text-green-700'
    },
    {
      title: 'Expiring Soon',
      value: documents.filter(doc => isExpiringSoon(doc)).length,
      icon: <Clock className="h-6 w-6 text-orange-500" />,
      color: 'bg-orange-50 text-orange-700'
    },
    {
      title: 'Expired',
      value: documents.filter(doc => doc.status === 'expired').length,
      icon: <AlertCircle className="h-6 w-6 text-red-500" />,
      color: 'bg-red-50 text-red-700'
    }
  ];

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900">My Documents</h2>
        <button
          onClick={() => setShowUploadModal(true)}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md shadow hover:bg-blue-700 flex items-center"
        >
          <FilePlus className="h-4 w-4 mr-2" />
          Upload New Document
        </button>
      </div>

      {/* Stats Section */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {documentStats.map((stat, index) => (
          <div key={index} className={`p-4 rounded-lg shadow-sm border border-gray-100 ${stat.color.split(' ')[0]}`}>
            <div className="flex items-center">
              <div className={`p-2 rounded-full ${stat.color}`}>
                {stat.icon}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row justify-between mb-6 space-y-3 sm:space-y-0">
        <div className="w-full sm:w-1/3">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Search documents..."
            />
          </div>
        </div>
        
        <div className="flex items-center">
          <Filter className="h-5 w-5 text-gray-400 mr-2" />
          <select
            value={selectedCategory}
            onChange={e => setSelectedCategory(e.target.value)}
            className="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            {categories.map(category => (
              <option key={category.value} value={category.value}>
                {category.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Documents List */}
      {filteredDocuments.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          <FileText className="h-12 w-12 mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900">No documents found</h3>
          <p className="text-gray-500 mt-2">
            {searchTerm 
              ? "No documents match your search criteria" 
              : selectedCategory !== 'all'
                ? `You don't have any ${selectedCategory} documents`
                : "You don't have any documents yet"}
          </p>
          <button 
            onClick={() => setShowUploadModal(true)}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Upload className="h-4 w-4 mr-2" />
            Upload your first document
          </button>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Document
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Upload Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Size
                </th>
                <th scope="col" className="relative px-6 py-3">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredDocuments.map((document) => (
                <tr key={document.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center">
                        {getDocumentIcon(document)}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{document.name}</div>
                        {document.expiryDate && (
                          <div className="text-xs text-gray-500 flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            Expires: {formatDate(document.expiryDate)}
                            {isExpiringSoon(document) && (
                              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Expiring Soon
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800 capitalize">
                      {document.category}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(document.uploadDate)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full capitalize
                      ${document.status === 'active' ? 'bg-green-100 text-green-800' :
                        document.status === 'expired' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                      {document.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {document.fileSize}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex space-x-2 justify-end">
                      <button
                        onClick={() => handleViewDocument(document)}
                        className="text-blue-600 hover:text-blue-900"
                        title="View"
                      >
                        <Eye className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDownload(document)}
                        className="text-green-600 hover:text-green-900"
                        title="Download"
                      >
                        <Download className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDelete(document.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Document upload modal */}
      {renderUploadModal()}
      
      {/* Document view modal */}
      {renderDocumentViewModal()}
    </div>
  );
};

export default EmployeeDocumentsView; 