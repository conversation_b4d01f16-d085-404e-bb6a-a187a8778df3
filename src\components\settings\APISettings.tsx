import React, { useState, useEffect } from 'react';
import { Code, Save, AlertCircle, TestTube, Activity, Clock, Globe, Shield } from 'lucide-react';

interface Setting {
  id: string;
  category: string;
  key: string;
  value: string;
  description: string;
  lastUpdated?: string;
  updatedBy?: string;
}

interface APISettingsProps {
  settings: Setting[];
  onSettingChange: (id: string, value: string) => void;
  onSave: () => void;
  isSaving?: boolean;
  saveSuccess?: boolean;
}

const APISettings: React.FC<APISettingsProps> = ({
  settings,
  onSettingChange,
  onSave,
  isSaving = false,
  saveSuccess = false
}) => {
  const [localSettings, setLocalSettings] = useState<Setting[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [testingAPI, setTestingAPI] = useState(false);
  const [apiTestResult, setApiTestResult] = useState<{
    success: boolean;
    message: string;
    responseTime?: number;
  } | null>(null);
  const [apiMetrics, setApiMetrics] = useState({
    totalRequests: 1247,
    successRate: 98.5,
    averageResponseTime: 145,
    rateLimitHits: 23
  });

  useEffect(() => {
    const apiSettings = settings.filter(setting => setting.category === 'api');
    setLocalSettings(apiSettings);
  }, [settings]);

  useEffect(() => {
    // Simulate real-time API metrics updates
    const interval = setInterval(() => {
      setApiMetrics(prev => ({
        totalRequests: prev.totalRequests + Math.floor(Math.random() * 5),
        successRate: Math.max(95, Math.min(100, prev.successRate + (Math.random() - 0.5) * 2)),
        averageResponseTime: Math.max(50, Math.min(300, prev.averageResponseTime + (Math.random() - 0.5) * 20)),
        rateLimitHits: prev.rateLimitHits + (Math.random() > 0.9 ? 1 : 0)
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const handleChange = (id: string, value: string) => {
    setLocalSettings(prev => prev.map(setting => 
      setting.id === id ? { ...setting, value } : setting
    ));
    setHasChanges(true);
    onSettingChange(id, value);
  };

  const handleSave = () => {
    onSave();
    setHasChanges(false);
  };

  const handleTestAPI = async () => {
    setTestingAPI(true);
    setApiTestResult(null);
    
    // Simulate API test
    const startTime = Date.now();
    setTimeout(() => {
      const responseTime = Date.now() - startTime;
      const success = Math.random() > 0.1; // 90% success rate
      
      setApiTestResult({
        success,
        message: success 
          ? 'API endpoint is responding correctly. All authentication and rate limiting configurations are working as expected.'
          : 'API test failed. Please check your configuration settings and ensure the endpoint is accessible.',
        responseTime
      });
      setTestingAPI(false);
    }, 2000);
  };

  const isNumberField = (key: string) => {
    return key.toLowerCase().includes('timeout') || 
           key.toLowerCase().includes('limit') || 
           key.toLowerCase().includes('rate') || 
           key.toLowerCase().includes('max');
  };

  const isURLField = (key: string) => {
    return key.toLowerCase().includes('url') || 
           key.toLowerCase().includes('endpoint') || 
           key.toLowerCase().includes('webhook');
  };

  const getMetricColor = (value: number, type: string) => {
    if (type === 'successRate') {
      if (value >= 98) return 'text-green-600';
      if (value >= 95) return 'text-yellow-600';
      return 'text-red-600';
    } else if (type === 'responseTime') {
      if (value <= 100) return 'text-green-600';
      if (value <= 200) return 'text-yellow-600';
      return 'text-red-600';
    }
    return 'text-gray-600';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Code className="h-6 w-6 text-blue-600" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">API Settings</h2>
            <p className="text-sm text-gray-600">Configure API endpoints, authentication, rate limiting, and monitoring</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={handleTestAPI}
            disabled={testingAPI}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {testingAPI ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <TestTube className="h-4 w-4" />
            )}
            {testingAPI ? 'Testing...' : 'Test API'}
          </button>
          {hasChanges && (
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSaving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save className="h-4 w-4" />
              )}
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          )}
        </div>
      </div>

      {saveSuccess && (
        <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700">
          <AlertCircle className="h-4 w-4" />
          API settings saved successfully!
        </div>
      )}

      {apiTestResult && (
        <div className={`p-4 rounded-lg border ${
          apiTestResult.success 
            ? 'bg-green-50 border-green-200 text-green-700' 
            : 'bg-red-50 border-red-200 text-red-700'
        }`}>
          <div className="flex items-center gap-2 mb-2">
            {apiTestResult.success ? (
              <Shield className="h-4 w-4 text-green-600" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-600" />
            )}
            <span className="font-medium">
              {apiTestResult.success ? 'API Test Successful' : 'API Test Failed'}
            </span>
            {apiTestResult.responseTime && (
              <span className="text-sm">({apiTestResult.responseTime}ms)</span>
            )}
          </div>
          <p className="text-sm">{apiTestResult.message}</p>
        </div>
      )}

      {/* API Metrics Dashboard */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center gap-3 mb-4">
          <Activity className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-medium text-gray-900">API Metrics</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-900 mb-1">Total Requests</div>
            <div className="text-2xl font-semibold text-gray-900">{apiMetrics.totalRequests.toLocaleString()}</div>
            <div className="text-xs text-gray-600">Last 24 hours</div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-900 mb-1">Success Rate</div>
            <div className={`text-2xl font-semibold ${getMetricColor(apiMetrics.successRate, 'successRate')}`}>
              {apiMetrics.successRate.toFixed(1)}%
            </div>
            <div className="text-xs text-gray-600">Last 24 hours</div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-900 mb-1">Avg Response Time</div>
            <div className={`text-2xl font-semibold ${getMetricColor(apiMetrics.averageResponseTime, 'responseTime')}`}>
              {apiMetrics.averageResponseTime}ms
            </div>
            <div className="text-xs text-gray-600">Last 24 hours</div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-900 mb-1">Rate Limit Hits</div>
            <div className="text-2xl font-semibold text-gray-900">{apiMetrics.rateLimitHits}</div>
            <div className="text-xs text-gray-600">Last 24 hours</div>
          </div>
        </div>
      </div>

      {/* API Configuration Settings */}
      <div className="bg-white rounded-lg border border-gray-200 divide-y divide-gray-200">
        <div className="p-4 bg-gray-50">
          <h3 className="text-lg font-medium text-gray-900">API Configuration</h3>
        </div>
        {localSettings.map((setting) => (
          <div key={setting.id} className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-900 mb-1">
                  {setting.key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </label>
                <p className="text-sm text-gray-600 mb-3">{setting.description}</p>
                
                {setting.key.includes('enable') || setting.key.includes('Enable') ? (
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={setting.id}
                      checked={setting.value === 'true'}
                      onChange={(e) => handleChange(setting.id, e.target.checked ? 'true' : 'false')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor={setting.id} className="ml-2 text-sm text-gray-700">
                      {setting.value === 'true' ? 'Enabled' : 'Disabled'}
                    </label>
                  </div>
                ) : setting.key.includes('cors') || setting.key.includes('CORS') ? (
                  <div className="space-y-2">
                    <textarea
                      value={setting.value}
                      onChange={(e) => handleChange(setting.id, e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter allowed origins (one per line)\ne.g., https://example.com"
                    />
                    <p className="text-xs text-gray-500">Enter allowed origins, one per line. Use * for all origins (not recommended for production).</p>
                  </div>
                ) : isNumberField(setting.key) ? (
                  <div className="flex items-center gap-3">
                    <input
                      type="number"
                      value={setting.value}
                      onChange={(e) => handleChange(setting.id, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder={`Enter ${setting.key}`}
                      min="0"
                    />
                    {setting.key.includes('timeout') && (
                      <span className="text-sm text-gray-500">seconds</span>
                    )}
                    {setting.key.includes('limit') && (
                      <span className="text-sm text-gray-500">requests/minute</span>
                    )}
                    {setting.key.includes('rate') && (
                      <span className="text-sm text-gray-500">requests/minute</span>
                    )}
                  </div>
                ) : isURLField(setting.key) ? (
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-gray-400" />
                    <input
                      type="url"
                      value={setting.value}
                      onChange={(e) => handleChange(setting.id, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder={`Enter ${setting.key} URL`}
                    />
                  </div>
                ) : setting.key.includes('key') || setting.key.includes('secret') || setting.key.includes('token') ? (
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-gray-400" />
                    <input
                      type="password"
                      value={setting.value}
                      onChange={(e) => handleChange(setting.id, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder={`Enter ${setting.key}`}
                    />
                  </div>
                ) : (
                  <input
                    type="text"
                    value={setting.value}
                    onChange={(e) => handleChange(setting.id, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder={`Enter ${setting.key}`}
                  />
                )}
              </div>
            </div>
            
            {setting.lastUpdated && (
              <div className="mt-3 text-xs text-gray-500">
                Last updated: {new Date(setting.lastUpdated).toLocaleString()} by {setting.updatedBy}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* API Documentation Links */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-2">
          <Code className="h-4 w-4 text-blue-600" />
          <span className="text-sm font-medium text-blue-900">API Documentation</span>
        </div>
        <p className="text-sm text-blue-700 mb-3">
          For detailed API documentation, authentication methods, and usage examples, please refer to our developer resources.
        </p>
        <div className="flex gap-3">
          <button className="text-sm text-blue-600 hover:text-blue-800 underline">
            View API Documentation
          </button>
          <button className="text-sm text-blue-600 hover:text-blue-800 underline">
            Download Postman Collection
          </button>
          <button className="text-sm text-blue-600 hover:text-blue-800 underline">
            API Status Page
          </button>
        </div>
      </div>
    </div>
  );
};

export default APISettings;