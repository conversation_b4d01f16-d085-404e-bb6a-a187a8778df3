@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-gradient-start: #4361ee;
  --primary-gradient-end: #7209b7;
  --secondary-color: #3a86ff;
  --text-primary: #1F2937;
  --text-secondary: #4B5563;
  --background-light: #F9FAFB;
  --card-background: #FFFFFF;
  --accent-color: #3a86ff;
  --tag-background: #EBF5FF;
  --tag-text: #2563EB;
  --border-color: #E5E7EB;
}

.dark {
  --primary-gradient-start: #3730a3;
  --primary-gradient-end: #6d28d9;
  --secondary-color: #4f46e5;
  --text-primary: #F9FAFB;
  --text-secondary: #D1D5DB;
  --background-light: #111827;
  --card-background: #1F2937;
  --accent-color: #818cf8;
  --tag-background: #374151;
  --tag-text: #a5b4fc;
  --border-color: #374151;
}

/* Main background styles */
body {
  background: linear-gradient(135deg, var(--primary-gradient-start), var(--primary-gradient-end));
  color: var(--text-primary);
  min-height: 100vh;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Card and container styles */
.knowledge-base-container {
  background-color: var(--background-light);
  border-radius: 1.5rem;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  margin: 2rem auto;
  max-width: 1200px;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.knowledge-base-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.05), transparent);
  z-index: 0;
}

.article-card {
  background-color: var(--card-background);
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  padding: 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
  position: relative;
  z-index: 1;
}

.article-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* Search bar styles */
.search-bar {
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 9999px;
  padding: 0.75rem 1.5rem;
  width: 100%;
  max-width: 600px;
  margin: 1rem auto;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-bar:focus {
  border-color: var(--accent-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Button styles */
.primary-button {
  background: linear-gradient(135deg, var(--primary-gradient-start), var(--primary-gradient-end));
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.primary-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

/* Category tag styles */
.category-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  background-color: var(--tag-background);
  color: var(--tag-text);
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  margin-right: 0.5rem;
}

/* Featured badge */
.featured-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  background-color: #FEF3C7;
  color: #D97706;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  margin-right: 0.5rem;
}

/* Wave decoration */
.wave-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%23FFFFFF'/%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' fill='%23FFFFFF'/%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' fill='%23FFFFFF'/%3E%3C/svg%3E") no-repeat;
  background-size: cover;
}

/* Force dark mode styling for all input types */
.dark input[type="text"],
.dark input[type="email"],
.dark input[type="tel"],
.dark input[type="date"],
.dark input[type="password"],
.dark input[type="number"],
.dark select,
.dark textarea {
  background-color: #374151 !important;
  color: #ffffff !important;
  border-color: #4b5563 !important;
}

.dark input[type="text"]:focus,
.dark input[type="email"]:focus,
.dark input[type="tel"]:focus,
.dark input[type="date"]:focus,
.dark input[type="password"]:focus,
.dark input[type="number"]:focus,
.dark select:focus,
.dark textarea:focus {
  background-color: #374151 !important;
  color: #ffffff !important;
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.5) !important;
}

.dark input[type="text"]::placeholder,
.dark input[type="email"]::placeholder,
.dark input[type="tel"]::placeholder,
.dark input[type="password"]::placeholder,
.dark input[type="number"]::placeholder,
.dark textarea::placeholder {
  color: #9ca3af !important;
}

/* Special handling for date inputs in dark mode */
.dark input[type="date"]::-webkit-calendar-picker-indicator {
  filter: invert(1);
}

.dark input[type="date"]::-webkit-datetime-edit {
  color: #ffffff !important;
}

.dark input[type="date"]::-webkit-datetime-edit-fields-wrapper {
  background-color: transparent !important;
}

.dark input[type="date"]::-webkit-datetime-edit-text {
  color: #ffffff !important;
}

.dark input[type="date"]::-webkit-datetime-edit-month-field,
.dark input[type="date"]::-webkit-datetime-edit-day-field,
.dark input[type="date"]::-webkit-datetime-edit-year-field {
  color: #ffffff !important;
}

/* Force dark mode styling for all headings and text elements */
.dark h1,
.dark h2,
.dark h3,
.dark h4,
.dark h5,
.dark h6 {
  color: #ffffff !important;
}

.dark label {
  color: #d1d5db !important;
}

.dark p {
  color: #e5e7eb !important;
}

.dark span {
  color: #e5e7eb !important;
}

/* Ensure error messages are visible in dark mode */
.dark .text-red-600 {
  color: #f87171 !important;
}

.dark .text-red-500 {
  color: #ef4444 !important;
}

/* Custom styles for sidebar transitions */
.sidebar-container {
  will-change: transform, width;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1), width 300ms cubic-bezier(0.4, 0, 0.2, 1);
  left: 0;
  margin-left: 0;
  padding-left: 0;
}

/* Specific styles for collapsed sidebar menu items */
.sidebar-container[data-collapsed="true"] .sidebar-menu-item {
  padding: 0.625rem !important;
  justify-content: center !important;
  align-items: center !important;
  position: relative;
}

.sidebar-container[data-collapsed="true"] .sidebar-menu-item svg {
  margin: 0 !important;
}

.sidebar-container[data-collapsed="true"] .sidebar-submenu {
  display: none;
}

.sidebar-force-reflow {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* Force hardware acceleration */
  transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);
}

/* Fix for sidebar toggle button */
.sidebar-toggle-button {
  cursor: pointer !important;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation !important;
  user-select: none;
  -webkit-user-select: none;
  z-index: 50;
  position: relative;
  min-width: 48px;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(55, 65, 81, 0.8);
  border-radius: 0.375rem;
  transition: background-color 0.2s ease, transform 0.2s ease;
  padding: 0.75rem;
  margin-left: -0.25rem;
  outline: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sidebar-toggle-button:hover {
  background-color: rgba(55, 65, 81, 0.9) !important;
}

.sidebar-toggle-button:active {
  background-color: rgba(55, 65, 81, 1) !important;
  transform: scale(0.98);
}

.sidebar-toggle-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* Ensure smooth transitions */
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  will-change: transform;
}

/* Header container */
.header-container {
  will-change: transform, left;
  z-index: 40;
  transition: left 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Improve mobile experience */
@media (max-width: 1023px) {
  .sidebar-container {
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
  }
  
  .sidebar-toggle-button {
    padding: 0.75rem;
    margin-left: -0.25rem;
    border-radius: 0.375rem;
    /* Increase touch target size */
    min-width: 50px;
    min-height: 50px;
  }
}

.sidebar-submenu {
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.sidebar-submenu-expanded {
  max-height: 500px; /* Adjust based on your needs */
}

.sidebar-submenu-collapsed {
  max-height: 0;
}

.sidebar-menu-item {
  transition: all 0.3s ease;
}

.sidebar-menu-item:active {
  transform: scale(0.98);
}

/* Main content transition to match sidebar */
.main-content {
  transition: padding-left 300ms cubic-bezier(0.4, 0, 0.2, 1);
  margin-left: 0 !important; /* Ensure no margin is applied */
}

/* Tooltip for collapsed sidebar */
.sidebar-container[data-collapsed="true"] .sidebar-menu-item {
  position: relative;
}

.sidebar-container[data-collapsed="true"] .sidebar-menu-item:hover::after {
  content: attr(aria-label);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(26, 34, 52, 0.95);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 100;
  margin-left: 0.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  pointer-events: none;
  animation: fadeIn 0.2s ease forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-50%) translateX(-5px); }
  to { opacity: 1; transform: translateY(-50%) translateX(0); }
}

/* Fix for collapsed sidebar */
.sidebar-container[data-collapsed="true"] {
  width: 4rem !important; /* Force 16px width */
}

/* Flyout submenu styles */
.flyout-submenu {
  animation: flyoutAppear 0.2s ease forwards;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(75, 85, 99, 0.4);
  overflow: hidden;
  z-index: 9999 !important;
  position: fixed;
  left: 4rem !important; /* Ensure it's positioned correctly relative to the collapsed sidebar */
  background-color: #1a2234;
  border-radius: 0.5rem;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
}

@keyframes flyoutAppear {
  from { 
    opacity: 0; 
    transform: translateX(-10px);
  }
  to { 
    opacity: 1; 
    transform: translateX(0);
  }
}

.flyout-submenu button {
  transition: all 0.2s ease;
  width: 100%;
  text-align: left;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.flyout-submenu button:hover {
  background-color: rgba(75, 85, 99, 0.3);
}

/* Ensure the sidebar menu items have a visual indicator when they have an active submenu */
.sidebar-menu-item[data-has-submenu="true"] {
  position: relative;
}

.sidebar-container[data-collapsed="true"] .sidebar-menu-item[data-has-submenu="true"][data-expanded="true"] {
  background-color: rgba(75, 85, 99, 0.3);
}

/* Animation for fading in elements */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}
