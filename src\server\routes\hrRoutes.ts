import { Router } from 'express';
import { auth } from '../middleware/auth';
import { checkHRPermission, checkDepartmentalHRAccess } from '../routers/hr/hrPermissionMiddleware';
import * as employeeController from '../controllers/employeeController';

const router = Router();

// Base authentication for all HR routes
router.use(auth);

// Employee management routes with permission checks
router.get('/employees', 
  checkHRPermission('canViewEmployees'), 
  (req, res) => employeeController.getAllEmployees(req, res)
);

router.get('/employees/:id', 
  checkHRPermission('canViewEmployees'), 
  (req, res) => employeeController.getEmployeeById(req, res)
);

router.post('/employees', 
  checkHRPermission('canCreateEmployee'), 
  (req, res) => employeeController.createEmployee(req, res)
);

router.put('/employees/:id', 
  checkHRPermission('canEditEmployee'), 
  (req, res) => employeeController.updateEmployee(req, res)
);

router.delete('/employees/:id', 
  checkHRPermission('canDeleteEmployee'), 
  (req, res) => employeeController.deleteEmployee(req, res)
);

// Department-specific routes
router.get('/department/:department/employees',
  checkHRPermission('canViewEmployees'),
  checkDepartmentalHRAccess(false), // Only allow access to user's department
  (req, res) => {
    // Filter employees by department
    const departmentName = req.params.department;
    req.query.department = departmentName;
    employeeController.getAllEmployees(req, res);
  }
);

// Import/Export routes
router.post('/employees/import',
  checkHRPermission('canCreateEmployee'),
  (req, res) => employeeController.importEmployees(req, res)
);

router.get('/employees/export',
  checkHRPermission('canViewEmployees'),
  employeeController.exportEmployees
);

// Attendance management
router.get('/attendance',
  checkHRPermission('canManageAttendance'),
  (req, res) => {
    res.status(501).json({ message: 'Attendance management not implemented yet' });
  }
);

router.post('/attendance',
  checkHRPermission('canManageAttendance'),
  (req, res) => {
    res.status(501).json({ message: 'Attendance management not implemented yet' });
  }
);

// Leave management
router.get('/leaves',
  checkHRPermission('canManageLeave'),
  (req, res) => {
    res.status(501).json({ message: 'Leave management not implemented yet' });
  }
);

router.post('/leaves',
  checkHRPermission('canManageLeave'),
  (req, res) => {
    res.status(501).json({ message: 'Leave management not implemented yet' });
  }
);

router.put('/leaves/:id',
  checkHRPermission('canManageLeave'),
  (req, res) => {
    res.status(501).json({ message: 'Leave management not implemented yet' });
  }
);

// Payroll routes
router.get('/payroll',
  checkHRPermission('canManagePayroll'),
  (req, res) => {
    res.status(501).json({ message: 'Payroll management not implemented yet' });
  }
);

router.post('/payroll',
  checkHRPermission('canManagePayroll'),
  (req, res) => {
    res.status(501).json({ message: 'Payroll management not implemented yet' });
  }
);

export default router;
