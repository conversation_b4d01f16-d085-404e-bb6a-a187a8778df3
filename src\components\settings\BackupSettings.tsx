import React, { useState, useEffect } from 'react';
import { Database, Save, AlertCircle, Download, Upload, HardDrive } from 'lucide-react';

interface Setting {
  id: string;
  category: string;
  key: string;
  value: string;
  description: string;
  lastUpdated?: string;
  updatedBy?: string;
}

interface BackupSettingsProps {
  settings: Setting[];
  onSettingChange: (id: string, value: string) => void;
  onSave: () => void;
  isSaving?: boolean;
  saveSuccess?: boolean;
}

const BackupSettings: React.FC<BackupSettingsProps> = ({
  settings,
  onSettingChange,
  onSave,
  isSaving = false,
  saveSuccess = false
}) => {
  const [localSettings, setLocalSettings] = useState<Setting[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [backupInProgress, setBackupInProgress] = useState(false);
  const [backupResult, setBackupResult] = useState<{success: boolean, message: string} | null>(null);

  useEffect(() => {
    const backupSettings = settings.filter(setting => setting.category === 'backup');
    setLocalSettings(backupSettings);
  }, [settings]);

  const handleChange = (id: string, value: string) => {
    setLocalSettings(prev => prev.map(setting => 
      setting.id === id ? { ...setting, value } : setting
    ));
    setHasChanges(true);
    onSettingChange(id, value);
  };

  const handleSave = () => {
    onSave();
    setHasChanges(false);
  };

  const handleBackupNow = async () => {
    setBackupInProgress(true);
    setBackupResult(null);
    
    // Simulate backup process
    setTimeout(() => {
      const autoBackupEnabled = localSettings.find(s => s.key === 'enableAutoBackup')?.value === 'true';
      
      if (autoBackupEnabled) {
        setBackupResult({
          success: true,
          message: 'Backup completed successfully! Data has been saved to the configured location.'
        });
      } else {
        setBackupResult({
          success: false,
          message: 'Backup failed. Please enable auto backup and configure storage location.'
        });
      }
      setBackupInProgress(false);
    }, 3000);
  };

  const isNumberField = (key: string) => {
    return key.toLowerCase().includes('retention') || key.toLowerCase().includes('size') || key.toLowerCase().includes('interval');
  };

  const isSelectField = (key: string) => {
    return key === 'backupFrequency';
  };

  const getBackupFrequencyOptions = () => [
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'custom', label: 'Custom' }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Database className="h-6 w-6 text-purple-600" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Backup & Storage Settings</h2>
            <p className="text-sm text-gray-600">Configure data backup, storage locations, and retention policies</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={handleBackupNow}
            disabled={backupInProgress}
            className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {backupInProgress ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Download className="h-4 w-4" />
            )}
            {backupInProgress ? 'Backing up...' : 'Backup Now'}
          </button>
          {hasChanges && (
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSaving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save className="h-4 w-4" />
              )}
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          )}
        </div>
      </div>

      {saveSuccess && (
        <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700">
          <AlertCircle className="h-4 w-4" />
          Backup settings saved successfully!
        </div>
      )}

      {backupResult && (
        <div className={`flex items-center gap-2 p-3 rounded-lg ${
          backupResult.success 
            ? 'bg-green-50 border border-green-200 text-green-700'
            : 'bg-red-50 border border-red-200 text-red-700'
        }`}>
          <AlertCircle className="h-4 w-4" />
          {backupResult.message}
        </div>
      )}

      {/* Storage Usage Overview */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center gap-3 mb-4">
          <HardDrive className="h-5 w-5 text-purple-600" />
          <h3 className="text-lg font-medium text-gray-900">Storage Usage</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm text-gray-600">Total Storage</div>
            <div className="text-2xl font-semibold text-gray-900">500 GB</div>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm text-gray-600">Used Storage</div>
            <div className="text-2xl font-semibold text-blue-600">125 GB</div>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm text-gray-600">Available Storage</div>
            <div className="text-2xl font-semibold text-green-600">375 GB</div>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex justify-between text-sm text-gray-600 mb-1">
            <span>Storage Usage</span>
            <span>25%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-blue-600 h-2 rounded-full" style={{width: '25%'}}></div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 divide-y divide-gray-200">
        {localSettings.map((setting) => (
          <div key={setting.id} className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-900 mb-1">
                  {setting.key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </label>
                <p className="text-sm text-gray-600 mb-3">{setting.description}</p>
                
                {setting.key.includes('enable') || setting.key.includes('Enable') ? (
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={setting.id}
                      checked={setting.value === 'true'}
                      onChange={(e) => handleChange(setting.id, e.target.checked ? 'true' : 'false')}
                      className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                    />
                    <label htmlFor={setting.id} className="ml-2 text-sm text-gray-700">
                      {setting.value === 'true' ? 'Enabled' : 'Disabled'}
                    </label>
                  </div>
                ) : isSelectField(setting.key) ? (
                  <select
                    value={setting.value}
                    onChange={(e) => handleChange(setting.id, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    {getBackupFrequencyOptions().map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                ) : isNumberField(setting.key) ? (
                  <input
                    type="number"
                    value={setting.value}
                    onChange={(e) => handleChange(setting.id, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder={`Enter ${setting.key}`}
                    min="0"
                  />
                ) : (
                  <input
                    type="text"
                    value={setting.value}
                    onChange={(e) => handleChange(setting.id, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder={`Enter ${setting.key}`}
                  />
                )}
              </div>
            </div>
            
            {setting.lastUpdated && (
              <div className="mt-3 text-xs text-gray-500">
                Last updated: {new Date(setting.lastUpdated).toLocaleString()} by {setting.updatedBy}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default BackupSettings;