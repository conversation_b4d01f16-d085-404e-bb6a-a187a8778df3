import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTicketLocking1740989157520 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add lockedById column
        await queryRunner.query(`ALTER TABLE tickets ADD COLUMN lockedById VARCHAR(36) NULL`);
        
        // Add lockedAt column
        await queryRunner.query(`ALTER TABLE tickets ADD COLUMN lockedAt TIMESTAMP NULL`);
        
        // Add foreign key constraint
        await queryRunner.query(`
            ALTER TABLE tickets 
            ADD CONSTRAINT FK_tickets_lockedById 
            FOREIGN KEY (lockedById) 
            REFERENCES users(id) 
            ON DELETE SET NULL
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key constraint
        await queryRunner.query(`ALTER TABLE tickets DROP CONSTRAINT IF EXISTS FK_tickets_lockedById`);
        
        // Drop lockedAt column
        await queryRunner.query(`ALTER TABLE tickets DROP COLUMN IF EXISTS lockedAt`);
        
        // Drop lockedById column
        await queryRunner.query(`ALTER TABLE tickets DROP COLUMN IF EXISTS lockedById`);
    }

}
