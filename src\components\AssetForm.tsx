import React, { useState, useEffect, ChangeEvent } from 'react';
import { toast } from 'react-hot-toast';
import { X, Save, FileImage, Tag, Box, Cpu, Hash, Barcode, Calendar, MapPin, AlertTriangle, User, Truck, Shield, Image as ImageIcon, Server, Settings, Building2, ShoppingBag, Camera, Monitor, Video, Printer as PrinterIcon, Monitor as ScannerIcon, Smartphone, HardDrive, Palette, DollarSign, Calculator, Clock, TrendingDown, Upload, Keyboard } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { AssetFrontend } from './AssetManagement';
import { UserSelectDropdown } from './UserSelectDropdown';
import { User as UserType } from '../types/user';

interface AssetFormProps {
  onSubmit: (asset: AssetFrontend, formData?: FormData) => Promise<boolean>;
  onClose: () => void;
  onSaveSuccess?: () => void;
  initialData?: AssetFrontend;
}

// Define a comprehensive interface for the form data
interface AssetFormData {
  // Basic Details
  id?: number; // Changed from string to number for auto-increment
  assetType: string;
  customAssetType: string;
  category: string;
  customCategory: string;
  manufacturer: string;
  customManufacturer: string;
  model: string;
  serialNumber: string;
  assetTag: string;
  status: string;
  purchaseDate: string;
  warrantyExpiration: string;
  vendor: string;
  project: string;
  customProject: string;
  location: string;
  customLocation: string;
  condition: string;
  customCondition: string;
  department: string;
  internetAccess: boolean;
  ipAddress: string;
  // Updated assignedTo to match AssetFrontend interface
  assignedTo: string | {
    id: string;
    name: string;
    department: string;
    assignedAt: string;
  }[];
  assignedToId?: string | null; // Update to allow null values
  assignmentDate: string;
  notes: string;
  createdAt?: string;
  
  // Computing Attributes
  operatingSystem: string;
  processor: string;
  ramCapacity: string;
  storageType: string;
  storageCapacity: string;
  graphicsCard: string;
  hardDiskCount: string;
  hardDiskDetails: string;
  
  // Mobile & Tablet Attributes
  mobileOperatingSystem: string;
  mobileRam: string;
  mobileStorage: string;
  imei: string;
  simNumber: string;
  color: string;
  
  // Display & Multimedia Attributes
  screenSize: string;
  resolution: string;
  smartTv: string;
  hdmiPorts: string;
  usbPorts: string;
  ledScreenSize: string;
  ledBrand: string;
  projectorType: string;
  brightness: string;
  audioType: string;
  connectionInterface: string;
  powerOutput: string;
  frequencyResponse: string;
  conferenceType: string;
  maxParticipants: string;
  videoResolution: string;
  compatibleSoftware: string;
  
  // Security & Surveillance Attributes
  cameraResolution: string;
  cameraFOV: string;
  cameraNightVision: string;
  cameraZoom: string;
  cameraCategory: string;
  dvrChannelCount: string;
  dvrRecordingResolution: string;
  biometricType: string;
  userCapacity: string;
  accessControlType: string;
  doorCapacity: string;
  
  // Printing Attributes
  printerSpeed: string;
  printerColor: string;
  scannerResolution: string;
  scannerType: string;
  printerTechnology: string;
  buildVolume: string;
  filamentType: string;
  scanSpeed: string;
  copySpeed: string;
  copyResolution: string;
  copyColor: string;
  paperCapacity: string;
  plotterWidth: string;
  plotterResolution: string;
  plotterColor: string;
  labelWidth: string;
  printMethod: string;
  labelResolution: string;
  
  // Networking & Communication Attributes
  networkType: string;
  portCount: string;
  macAddress: string;
  cableType: string;
  cableLength: string;
  extensionNumber: string;
  sipAccount: string;
  pbxExtensionCapacity: string;
  pbxType: string;
  
  // Peripherals & Accessories Attributes
  peripheralType: string;
  connectionType: string;
  compatibility: string;
  keyboardType: string;
  keyboardLayout: string;
  mouseType: string;
  dpi: string;
  webcamResolution: string;
  microphone: string;
  
  // Maintenance
  lastMaintenance: string;
  maintenanceBy: string;
  issueReported: string;
  nextMaintenance: string;
  
  // Financial
  purchasePrice: string;
  maintenanceCost: string;
  estimatedLifespan: string;
  salvageValue: string;
  annualOperatingCost: string;
  insuranceCost: string;
}

const AssetForm: React.FC<AssetFormProps> = ({ onSubmit, onClose, onSaveSuccess, initialData }) => {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // categoryMap (for Asset Type -> Category options)
  const categoryMap: { [key: string]: Array<string> } = {
    'Computing': ['Desktop', 'Laptop', 'Server', 'Workstation', 'Mini PC', 'Embedded System', 'Other'],
    'Mobile & Tablet': ['Smartphone', 'Tablet', 'Feature Phone', 'PDAs & Rugged Devices', 'Wearables', 'Other'],
    'Networking & Communication': ['Router & Modem', 'Switch', 'Access Point', 'VoIP Phone', 'Gateway', 'Firewall', 'Load Balancer', 'Controller', 'Cables & Connectors', 'PBX', 'Other'],
    'Printing': ['Inkjet Printer', 'Laser Printer', '3D Printer', 'Scanner', 'Photo Copier', 'Plotter', 'Label Printer', 'Dot Matrix Printer', 'Other'],
    'Display & Multimedia': ['Monitor', 'Projector', 'TV', 'LED Display', 'Digital Signage', 'Speakers & Microphones', 'Conference Equipment', 'Other'],
    'Security & Surveillance': ['CCTV Camera', 'IP Camera', 'NVR', 'DVR', 'Biometric Device', 'RFID Scanner', 'Access Control', 'Alarm System', 'Other'],
    'Peripherals & Accessories': ['Keyboard', 'Mouse', 'Webcam', 'Docking Station', 'External Storage', 'Headset', 'Other'],
    'Other Equipment': ['Other']
  };

  // Projects and Locations
  const PROJECTS = [
    'Eurobiz Corporations',
    'Guardian International',
    'Guardian Developers',
    'Grand Developers',
    'ARD Developers'
  ];

  const PROJECT_LOCATIONS: Record<string, string[]> = {
    'Eurobiz Corporations': [
      'Grand City Head Office Lahore',
      'Grand City Site Office Kharian'
    ],
    'Guardian International': [
      'Grand City Head Office Lahore',
      'Grand City Site Office Arifwala'
    ],
    'Guardian Developers': [
      'Grand City Head Office Lahore',
      'Grand City Site Office Vehari'
    ],
    'Grand Developers': [
      'Grand City Head Office Lahore',
      'Grand City Site Office Faisalabad',
      'Grand City Site Office Murree'
    ],
    'ARD Developers': [
      'Grand City Head Office Lahore'
    ]
  };
  
  // Get all unique locations
  const ALL_LOCATIONS = Array.from(
    new Set(
      Object.values(PROJECT_LOCATIONS).flat()
    )
  );

  // Initialize form data with proper typing
  const [formData, setFormData] = useState<AssetFormData>(() => {
    console.log('Initializing form data, initialData:', initialData ? 'present' : 'not present');
    
    // Default empty form data with all required fields
    const defaultFormData: AssetFormData = {
      // Basic Details
      assetType: '',
      customAssetType: '',
      category: '',
      customCategory: '',
      manufacturer: '',
      customManufacturer: '',
      model: '',
      serialNumber: '',
      assetTag: '',
      status: 'Active',
      purchaseDate: '',
      warrantyExpiration: '',
      vendor: '',
      project: '',
      customProject: '',
      location: '',
      customLocation: '',
      condition: 'New',
      customCondition: '',
      department: '',
      internetAccess: false,
      ipAddress: '',
      assignedTo: [],
      assignmentDate: '',
      notes: '',
      
      // Computing Attributes
      operatingSystem: '',
      processor: '',
      ramCapacity: '',
      storageType: '',
      storageCapacity: '',
      graphicsCard: '',
      hardDiskCount: '',
      hardDiskDetails: '',
      
      // Mobile & Tablet Attributes
      mobileOperatingSystem: '',
      mobileRam: '',
      mobileStorage: '',
      imei: '',
      simNumber: '',
      color: '',
      
      // Display & Multimedia Attributes
      screenSize: '',
      resolution: '',
      smartTv: 'Yes',
      hdmiPorts: '',
      usbPorts: '',
      ledScreenSize: '',
      ledBrand: '',
      projectorType: '',
      brightness: '',
      audioType: '',
      connectionInterface: '',
      powerOutput: '',
      frequencyResponse: '',
      conferenceType: '',
      maxParticipants: '',
      videoResolution: '',
      compatibleSoftware: '',
      
      // Security & Surveillance Attributes
      cameraResolution: '',
      cameraFOV: '',
      cameraNightVision: 'Yes',
      cameraZoom: '',
      cameraCategory: '',
      dvrChannelCount: '',
      dvrRecordingResolution: '',
      biometricType: '',
      userCapacity: '',
      accessControlType: '',
      doorCapacity: '',
      
      // Printing Attributes
      printerSpeed: '',
      printerColor: 'No',
      scannerResolution: '',
      scannerType: '',
      printerTechnology: '',
      buildVolume: '',
      filamentType: '',
      scanSpeed: '',
      copySpeed: '',
      copyResolution: '',
      copyColor: '',
      paperCapacity: '',
      plotterWidth: '',
      plotterResolution: '',
      plotterColor: '',
      labelWidth: '',
      printMethod: '',
      labelResolution: '',
      
      // Networking & Communication Attributes
      networkType: '',
      portCount: '',
      macAddress: '',
      cableType: '',
      cableLength: '',
      extensionNumber: '',
      sipAccount: '',
      pbxExtensionCapacity: '',
      pbxType: '',
      
      // Peripherals & Accessories Attributes
      peripheralType: '',
      connectionType: '',
      compatibility: '',
      keyboardType: '',
      keyboardLayout: '',
      mouseType: '',
      dpi: '',
      webcamResolution: '',
      microphone: '',
      
      // Maintenance
      lastMaintenance: '',
      maintenanceBy: '',
      issueReported: '',
      nextMaintenance: '',
      
      // Financial
      purchasePrice: '',
      maintenanceCost: '',
      estimatedLifespan: '5',
      salvageValue: '',
      annualOperatingCost: '',
      insuranceCost: '',
    };

    // Only use initialData if it's provided, otherwise use empty defaults
    if (initialData) {
      // If we have initial data, convert from AssetFrontend to AssetFormData
      // Start with the default form data to ensure all fields are present
      const formInitialData: AssetFormData = {
        ...defaultFormData,
        // Override with values from initialData
        id: initialData.id ? Number(initialData.id) : undefined, // Convert to number
        assetType: initialData.assetType || '',
        category: initialData.category || '',
        manufacturer: initialData.manufacturer || '',
        model: initialData.model || '',
        serialNumber: initialData.serialNumber || '',
        status: initialData.status ? (initialData.status.charAt(0).toUpperCase() + initialData.status.slice(1).toLowerCase()) : 'Active',
        purchaseDate: initialData.purchaseDate || '',
        warrantyExpiration: initialData.warrantyExpiry || '',
        vendor: initialData.vendor || '',
        location: initialData.location || '',
        condition: initialData.condition || '',
        department: initialData.department || user?.department || '',
        // Update to use the array directly if it exists, otherwise empty string
        assignedTo: initialData.assignedTo && initialData.assignedTo.length > 0 ? initialData.assignedTo : [],
        assignmentDate: initialData.assignedTo?.[0]?.assignedAt || '',
        notes: initialData.notes || '',
        createdAt: initialData.createdAt,
        // Round any currency values when loading and ensure they're not negative
        purchasePrice: initialData.cost ? Math.max(0, Math.round(Number(initialData.cost))).toString() : '',
        maintenanceCost: initialData.attributes?.maintenanceCost ? Math.max(0, Math.round(Number(initialData.attributes.maintenanceCost))).toString() : '',
        salvageValue: initialData.attributes?.salvageValue ? Math.max(0, Math.round(Number(initialData.attributes.salvageValue))).toString() : '',
        annualOperatingCost: initialData.attributes?.annualOperatingCost ? Math.max(0, Math.round(Number(initialData.attributes.annualOperatingCost))).toString() : '',
        insuranceCost: initialData.attributes?.insuranceCost ? Math.max(0, Math.round(Number(initialData.attributes.insuranceCost))).toString() : '',
      };
      
      // Copy attributes from initialData if they exist
      if (initialData.attributes) {
        // Extract attributes based on asset type
        const attrs = initialData.attributes;
        
        // Computing attributes
        if (attrs.operatingSystem) formInitialData.operatingSystem = attrs.operatingSystem;
        if (attrs.processor) formInitialData.processor = attrs.processor;
        if (attrs.ramCapacity) formInitialData.ramCapacity = attrs.ramCapacity;
        if (attrs.storageType) formInitialData.storageType = attrs.storageType;
        if (attrs.storageCapacity) formInitialData.storageCapacity = attrs.storageCapacity;
        if (attrs.graphicsCard) formInitialData.graphicsCard = attrs.graphicsCard;
        if (attrs.hardDiskCount) formInitialData.hardDiskCount = attrs.hardDiskCount;
        if (attrs.hardDiskDetails) formInitialData.hardDiskDetails = attrs.hardDiskDetails;
        
        // Mobile attributes
        if (initialData.assetType === 'Mobile & Tablet') {
          if (attrs.operatingSystem) formInitialData.mobileOperatingSystem = attrs.operatingSystem;
          if (attrs.ramCapacity) formInitialData.mobileRam = attrs.ramCapacity;
          if (attrs.storageCapacity) formInitialData.mobileStorage = attrs.storageCapacity;
          if (attrs.imei) formInitialData.imei = attrs.imei;
          if (attrs.simNumber) formInitialData.simNumber = attrs.simNumber;
          if (attrs.color) formInitialData.color = attrs.color;
        }
        
        // Display attributes
        if (initialData.assetType === 'Display & Multimedia') {
          if (attrs.screenSize) formInitialData.screenSize = attrs.screenSize;
          if (attrs.resolution) formInitialData.resolution = attrs.resolution;
          if (attrs.smartTv) formInitialData.smartTv = attrs.smartTv;
          if (attrs.hdmiPorts) formInitialData.hdmiPorts = attrs.hdmiPorts;
          if (attrs.usbPorts) formInitialData.usbPorts = attrs.usbPorts;
        }
        
        // Security & Surveillance attributes
        if (initialData.assetType === 'Security & Surveillance') {
          if (attrs.resolution) formInitialData.cameraResolution = attrs.resolution;
          if (attrs.fov) formInitialData.cameraFOV = attrs.fov;
          if (attrs.nightVision) formInitialData.cameraNightVision = attrs.nightVision;
          if (attrs.zoom) formInitialData.cameraZoom = attrs.zoom;
          if (attrs.category) formInitialData.cameraCategory = attrs.category;
          
          // DVR specific attributes
          if (initialData.category === 'DVR' || initialData.category === 'NVR') {
            if (attrs.channelCount) formInitialData.dvrChannelCount = attrs.channelCount;
            if (attrs.recordingResolution) formInitialData.dvrRecordingResolution = attrs.recordingResolution;
          }
        }
        
        // Printing attributes
        if (initialData.assetType === 'Printing') {
          if (attrs.printerSpeed) formInitialData.printerSpeed = attrs.printerSpeed;
          if (attrs.printerColor) formInitialData.printerColor = attrs.printerColor;
          if (attrs.scannerResolution) formInitialData.scannerResolution = attrs.scannerResolution;
          if (attrs.scannerType) formInitialData.scannerType = attrs.scannerType;
          if (attrs.printerTechnology) formInitialData.printerTechnology = attrs.printerTechnology;
          if (attrs.buildVolume) formInitialData.buildVolume = attrs.buildVolume;
          if (attrs.filamentType) formInitialData.filamentType = attrs.filamentType;
          if (attrs.scanSpeed) formInitialData.scanSpeed = attrs.scanSpeed;
          if (attrs.copySpeed) formInitialData.copySpeed = attrs.copySpeed;
          if (attrs.copyResolution) formInitialData.copyResolution = attrs.copyResolution;
          if (attrs.copyColor) formInitialData.copyColor = attrs.copyColor;
          if (attrs.paperCapacity) formInitialData.paperCapacity = attrs.paperCapacity;
          if (attrs.plotterWidth) formInitialData.plotterWidth = attrs.plotterWidth;
          if (attrs.plotterResolution) formInitialData.plotterResolution = attrs.plotterResolution;
          if (attrs.plotterColor) formInitialData.plotterColor = attrs.plotterColor;
          if (attrs.labelWidth) formInitialData.labelWidth = attrs.labelWidth;
          if (attrs.printMethod) formInitialData.printMethod = attrs.printMethod;
          if (attrs.labelResolution) formInitialData.labelResolution = attrs.labelResolution;
        }
        
        // Display & Multimedia attributes
        if (initialData.assetType === 'Display & Multimedia') {
          if (attrs.screenSize) formInitialData.screenSize = attrs.screenSize;
          if (attrs.resolution) formInitialData.resolution = attrs.resolution;
          if (attrs.smartTv) formInitialData.smartTv = attrs.smartTv;
          if (attrs.hdmiPorts) formInitialData.hdmiPorts = attrs.hdmiPorts;
          if (attrs.usbPorts) formInitialData.usbPorts = attrs.usbPorts;
          
          // LED Display specific attributes
          if (initialData.category === 'LED Display' || initialData.category === 'LED TV') {
            if (attrs.ledScreenSize) formInitialData.ledScreenSize = attrs.ledScreenSize;
            if (attrs.ledBrand) formInitialData.ledBrand = attrs.ledBrand;
          }
        }
        
        // Peripherals & Accessories attributes
        if (initialData.assetType === 'Peripherals & Accessories') {
          if (attrs.peripheralType) formInitialData.peripheralType = attrs.peripheralType;
          if (attrs.connectionType) formInitialData.connectionType = attrs.connectionType;
          if (attrs.compatibility) formInitialData.compatibility = attrs.compatibility;
        }
        
        // Networking & Communication attributes
        if (initialData.assetType === 'Networking & Communication') {
          if (attrs.networkType) formInitialData.networkType = attrs.networkType;
          if (attrs.portCount) formInitialData.portCount = attrs.portCount;
          if (attrs.macAddress) formInitialData.macAddress = attrs.macAddress;
          if (attrs.cableType) formInitialData.cableType = attrs.cableType;
          if (attrs.cableLength) formInitialData.cableLength = attrs.cableLength;
          if (attrs.extensionNumber) formInitialData.extensionNumber = attrs.extensionNumber;
          if (attrs.sipAccount) formInitialData.sipAccount = attrs.sipAccount;
          if (attrs.pbxExtensionCapacity) formInitialData.pbxExtensionCapacity = attrs.pbxExtensionCapacity;
          if (attrs.pbxType) formInitialData.pbxType = attrs.pbxType;
        }
      }
      
      return formInitialData;
    } else {
      // Return the default form data
      console.log('Using default form data with empty serialNumber');
      return defaultFormData;
    }
  });

  // Explicitly clear the serial number field on component mount
  useEffect(() => {
    // Only run this effect once on component mount
    if (!initialData) {
      console.log('Explicitly clearing serial number field on component mount');
      setTimeout(() => {
        setFormData(currentData => ({
          ...currentData,
          serialNumber: ''
        }));
      }, 0);
    }
  }, []); // Empty dependency array means this runs once on mount
  
  // Add a debug effect to track serialNumber changes
  useEffect(() => {
    console.log('Serial number value:', formData.serialNumber);
    if (formData.serialNumber && formData.serialNumber.startsWith('SN-') && !initialData) {
      console.log('Auto-generated serial number detected, clearing it');
      setFormData(currentData => ({
        ...currentData,
        serialNumber: ''
      }));
    }
  }, [formData.serialNumber, initialData]);

  // General input change handler
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    // Special handling for serialNumber field
    if (name === 'serialNumber') {
      console.log('Serial number changed via input to:', value);
    }
    
    // Handle checkbox inputs
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData({
        ...formData,
        [name]: checked
      });
      return;
    }
    
    // Handle currency inputs - ensure positive whole numbers
    if (type === 'number' && ['purchasePrice', 'maintenanceCost', 'salvageValue', 'annualOperatingCost', 'insuranceCost'].includes(name)) {
      const numValue = Number(value);
      // Only update if the value is positive or empty
      if (value === '' || numValue >= 0) {
        setFormData({
          ...formData,
          [name]: value === '' ? '' : Math.round(numValue).toString()
        });
      }
      return;
    }
    
    // Handle all other inputs
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Special handler for assetType to reset dependent fields
  const handleAssetTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const assetType = e.target.value;
    setFormData((prev) => ({
      ...prev,
      assetType,
      category: '',
      customCategory: '',
      manufacturer: '',
      customManufacturer: '',
      // Reset nested attributes when asset type changes
      attributes: {
        operatingSystem: '',
        processor: '',
        ramCapacity: '',
        storageType: '',
        storageCapacity: '',
        mobileRam: '',
        mobileStorage: '',
        imei: '',
      },
    }));
  };

  // Generic handler for fields that need a custom "Other" value
  const handleSelectWithOther = (e: React.ChangeEvent<HTMLSelectElement>, field: string, customField: string) => {
    const value = e.target.value;
    setFormData((prev) => {
      // Create a new object with the updated fields
      const updatedData = { ...prev };
      
      // Update the field with the selected value
      (updatedData as any)[field] = value;
      
      // If value is "Other", keep the custom field value, otherwise clear it
      if (value === 'Other') {
        // Keep the existing custom field value
        (updatedData as any)[customField] = (prev as any)[customField];
      } else {
        // Clear the custom field
        (updatedData as any)[customField] = '';
      }
      
      return updatedData;
    });
  };

  const [selectedUser, setSelectedUser] = useState<UserType | undefined>(() => {
    // Check if initialData has assignedTo data
    if (initialData?.assignedTo && typeof initialData.assignedTo === 'object') {
      const assignedUser = Array.isArray(initialData.assignedTo) 
        ? initialData.assignedTo[0] 
        : initialData.assignedTo;

      if (assignedUser) {
        return {
          id: assignedUser.id,
          name: assignedUser.name,
          department: assignedUser.department || '',
          email: '',
          password: '',
          project: null,
          location: null,
          role: 'EMPLOYEE',
          permissions: [],
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };
      }
    }
    return undefined;
  });

  const handleUserSelect = (user: UserType) => {
    console.log('User selected:', user);
    console.log('User ID type:', typeof user.id);
    console.log('User ID value:', user.id);
    
    if (!user.id) {
      // User is being unassigned
      setSelectedUser(undefined);
      setFormData(prev => ({
        ...prev,
        assignedTo: [], // Set to empty array instead of empty string
        assignedToId: null,
        // Keep the existing department if it's already set
        department: prev.department || ''
      }));
      return;
    }
    
    // Ensure user ID is a string
    const userId = String(user.id);
    console.log('Converted user ID to string:', userId);
    
    setSelectedUser(user);
    
    // Create the user object for assignedTo
    const userObj = {
      id: userId,
      name: user.name,
      department: user.department || '',
      assignedAt: new Date().toISOString()
    };
    console.log('Created user object for assignedTo:', userObj);
    
    // Update formData with both assignedTo array and assignedToId
    setFormData(prevFormData => {
      const updated = {
        ...prevFormData,
        assignedTo: [userObj], // Always set as an array with the user object
        assignedToId: userId, // Explicitly set assignedToId field
        // Update department only if it's not already set
        department: prevFormData.department || user.department || ''
      };
      console.log('Updated formData with assignedTo array and assignedToId:', updated.assignedTo);
      console.log('Updated assignedToId:', updated.assignedToId);
      console.log('Updated department:', updated.department);
      return updated;
    });
  };

  // Add a function to ensure we're working with a valid File object
  const ensureFileObject = (fileOrBlob: File | Blob): File => {
    if (fileOrBlob instanceof File) {
      return fileOrBlob;
    }
    // If it's a Blob but not a File, convert it
    return new File([fileOrBlob], 'asset-image.png', { 
      type: fileOrBlob.type || 'image/png',
      lastModified: Date.now()
    });
  };

  // Add a helper function to format dates properly for the backend
  const formatDateForBackend = (dateString?: string): string | undefined => {
    if (!dateString) return undefined;
    
    try {
      // Handle ISO date strings with time components (like 2025-03-13T07:03:29.594Z)
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        console.warn(`Invalid date: ${dateString}`);
        return undefined;
      }
      
      // Format as yyyy-MM-dd
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return undefined;
    }
  };

  // Fix the submit handler
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Create a copy of the form data for submission
      const submissionData = { ...formData };
      
      // Handle cost/price fields - ensure whole numbers
      if (submissionData.purchasePrice) {
        // Convert to whole number
        submissionData.purchasePrice = Math.round(Number(submissionData.purchasePrice)).toString();
      }
      
      // Create the asset data object
      const assetData: Partial<AssetFrontend> = {
        id: submissionData.id,
        assetType: submissionData.assetType === 'Other' ? submissionData.customAssetType : submissionData.assetType,
        category: submissionData.category === 'Other' ? submissionData.customCategory : submissionData.category,
        manufacturer: submissionData.manufacturer === 'Other' ? submissionData.customManufacturer : submissionData.manufacturer,
        model: submissionData.model,
        serialNumber: submissionData.serialNumber,
        status: submissionData.status,
        condition: submissionData.condition === 'Other' ? submissionData.customCondition : submissionData.condition,
        location: submissionData.location === 'Other' ? submissionData.customLocation : submissionData.location,
        department: submissionData.department,
        purchaseDate: submissionData.purchaseDate || undefined,
        warrantyExpiry: submissionData.warrantyExpiration || undefined,
        cost: submissionData.purchasePrice ? Number(submissionData.purchasePrice) : undefined,
        vendor: submissionData.vendor || undefined,
        project: submissionData.project === 'Other' ? submissionData.customProject : submissionData.project,
        internetAccess: submissionData.internetAccess,
        ipAddress: submissionData.ipAddress || undefined,
        notes: submissionData.notes || undefined,
        // ... rest of the existing code ...
      };

      // Add assignedTo information if a user is selected
      if (Array.isArray(submissionData.assignedTo) && submissionData.assignedTo.length > 0) {
        console.log('Using assignedTo from formData:', submissionData.assignedTo);
        console.log('First user in assignedTo array:', submissionData.assignedTo[0]);
        console.log('First user ID:', submissionData.assignedTo[0].id);
        console.log('First user ID type:', typeof submissionData.assignedTo[0].id);
        
        // Ensure the ID is a string
        const userId = String(submissionData.assignedTo[0].id);
        console.log('Converted user ID to string:', userId);
        
        const assignedToWithStringId = submissionData.assignedTo.map(user => ({
          ...user,
          id: String(user.id)
        }));
        
        assetData.assignedTo = assignedToWithStringId;
        // Explicitly set assignedToId field for the backend
        assetData.assignedToId = userId;
        console.log('Asset assigned to user:', assignedToWithStringId[0].name);
        console.log('Final assignedTo data:', assetData.assignedTo);
        console.log('Final assignedToId:', assetData.assignedToId);
      } else if (selectedUser) {
        // Fallback to selectedUser if formData.assignedTo is not an array
        console.log('Using selectedUser as fallback:', selectedUser);
        console.log('selectedUser ID:', selectedUser.id);
        console.log('selectedUser ID type:', typeof selectedUser.id);
        
        // Ensure the ID is a string
        const userId = String(selectedUser.id);
        console.log('Converted user ID to string:', userId);
        
        assetData.assignedTo = [{
          id: userId,
          name: selectedUser.name,
          department: selectedUser.department || '',
          assignedAt: new Date().toISOString()
        }];
        // Explicitly set assignedToId field for the backend
        assetData.assignedToId = userId;
        console.log('Asset assigned to user:', selectedUser.name);
        console.log('Final assignedTo data:', assetData.assignedTo);
        console.log('Final assignedToId:', assetData.assignedToId);
      } else {
        console.log('No user selected for assignment');
        // Explicitly set assignedToId to null to clear any existing assignment
        assetData.assignedToId = null;
      }

      // Add attributes based on asset type
      if (submissionData.assetType === 'Computing') {
        assetData.attributes = {
          operatingSystem: submissionData.operatingSystem,
          processor: submissionData.processor,
          ramCapacity: submissionData.ramCapacity,
          storageType: submissionData.storageType,
          storageCapacity: submissionData.storageCapacity,
          graphicsCard: submissionData.graphicsCard,
          hardDiskCount: submissionData.hardDiskCount,
          hardDiskDetails: submissionData.hardDiskDetails,
        };
      } else if (submissionData.assetType === 'Mobile & Tablet') {
        assetData.attributes = {
          mobileOperatingSystem: submissionData.mobileOperatingSystem,
          mobileRam: submissionData.mobileRam,
          mobileStorage: submissionData.mobileStorage,
          imei: submissionData.imei,
          simNumber: submissionData.simNumber,
          color: submissionData.color,
        };
      } else if (submissionData.assetType === 'Networking & Communication') {
        assetData.attributes = {
          networkType: submissionData.networkType,
          portCount: submissionData.portCount,
          macAddress: submissionData.macAddress,
          cableType: submissionData.cableType,
          cableLength: submissionData.cableLength,
          extensionNumber: submissionData.extensionNumber,
          sipAccount: submissionData.sipAccount,
          pbxExtensionCapacity: submissionData.pbxExtensionCapacity,
          pbxType: submissionData.pbxType,
        };
      } else if (submissionData.assetType === 'Security & Surveillance') {
        // Base attributes for Security & Surveillance
        assetData.attributes = {};
        
        // Camera specific attributes
        if (submissionData.category === 'CCTV Camera' || submissionData.category === 'IP Camera') {
          assetData.attributes = {
            cameraResolution: submissionData.cameraResolution,
            cameraFOV: submissionData.cameraFOV,
            cameraNightVision: submissionData.cameraNightVision,
            cameraZoom: submissionData.cameraZoom,
          };
        }
        
        // NVR/DVR specific attributes
        else if (submissionData.category === 'NVR' || submissionData.category === 'DVR') {
          assetData.attributes = {
            dvrChannelCount: submissionData.dvrChannelCount,
            dvrRecordingResolution: submissionData.dvrRecordingResolution,
          };
        }
        
        // Biometric Device specific attributes
        else if (submissionData.category === 'Biometric Device') {
          assetData.attributes = {
            biometricType: submissionData.biometricType,
            userCapacity: submissionData.userCapacity,
          };
        }
        
        // Access Control specific attributes
        else if (submissionData.category === 'Access Control') {
          assetData.attributes = {
            accessControlType: submissionData.accessControlType,
            doorCapacity: submissionData.doorCapacity,
          };
        }
      } else if (submissionData.assetType === 'Printing') {
        // Base attributes for Printing
        assetData.attributes = {
          printerColor: submissionData.printerColor,
          printerSpeed: submissionData.printerSpeed,
        };
        
        // 3D Printer specific attributes
        if (submissionData.category === '3D Printer') {
          assetData.attributes = {
            ...assetData.attributes,
            printerTechnology: submissionData.printerTechnology,
            buildVolume: submissionData.buildVolume,
            filamentType: submissionData.filamentType,
          };
        }
        
        // Scanner specific attributes
        else if (submissionData.category === 'Scanner') {
          assetData.attributes = {
            ...assetData.attributes,
            scannerType: submissionData.scannerType,
            scannerResolution: submissionData.scannerResolution,
            scanSpeed: submissionData.scanSpeed,
          };
        }
        
        // Photo Copier specific attributes
        else if (submissionData.category === 'Photo Copier') {
          assetData.attributes = {
            ...assetData.attributes,
            copySpeed: submissionData.copySpeed,
            copyResolution: submissionData.copyResolution,
            copyColor: submissionData.copyColor,
            paperCapacity: submissionData.paperCapacity,
          };
        }
      } else if (submissionData.assetType === 'Display & Multimedia') {
        // Base attributes for Display & Multimedia
        assetData.attributes = {
          screenSize: submissionData.screenSize,
          resolution: submissionData.resolution,
        };
        
        // TV specific attributes
        if (submissionData.category === 'TV' || submissionData.category === 'Smart TV') {
          assetData.attributes = {
            ...assetData.attributes,
            smartTv: submissionData.smartTv,
            hdmiPorts: submissionData.hdmiPorts,
            usbPorts: submissionData.usbPorts,
          };
        }
        
        // LED Display specific attributes
        else if (submissionData.category === 'LED Display') {
          assetData.attributes = {
            ...assetData.attributes,
            ledScreenSize: submissionData.ledScreenSize,
            ledBrand: submissionData.ledBrand,
          };
        }
        
        // Projector specific attributes
        else if (submissionData.category === 'Projector') {
          assetData.attributes = {
            ...assetData.attributes,
            projectorType: submissionData.projectorType,
            brightness: submissionData.brightness,
          };
        }
        
        // Speakers & Microphones specific attributes
        else if (submissionData.category === 'Speakers & Microphones') {
          assetData.attributes = {
            ...assetData.attributes,
            audioType: submissionData.audioType,
            connectionInterface: submissionData.connectionInterface,
            powerOutput: submissionData.powerOutput,
            frequencyResponse: submissionData.frequencyResponse,
          };
        }
        
        // Conference Equipment specific attributes
        else if (submissionData.category === 'Conference Equipment') {
          assetData.attributes = {
            ...assetData.attributes,
            conferenceType: submissionData.conferenceType,
            maxParticipants: submissionData.maxParticipants,
            videoResolution: submissionData.videoResolution,
            compatibleSoftware: submissionData.compatibleSoftware,
          };
        }
      } else if (submissionData.assetType === 'Peripherals & Accessories') {
        // Base attributes for Peripherals & Accessories
        assetData.attributes = {
          connectionType: submissionData.connectionType,
          compatibility: submissionData.compatibility,
        };
        
        // Keyboard specific attributes
        if (submissionData.category === 'Keyboard') {
          assetData.attributes = {
            ...assetData.attributes,
            keyboardType: submissionData.keyboardType,
            keyboardLayout: submissionData.keyboardLayout,
          };
        }
        
        // Mouse specific attributes
        else if (submissionData.category === 'Mouse') {
          assetData.attributes = {
            ...assetData.attributes,
            mouseType: submissionData.mouseType,
            dpi: submissionData.dpi,
          };
        }
        
        // Webcam specific attributes
        else if (submissionData.category === 'Webcam') {
          assetData.attributes = {
            ...assetData.attributes,
            webcamResolution: submissionData.webcamResolution,
            microphone: submissionData.microphone,
          };
        }
      }

      // Add maintenance and financial data
      assetData.attributes = {
        ...assetData.attributes,
        lastMaintenance: submissionData.lastMaintenance,
        maintenanceBy: submissionData.maintenanceBy,
        issueReported: submissionData.issueReported,
        nextMaintenance: submissionData.nextMaintenance,
        purchasePrice: submissionData.purchasePrice,
        maintenanceCost: submissionData.maintenanceCost,
        estimatedLifespan: submissionData.estimatedLifespan,
        salvageValue: submissionData.salvageValue,
        annualOperatingCost: submissionData.annualOperatingCost,
        insuranceCost: submissionData.insuranceCost,
      };

      // Ensure all required fields are present
      const completeAssetData: AssetFrontend = {
        id: assetData.id,
        assetType: assetData.assetType || 'Other',
        category: assetData.category || 'Other',
        manufacturer: assetData.manufacturer || 'Unknown',
        model: assetData.model || '',
        serialNumber: assetData.serialNumber || '',
        status: assetData.status || 'Active',
        condition: assetData.condition || 'Good',
        location: assetData.location || '',
        department: assetData.department || '',
        attributes: assetData.attributes || {},
        createdAt: assetData.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        cost: assetData.cost,
        vendor: assetData.vendor,
        project: assetData.project,
        internetAccess: assetData.internetAccess,
        ipAddress: assetData.ipAddress,
        notes: assetData.notes,
        assignedTo: assetData.assignedTo,
        assignedToId: assetData.assignedToId,
        maintenanceHistory: assetData.maintenanceHistory,
        purchaseDate: assetData.purchaseDate,
        warrantyExpiry: assetData.warrantyExpiry,
        assetTag: assetData.assetTag
      };

      // Log the data before submission
      console.log('Submitting asset data:', completeAssetData);
      
      // Submit the form
      const submitSuccess: boolean = await onSubmit(completeAssetData);
      
      if (submitSuccess) {
        toast.success(`Asset ${initialData ? 'updated' : 'created'} successfully!`);
        if (onSaveSuccess) {
          onSaveSuccess();
        }
        onClose();
      } else {
        toast.error(`Failed to ${initialData ? 'update' : 'create'} asset`);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Failed to save asset. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Helper function to convert File to base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result);
        } else {
          reject(new Error('Failed to convert file to base64'));
        }
      };
      reader.onerror = error => reject(error);
    });
  };

  const inputClassName = "w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500";
  const labelClassName = "block text-sm font-medium text-gray-700 mb-1 flex items-center gap-1";

  console.log("Asset Type: ", formData.assetType);

  // Calculate depreciation and total cost
  const calculateDepreciation = () => {
    const purchasePrice = parseFloat(formData.purchasePrice) || 0;
    const salvageValue = parseFloat(formData.salvageValue) || 0;
    const lifespan = parseFloat(formData.estimatedLifespan) || 5; // Default to 5 years if not specified
    
    // Calculate straight-line depreciation
    const annualDepreciation = (purchasePrice - salvageValue) / lifespan;
    
    const monthlyDepreciation = annualDepreciation / 12;
    
    return {
      annual: annualDepreciation,
      monthly: monthlyDepreciation
    };
  };

  const calculateTotalCost = () => {
    const purchasePrice = parseFloat(formData.purchasePrice) || 0;
    
    // Total initial cost is just the purchase price
    return purchasePrice;
  };

  const calculateAnnualTotalCost = () => {
    const maintenanceCost = parseFloat(formData.maintenanceCost) || 0;
    const operatingCost = parseFloat(formData.annualOperatingCost) || 0;
    const insuranceCost = parseFloat(formData.insuranceCost) || 0;
    
    // Annual total cost includes all annual costs (excluding depreciation)
    return maintenanceCost + operatingCost + insuranceCost;
  };

  // Helper function to format currency in PKR
  const formatPKR = (amount: number) => {
    return new Intl.NumberFormat('ur-PK', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(Math.round(amount));
  };

  useEffect(() => {
    if (initialData) {
      // Create a copy of the default form data
      const updatedFormData = { ...formData };
      
      // Map basic fields from initialData
      Object.keys(updatedFormData).forEach(key => {
        if (key in initialData && key !== 'attributes') {
          // @ts-ignore - We're checking if the key exists
          updatedFormData[key] = initialData[key];
        }
      });
      
      // Handle special cases
      updatedFormData.assetType = initialData.assetType || '';
      updatedFormData.category = initialData.category || '';
      updatedFormData.manufacturer = initialData.manufacturer || '';
      updatedFormData.model = initialData.model || '';
      updatedFormData.serialNumber = initialData.serialNumber || '';
      updatedFormData.assetTag = initialData.assetTag || '';
      updatedFormData.status = initialData.status || 'Active';
      updatedFormData.purchaseDate = initialData.purchaseDate || '';
      updatedFormData.warrantyExpiration = initialData.warrantyExpiry || '';
      updatedFormData.vendor = initialData.vendor || '';
      updatedFormData.location = initialData.location || '';
      updatedFormData.condition = initialData.condition || '';
      updatedFormData.department = initialData.department || '';
      updatedFormData.notes = initialData.notes || '';
      updatedFormData.purchasePrice = initialData.cost?.toString() || '';
      
      // Handle project field
      if (initialData.project) {
        if (PROJECTS.includes(initialData.project)) {
          updatedFormData.project = initialData.project;
        } else {
          updatedFormData.project = 'Other';
          updatedFormData.customProject = initialData.project;
        }
      }
      
      // Handle assignedTo
      if (initialData.assignedTo && Array.isArray(initialData.assignedTo) && initialData.assignedTo.length > 0) {
        // Use the array directly since we updated the interface to accept an array
        updatedFormData.assignedTo = initialData.assignedTo;
        updatedFormData.assignmentDate = initialData.attributes?.assignmentDate || '';
      }
      
      // Handle attributes based on asset type and category
      if (initialData.attributes) {
        // Computing attributes
        if (initialData.assetType === 'Computing') {
          updatedFormData.operatingSystem = initialData.attributes.operatingSystem || '';
          updatedFormData.processor = initialData.attributes.processor || '';
          updatedFormData.ramCapacity = initialData.attributes.ramCapacity || '';
          updatedFormData.storageType = initialData.attributes.storageType || '';
          updatedFormData.storageCapacity = initialData.attributes.storageCapacity || '';
          updatedFormData.graphicsCard = initialData.attributes.graphicsCard || '';
          updatedFormData.hardDiskCount = initialData.attributes.hardDiskCount || '';
          updatedFormData.hardDiskDetails = initialData.attributes.hardDiskDetails || '';
        }
        
        // Mobile & Tablet attributes
        else if (initialData.assetType === 'Mobile & Tablet') {
          updatedFormData.mobileOperatingSystem = initialData.attributes.mobileOperatingSystem || '';
          updatedFormData.mobileRam = initialData.attributes.mobileRam || '';
          updatedFormData.mobileStorage = initialData.attributes.mobileStorage || '';
          updatedFormData.imei = initialData.attributes.imei || '';
          updatedFormData.simNumber = initialData.attributes.simNumber || '';
          updatedFormData.color = initialData.attributes.color || '';
        }
        
        // Security & Surveillance attributes
        else if (initialData.assetType === 'Security & Surveillance') {
          // Common attributes
          
          // Camera specific attributes
          if (initialData.category === 'CCTV Camera' || initialData.category === 'IP Camera') {
            updatedFormData.cameraResolution = initialData.attributes.cameraResolution || '';
            updatedFormData.cameraFOV = initialData.attributes.cameraFOV || '';
            updatedFormData.cameraNightVision = initialData.attributes.cameraNightVision || 'Yes';
            updatedFormData.cameraZoom = initialData.attributes.cameraZoom || '';
          }
          
          // NVR/DVR specific attributes
          else if (initialData.category === 'NVR' || initialData.category === 'DVR') {
            updatedFormData.dvrChannelCount = initialData.attributes.dvrChannelCount || initialData.attributes.channelCount || '';
            updatedFormData.dvrRecordingResolution = initialData.attributes.dvrRecordingResolution || initialData.attributes.recordingResolution || '';
          }
          
          // Biometric Device specific attributes
          else if (initialData.category === 'Biometric Device') {
            updatedFormData.biometricType = initialData.attributes.biometricType || '';
            updatedFormData.userCapacity = initialData.attributes.userCapacity || '';
          }
          
          // Access Control specific attributes
          else if (initialData.category === 'Access Control') {
            updatedFormData.accessControlType = initialData.attributes.accessControlType || '';
            updatedFormData.doorCapacity = initialData.attributes.doorCapacity || '';
          }
        }
        
        // Printing attributes
        else if (initialData.assetType === 'Printing') {
          // Common printing attributes
          updatedFormData.printerColor = initialData.attributes.printerColor || 'No';
          updatedFormData.printerSpeed = initialData.attributes.printerSpeed || '';
          
          // 3D Printer specific attributes
          if (initialData.category === '3D Printer') {
            updatedFormData.printerTechnology = initialData.attributes.printerTechnology || '';
            updatedFormData.buildVolume = initialData.attributes.buildVolume || '';
            updatedFormData.filamentType = initialData.attributes.filamentType || '';
          }
          
          // Scanner specific attributes
          else if (initialData.category === 'Scanner') {
            updatedFormData.scannerType = initialData.attributes.scannerType || '';
            updatedFormData.scannerResolution = initialData.attributes.scannerResolution || '';
            updatedFormData.scanSpeed = initialData.attributes.scanSpeed || '';
          }
          
          // Photo Copier specific attributes
          else if (initialData.category === 'Photo Copier') {
            updatedFormData.copySpeed = initialData.attributes.copySpeed || '';
            updatedFormData.copyResolution = initialData.attributes.copyResolution || '';
            updatedFormData.copyColor = initialData.attributes.copyColor || '';
            updatedFormData.paperCapacity = initialData.attributes.paperCapacity || '';
          }
        }
        
        // Display & Multimedia attributes
        else if (initialData.assetType === 'Display & Multimedia') {
          // Common display attributes
          updatedFormData.screenSize = initialData.attributes.screenSize || '';
          updatedFormData.resolution = initialData.attributes.resolution || '';
          
          // TV specific attributes
          if (initialData.category === 'TV' || initialData.category === 'Smart TV') {
            updatedFormData.smartTv = initialData.attributes.smartTv || 'Yes';
            updatedFormData.hdmiPorts = initialData.attributes.hdmiPorts || '';
            updatedFormData.usbPorts = initialData.attributes.usbPorts || '';
          }
          
          // LED Display specific attributes
          else if (initialData.category === 'LED Display') {
            updatedFormData.ledScreenSize = initialData.attributes.ledScreenSize || '';
            updatedFormData.ledBrand = initialData.attributes.ledBrand || '';
          }
          
          // Projector specific attributes
          else if (initialData.category === 'Projector') {
            updatedFormData.projectorType = initialData.attributes.projectorType || '';
            updatedFormData.brightness = initialData.attributes.brightness || '';
          }
          
          // Speakers & Microphones specific attributes
          else if (initialData.category === 'Speakers & Microphones') {
            updatedFormData.audioType = initialData.attributes.audioType || '';
            updatedFormData.connectionInterface = initialData.attributes.connectionInterface || '';
            updatedFormData.powerOutput = initialData.attributes.powerOutput || '';
            updatedFormData.frequencyResponse = initialData.attributes.frequencyResponse || '';
          }
          
          // Conference Equipment specific attributes
          else if (initialData.category === 'Conference Equipment') {
            updatedFormData.conferenceType = initialData.attributes.conferenceType || '';
            updatedFormData.maxParticipants = initialData.attributes.maxParticipants || '';
            updatedFormData.videoResolution = initialData.attributes.videoResolution || '';
            updatedFormData.compatibleSoftware = initialData.attributes.compatibleSoftware || '';
          }
        }
        
        // Peripherals & Accessories attributes
        else if (initialData.assetType === 'Peripherals & Accessories') {
          // Common peripheral attributes
          updatedFormData.connectionType = initialData.attributes.connectionType || '';
          updatedFormData.compatibility = initialData.attributes.compatibility || '';
          
          // Keyboard specific attributes
          if (initialData.category === 'Keyboard') {
            updatedFormData.keyboardType = initialData.attributes.keyboardType || '';
            updatedFormData.keyboardLayout = initialData.attributes.keyboardLayout || '';
          }
          
          // Mouse specific attributes
          else if (initialData.category === 'Mouse') {
            updatedFormData.mouseType = initialData.attributes.mouseType || '';
            updatedFormData.dpi = initialData.attributes.dpi || '';
          }
          
          // Webcam specific attributes
          else if (initialData.category === 'Webcam') {
            updatedFormData.webcamResolution = initialData.attributes.webcamResolution || '';
            updatedFormData.microphone = initialData.attributes.microphone || '';
          }
        }
        
        // Networking & Communication attributes
        else if (initialData.assetType === 'Networking & Communication') {
          updatedFormData.networkType = initialData.attributes.networkType || '';
          updatedFormData.portCount = initialData.attributes.portCount || '';
          updatedFormData.macAddress = initialData.attributes.macAddress || '';
          updatedFormData.cableType = initialData.attributes.cableType || '';
          updatedFormData.cableLength = initialData.attributes.cableLength || '';
          updatedFormData.extensionNumber = initialData.attributes.extensionNumber || '';
          updatedFormData.sipAccount = initialData.attributes.sipAccount || '';
          updatedFormData.pbxExtensionCapacity = initialData.attributes.pbxExtensionCapacity || '';
          updatedFormData.pbxType = initialData.attributes.pbxType || '';
        }
        
        // Maintenance attributes
        updatedFormData.lastMaintenance = initialData.attributes.lastMaintenance || '';
        updatedFormData.maintenanceBy = initialData.attributes.maintenanceBy || '';
        updatedFormData.issueReported = initialData.attributes.issueReported || '';
        updatedFormData.nextMaintenance = initialData.attributes.nextMaintenance || '';
        
        // Financial attributes
        updatedFormData.purchasePrice = initialData.attributes.purchasePrice || initialData.cost?.toString() || '';
        updatedFormData.maintenanceCost = initialData.attributes.maintenanceCost || '';
        updatedFormData.estimatedLifespan = initialData.attributes.estimatedLifespan || '5';
        updatedFormData.salvageValue = initialData.attributes.salvageValue || '';
        updatedFormData.annualOperatingCost = initialData.attributes.annualOperatingCost || '';
        updatedFormData.insuranceCost = initialData.attributes.insuranceCost || '';
      }
      
      setFormData(updatedFormData);
    }
  }, [initialData]); // Remove formData from the dependency array

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{initialData ? 'Edit Asset' : 'Add New Asset'}</h1>
            <p className="text-gray-600">{initialData ? 'Update asset information' : 'Add a new asset to inventory'}</p>
          </div>
          <button
            onClick={onClose}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-800"
          >
            <X className="h-5 w-5" />
            Close
          </button>
        </div>
      </div>
      <div className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="assetType" className={labelClassName}>
                  <Box className="h-5 w-5" /> Asset Type <span className="text-red-500">*</span>
                </label>
                <select
                  id="assetType"
                  name="assetType"
                  value={formData.assetType}
                  onChange={handleAssetTypeChange}
                  className={inputClassName}
                  required
                >
                  <option value="">Select Asset Type</option>
                  <option value="Computing">Computing</option>
                  <option value="Mobile & Tablet">Mobile & Tablet</option>
                  <option value="Networking & Communication">Networking & Communication</option>
                  <option value="Printing">Printing</option>
                  <option value="Display & Multimedia">Display & Multimedia</option>
                  <option value="Security & Surveillance">Security & Surveillance</option>
                  <option value="Peripherals & Accessories">Peripherals & Accessories</option>
                  <option value="Other Equipment">Other Equipment</option>
                </select>
                {formData.assetType === 'Other' && (
                  <input
                    type="text"
                    name="customAssetType"
                    placeholder="Enter custom asset type"
                    value={formData.customAssetType}
                    onChange={handleChange}
                    className={inputClassName}
                  />
                )}
              </div>
              <div>
                <label htmlFor="category" className={labelClassName}>
                  <Tag className="h-5 w-5" /> Category <span className="text-red-500">*</span>
                </label>
                <select 
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  className={inputClassName}
                  required
                >
                  <option value="">Select Category</option>
                  {formData.assetType && categoryMap[formData.assetType]?.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
                {formData.category === 'Other' && (
                  <input 
                    type="text"
                    name="customCategory"
                    placeholder="Enter custom category"
                    value={formData.customCategory}
                    onChange={handleChange}
                    className={inputClassName}
                  />
                )}
              </div>

              {/* Row 2: Manufacturer (full width) */}
              <div className="col-span-2">
                <label htmlFor="manufacturer" className={labelClassName}>
                  <Building2 className="h-5 w-5" /> Manufacturer <span className="text-red-500">*</span>
                </label>
                <select
                  id="manufacturer"
                  name="manufacturer"
                  value={formData.manufacturer}
                  onChange={handleChange}
                  className={inputClassName}
                  required
                >
                  <option value="">Select Manufacturer</option>
                  {formData.assetType === 'Computing' && formData.category === 'Desktop' && (
                    <>
                      <option value="Dell">Dell</option>
                      <option value="HP">HP</option>
                      <option value="Lenovo">Lenovo</option>
                      <option value="Apple">Apple</option>
                      <option value="Asus">Asus</option>
                      <option value="Acer">Acer</option>
                      <option value="MSI">MSI</option>
                      <option value="Fujitsu">Fujitsu</option>
                      <option value="Gigabyte">Gigabyte</option>
                      <option value="Samsung">Samsung</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                  {formData.assetType === 'Computing' && formData.category === 'Laptop' && (
                    <>
                      <option value="Dell">Dell</option>
                      <option value="HP">HP</option>
                      <option value="Lenovo">Lenovo</option>
                      <option value="Apple">Apple</option>
                      <option value="Asus">Asus</option>
                      <option value="MSI">MSI</option>
                      <option value="Acer">Acer</option>
                      <option value="Razer">Razer</option>
                      <option value="Huawei">Huawei</option>
                      <option value="Microsoft">Microsoft (Surface)</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                  {formData.assetType === 'Computing' && formData.category === 'Server' && (
                    <>
                      <option value="Dell">Dell (PowerEdge)</option>
                      <option value="HP">HP (HPE ProLiant)</option>
                      <option value="IBM">IBM</option>
                      <option value="Lenovo">Lenovo (ThinkSystem)</option>
                      <option value="Cisco">Cisco</option>
                      <option value="Supermicro">Supermicro</option>
                      <option value="Fujitsu">Fujitsu</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                  {formData.assetType === 'Computing' && formData.category === 'Workstation' && (
                    <>
                      <option value="Dell">Dell (Precision)</option>
                      <option value="HP">HP (Z Series)</option>
                      <option value="Lenovo">Lenovo (ThinkStation)</option>
                      <option value="Apple">Apple (Mac Studio)</option>
                      <option value="Asus">ASUS (ProArt)</option>
                      <option value="MSI">MSI (Creator)</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                  {formData.assetType === 'Computing' && formData.category === 'Mini PC' && (
                    <>
                      <option value="Intel">Intel (NUC)</option>
                      <option value="Lenovo">Lenovo (ThinkCentre)</option>
                      <option value="HP">HP</option>
                      <option value="Asus">Asus (VivoMini)</option>
                      <option value="Apple">Apple (Mac Mini)</option>
                      <option value="Gigabyte">Gigabyte (Brix)</option>
                      <option value="Zotac">Zotac</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                  {formData.assetType === 'Computing' && formData.category === 'Embedded System' && (
                    <>
                      <option value="Raspberry Pi">Raspberry Pi</option>
                      <option value="Advantech">Advantech</option>
                      <option value="NVIDIA">NVIDIA (Jetson)</option>
                      <option value="Intel">Intel</option>
                      <option value="BeagleBone">BeagleBone</option>
                      <option value="Toradex">Toradex</option>
                      <option value="AAEON">AAEON</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                  {formData.assetType === 'Computing' && formData.category === 'Other' && (
                    <>
                      <option value="Custom-built">Custom-built Systems</option>
                      <option value="High-Performance">High-Performance Clusters</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                  {formData.assetType === 'Mobile & Tablet' && formData.category === 'Smartphone' && (
                    <>
                      <option value="Apple">Apple (iPhone)</option>
                      <option value="Samsung">Samsung</option>
                      <option value="Google">Google (Pixel)</option>
                      <option value="OnePlus">OnePlus</option>
                      <option value="Xiaomi">Xiaomi</option>
                      <option value="Huawei">Huawei</option>
                      <option value="Vivo">Vivo</option>
                      <option value="Oppo">Oppo</option>
                      <option value="Realme">Realme</option>
                      <option value="Sony">Sony</option>
                      <option value="Motorola">Motorola</option>
                      <option value="Asus">Asus (ROG)</option>
                      <option value="Nokia">Nokia</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                  {formData.assetType === 'Mobile & Tablet' && formData.category === 'Tablet' && (
                    <>
                      <option value="Apple">Apple (iPad)</option>
                      <option value="Samsung">Samsung (Galaxy Tab)</option>
                      <option value="Microsoft">Microsoft (Surface)</option>
                      <option value="Lenovo">Lenovo (Tab Series)</option>
                      <option value="Huawei">Huawei (MatePad)</option>
                      <option value="Xiaomi">Xiaomi (Pad)</option>
                      <option value="Amazon">Amazon (Fire Tablet)</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                  {formData.assetType === 'Mobile & Tablet' && formData.category === 'Feature Phone' && (
                    <>
                      <option value="Nokia">Nokia</option>
                      <option value="Samsung">Samsung</option>
                      <option value="Itel">Itel</option>
                      <option value="Tecno">Tecno</option>
                      <option value="Micromax">Micromax</option>
                      <option value="Lava">Lava</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                  {formData.assetType === 'Mobile & Tablet' && formData.category === 'PDAs & Rugged Devices' && (
                    <>
                      <option value="Zebra">Zebra</option>
                      <option value="Honeywell">Honeywell</option>
                      <option value="Panasonic">Panasonic (Toughbook)</option>
                      <option value="Getac">Getac</option>
                      <option value="Dell">Dell (Rugged Latitude)</option>
                      <option value="Datalogic">Datalogic</option>
                      <option value="CAT">CAT Phones</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                  {formData.assetType === 'Mobile & Tablet' && formData.category === 'Wearables' && (
                    <>
                      <option value="Apple">Apple (Apple Watch)</option>
                      <option value="Samsung">Samsung (Galaxy Watch)</option>
                      <option value="Garmin">Garmin</option>
                      <option value="Fitbit">Fitbit</option>
                      <option value="Huawei">Huawei</option>
                      <option value="Xiaomi">Xiaomi (Mi Band)</option>
                      <option value="Fossil">Fossil</option>
                      <option value="Polar">Polar</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                  {formData.assetType === 'Mobile & Tablet' && formData.category === 'Other' && (
                    <>
                      <option value="Specialized">Specialized Industrial Devices</option>
                      <option value="IoT">IoT Modules</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                  {formData.assetType === 'Mobile & Tablet' && !['Smartphone', 'Tablet', 'Feature Phone', 'PDAs & Rugged Devices', 'Wearables', 'Other'].includes(formData.category) && (
                    <>
                      <option value="Apple">Apple</option>
                      <option value="Samsung">Samsung</option>
                      <option value="Huawei">Huawei</option>
                      <option value="Xiaomi">Xiaomi</option>
                      <option value="OnePlus">OnePlus</option>
                      <option value="Microsoft">Microsoft</option>
                      <option value="Lenovo">Lenovo</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                  {formData.assetType === 'Networking & Communication' && (
                    <>
                      {formData.category === 'Router & Modem' && (
                        <>
                          <option value="Cisco">Cisco</option>
                          <option value="MikroTik">MikroTik</option>
                          <option value="TP-Link">TP-Link</option>
                          <option value="Ubiquiti">Ubiquiti</option>
                          <option value="Netgear">Netgear</option>
                          <option value="Huawei">Huawei</option>
                          <option value="ASUS">ASUS</option>
                          <option value="Linksys">Linksys</option>
                          <option value="D-Link">D-Link</option>
                          <option value="Juniper">Juniper</option>
                          <option value="DrayTek">DrayTek</option>
                        </>
                      )}
                      {formData.category === 'Switch' && (
                        <>
                          <option value="Cisco">Cisco</option>
                          <option value="HP">HP (Aruba)</option>
                          <option value="Dell">Dell</option>
                          <option value="Juniper">Juniper</option>
                          <option value="TP-Link">TP-Link</option>
                          <option value="Netgear">Netgear</option>
                          <option value="Huawei">Huawei</option>
                          <option value="Extreme Networks">Extreme Networks</option>
                          <option value="Brocade">Brocade</option>
                        </>
                      )}
                      {formData.category === 'Access Point' && (
                        <>
                          <option value="Ubiquiti">Ubiquiti (UniFi)</option>
                          <option value="Cisco">Cisco (Meraki)</option>
                          <option value="TP-Link">TP-Link (Omada)</option>
                          <option value="Aruba">Aruba</option>
                          <option value="Ruckus">Ruckus</option>
                          <option value="Cambium Networks">Cambium Networks</option>
                          <option value="Grandstream">Grandstream</option>
                        </>
                      )}
                      {formData.category === 'PBX' && (
                        <>
                          <option value="Grandstream">Grandstream</option>
                          <option value="Cisco">Cisco</option>
                          <option value="Avaya">Avaya</option>
                          <option value="Panasonic">Panasonic</option>
                          <option value="Mitel">Mitel</option>
                          <option value="NEC">NEC</option>
                          <option value="Alcatel-Lucent">Alcatel-Lucent</option>
                          <option value="3CX">3CX</option>
                          <option value="Yeastar">Yeastar</option>
                        </>
                      )}
                      {formData.category === 'VoIP Phone' && (
                        <>
                          <option value="Cisco">Cisco</option>
                          <option value="Yealink">Yealink</option>
                          <option value="Grandstream">Grandstream</option>
                          <option value="Poly">Poly (Polycom)</option>
                          <option value="Avaya">Avaya</option>
                          <option value="Snom">Snom</option>
                          <option value="Mitel">Mitel</option>
                          <option value="Alcatel-Lucent">Alcatel-Lucent</option>
                        </>
                      )}
                      {formData.category === 'Gateway' && (
                        <>
                          <option value="Grandstream">Grandstream</option>
                          <option value="Patton">Patton</option>
                          <option value="Cisco">Cisco</option>
                          <option value="Audiocodes">Audiocodes</option>
                          <option value="Ribbon Communications">Ribbon Communications</option>
                        </>
                      )}
                      {formData.category === 'Firewall' && (
                        <>
                          <option value="Fortinet">Fortinet</option>
                          <option value="Cisco">Cisco (ASA)</option>
                          <option value="Palo Alto">Palo Alto</option>
                          <option value="Sophos">Sophos</option>
                          <option value="Check Point">Check Point</option>
                          <option value="SonicWall">SonicWall</option>
                          <option value="WatchGuard">WatchGuard</option>
                          <option value="Juniper">Juniper</option>
                          <option value="Barracuda">Barracuda</option>
                        </>
                      )}
                      {formData.category === 'Load Balancer' && (
                        <>
                          <option value="F5 Networks">F5 Networks</option>
                          <option value="Citrix">Citrix</option>
                          <option value="Barracuda">Barracuda</option>
                          <option value="Kemp">Kemp</option>
                          <option value="A10 Networks">A10 Networks</option>
                        </>
                      )}
                      {formData.category === 'Controller' && (
                        <>
                          <option value="Cisco">Cisco</option>
                          <option value="Aruba">Aruba</option>
                          <option value="Ruckus">Ruckus</option>
                          <option value="Juniper">Juniper</option>
                          <option value="Extreme Networks">Extreme Networks</option>
                          <option value="Cambium Networks">Cambium Networks</option>
                        </>
                      )}
                      {formData.category === 'Cables & Connectors' && (
                        <>
                          <option value="Belkin">Belkin</option>
                          <option value="Anker">Anker</option>
                          <option value="Ubiquiti">Ubiquiti</option>
                          <option value="Panduit">Panduit</option>
                          <option value="Nexans">Nexans</option>
                          <option value="Dintek">Dintek</option>
                          <option value="AMP Netconnect">AMP Netconnect</option>
                        </>
                      )}
                      {formData.category === 'Other' && (
                        <>
                          <option value="Network Adapters">Network Adapters</option>
                          <option value="Industrial Communication Devices">Industrial Communication Devices</option>
                          <option value="Media Converters">Media Converters</option>
                        </>
                      )}
                      {!['Router & Modem', 'Switch', 'Access Point', 'VoIP Phone', 'Gateway', 'Firewall', 'Load Balancer', 'Controller', 'Cables & Connectors', 'Other'].includes(formData.category) && (
                    <>
                      <option value="Aruba Networks">Aruba Networks</option>
                      <option value="Cisco">Cisco</option>
                      <option value="D-Link">D-Link</option>
                      <option value="Fortinet">Fortinet</option>
                      <option value="Grandstream">Grandstream</option>
                      <option value="Huawei">Huawei</option>
                      <option value="Juniper">Juniper</option>
                      <option value="Mikrotik">Mikrotik</option>
                      <option value="TP-Link">TP-Link</option>
                      <option value="Ubiquiti">Ubiquiti</option>
                        </>
                      )}
                      <option value="Other">Other</option>
                    </>
                  )}
                  {formData.assetType === 'Printing' && (
                    <>
                      {formData.category === 'Inkjet Printer' && (
                        <>
                          <option value="HP">HP</option>
                      <option value="Epson">Epson</option>
                          <option value="Canon">Canon</option>
                          <option value="Brother">Brother</option>
                          <option value="Lexmark">Lexmark</option>
                          <option value="Ricoh">Ricoh</option>
                          <option value="Xerox">Xerox</option>
                          <option value="Kodak">Kodak</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                      {formData.category === 'Laser Printer' && (
                        <>
                          <option value="HP">HP</option>
                          <option value="Brother">Brother</option>
                          <option value="Lexmark">Lexmark</option>
                          <option value="Kyocera">Kyocera</option>
                          <option value="Xerox">Xerox</option>
                      <option value="Samsung">Samsung</option>
                          <option value="Ricoh">Ricoh</option>
                          <option value="OKI">OKI</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                      {formData.category === '3D Printer' && (
                        <>
                          <option value="Creality">Creality</option>
                          <option value="Prusa">Prusa</option>
                          <option value="Anycubic">Anycubic</option>
                          <option value="Ultimaker">Ultimaker</option>
                          <option value="MakerBot">MakerBot</option>
                          <option value="FlashForge">FlashForge</option>
                          <option value="Formlabs">Formlabs</option>
                          <option value="Raise3D">Raise3D</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                      {formData.category === 'Scanner' && (
                    <>
                          <option value="Fujitsu">Fujitsu</option>
                      <option value="Epson">Epson</option>
                          <option value="Canon">Canon</option>
                          <option value="HP">HP</option>
                      <option value="Brother">Brother</option>
                          <option value="Kodak Alaris">Kodak Alaris</option>
                          <option value="Plustek">Plustek</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                      {formData.category === 'Photo Copier' && (
                        <>
                          <option value="Xerox">Xerox</option>
                          <option value="Ricoh">Ricoh</option>
                          <option value="Konica Minolta">Konica Minolta</option>
                          <option value="Kyocera">Kyocera</option>
                      <option value="Canon">Canon</option>
                          <option value="Sharp">Sharp</option>
                          <option value="Toshiba">Toshiba</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                      {formData.category === 'Plotter' && (
                        <>
                          <option value="HP">HP (DesignJet)</option>
                          <option value="Canon">Canon</option>
                          <option value="Epson">Epson</option>
                          <option value="Mutoh">Mutoh</option>
                          <option value="Roland">Roland</option>
                          <option value="Graphtec">Graphtec</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                      {formData.category === 'Label Printer' && (
                        <>
                          <option value="Zebra">Zebra</option>
                          <option value="Brother">Brother</option>
                          <option value="Dymo">Dymo</option>
                          <option value="TSC">TSC</option>
                          <option value="SATO">SATO</option>
                          <option value="Brady">Brady</option>
                          <option value="Epson">Epson</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                      {!['Inkjet Printer', 'Laser Printer', '3D Printer', 'Scanner', 'Photo Copier', 'Plotter', 'Label Printer'].includes(formData.category) && (
                        <>
                          <option value="HP">HP</option>
                          <option value="Canon">Canon</option>
                          <option value="Epson">Epson</option>
                          <option value="Brother">Brother</option>
                          <option value="Xerox">Xerox</option>
                          <option value="Ricoh">Ricoh</option>
                          <option value="Kyocera">Kyocera</option>
                          <option value="Konica">Konica</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                    </>
                  )}
                  {formData.assetType === 'Display & Multimedia' && (
                    <>
                      {formData.category === 'Monitor' && (
                        <>
                          <option value="Dell">Dell</option>
                          <option value="LG">LG</option>
                          <option value="Samsung">Samsung</option>
                          <option value="Asus">Asus</option>
                          <option value="BenQ">BenQ</option>
                          <option value="HP">HP</option>
                          <option value="Acer">Acer</option>
                          <option value="AOC">AOC</option>
                          <option value="MSI">MSI</option>
                          <option value="Eizo">Eizo</option>
                          <option value="ViewSonic">ViewSonic</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'Projector' && (
                        <>
                          <option value="Epson">Epson</option>
                          <option value="BenQ">BenQ</option>
                          <option value="Sony">Sony</option>
                          <option value="Optoma">Optoma</option>
                          <option value="ViewSonic">ViewSonic</option>
                          <option value="Panasonic">Panasonic</option>
                          <option value="NEC">NEC</option>
                          <option value="Christie">Christie</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'TV' && (
                        <>
                          <option value="Samsung">Samsung</option>
                          <option value="LG">LG</option>
                          <option value="Sony">Sony</option>
                          <option value="TCL">TCL</option>
                          <option value="Hisense">Hisense</option>
                          <option value="Philips">Philips</option>
                          <option value="Sharp">Sharp</option>
                          <option value="Vizio">Vizio</option>
                          <option value="Panasonic">Panasonic</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'LED Display' && (
                        <>
                          <option value="Samsung">Samsung</option>
                          <option value="LG">LG</option>
                          <option value="NEC">NEC</option>
                          <option value="Sharp">Sharp</option>
                          <option value="Barco">Barco</option>
                          <option value="Planar">Planar</option>
                          <option value="Sony">Sony</option>
                          <option value="Daktronics">Daktronics</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'Digital Signage' && (
                        <>
                          <option value="Samsung">Samsung</option>
                          <option value="LG">LG</option>
                          <option value="Sharp">Sharp</option>
                          <option value="NEC">NEC</option>
                          <option value="Philips">Philips</option>
                          <option value="Panasonic">Panasonic</option>
                          <option value="BrightSign">BrightSign</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'Speakers & Microphones' && (
                        <>
                          <option value="Bose">Bose</option>
                          <option value="JBL">JBL</option>
                          <option value="Shure">Shure</option>
                          <option value="Rode">Rode</option>
                          <option value="Sennheiser">Sennheiser</option>
                          <option value="AKG">AKG</option>
                          <option value="Audio-Technica">Audio-Technica</option>
                          <option value="Behringer">Behringer</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'Conference Equipment' && (
                        <>
                          <option value="Logitech">Logitech</option>
                          <option value="Poly">Poly</option>
                          <option value="Cisco">Cisco</option>
                          <option value="Jabra">Jabra</option>
                          <option value="AVer">AVer</option>
                          <option value="Yealink">Yealink</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'Other' && (
                        <>
                          <option value="Interactive Whiteboards">Interactive Whiteboards</option>
                          <option value="Studio Recording Equipment">Studio Recording Equipment</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {!['Monitor', 'Projector', 'TV', 'LED Display', 'Digital Signage', 'Speakers & Microphones', 'Conference Equipment', 'Other'].includes(formData.category) && (
                        <>
                          <option value="LG">LG</option>
                          <option value="Samsung">Samsung</option>
                          <option value="Sony">Sony</option>
                          <option value="Sharp">Sharp</option>
                          <option value="Panasonic">Panasonic</option>
                          <option value="Philips">Philips</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                    </>
                  )}
                  {formData.assetType === 'Security & Surveillance' && (
                    <>
                      {formData.category === 'CCTV Camera' && (
                        <>
                          <option value="Hikvision">Hikvision</option>
                          <option value="Dahua">Dahua</option>
                          <option value="Hanwha Techwin">Hanwha Techwin</option>
                          <option value="Bosch">Bosch</option>
                          <option value="Uniview">Uniview</option>
                          <option value="Lorex">Lorex</option>
                          <option value="Swann">Swann</option>
                          <option value="Reolink">Reolink</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'IP Camera' && (
                        <>
                          <option value="Axis">Axis</option>
                          <option value="Hikvision">Hikvision</option>
                          <option value="Dahua">Dahua</option>
                          <option value="Avigilon">Avigilon</option>
                          <option value="Bosch">Bosch</option>
                          <option value="Hanwha Techwin">Hanwha Techwin</option>
                          <option value="Uniview">Uniview</option>
                          <option value="Reolink">Reolink</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'NVR' && (
                        <>
                          <option value="Hikvision">Hikvision</option>
                          <option value="Dahua">Dahua</option>
                          <option value="QNAP">QNAP</option>
                          <option value="Synology">Synology</option>
                          <option value="Uniview">Uniview</option>
                          <option value="Axis">Axis</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'DVR' && (
                        <>
                          <option value="Hikvision">Hikvision</option>
                          <option value="Dahua">Dahua</option>
                          <option value="Lorex">Lorex</option>
                          <option value="Swann">Swann</option>
                          <option value="Night Owl">Night Owl</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'Biometric Device' && (
                        <>
                          <option value="ZKTeco">ZKTeco</option>
                          <option value="Suprema">Suprema</option>
                          <option value="HID">HID</option>
                          <option value="Anviz">Anviz</option>
                          <option value="IDEMIA">IDEMIA</option>
                          <option value="Realtime">Realtime</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'RFID Scanner' && (
                        <>
                          <option value="Zebra">Zebra</option>
                          <option value="HID">HID</option>
                          <option value="Impinj">Impinj</option>
                          <option value="Alien Technology">Alien Technology</option>
                          <option value="GAO RFID">GAO RFID</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'Access Control' && (
                        <>
                          <option value="HID">HID</option>
                          <option value="Honeywell">Honeywell</option>
                          <option value="Bosch">Bosch</option>
                          <option value="Suprema">Suprema</option>
                          <option value="IDEMIA">IDEMIA</option>
                          <option value="CDVI">CDVI</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'Alarm System' && (
                        <>
                          <option value="Honeywell">Honeywell</option>
                          <option value="Bosch">Bosch</option>
                          <option value="DSC">DSC</option>
                          <option value="Ring">Ring</option>
                          <option value="Texecom">Texecom</option>
                          <option value="Paradox">Paradox</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'Other' && (
                        <>
                          <option value="Motion Sensors">Motion Sensors</option>
                          <option value="Smart Locks">Smart Locks</option>
                          <option value="Security Lighting">Security Lighting</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {!['CCTV Camera', 'IP Camera', 'NVR', 'DVR', 'Biometric Device', 'RFID Scanner', 'Access Control', 'Alarm System', 'Other'].includes(formData.category) && (
                        <>
                          <option value="Axis">Axis</option>
                          <option value="Bosch">Bosch</option>
                          <option value="Dahua">Dahua</option>
                          <option value="Hikvision">Hikvision</option>
                          <option value="Honeywell">Honeywell</option>
                          <option value="Sony">Sony</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                    </>
                  )}
                  {formData.assetType === 'Peripherals & Accessories' && (
                    <>
                      {formData.category === 'Keyboard' && (
                        <>
                          <option value="Logitech">Logitech</option>
                          <option value="Microsoft">Microsoft</option>
                          <option value="Corsair">Corsair</option>
                          <option value="Razer">Razer</option>
                          <option value="Dell">Dell</option>
                          <option value="HP">HP</option>
                          <option value="Keychron">Keychron</option>
                          <option value="SteelSeries">SteelSeries</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'Mouse' && (
                        <>
                          <option value="Logitech">Logitech</option>
                          <option value="Microsoft">Microsoft</option>
                          <option value="Razer">Razer</option>
                          <option value="Corsair">Corsair</option>
                          <option value="SteelSeries">SteelSeries</option>
                          <option value="Dell">Dell</option>
                          <option value="HP">HP</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'Webcam' && (
                        <>
                          <option value="Logitech">Logitech</option>
                          <option value="Microsoft">Microsoft</option>
                          <option value="Razer">Razer</option>
                          <option value="Dell">Dell</option>
                          <option value="Poly">Poly</option>
                          <option value="Cisco">Cisco</option>
                          <option value="Ausdom">Ausdom</option>
                          <option value="Anker">Anker</option>
                          <option value="Creative">Creative</option>
                          <option value="Elgato">Elgato</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'Docking Station' && (
                        <>
                          <option value="Dell">Dell</option>
                          <option value="HP">HP</option>
                          <option value="Lenovo">Lenovo</option>
                          <option value="Belkin">Belkin</option>
                          <option value="Anker">Anker</option>
                          <option value="Kensington">Kensington</option>
                          <option value="CalDigit">CalDigit</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'External Storage' && (
                        <>
                          <option value="Western Digital">Western Digital</option>
                          <option value="Seagate">Seagate</option>
                          <option value="Samsung">Samsung</option>
                          <option value="SanDisk">SanDisk</option>
                          <option value="Toshiba">Toshiba</option>
                          <option value="LaCie">LaCie</option>
                          <option value="Crucial">Crucial</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {formData.category === 'Headset' && (
                        <>
                          <option value="Logitech">Logitech</option>
                          <option value="Jabra">Jabra</option>
                          <option value="Poly">Poly (Plantronics)</option>
                          <option value="Microsoft">Microsoft</option>
                          <option value="Corsair">Corsair</option>
                          <option value="SteelSeries">SteelSeries</option>
                          <option value="Sennheiser">Sennheiser</option>
                          <option value="Sony">Sony</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                      {!['Keyboard', 'Mouse', 'Webcam', 'Docking Station', 'External Storage', 'Headset'].includes(formData.category) && (
                        <>
                          <option value="Logitech">Logitech</option>
                          <option value="Microsoft">Microsoft</option>
                          <option value="Dell">Dell</option>
                          <option value="HP">HP</option>
                          <option value="Belkin">Belkin</option>
                          <option value="Other">Other</option>
                        </>
                      )}
                    </>
                  )}
                  {formData.assetType === 'Other Equipment' && (
                    <>
                      <option value="Other">Other</option>
                    </>
                  )}
                </select>
                {formData.manufacturer === 'Other' && (
                  <input 
                    type="text"
                    name="customManufacturer"
                    placeholder="Enter custom manufacturer"
                    value={formData.customManufacturer}
                    onChange={handleChange}
                    className={inputClassName}
                  />
                )}
              </div>

              {/* New Row: Model & Serial Number */}
              <div className="flex gap-4 col-span-2">
                <div className="flex-1">
                  <label htmlFor="model" className={labelClassName}>
                    <Box className="h-5 w-5" /> Model <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="model"
                    name="model"
                    placeholder="Enter model"
                    value={formData.model}
                    onChange={handleChange}
                    className={inputClassName}
                    required
                  />
                </div>
                <div className="flex-1">
                  <label htmlFor="serialNumber" className={labelClassName}>
                    <Barcode className="h-5 w-5" /> Serial Number <span className="text-gray-400">(Optional)</span>
                  </label>
                  <input
                    type="text"
                    id="serialNumber"
                    name="serialNumber"
                    placeholder="Enter serial number if available"
                    value={formData.serialNumber || ''}
                    onChange={handleChange}
                    className={inputClassName}
                    autoComplete="off"
                    key="serialNumber-input"
                  />
                </div>
              </div>

              {/* Serial Number field removed as requested */}

              {/* New Row: Purchase Details (Purchase from Vendor, Purchase Date, Warranty Expiration) */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 col-span-2">
                <div>
                  <label htmlFor="vendor" className={labelClassName}>
                    <ShoppingBag className="h-5 w-5" /> Vendor Name
                  </label>
                  <input
                    type="text"
                    id="vendor"
                    name="vendor"
                    placeholder="Enter vendor name"
                    value={formData.vendor}
                    onChange={handleChange}
                    className={inputClassName}
                  />
                </div>
                <div>
                  <label htmlFor="purchaseDate" className={labelClassName}>
                    <Calendar className="h-5 w-5" /> Purchase Date
                  </label>
                  <input
                    type="date"
                    id="purchaseDate"
                    name="purchaseDate"
                    value={formData.purchaseDate}
                    onChange={handleChange}
                    className={inputClassName}
                  />
                </div>
                <div>
                  <label htmlFor="warrantyExpiration" className={labelClassName}>
                    <Calendar className="h-5 w-5" /> Warranty Expiration
                  </label>
                  <input
                    type="date"
                    id="warrantyExpiration"
                    name="warrantyExpiration"
                    value={formData.warrantyExpiration}
                    onChange={handleChange}
                    className={inputClassName}
                  />
                </div>
              </div>

              {/* Combined Row: Project and Location */}
              <div className="flex gap-4 col-span-2">
                <div className="flex-1">
                  <label htmlFor="project" className={labelClassName}>
                    <Tag className="h-5 w-5" /> Project
                  </label>
                  <select
                    id="project"
                    name="project"
                    value={formData.project}
                    onChange={handleChange}
                    className={inputClassName}
                  >
                    <option value="">Select Project</option>
                    {PROJECTS.map(project => (
                      <option key={project} value={project}>{project}</option>
                    ))}
                    <option value="Other">Other</option>
                  </select>
                  {formData.project === 'Other' && (
                    <input
                      type="text"
                      name="customProject"
                      placeholder="Enter custom project name"
                      value={formData.customProject}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  )}
                </div>
                <div className="flex-1">
                  <label htmlFor="location" className={labelClassName}>
                    <MapPin className="h-5 w-5" /> Location <span className="text-red-500">*</span>
                  </label>
                  <select 
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={handleChange}
                    className={inputClassName}
                    required
                  >
                    <option value="">Select Location</option>
                    {formData.project && formData.project !== 'Other' 
                      ? PROJECT_LOCATIONS[formData.project].map(location => (
                          <option key={location} value={location}>{location}</option>
                        ))
                      : ALL_LOCATIONS.map(location => (
                          <option key={location} value={location}>{location}</option>
                        ))
                    }
                    <option value="Other">Other</option>
                  </select>
                  {formData.location === 'Other' && (
                    <input 
                      type="text"
                      name="customLocation"
                      placeholder="Enter custom location"
                      value={formData.customLocation}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  )}
                </div>
              </div>

              {/* New Row: Internet Access and IP Address */}
              <div className="flex gap-4 col-span-2">
                <div className="flex-1">
                  <label htmlFor="internetAccess" className={labelClassName}>
                    Internet Access
                  </label>
                  <select 
                    id="internetAccess"
                    name="internetAccess"
                    value={formData.internetAccess ? "true" : "false"}
                    onChange={(e) => setFormData({
                      ...formData,
                      internetAccess: e.target.value === "true"
                    })}
                    className={inputClassName}
                  >
                    <option value="false">No</option>
                    <option value="true">Yes</option>
                  </select>
                </div>
                {formData.internetAccess === true && (
                  <div className="flex-1">
                    <label htmlFor="ipAddress" className={labelClassName}>
                      IP Address
                    </label>
                    <input 
                      type="text"
                      id="ipAddress"
                      name="ipAddress"
                      placeholder="Enter IP address"
                      value={formData.ipAddress}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Asset Attributes */}
          {(formData.assetType === 'Computing' || formData.assetType === 'Mobile & Tablet') && (
            <section>
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Cpu className="h-5 w-5" /> Asset Attributes
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {formData.assetType === 'Computing' && (
                  <>
                    <div>
                      <label htmlFor="operatingSystem" className={labelClassName}>
                        <Monitor className="inline-block mr-1 h-4 w-4" /> Operating System
                      </label>
                      <input
                        type="text"
                        id="operatingSystem"
                        name="operatingSystem"
                        placeholder="Enter Operating System"
                        value={formData.operatingSystem}
                        onChange={handleChange}
                        className={inputClassName}
                      />
                    </div>
                    <div>
                      <label htmlFor="processor" className={labelClassName}>
                        <Cpu className="inline-block mr-1 h-4 w-4" /> Processor
                      </label>
                      <input
                        type="text"
                        id="processor"
                        name="processor"
                        placeholder="Enter Processor details"
                        value={formData.processor}
                        onChange={handleChange}
                        className={inputClassName}
                      />
                    </div>
                    <div>
                      <label htmlFor="ramCapacity" className={labelClassName}>
                        <Cpu className="inline-block mr-1 h-4 w-4" /> RAM Capacity
                      </label>
                      <input 
                        type="text"
                        id="ramCapacity"
                        name="ramCapacity"
                        placeholder="e.g., 16GB, 256GB"
                        value={formData.ramCapacity}
                        onChange={handleChange}
                        className={inputClassName}
                      />
                    </div>
                    <div>
                      <label htmlFor="storageType" className={labelClassName}>
                        <HardDrive className="inline-block mr-1 h-4 w-4" /> Storage Type
                      </label>
                      <input 
                        type="text"
                        id="storageType"
                        name="storageType"
                        placeholder="SSD, HDD, NVMe etc."
                        value={formData.storageType}
                        onChange={handleChange}
                        className={inputClassName}
                      />
                    </div>
                    <div>
                      <label htmlFor="storageCapacity" className={labelClassName}>
                        Storage Capacity
                      </label>
                      <input 
                        type="text"
                        id="storageCapacity"
                        name="storageCapacity"
                        placeholder="Enter Storage Capacity"
                        value={formData.storageCapacity}
                        onChange={handleChange}
                        className={inputClassName}
                      />
                    </div>
                    <div>
                      <label htmlFor="graphicsCard" className={labelClassName}>
                        Graphics Card
                      </label>
                      <input 
                        type="text"
                        id="graphicsCard"
                        name="graphicsCard"
                        placeholder="Enter Graphics Card details"
                        value={formData.graphicsCard}
                        onChange={handleChange}
                        className={inputClassName}
                      />
                    </div>
                    <div>
                      <label htmlFor="hardDiskCount" className={labelClassName}>
                        Hard Disk Count
                      </label>
                      <input 
                        type="number"
                        id="hardDiskCount"
                        name="hardDiskCount"
                        placeholder="Enter number of hard drives"
                        value={formData.hardDiskCount}
                        onChange={handleChange}
                        className={inputClassName}
                      />
                    </div>
                    <div>
                      <label htmlFor="hardDiskDetails" className={labelClassName}>
                        Hard Disk Details
                      </label>
                      <input 
                        type="text"
                        id="hardDiskDetails"
                        name="hardDiskDetails"
                        placeholder="e.g., 2x 1TB HDD, 1x 512GB SSD"
                        value={formData.hardDiskDetails}
                        onChange={handleChange}
                        className={inputClassName}
                      />
                    </div>
                  </>
                )}
                {(formData.assetType === 'Mobile & Tablet') && (
                  <>
                    <div>
                      <label htmlFor="mobileOperatingSystem" className={labelClassName}>
                        <Monitor className="inline-block mr-1 h-4 w-4" /> Operating System <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="mobileOperatingSystem"
                        name="mobileOperatingSystem"
                        value={formData.mobileOperatingSystem}
                        onChange={handleChange}
                        className={inputClassName}
                        required
                      >
                        <option value="">Select OS</option>
                        <option value="Android">Android</option>
                        <option value="iOS">iOS</option>
                        <option value="Other">Other</option>
                      </select>
                    </div>
                    <div>
                      <label htmlFor="mobileRam" className={labelClassName}>
                        <Cpu className="inline-block mr-1 h-4 w-4" /> RAM <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="mobileRam"
                        name="mobileRam"
                        value={formData.mobileRam}
                        onChange={handleChange}
                        placeholder="Enter RAM (e.g., 4GB)"
                        className={inputClassName}
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="mobileStorage" className={labelClassName}>
                        <HardDrive className="inline-block mr-1 h-4 w-4" /> Storage <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="mobileStorage"
                        name="mobileStorage"
                        value={formData.mobileStorage}
                        onChange={handleChange}
                        placeholder="Enter Storage (e.g., 64GB)"
                        className={inputClassName}
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="imei" className={labelClassName}>
                        <Barcode className="inline-block mr-1 h-4 w-4" /> IMEI <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="imei"
                        name="imei"
                        value={formData.imei}
                        onChange={handleChange}
                        className={inputClassName}
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="simNumber" className={labelClassName}>
                        <Hash className="inline-block mr-1 h-4 w-4" /> SIM Number
                      </label>
                      <input
                        type="text"
                        id="simNumber"
                        name="simNumber"
                        value={formData.simNumber}
                        onChange={handleChange}
                        className={inputClassName}
                      />
                    </div>
                    <div>
                      <label htmlFor="color" className={labelClassName}>
                        <Palette className="inline-block mr-1 h-4 w-4" /> Color
                      </label>
                      <input 
                        type="text"
                        id="color"
                        name="color"
                        placeholder="e.g., Black, White, Blue"
                        value={formData.color}
                        onChange={handleChange}
                        className={inputClassName}
                      />
                    </div>
                  </>
                )}
              </div>
            </section>
          )}

          {/* Display & Multimedia section */}
          {formData.assetType === 'Display & Multimedia' && (
            <section>
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Monitor className="h-5 w-5" /> Display & Multimedia Attributes
              </h3>
              
              {/* Common display attributes */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="screenSize" className={labelClassName}>
                    Screen Size
                  </label>
                  <input 
                    type="text"
                    id="screenSize"
                    name="screenSize"
                    placeholder="e.g., 32 inches"
                    value={formData.screenSize}
                    onChange={handleChange}
                    className={inputClassName}
                  />
                </div>
                <div>
                  <label htmlFor="resolution" className={labelClassName}>
                    Resolution
                  </label>
                  <input 
                    type="text"
                    id="resolution"
                    name="resolution"
                    placeholder="e.g., 1080p, 4K"
                    value={formData.resolution}
                    onChange={handleChange}
                    className={inputClassName}
                  />
                </div>
              </div>
              
              {/* TV specific attributes */}
              {(formData.category === 'TV' || formData.category === 'Smart TV') && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div>
                    <label htmlFor="smartTv" className={labelClassName}>
                      Smart TV
                    </label>
                    <select
                      id="smartTv"
                      name="smartTv"
                      value={formData.smartTv}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="Yes">Yes</option>
                      <option value="No">No</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="hdmiPorts" className={labelClassName}>
                      HDMI Ports
                    </label>
                    <input
                      type="text"
                      id="hdmiPorts"
                      name="hdmiPorts"
                      placeholder="Number of ports"
                      value={formData.hdmiPorts}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                  <div>
                    <label htmlFor="usbPorts" className={labelClassName}>
                      USB Ports
                    </label>
                    <input
                      type="text"
                      id="usbPorts"
                      name="usbPorts"
                      placeholder="Number of ports"
                      value={formData.usbPorts}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                </div>
              )}
              
              {/* LED Display specific attributes */}
              {formData.category === 'LED Display' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div>
                    <label htmlFor="ledScreenSize" className={labelClassName}>
                      LED Screen Size
                    </label>
                    <input
                      type="text"
                      id="ledScreenSize"
                      name="ledScreenSize"
                      placeholder="e.g., P2.5, P3, P4"
                      value={formData.ledScreenSize}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                  <div>
                    <label htmlFor="ledBrand" className={labelClassName}>
                      LED Brand/Module
                    </label>
                    <input
                      type="text"
                      id="ledBrand"
                      name="ledBrand"
                      placeholder="e.g., Nationstar, Nichia"
                      value={formData.ledBrand}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                </div>
              )}
              
              {/* Projector specific attributes */}
              {formData.category === 'Projector' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div>
                    <label htmlFor="projectorType" className={labelClassName}>
                      Projector Type
                    </label>
                    <select
                      id="projectorType"
                      name="projectorType"
                      value={formData.projectorType || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="">Select Type</option>
                      <option value="DLP">DLP</option>
                      <option value="LCD">LCD</option>
                      <option value="LED">LED</option>
                      <option value="Laser">Laser</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="brightness" className={labelClassName}>
                      Brightness (Lumens)
                    </label>
                    <input
                      type="text"
                      id="brightness"
                      name="brightness"
                      placeholder="e.g., 3000, 5000"
                      value={formData.brightness || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                </div>
              )}
              
              {/* Speakers & Microphones specific attributes */}
              {formData.category === 'Speakers & Microphones' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div>
                    <label htmlFor="audioType" className={labelClassName}>
                      Audio Device Type
                    </label>
                    <select
                      id="audioType"
                      name="audioType"
                      value={formData.audioType || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="">Select Type</option>
                      <option value="Speaker">Speaker</option>
                      <option value="Microphone">Microphone</option>
                      <option value="Combo">Combo Device</option>
                      <option value="Soundbar">Soundbar</option>
                      <option value="Subwoofer">Subwoofer</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="connectionInterface" className={labelClassName}>
                      Connection Interface
                    </label>
                    <select
                      id="connectionInterface"
                      name="connectionInterface"
                      value={formData.connectionInterface || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="">Select Interface</option>
                      <option value="Bluetooth">Bluetooth</option>
                      <option value="USB">USB</option>
                      <option value="3.5mm">3.5mm Jack</option>
                      <option value="XLR">XLR</option>
                      <option value="Wireless">Wireless</option>
                      <option value="HDMI">HDMI</option>
                      <option value="Optical">Optical</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="powerOutput" className={labelClassName}>
                      Power Output (Watts)
                    </label>
                    <input
                      type="text"
                      id="powerOutput"
                      name="powerOutput"
                      placeholder="e.g., 50W, 100W"
                      value={formData.powerOutput || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                  <div>
                    <label htmlFor="frequencyResponse" className={labelClassName}>
                      Frequency Response
                    </label>
                    <input
                      type="text"
                      id="frequencyResponse"
                      name="frequencyResponse"
                      placeholder="e.g., 20Hz-20kHz"
                      value={formData.frequencyResponse || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                </div>
              )}
              
              {/* Conference Equipment specific attributes */}
              {formData.category === 'Conference Equipment' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div>
                    <label htmlFor="conferenceType" className={labelClassName}>
                      Equipment Type
                    </label>
                    <select
                      id="conferenceType"
                      name="conferenceType"
                      value={formData.conferenceType || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="">Select Type</option>
                      <option value="Video Conferencing">Video Conferencing System</option>
                      <option value="Speakerphone">Speakerphone</option>
                      <option value="Conference Camera">Conference Camera</option>
                      <option value="Microphone Array">Microphone Array</option>
                      <option value="All-in-One">All-in-One Solution</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="maxParticipants" className={labelClassName}>
                      Max Participants
                    </label>
                    <input
                      type="text"
                      id="maxParticipants"
                      name="maxParticipants"
                      placeholder="e.g., 10, 20, 50"
                      value={formData.maxParticipants || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                  <div>
                    <label htmlFor="videoResolution" className={labelClassName}>
                      Video Resolution
                    </label>
                    <select
                      id="videoResolution"
                      name="videoResolution"
                      value={formData.videoResolution || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="">Select Resolution</option>
                      <option value="720p">720p HD</option>
                      <option value="1080p">1080p Full HD</option>
                      <option value="4K">4K Ultra HD</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="compatibleSoftware" className={labelClassName}>
                      Compatible Software
                    </label>
                    <input
                      type="text"
                      id="compatibleSoftware"
                      name="compatibleSoftware"
                      placeholder="e.g., Zoom, Teams, Webex"
                      value={formData.compatibleSoftware || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                </div>
              )}
            </section>
          )}

          {/* Security & Surveillance section */}
          {formData.assetType === 'Security & Surveillance' && (
            <section>
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Shield className="h-5 w-5" /> Security & Surveillance Attributes
              </h3>
              
              {/* Camera specific attributes */}
              {(formData.category === 'CCTV Camera' || formData.category === 'IP Camera') && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="cameraResolution" className={labelClassName}>
                      Camera Resolution
                    </label>
                    <input
                      type="text"
                      id="cameraResolution"
                      name="cameraResolution"
                      placeholder="e.g., 1080p, 4K, etc."
                      value={formData.cameraResolution}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                  <div>
                    <label htmlFor="cameraFOV" className={labelClassName}>
                      Field of View (FOV)
                    </label>
                    <input
                      type="text"
                      id="cameraFOV"
                      name="cameraFOV"
                      placeholder="e.g., 90°, 120°, etc."
                      value={formData.cameraFOV}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                  <div>
                    <label htmlFor="cameraNightVision" className={labelClassName}>
                      Night Vision
                    </label>
                    <select
                      id="cameraNightVision"
                      name="cameraNightVision"
                      value={formData.cameraNightVision}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="Yes">Yes</option>
                      <option value="No">No</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="cameraZoom" className={labelClassName}>
                      Zoom Capability
                    </label>
                    <input
                      type="text"
                      id="cameraZoom"
                      name="cameraZoom"
                      placeholder="e.g., 3x, 10x, etc."
                      value={formData.cameraZoom}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                </div>
              )}
              
              {/* NVR/DVR specific attributes */}
              {(formData.category === 'NVR' || formData.category === 'DVR') && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="dvrChannelCount" className={labelClassName}>
                      Channel Count
                    </label>
                    <input
                      type="text"
                      id="dvrChannelCount"
                      name="dvrChannelCount"
                      placeholder="e.g., 4, 8, 16, etc."
                      value={formData.dvrChannelCount}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                  <div>
                    <label htmlFor="dvrRecordingResolution" className={labelClassName}>
                      Recording Resolution
                    </label>
                    <input
                      type="text"
                      id="dvrRecordingResolution"
                      name="dvrRecordingResolution"
                      placeholder="e.g., 1080p, 4K, etc."
                      value={formData.dvrRecordingResolution}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                </div>
              )}
              
              {/* Biometric Device specific attributes */}
              {formData.category === 'Biometric Device' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="biometricType" className={labelClassName}>
                      Biometric Type
                    </label>
                    <select
                      id="biometricType"
                      name="biometricType"
                      value={formData.biometricType || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="">Select Type</option>
                      <option value="Fingerprint">Fingerprint</option>
                      <option value="Facial Recognition">Facial Recognition</option>
                      <option value="Iris Scanner">Iris Scanner</option>
                      <option value="Palm Vein">Palm Vein</option>
                      <option value="Voice Recognition">Voice Recognition</option>
                      <option value="Multiple">Multiple</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="userCapacity" className={labelClassName}>
                      User Capacity
                    </label>
                    <input
                      type="text"
                      id="userCapacity"
                      name="userCapacity"
                      placeholder="e.g., 1000, 5000, etc."
                      value={formData.userCapacity || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                </div>
              )}
              
              {/* Access Control specific attributes */}
              {formData.category === 'Access Control' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="accessControlType" className={labelClassName}>
                      Access Control Type
                    </label>
                    <select
                      id="accessControlType"
                      name="accessControlType"
                      value={formData.accessControlType || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="">Select Type</option>
                      <option value="Card Reader">Card Reader</option>
                      <option value="Biometric">Biometric</option>
                      <option value="Keypad">Keypad</option>
                      <option value="Multi-factor">Multi-factor</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="doorCapacity" className={labelClassName}>
                      Door Capacity
                    </label>
                    <input
                      type="text"
                      id="doorCapacity"
                      name="doorCapacity"
                      placeholder="Number of doors"
                      value={formData.doorCapacity || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                </div>
              )}
            </section>
          )}

          {/* Printing section */}
          {formData.assetType === 'Printing' && (
            <section>
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <PrinterIcon className="h-5 w-5" /> Printing Attributes
              </h3>
              
              {/* Common printer attributes */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="printerColor" className={labelClassName}>
                    Color Printing
                  </label>
                  <select
                    id="printerColor"
                    name="printerColor"
                    value={formData.printerColor}
                    onChange={handleChange}
                    className={inputClassName}
                  >
                    <option value="Yes">Yes</option>
                    <option value="No">No</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="printerSpeed" className={labelClassName}>
                    Print Speed (PPM)
                  </label>
                  <input
                    type="text"
                    id="printerSpeed"
                    name="printerSpeed"
                    placeholder="Pages per minute"
                    value={formData.printerSpeed}
                    onChange={handleChange}
                    className={inputClassName}
                  />
                </div>
              </div>
              
              {/* 3D Printer specific attributes */}
              {formData.category === '3D Printer' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div>
                    <label htmlFor="printerTechnology" className={labelClassName}>
                      Printer Technology
                    </label>
                    <select
                      id="printerTechnology"
                      name="printerTechnology"
                      value={formData.printerTechnology}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="">Select Technology</option>
                      <option value="FDM">FDM (Fused Deposition Modeling)</option>
                      <option value="SLA">SLA (Stereolithography)</option>
                      <option value="SLS">SLS (Selective Laser Sintering)</option>
                      <option value="DLP">DLP (Digital Light Processing)</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="buildVolume" className={labelClassName}>
                      Build Volume
                    </label>
                    <input
                      type="text"
                      id="buildVolume"
                      name="buildVolume"
                      placeholder="e.g., 220x220x250mm"
                      value={formData.buildVolume}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                  <div>
                    <label htmlFor="filamentType" className={labelClassName}>
                      Filament Type
                    </label>
                    <input
                      type="text"
                      id="filamentType"
                      name="filamentType"
                      placeholder="e.g., PLA, ABS, PETG"
                      value={formData.filamentType}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                </div>
              )}
              
              {/* Scanner specific attributes */}
              {formData.category === 'Scanner' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div>
                    <label htmlFor="scannerType" className={labelClassName}>
                      Scanner Type
                    </label>
                    <select
                      id="scannerType"
                      name="scannerType"
                      value={formData.scannerType}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="">Select Type</option>
                      <option value="Flatbed">Flatbed</option>
                      <option value="Document">Document</option>
                      <option value="Sheet-fed">Sheet-fed</option>
                      <option value="Handheld">Handheld</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="scannerResolution" className={labelClassName}>
                      Scanner Resolution
                    </label>
                    <input
                      type="text"
                      id="scannerResolution"
                      name="scannerResolution"
                      placeholder="e.g., 600 dpi, 1200 dpi"
                      value={formData.scannerResolution}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                  <div>
                    <label htmlFor="scanSpeed" className={labelClassName}>
                      Scan Speed
                    </label>
                    <input
                      type="text"
                      id="scanSpeed"
                      name="scanSpeed"
                      placeholder="Pages per minute"
                      value={formData.scanSpeed}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                </div>
              )}
              
              {/* Photo Copier specific attributes */}
              {formData.category === 'Photo Copier' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div>
                    <label htmlFor="copySpeed" className={labelClassName}>
                      Copy Speed
                    </label>
                    <input
                      type="text"
                      id="copySpeed"
                      name="copySpeed"
                      placeholder="Pages per minute"
                      value={formData.copySpeed}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                  <div>
                    <label htmlFor="copyResolution" className={labelClassName}>
                      Copy Resolution
                    </label>
                    <input
                      type="text"
                      id="copyResolution"
                      name="copyResolution"
                      placeholder="e.g., 600 dpi, 1200 dpi"
                      value={formData.copyResolution}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                  <div>
                    <label htmlFor="copyColor" className={labelClassName}>
                      Color Copying
                    </label>
                    <select
                      id="copyColor"
                      name="copyColor"
                      value={formData.copyColor}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="Yes">Yes</option>
                      <option value="No">No</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="paperCapacity" className={labelClassName}>
                      Paper Capacity
                    </label>
                    <input
                      type="text"
                      id="paperCapacity"
                      name="paperCapacity"
                      placeholder="Number of sheets"
                      value={formData.paperCapacity}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                </div>
              )}
            </section>
          )}

          {/* Peripherals & Accessories section */}
          {formData.assetType === 'Peripherals & Accessories' && (
            <section>
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Keyboard className="h-5 w-5" /> Peripherals & Accessories Attributes
              </h3>
              
              {/* Common peripheral attributes */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="connectionType" className={labelClassName}>
                    Connection Type
                  </label>
                  <select
                    id="connectionType"
                    name="connectionType"
                    value={formData.connectionType}
                    onChange={handleChange}
                    className={inputClassName}
                  >
                    <option value="">Select Connection Type</option>
                    <option value="USB">USB</option>
                    <option value="Wireless">Wireless</option>
                    <option value="Bluetooth">Bluetooth</option>
                    <option value="PS/2">PS/2</option>
                    <option value="USB-C">USB-C</option>
                    <option value="Thunderbolt">Thunderbolt</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="compatibility" className={labelClassName}>
                    Compatibility
                  </label>
                  <input
                    type="text"
                    id="compatibility"
                    name="compatibility"
                    placeholder="e.g., Windows, Mac, Linux"
                    value={formData.compatibility}
                    onChange={handleChange}
                    className={inputClassName}
                  />
                </div>
              </div>
              
              {/* Keyboard specific attributes */}
              {formData.category === 'Keyboard' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div>
                    <label htmlFor="keyboardType" className={labelClassName}>
                      Keyboard Type
                    </label>
                    <select
                      id="keyboardType"
                      name="keyboardType"
                      value={formData.keyboardType || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="">Select Type</option>
                      <option value="Mechanical">Mechanical</option>
                      <option value="Membrane">Membrane</option>
                      <option value="Chiclet">Chiclet</option>
                      <option value="Ergonomic">Ergonomic</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="keyboardLayout" className={labelClassName}>
                      Layout
                    </label>
                    <select
                      id="keyboardLayout"
                      name="keyboardLayout"
                      value={formData.keyboardLayout || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="">Select Layout</option>
                      <option value="QWERTY">QWERTY</option>
                      <option value="AZERTY">AZERTY</option>
                      <option value="QWERTZ">QWERTZ</option>
                      <option value="Dvorak">Dvorak</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                </div>
              )}
              
              {/* Mouse specific attributes */}
              {formData.category === 'Mouse' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div>
                    <label htmlFor="mouseType" className={labelClassName}>
                      Mouse Type
                    </label>
                    <select
                      id="mouseType"
                      name="mouseType"
                      value={formData.mouseType || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="">Select Type</option>
                      <option value="Optical">Optical</option>
                      <option value="Laser">Laser</option>
                      <option value="Trackball">Trackball</option>
                      <option value="Gaming">Gaming</option>
                      <option value="Ergonomic">Ergonomic</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="dpi" className={labelClassName}>
                      DPI
                    </label>
                    <input
                      type="text"
                      id="dpi"
                      name="dpi"
                      placeholder="e.g., 800, 1600, 3200"
                      value={formData.dpi || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    />
                  </div>
                </div>
              )}
              
              {/* Webcam specific attributes */}
              {formData.category === 'Webcam' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div>
                    <label htmlFor="webcamResolution" className={labelClassName}>
                      Resolution
                    </label>
                    <select
                      id="webcamResolution"
                      name="webcamResolution"
                      value={formData.webcamResolution || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="">Select Resolution</option>
                      <option value="720p">720p HD</option>
                      <option value="1080p">1080p Full HD</option>
                      <option value="1440p">1440p QHD</option>
                      <option value="4K">4K Ultra HD</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="microphone" className={labelClassName}>
                      Built-in Microphone
                    </label>
                    <select
                      id="microphone"
                      name="microphone"
                      value={formData.microphone || ''}
                      onChange={handleChange}
                      className={inputClassName}
                    >
                      <option value="Yes">Yes</option>
                      <option value="No">No</option>
                    </select>
                  </div>
                </div>
              )}
            </section>
          )}

          {/* Asset Condition */}
          <section>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" /> Asset Condition
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="condition" className={labelClassName}>
                  Condition
                </label>
                <select 
                  id="condition"
                  name="condition"
                  value={formData.condition}
                  onChange={handleChange}
                  className={inputClassName}
                >
                  <option value="">Select Condition</option>
                  <option value="New">New</option>
                  <option value="Good">Good</option>
                  <option value="Fair">Fair</option>
                  <option value="Poor">Poor</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              {formData.condition === 'Other' && (
                <div>
                  <label htmlFor="customCondition" className={labelClassName}>
                    Custom Condition
                  </label>
                  <input 
                    type="text"
                    id="customCondition"
                    name="customCondition"
                    placeholder="Enter custom condition"
                    value={formData.customCondition}
                    onChange={handleChange}
                    className={inputClassName}
                  />
                </div>
              )}
            </div>
          </section>

          {/* Status & Assignment */}
          <section>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <User className="h-5 w-5" /> Status & Assignment
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="status" className={labelClassName}>
                  <Shield className="h-5 w-5" /> Asset Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  className={inputClassName}
                  required
                >
                  <option value="Active">Active</option>
                  <option value="Reserved">Reserved</option>
                  <option value="Under Repair">Under Repair</option>
                  <option value="Retired">Retired</option>
                  <option value="Lost/Stolen">Lost/Stolen</option>
                  <option value="Decommissioned">Decommissioned</option>
                  <option value="In Transit">In Transit</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              <div>
                <label htmlFor="department" className={labelClassName}>
                  <Building2 className="h-5 w-5" /> Department <span className="text-red-500">*</span>
                </label>
                <select
                  id="department"
                  name="department"
                  value={formData.department}
                  onChange={handleChange}
                  className={inputClassName}
                  required
                >
                  <option value="">Select Department</option>
                  <option value="CSD">CSD</option>
                  <option value="FINANCE">FINANCE</option>
                  <option value="HR">HR</option>
                  <option value="IT">IT</option>
                  <option value="LAND">LAND</option>
                  <option value="LEGAL">LEGAL</option>
                  <option value="MANAGEMENT">MANAGEMENT</option>
                  <option value="MARKETING">MARKETING</option>
                  <option value="OPERATIONS">OPERATIONS</option>
                  <option value="PND">PND</option>
                  <option value="SALES">SALES</option>
                </select>
              </div>
              <div>
                <label htmlFor="assignedTo" className={labelClassName}>
                  <User className="h-5 w-5" /> Assigned To
                </label>
                <UserSelectDropdown
                  onSelect={handleUserSelect}
                  selectedUser={selectedUser}
                  className={inputClassName}
                />
              </div>
              <div>
                <label htmlFor="assignmentDate" className={labelClassName}>
                  <Calendar className="h-5 w-5" /> Date of Assignment
                </label>
                <input 
                  type="date"
                  id="assignmentDate"
                  name="assignmentDate"
                  value={formData.assignmentDate}
                  onChange={handleChange}
                  className={inputClassName}
                />
              </div>
            </div>
          </section>

          {/* Maintenance & Repair History */}
          <section>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Calendar className="h-5 w-5" /> Maintenance & Repair History
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="lastMaintenance" className={labelClassName}>
                  <Calendar className="h-5 w-5" /> Last Maintenance Date
                </label>
                <input 
                  type="date"
                  id="lastMaintenance"
                  name="lastMaintenance"
                  value={formData.lastMaintenance}
                  onChange={handleChange}
                  className={inputClassName}
                />
              </div>
              <div>
                <label htmlFor="maintenanceBy" className={labelClassName}>
                  <User className="h-5 w-5" /> Maintenance Performed By
                </label>
                <select 
                  id="maintenanceBy"
                  name="maintenanceBy"
                  value={formData.maintenanceBy}
                  onChange={handleChange}
                  className={inputClassName}
                >
                  <option value="">Select Maintainer</option>
                  <option value="Vendor">Vendor</option>
                  <option value="IT Team">IT Team</option>
                  <option value="Third-Party">Third-Party</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              <div className="col-span-2">
                <label htmlFor="issueReported" className={labelClassName}>
                  <AlertTriangle className="h-5 w-5" /> Issue Reported
                </label>
                <textarea 
                  id="issueReported"
                  name="issueReported"
                  value={formData.issueReported}
                  onChange={handleChange}
                  className={inputClassName}
                />
              </div>
              <div className="col-span-2">
                <label htmlFor="notes" className={labelClassName}>
                  <AlertTriangle className="h-5 w-5" /> Notes
                </label>
                <textarea 
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  className={inputClassName}
                />
              </div>
              <div>
                <label htmlFor="nextMaintenance" className={labelClassName}>
                  <Calendar className="h-5 w-5" /> Next Maintenance Date
                </label>
                <input 
                  type="date"
                  id="nextMaintenance"
                  name="nextMaintenance"
                  value={formData.nextMaintenance}
                  onChange={handleChange}
                  className={inputClassName}
                />
              </div>
              <div>
                <label htmlFor="maintenanceCost" className={labelClassName}>
                  <Calendar className="h-5 w-5" /> Maintenance Cost
                </label>
                <input 
                  type="number"
                  id="maintenanceCost"
                  name="maintenanceCost"
                  value={formData.maintenanceCost}
                  onChange={handleChange}
                  className={inputClassName}
                  placeholder="0"
                  step="1"
                  min="0"
                />
              </div>
            </div>
          </section>

          {/* Cost Estimation Section */}
          <section>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <DollarSign className="h-5 w-5" /> Cost Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="purchasePrice" className={labelClassName}>
                  <DollarSign className="h-5 w-5" /> Purchase Price <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  id="purchasePrice"
                  name="purchasePrice"
                  value={formData.purchasePrice}
                  onChange={handleChange}
                  className={inputClassName}
                  placeholder="0"
                  step="1"
                  min="0"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="maintenanceCost" className={labelClassName}>
                  <Settings className="h-5 w-5" /> Annual Maintenance Cost
                </label>
                <input
                  type="number"
                  id="maintenanceCost"
                  name="maintenanceCost"
                  value={formData.maintenanceCost}
                  onChange={handleChange}
                  className={inputClassName}
                  placeholder="0"
                  step="1"
                  min="0"
                />
              </div>
              
              <div>
                <label htmlFor="estimatedLifespan" className={labelClassName}>
                  <Clock className="h-5 w-5" /> Estimated Lifespan (Years)
                </label>
                <input
                  type="number"
                  id="estimatedLifespan"
                  name="estimatedLifespan"
                  value={formData.estimatedLifespan}
                  onChange={handleChange}
                  className={inputClassName}
                  placeholder="5"
                />
              </div>
              
              <div>
                <label htmlFor="salvageValue" className={labelClassName}>
                  <TrendingDown className="h-5 w-5" /> Salvage Value
                </label>
                <input
                  type="number"
                  id="salvageValue"
                  name="salvageValue"
                  value={formData.salvageValue}
                  onChange={handleChange}
                  className={inputClassName}
                  placeholder="0"
                  step="1"
                  min="0"
                />
              </div>
              
              <div>
                <label htmlFor="annualOperatingCost" className={labelClassName}>
                  <Calculator className="h-5 w-5" /> Annual Operating Cost
                </label>
                <input
                  type="number"
                  id="annualOperatingCost"
                  name="annualOperatingCost"
                  value={formData.annualOperatingCost}
                  onChange={handleChange}
                  className={inputClassName}
                  placeholder="0"
                  step="1"
                  min="0"
                />
              </div>
              
              <div>
                <label htmlFor="insuranceCost" className={labelClassName}>
                  <Shield className="h-5 w-5" /> Annual Insurance Cost
                </label>
                <input
                  type="number"
                  id="insuranceCost"
                  name="insuranceCost"
                  value={formData.insuranceCost}
                  onChange={handleChange}
                  className={inputClassName}
                  placeholder="0"
                  step="1"
                  min="0"
                />
              </div>
            </div>
            
            {/* Cost Summary */}
            {formData.purchasePrice && (
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-3">Cost Summary</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Initial Purchase Cost:</p>
                    <p className="font-medium">{formatPKR(calculateTotalCost())}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Salvage Value:</p>
                    <p className="font-medium">{formatPKR(parseFloat(formData.salvageValue) || 0)}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Annual Depreciation:</p>
                    <p className="font-medium">{formatPKR(calculateDepreciation().annual)}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Monthly Depreciation:</p>
                    <p className="font-medium">{formatPKR(calculateDepreciation().monthly)}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Annual Operating Costs:</p>
                    <p className="font-medium">{formatPKR(calculateAnnualTotalCost())}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Total Annual Cost:</p>
                    <p className="font-medium">
                      {formatPKR(calculateAnnualTotalCost() + calculateDepreciation().annual)}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </section>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg min-w-[100px] hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg min-w-[100px] hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Save className="w-4 h-4 mr-2" />
              Save Asset
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AssetForm;