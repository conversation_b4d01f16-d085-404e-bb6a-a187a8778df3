import React, { useState, useEffect } from 'react';
import { Eye, Check, X, Calendar } from 'lucide-react';
import { LeaveRequest, LeaveStatus } from '../../../types/attendance';
import { ChevronLeft, ChevronRight, Search, Filter, Download, Clock, User } from 'lucide-react';

interface EmployeeLeaveData {
  employeeId: number;
  employeeName: string;
  employeeCode: string;
  department: string;
  position: string;
  leaveBalances: any[];
  recentRequests: LeaveRequest[];
  totalDaysUsed: number;
  totalDaysRemaining: number;
}

interface LeaveRequestsTableProps {
  paginatedRequests: LeaveRequest[];
  employeeLeaveData: EmployeeLeaveData[];
  onApprove: (request: LeaveRequest) => void;
  onReject: (request: LeaveRequest) => void;
  onViewDetails: (request: LeaveRequest) => void;
}

const LeaveRequestsTable: React.FC<LeaveRequestsTableProps> = ({
  paginatedRequests,
  employeeLeaveData,
  onApprove,
  onReject,
  onViewDetails
}) => {
  const calculateDays = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  };

  const getStatusBadge = (status: LeaveStatus) => {
    const statusStyles = {
      [LeaveStatus.PENDING]: 'bg-yellow-100 text-yellow-800',
      [LeaveStatus.APPROVED]: 'bg-green-100 text-green-800',
      [LeaveStatus.REJECTED]: 'bg-red-100 text-red-800',
      [LeaveStatus.CANCELLED]: 'bg-gray-100 text-gray-800'
    };

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusStyles[status]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getLeaveTypeLabel = (leaveType: string) => {
    const labels = {
      'Annual': 'Annual Leave',
      'Sick': 'Sick Leave',
      'Personal': 'Personal Leave',
      'Unpaid': 'Unpaid Leave',
      'Maternity': 'Maternity Leave',
      'Paternity': 'Paternity Leave',
      'Bereavement': 'Bereavement Leave',
      'Compensatory Off': 'Compensatory Off',
      'Other': 'Other'
    };
    return labels[leaveType] || leaveType;
  };

  if (paginatedRequests.length === 0) {
    return (
      <div className="text-center py-12">
        <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No leave requests found</h3>
        <p className="text-gray-500">
          No requests match your current filters.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
      <table className="w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
              Employee
            </th>
            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
              Type
            </th>
            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
              Days
            </th>
            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase hidden md:table-cell">
              Reason
            </th>
            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
              Status
            </th>
            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {paginatedRequests.map((request) => (
            <tr key={request.id} className="hover:bg-gray-50">
              <td className="px-3 py-3">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-6 w-6 bg-gray-200 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-gray-600">
                      {request.employeeName.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div className="ml-2">
                    <div className="text-sm font-medium text-gray-900 truncate max-w-32">{request.employeeName}</div>
                  </div>
                </div>
              </td>
              <td className="px-3 py-3">
                <div className="text-xs text-gray-900">{getLeaveTypeLabel(request.leaveType).replace(' Leave', '')}</div>
                {request.isHalfDay && (
                  <span className="text-xs text-blue-600">Half Day</span>
                )}
              </td>
              <td className="px-3 py-3">
                <div className="text-sm text-gray-900">
                  {calculateDays(request.startDate, request.endDate)}
                </div>
                <div className="text-xs text-gray-500">
                  {new Date(request.startDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                </div>
              </td>
              <td className="px-3 py-3 hidden md:table-cell">
                <div className="text-xs text-gray-900 max-w-24 truncate" title={request.reason}>
                  {request.reason}
                </div>
              </td>
              <td className="px-3 py-3">
                {getStatusBadge(request.status)}
              </td>
              <td className="px-3 py-3 text-sm font-medium">
                <div className="flex items-center space-x-1">
                  <button 
                    onClick={() => onViewDetails(request)}
                    className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" 
                    title="View Details"
                  >
                    <Eye className="h-3 w-3" />
                  </button>
                  {request.status === LeaveStatus.PENDING && (
                    <>
                      <button 
                        onClick={() => onApprove(request)}
                        className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50"
                        title="Approve"
                      >
                        <Check className="h-3 w-3" />
                      </button>
                      <button 
                        onClick={() => onReject(request)}
                        className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                        title="Reject"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default LeaveRequestsTable; 