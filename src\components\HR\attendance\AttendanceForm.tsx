import React, { useState, useEffect, useRef } from 'react';
import { Attendance, AttendanceStatus } from '../../../types/attendance';
import employeeService from '../../../services/EmployeeService';
import locationService from '../../../services/LocationService';
import LocationModal from './LocationModal';
import { 
  CheckCircle2, 
  Clock, 
  Calendar as CalendarIcon, 
  Users, 
  MapPin, 
  FileText, 
  Home, 
  Building,
  Upload,
  BarChart,
  CheckCircle,
  X,
  ChevronDown,
  Save,
  Upload as UploadIcon,
  AlertCircle,
  PlusCircle,
  UsersRound,
  FileSpreadsheet,
  ClipboardCheck,
  UserCheck,
  User,
  Database,
  Info,
  Settings
} from 'lucide-react';

interface EmployeeInfo {
  id: number;
  name: string;
  photo?: string;
  designation?: string;
  department?: string;
  location?: string;
  project?: string;
}

interface AttendanceFormProps {
  onSubmit: (attendance: Omit<Attendance, 'id'>) => void;
  editMode?: boolean;
  attendanceToEdit?: Attendance;
  employees?: { 
    id: number; 
    name: string; 
    firstName?: string;
    lastName?: string;
    department: string; 
    position: string; 
    avatar?: string; 
    location?: string; 
    project?: string;
    employeeId?: string;
    email?: string;
  }[];
  departments?: string[];
  locations?: string[];
  shifts?: { id: number; name: string; startTime: string; endTime: string }[];
}

const AttendanceForm: React.FC<AttendanceFormProps> = ({
  onSubmit,
  editMode = false,
  attendanceToEdit,
  employees = [],
  departments = ['IT', 'HR', 'Finance', 'Marketing', 'Operations', 'Sales'],
  locations,
  shifts = []
}) => {
  const [activeTab, setActiveTab] = useState<'single' | 'bulk'>('single');
  const [formMode, setFormMode] = useState<'manual' | 'biometric' | 'smartcard' | 'app'>('manual');
  const [selectedEmployees, setSelectedEmployees] = useState<number[]>([]);
  const [bulkDate, setBulkDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [bulkStatus, setBulkStatus] = useState<AttendanceStatus>(AttendanceStatus.PRESENT);
  const [bulkShift, setBulkShift] = useState<number>(1);
  const [bulkLocation, setBulkLocation] = useState<string>(locations?.[0] || '');
  const [bulkNotes, setBulkNotes] = useState<string>('');
  const [bulkCheckIn, setBulkCheckIn] = useState<string>('09:00');
  const [bulkCheckOut, setBulkCheckOut] = useState<string>('17:00');
  const [showEmployeeSelector, setShowEmployeeSelector] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isBulkSubmitting, setIsBulkSubmitting] = useState(false);
  const [selectedDepartmentFilter, setSelectedDepartmentFilter] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  
  // Add notification state
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'info';
    message: string;
  } | null>(null);
  
  // Single entry form state
  const [form, setForm] = useState<Omit<Attendance, 'id'>>({
    employeeId: 0,
    employeeName: '',
    date: new Date().toISOString().split('T')[0],
    checkInTime: '',
    checkOutTime: null,
    status: AttendanceStatus.PRESENT,
    location: null,
    notes: null,
    isRemote: false,
    department: '',
    position: '',
    shift: 1,
    workHours: 0,
    isRegularized: false,
    overtime: 0
  });
  
  // State for dynamic locations
  const [availableLocations, setAvailableLocations] = useState<string[]>([]);
  const [isLoadingLocations, setIsLoadingLocations] = useState(false);
  const [showLocationModal, setShowLocationModal] = useState(false);
  
  // State for single entry employee search
  const [singleEmployeeSearch, setSingleEmployeeSearch] = useState<string>('');
  const [showEmployeeDropdown, setShowEmployeeDropdown] = useState(false);
  
  // Ref for employee dropdown
  const employeeDropdownRef = useRef<HTMLDivElement>(null);
  
  // Handle clicking outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (employeeDropdownRef.current && !employeeDropdownRef.current.contains(event.target as Node)) {
        setShowEmployeeDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Function to load dynamic locations
  const loadDynamicLocations = async () => {
    setIsLoadingLocations(true);
    try {
      // Get combined locations from all sources
      const dynamicLocations = await locationService.getCombinedLocations(employees, []);
      setAvailableLocations(dynamicLocations);
      
      // Initialize bulk location with first available location if not set
      if (dynamicLocations.length > 0 && !bulkLocation) {
        setBulkLocation(dynamicLocations[0]);
      }
      
      console.log('✅ Loaded dynamic locations:', dynamicLocations);
    } catch (error) {
      console.error('❌ Error loading dynamic locations:', error);
      // Fallback to extracting from employees
      const fallbackLocations = locationService.extractLocationsFromEmployees(employees);
      setAvailableLocations(fallbackLocations);
    } finally {
      setIsLoadingLocations(false);
    }
  };

  // Load locations when component mounts or employees change
  useEffect(() => {
    loadDynamicLocations();
  }, [employees]);

  // Function to get employee-specific locations
  const getEmployeeLocations = async (employeeId: number): Promise<string[]> => {
    try {
      return await locationService.getEmployeeLocations(employeeId);
    } catch (error) {
      console.error('Error fetching employee locations:', error);
      return availableLocations;
    }
  };

  // Handle location added from modal
  const handleLocationAdded = (newLocation: string) => {
    if (!availableLocations.includes(newLocation)) {
      setAvailableLocations(prev => [...prev, newLocation].sort());
      // Set the new location as selected for bulk entry
      if (!bulkLocation) {
        setBulkLocation(newLocation);
      }
    }
  };

  // Enhanced location selector with employee-specific options and manage button
  const renderLocationSelector = (value: string | null, onChange: (value: string | null) => void, employeeId?: number, showManageButton = false) => {
    const [employeeSpecificLocations, setEmployeeSpecificLocations] = useState<string[]>([]);
    const [isLoadingEmployeeLocations, setIsLoadingEmployeeLocations] = useState(false);

    useEffect(() => {
      if (employeeId) {
        setIsLoadingEmployeeLocations(true);
        getEmployeeLocations(employeeId).then(locations => {
          setEmployeeSpecificLocations(locations);
          setIsLoadingEmployeeLocations(false);
        });
      }
    }, [employeeId]);

    const locationsToShow = employeeId && employeeSpecificLocations.length > 0 
      ? employeeSpecificLocations 
      : availableLocations;

    return (
      <div className="space-y-2">
        <div className="relative">
          <select
            value={value || ''}
            onChange={(e) => onChange(e.target.value || null)}
            className="w-full py-2 px-3 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled={isLoadingLocations || isLoadingEmployeeLocations}
          >
            <option value="">
              {isLoadingLocations || isLoadingEmployeeLocations ? 'Loading locations...' : 'Select Location'}
            </option>
            {locationsToShow.map((location, index) => (
              <option key={index} value={location}>
                {location}
              </option>
            ))}
          </select>
          {(isLoadingLocations || isLoadingEmployeeLocations) && (
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            </div>
          )}
        </div>
        
        {showManageButton && (
          <button
            type="button"
            onClick={() => setShowLocationModal(true)}
            className="w-full flex items-center justify-center px-3 py-1 bg-gray-100 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-200 transition-colors"
          >
            <Settings className="h-3 w-3 mr-1" />
            Manage Locations
          </button>
        )}
      </div>
    );
  };
  
  // Initialize form with editing data if provided
  useEffect(() => {
    if (editMode && attendanceToEdit) {
      setForm({
        employeeId: attendanceToEdit.employeeId,
        employeeName: attendanceToEdit.employeeName,
        date: attendanceToEdit.date,
        checkInTime: attendanceToEdit.checkInTime || '',
        checkOutTime: attendanceToEdit.checkOutTime || null,
        status: attendanceToEdit.status,
        location: attendanceToEdit.location || null,
        notes: attendanceToEdit.notes || null,
        isRemote: attendanceToEdit.isRemote || false,
        department: attendanceToEdit.department || '',
        position: attendanceToEdit.position || '',
        shift: attendanceToEdit.shift || 1,
        workHours: attendanceToEdit.workHours || 0,
        isRegularized: attendanceToEdit.isRegularized || false,
        overtime: attendanceToEdit.overtime || 0
      });
    }
  }, [editMode, attendanceToEdit]);

  // Calculate work hours when check-in and check-out times change
  useEffect(() => {
    if (form.checkInTime && form.checkOutTime) {
      const checkIn = new Date(`1970-01-01T${form.checkInTime}`);
      const checkOut = new Date(`1970-01-01T${form.checkOutTime}`);
      let diffHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
      
      // Handle overnight shifts
      if (diffHours < 0) {
        diffHours += 24;
      }
      
      setForm(prev => ({
        ...prev,
        workHours: parseFloat(diffHours.toFixed(2))
      }));
    }
  }, [form.checkInTime, form.checkOutTime]);

  // Form change handlers
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setForm(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setForm(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Employee selection handlers for single entry
  const handleEmployeeSelect = (employee: { id: number; name: string; department: string; position: string }) => {
    setForm(prev => ({
      ...prev,
      employeeId: employee.id,
      employeeName: employee.name,
      department: employee.department,
      position: employee.position
    }));
    setShowEmployeeSelector(false);
  };

  // Bulk entry handlers
  const handleBulkEmployeeToggle = (employeeId: number) => {
    if (selectedEmployees.includes(employeeId)) {
      setSelectedEmployees(selectedEmployees.filter(id => id !== employeeId));
    } else {
      setSelectedEmployees([...selectedEmployees, employeeId]);
    }
  };

  const handleSelectAllEmployees = () => {
    if (selectedEmployees.length === employees.length) {
      setSelectedEmployees([]);
    } else {
      setSelectedEmployees(employees.map(employee => employee.id));
    }
  };

  const handleBulkSubmit = async () => {
    if (selectedEmployees.length === 0) {
      showNotification('error', 'Please select at least one employee');
      return;
    }

    setIsBulkSubmitting(true);
    
    try {
      // Process each selected employee
      for (const employeeId of selectedEmployees) {
        const employee = employees.find(e => e.id === employeeId);
        if (employee) {
          const firstName = employee.firstName ? employee.firstName.toString().trim() : '';
          const lastName = employee.lastName ? employee.lastName.toString().trim() : '';
          const fullName = (firstName || lastName) ? `${firstName} ${lastName}`.trim() : '';
          const employeeName = fullName || (employee.name && employee.name.trim()) || `Employee ${employee.employeeId || employee.id}`;
          
          const attendanceEntry: Omit<Attendance, 'id'> = {
            employeeId,
            employeeName: employeeName,
            date: bulkDate,
            checkInTime: bulkCheckIn,
            checkOutTime: bulkCheckOut,
            status: bulkStatus,
            location: bulkLocation,
            notes: bulkNotes,
            isRemote: bulkLocation.toLowerCase().includes('remote'),
            department: employee.department,
            position: employee.position,
            shift: bulkShift,
            workHours: calculateWorkHours(bulkCheckIn, bulkCheckOut),
            isRegularized: false,
            overtime: 0
          };
          
          onSubmit(attendanceEntry);
          
          // Add delay to prevent API rate limiting in a real implementation
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
      
      // Reset form after submission
      setSelectedEmployees([]);
      showNotification('success', `Successfully marked attendance for ${selectedEmployees.length} employees.`);
    } catch (error) {
      console.error('Error submitting bulk attendance:', error);
      showNotification('error', 'Failed to submit attendance. Please try again.');
    } finally {
      setIsBulkSubmitting(false);
    }
  };

  // Single entry form submission
  const handleSingleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!form.employeeId || !form.date) {
      showNotification('error', 'Please fill in all required fields');
      return;
    }
    
    onSubmit(form);
    
    // Reset form if not in edit mode
    if (!editMode) {
      setForm({
        employeeId: 0,
        employeeName: '',
        date: new Date().toISOString().split('T')[0],
        checkInTime: '',
        checkOutTime: null,
        status: AttendanceStatus.PRESENT,
        location: null,
        notes: null,
        isRemote: false,
        department: '',
        position: '',
        shift: 1,
        workHours: 0,
        isRegularized: false,
        overtime: 0
      });
    }
    
    showNotification('success', 'Attendance record saved successfully!');
  };

  // CSV upload handler
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Check file type
    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
      setUploadError('Please upload a valid CSV file');
      return;
    }
    
    setIsUploading(true);
    setUploadError(null);
    
    // Simulate file upload and processing
    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const csvContent = event.target?.result as string;
        const lines = csvContent.split('\n');
        const headers = lines[0].split(',');
        
        // Validate required headers
        const requiredHeaders = ['employeeId', 'date', 'status'];
        const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
        
        if (missingHeaders.length > 0) {
          throw new Error(`Missing required headers: ${missingHeaders.join(', ')}`);
        }
        
        // Process rows
        let successCount = 0;
        const totalRows = lines.length - 1;
        
        // Simulate progress updates
        const interval = setInterval(() => {
          setUploadProgress(prev => {
            const newProgress = prev + Math.floor(Math.random() * 10);
            return newProgress > 100 ? 100 : newProgress;
          });
        }, 200);
        
        // Simulate processing completion
        setTimeout(() => {
          clearInterval(interval);
          setUploadProgress(100);
          setIsUploading(false);
          alert(`Successfully imported ${totalRows} attendance records.`);
          
          // Reset file input
          e.target.value = '';
        }, 2000);
        
      } catch (error) {
        setUploadError(`Error processing CSV: ${error instanceof Error ? error.message : 'Unknown error'}`);
        setIsUploading(false);
        setUploadProgress(0);
        
        // Reset file input
        e.target.value = '';
      }
    };
    
    reader.onerror = () => {
      setUploadError('Failed to read the file');
      setIsUploading(false);
      setUploadProgress(0);
      
      // Reset file input
      e.target.value = '';
    };
    
    reader.readAsText(file);
  };

  // Utility function to calculate work hours
  const calculateWorkHours = (checkIn: string, checkOut: string): number => {
    if (!checkIn || !checkOut) return 0;
    
    const inTime = new Date(`1970-01-01T${checkIn}`);
    const outTime = new Date(`1970-01-01T${checkOut}`);
    let diffHours = (outTime.getTime() - inTime.getTime()) / (1000 * 60 * 60);
    
    // Handle overnight shifts
    if (diffHours < 0) {
      diffHours += 24;
    }
    
    return parseFloat(diffHours.toFixed(2));
  };

  // Reset form function
  const resetForm = () => {
    if (activeTab === 'single') {
      setForm({
        employeeId: 0,
        employeeName: '',
        date: new Date().toISOString().split('T')[0],
        checkInTime: '',
        checkOutTime: null,
        status: AttendanceStatus.PRESENT,
        location: availableLocations.length > 0 ? availableLocations[0] : null,
        notes: null,
        isRemote: false,
        department: '',
        position: '',
        shift: 1,
        workHours: 0,
        isRegularized: false,
        overtime: 0
      });
    } else {
      // Reset bulk entry form
      setSelectedEmployees([]);
      setBulkStatus(AttendanceStatus.PRESENT);
      setBulkNotes('');
      setBulkCheckIn('09:00');
      setBulkCheckOut('17:00');
      setBulkLocation(availableLocations.length > 0 ? availableLocations[0] : '');
      setBulkShift(1);
      setSearchQuery('');
      setSelectedDepartmentFilter('');
    }
  };

  // Filter employees based on search query and department
  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = !searchQuery || 
      employee.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.department.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.id.toString().includes(searchQuery);
      
    const matchesDepartment = !selectedDepartmentFilter || 
      employee.department === selectedDepartmentFilter;
      
    return matchesSearch && matchesDepartment;
  });

  // Function to show notifications
  const showNotification = (type: 'success' | 'error' | 'info', message: string) => {
    setNotification({ type, message });
    // Auto-clear notification after 5 seconds
    setTimeout(() => {
      setNotification(null);
    }, 5000);
  };

  return (
    <div className="w-full min-h-screen bg-gray-50 p-4">
      {/* Notification Display */}
      {notification && (
        <div className={`mb-6 p-4 rounded-lg border ${
          notification.type === 'success' 
            ? 'bg-green-50 border-green-200 text-green-800' 
            : notification.type === 'error'
            ? 'bg-red-50 border-red-200 text-red-800'
            : 'bg-blue-50 border-blue-200 text-blue-800'
        }`}>
          <div className="flex items-start">
            {notification.type === 'success' ? (
              <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
            ) : notification.type === 'error' ? (
              <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
            ) : (
              <Info className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
            )}
            <div className="flex-1">
              <p className="font-medium">{notification.message}</p>
            </div>
            <button
              onClick={() => setNotification(null)}
              className="ml-2 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between mb-8">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Mark Attendance</h2>
          <p className="text-sm text-gray-600 mt-2">Record employee attendance for {bulkDate}</p>
        </div>
        
        <div className="mt-6 lg:mt-0 flex space-x-3">
          <button 
            className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
              activeTab === 'single' 
                ? 'bg-blue-600 text-white shadow-lg' 
                : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
            onClick={() => setActiveTab('single')}
          >
            <Users className="h-4 w-4 inline mr-2" /> Single Entry
          </button>
          <button 
            className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
              activeTab === 'bulk' 
                ? 'bg-blue-600 text-white shadow-lg' 
                : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
            onClick={() => setActiveTab('bulk')}
          >
            <BarChart className="h-4 w-4 inline mr-2" /> Bulk Entry
          </button>
        </div>
      </div>
      
      <div className="bg-white rounded-xl border border-gray-200 shadow-lg">
        <form onSubmit={activeTab === 'single' ? handleSingleSubmit : handleBulkSubmit}>
          <div className="p-8 border-b border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">Date</label>
                  <input
                    type="date"
                    value={bulkDate}
                    onChange={(e) => setBulkDate(e.target.value)}
                    className="w-full py-3 px-4 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
              </div>
              
              {activeTab === 'single' && (
                <>
                  <div className="md:col-span-1 lg:col-span-1">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Employee</label>
                    <div className="relative" ref={employeeDropdownRef}>
                      {/* Search Input */}
                      <div className="relative mb-2">
                        <input
                          type="text"
                          value={singleEmployeeSearch}
                          onChange={(e) => {
                            setSingleEmployeeSearch(e.target.value);
                            setShowEmployeeDropdown(true);
                          }}
                          onFocus={() => {
                            if (form.employeeId === 0) {
                              setShowEmployeeDropdown(true);
                            }
                          }}
                          placeholder={form.employeeId > 0 ? "Employee selected" : "Search employees by name, ID, or department..."}
                          className="w-full py-3 px-4 pl-10 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          disabled={form.employeeId > 0}
                        />
                        <User className="h-4 w-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                        {singleEmployeeSearch && (
                          <button
                            type="button"
                            onClick={() => {
                              setSingleEmployeeSearch('');
                              setForm(prev => ({ ...prev, employeeId: 0, employeeName: '', department: '', position: '' }));
                              setShowEmployeeDropdown(false);
                            }}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        )}
                      </div>

                      {/* Employee Dropdown */}
                      {showEmployeeDropdown && form.employeeId === 0 && (
                        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                          {(() => {
                            const searchTerm = singleEmployeeSearch.toLowerCase();
                            const filteredSingleEmployees = employees.filter(employee => {
                              const firstName = employee.firstName ? employee.firstName.toString().trim() : '';
                              const lastName = employee.lastName ? employee.lastName.toString().trim() : '';
                              const fullName = (firstName || lastName) ? `${firstName} ${lastName}`.trim() : '';
                              const displayName = fullName || (employee.name && employee.name.trim()) || `Employee ${employee.employeeId || employee.id}`;
                              
                              return displayName.toLowerCase().includes(searchTerm) ||
                                     employee.department.toLowerCase().includes(searchTerm) ||
                                     (employee.employeeId && employee.employeeId.toString().includes(searchTerm)) ||
                                     employee.id.toString().includes(searchTerm);
                            });

                            if (filteredSingleEmployees.length === 0) {
                              return (
                                <div className="px-4 py-3 text-sm text-gray-500 text-center">
                                  No employees found matching "{singleEmployeeSearch}"
                                </div>
                              );
                            }

                            return filteredSingleEmployees.map((employee) => {
                              const firstName = employee.firstName ? employee.firstName.toString().trim() : '';
                              const lastName = employee.lastName ? employee.lastName.toString().trim() : '';
                              const fullName = (firstName || lastName) ? `${firstName} ${lastName}`.trim() : '';
                              const displayName = fullName || (employee.name && employee.name.trim()) || `Employee ${employee.employeeId || employee.id}`;
                              const departmentInfo = employee.department && employee.department !== 'Unknown' 
                                ? employee.department 
                                : 'No Department';

                              return (
                                <button
                                  key={employee.id}
                                  type="button"
                                  onClick={() => {
                                    handleEmployeeSelect({
                                      id: employee.id,
                                      name: displayName,
                                      department: employee.department,
                                      position: employee.position || ''
                                    });
                                    setSingleEmployeeSearch('');
                                    setShowEmployeeDropdown(false);
                                  }}
                                  className="w-full px-4 py-3 text-left hover:bg-blue-50 focus:bg-blue-50 focus:outline-none transition-colors"
                                >
                                  <div className="flex items-center">
                                    <div className="h-8 w-8 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 mr-3 flex items-center justify-center text-white font-semibold text-sm">
                                      {displayName.charAt(0)}
                                    </div>
                                    <div className="flex-1">
                                      <div className="text-sm font-medium text-gray-900">
                                        {employee.employeeId ? `${employee.employeeId} - ` : ''}{displayName}
                                      </div>
                                      <div className="text-xs text-gray-500">
                                        {departmentInfo} • {employee.position || 'No Position'}
                                      </div>
                                    </div>
                                  </div>
                                </button>
                              );
                            });
                          })()}
                        </div>
                      )}

                      {/* Selected Employee Display */}
                      {form.employeeId > 0 && (
                        <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <div className="h-8 w-8 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 mr-3 flex items-center justify-center text-white font-semibold text-sm">
                                {form.employeeName.charAt(0)}
                              </div>
                              <div>
                                <div className="text-sm font-medium text-gray-900">{form.employeeName}</div>
                                <div className="text-xs text-gray-600">{form.department} • {form.position}</div>
                              </div>
                            </div>
                            <button
                              type="button"
                              onClick={() => {
                                setForm(prev => ({ ...prev, employeeId: 0, employeeName: '', department: '', position: '' }));
                                setSingleEmployeeSearch('');
                              }}
                              className="text-gray-400 hover:text-gray-600"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="md:col-span-1 lg:col-span-1">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Status</label>
                    <select
                      value={form.status}
                      onChange={(e) => setForm(prevForm => ({ ...prevForm, status: e.target.value as AttendanceStatus }))}
                      className="w-full py-3 px-4 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value={AttendanceStatus.PRESENT}>Present</option>
                      <option value={AttendanceStatus.ABSENT}>Absent</option>
                      <option value={AttendanceStatus.LATE}>Late</option>
                      <option value={AttendanceStatus.HALF_DAY}>Half Day</option>
                      <option value={AttendanceStatus.LEAVE}>Leave</option>
                      <option value={AttendanceStatus.WORK_FROM_HOME}>Work From Home</option>
                      <option value={AttendanceStatus.SICK_LEAVE}>Sick Leave</option>
                      <option value={AttendanceStatus.ANNUAL_LEAVE}>Annual Leave</option>
                    </select>
                  </div>
                </>
              )}
              
              {activeTab === 'bulk' && (
                <>
                  <div className="md:col-span-2 lg:col-span-3 xl:col-span-4">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Bulk Entry Method</label>
                    <div className="flex max-w-md">
                      <button
                        type="button"
                        className="flex-1 py-3 px-4 bg-blue-50 text-blue-700 border border-blue-300 rounded-l-lg text-sm font-medium"
                      >
                        Manual
                      </button>
                      <button
                        type="button"
                        className="flex-1 py-3 px-4 bg-white border border-gray-300 border-l-0 rounded-r-lg text-sm text-gray-700"
                        onClick={() => alert('CSV upload functionality would be implemented here')}
                      >
                        CSV Upload
                      </button>
                    </div>
                  </div>
                  
                  <div className="md:col-span-2 lg:col-span-3 xl:col-span-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-3">Default Status</label>
                      <select
                        value={bulkStatus}
                        onChange={(e) => setBulkStatus(e.target.value as AttendanceStatus)}
                        className="w-full py-3 px-4 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value={AttendanceStatus.PRESENT}>Present</option>
                        <option value={AttendanceStatus.ABSENT}>Absent</option>
                        <option value={AttendanceStatus.LATE}>Late</option>
                        <option value={AttendanceStatus.HALF_DAY}>Half Day</option>
                        <option value={AttendanceStatus.LEAVE}>Leave</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-3">Default Check-In Time</label>
                      <input
                        type="time"
                        value={bulkCheckIn}
                        onChange={(e) => setBulkCheckIn(e.target.value)}
                        className="w-full py-3 px-4 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-3">Default Check-Out Time</label>
                      <input
                        type="time"
                        value={bulkCheckOut}
                        onChange={(e) => setBulkCheckOut(e.target.value)}
                        className="w-full py-3 px-4 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-3">Default Location</label>
                      {renderLocationSelector(
                        bulkLocation,
                        (value: string | null) => setBulkLocation(value || ''),
                        undefined,
                        true
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-3">Default Shift</label>
                      <select
                        value={bulkShift}
                        onChange={(e) => setBulkShift(parseInt(e.target.value))}
                        className="w-full py-3 px-4 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value={1}>Morning Shift</option>
                        <option value={2}>Evening Shift</option>
                        <option value={3}>Night Shift</option>
                      </select>
                    </div>
                  </div>

                  <div className="md:col-span-2 lg:col-span-3 xl:col-span-4">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Default Notes</label>
                    <textarea
                      value={bulkNotes}
                      onChange={(e) => setBulkNotes(e.target.value)}
                      className="w-full py-3 px-4 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={4}
                      placeholder="Add default notes for bulk entry..."
                    />
                  </div>
                </>
              )}
            </div>
            
            {(activeTab === 'single' && form.status !== AttendanceStatus.ABSENT && form.status !== AttendanceStatus.LEAVE) && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mt-8 pt-8 border-t border-gray-100">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">Check-In Time</label>
                    <input
                      type="time"
                      value={form.checkInTime}
                      onChange={(e) => setForm(prevForm => ({ ...prevForm, checkInTime: e.target.value }))}
                    className="w-full py-3 px-4 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                </div>
                
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">Check-Out Time</label>
                    <input
                      type="time"
                      value={form.checkOutTime || ''}
                      onChange={(e) => setForm(prevForm => ({ ...prevForm, checkOutTime: e.target.value || null }))}
                    className="w-full py-3 px-4 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  {form.checkInTime && form.checkOutTime && (
                  <div className="mt-2 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                      Work hours: {calculateWorkHours(form.checkInTime, form.checkOutTime)} hours
                  </div>
                  )}
                </div>
                
                <div className="md:col-span-2 lg:col-span-1">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">Location</label>
                  {renderLocationSelector(
                    form.location,
                    (value: string | null) => setForm(prevForm => ({ ...prevForm, location: value })),
                    form.employeeId,
                    true
                  )}
                </div>
              </div>
            )}
            
            <div className="mt-8 pt-8 border-t border-gray-100">
              <label className="block text-sm font-semibold text-gray-700 mb-3">Notes</label>
              <textarea
                value={form.notes || ''}
                onChange={(e) => setForm(prevForm => ({ ...prevForm, notes: e.target.value || null }))}
                className="w-full py-3 px-4 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={4}
                placeholder="Add any additional notes here..."
              ></textarea>
            </div>
          </div>
          
          {activeTab === 'bulk' && (
            <div className="p-8 border-b border-gray-200">
              <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 lg:mb-0">Employee Selection</h3>
                <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search employees..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="py-2 pl-10 pr-4 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
                    />
                    <Users className="h-4 w-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  </div>
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      className="px-4 py-2 bg-blue-50 border border-blue-200 rounded-lg text-sm text-blue-700 font-medium hover:bg-blue-100 transition-colors"
                      onClick={() => handleSelectAllEmployees()}
                    >
                      Select All
                    </button>
                    <button
                      type="button"
                      className="px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-700 font-medium hover:bg-gray-100 transition-colors"
                      onClick={() => setSelectedEmployees([])}
                    >
                      Clear All
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-xl p-6">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-white">
                      <tr>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          <input
                            type="checkbox"
                            checked={selectedEmployees.length === employees.length}
                            onChange={(e) => handleSelectAllEmployees()}
                            className="rounded text-blue-600 focus:ring-blue-500 h-4 w-4"
                          />
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          Employee
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          Department
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          Position
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredEmployees.map((employee) => {
                        const isSelected = selectedEmployees.includes(employee.id);
                        return (
                          <tr key={employee.id} className={`hover:bg-blue-50 transition-colors ${isSelected ? 'bg-blue-25' : ''}`}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <input
                                type="checkbox"
                                checked={isSelected}
                                onChange={(e) => handleBulkEmployeeToggle(employee.id)}
                                className="rounded text-blue-600 focus:ring-blue-500 h-4 w-4"
                              />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                {employee.avatar ? (
                                  <img 
                                    className="h-10 w-10 rounded-full mr-4" 
                                    src={employee.avatar} 
                                    alt={employee.name} 
                                  />
                                ) : (
                                  <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 mr-4 flex items-center justify-center text-white font-semibold">
                                    {employee.name.charAt(0)}
                                  </div>
                                )}
                                <div>
                                  <div className="text-sm font-semibold text-gray-900">{employee.name}</div>
                                  <div className="text-xs text-gray-500">{employee.employeeId || `ID: ${employee.id}`}</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="inline-flex px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                {employee.department}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                              {employee.position}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <select
                                value={isSelected ? bulkStatus : AttendanceStatus.PRESENT}
                                onChange={(e) => setBulkStatus(e.target.value as AttendanceStatus)}
                                className="py-2 px-3 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                disabled={!isSelected}
                              >
                                <option value={AttendanceStatus.PRESENT}>Present</option>
                                <option value={AttendanceStatus.ABSENT}>Absent</option>
                                <option value={AttendanceStatus.LATE}>Late</option>
                                <option value={AttendanceStatus.HALF_DAY}>Half Day</option>
                                <option value={AttendanceStatus.LEAVE}>Leave</option>
                                <option value={AttendanceStatus.WORK_FROM_HOME}>WFH</option>
                              </select>
                            </td>
                          </tr>
                        );
                      })}
                      {filteredEmployees.length === 0 && (
                        <tr>
                          <td colSpan={5} className="px-6 py-8 text-center text-sm text-gray-500">
                            <div className="flex flex-col items-center">
                              <Users className="h-12 w-12 text-gray-300 mb-3" />
                              <p>No employees found with the given search criteria</p>
                            </div>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
                
                {selectedEmployees.length > 0 && (
                  <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <p className="text-sm text-blue-800">
                      <span className="font-semibold">{selectedEmployees.length}</span> employee{selectedEmployees.length !== 1 ? 's' : ''} selected for bulk attendance entry
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
          
          <div className="px-8 py-6 bg-gray-50 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <div className="text-sm text-gray-600">
              {activeTab === 'bulk' && selectedEmployees.length > 0 && (
                <span>Ready to mark attendance for {selectedEmployees.length} employee{selectedEmployees.length !== 1 ? 's' : ''}</span>
              )}
            </div>
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => resetForm()}
                className="px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Reset Form
              </button>
              <button
                type="submit"
                disabled={activeTab === 'bulk' && selectedEmployees.length === 0}
                className="px-8 py-3 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
              >
                {activeTab === 'single' ? 'Mark Attendance' : `Mark Attendance (${selectedEmployees.length})`}
              </button>
            </div>
          </div>
        </form>
      </div>
      
      {/* Location Modal */}
      <LocationModal
        isOpen={showLocationModal}
        onClose={() => setShowLocationModal(false)}
        onLocationAdded={handleLocationAdded}
        employees={employees}
      />
    </div>
  );
};

export default AttendanceForm; 