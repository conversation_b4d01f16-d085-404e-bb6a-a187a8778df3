import api from './api';
import { employeeApi } from './employeeApi';
import { leaveBalancesApi, leaveRequestsApi } from './leaveApi';

export interface EmployeePortalProfile {
  id: number;
  employeeId: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  email: string;
  department: string;
  designation: string;
  joinDate: string;
  employmentType: string;
  reportingManager: string;
  location: string;
  phone: string;
  profileImagePath?: string;
  
  // Personal Information
  gender: string;
  dateOfBirth: string;
  religion?: string;
  cnicNumber: string;
  cnicExpiryDate?: string;
  nationality?: string;
  maritalStatus?: string;
  bloodType?: string;
  fatherName?: string;
  status: string;
  statusDate?: string;
  notes?: string;
  specialInstructions?: string;
  
  // Employment Details
  employmentStatus?: string;
  employeeLevel?: string;
  probationEndDate?: string;
  noticePeriod?: string;
  reportingTo?: string;
  remoteWorkEligible?: boolean;
  nextReviewDate?: string;
  trainingRequirements?: string;
  workSchedule?: string;
  shiftType?: string;
  project?: string;
  
  // Compensation & Benefits
  totalSalary?: string;
  salaryTier?: string;
  salaryTrench?: string;
  cashAmount?: string;
  bankAmount?: string;
  paymentMode?: string;
  
  // Allowances
  foodAllowanceInSalary?: boolean;
  fuelAllowanceInSalary?: boolean;
  numberOfMeals?: string;
  fuelInLiters?: string;
  fuelAmount?: string;
  foodProvidedByCompany?: boolean;
  
  // Bank Details
  bankName?: string;
  bankBranch?: string;
  accountNumber?: string;
  accountTitle?: string;
  iban?: string;
  
  // Insurance
  healthInsuranceProvider?: string;
  healthInsurancePolicyNumber?: string;
  healthInsuranceExpiryDate?: string;
  lifeInsuranceProvider?: string;
  lifeInsurancePolicyNumber?: string;
  lifeInsuranceExpiryDate?: string;
  
  // Accommodation
  accommodationProvidedByEmployer?: boolean;
  accommodationType?: string;
  accommodationAddress?: string;
  
  // Contact Information
  mobileNumber: string;
  officialNumber?: string;
  officialEmail?: string;
  personalEmail?: string;
  permanentAddress?: string;
  currentAddress?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelationship?: string;
  linkedinProfile?: string;
  otherSocialProfiles?: string;
  
  // Education
  education: EmployeeEducationData[];
  
  // Experience
  experience: EmployeeExperienceData[];
  
  // Family
  family: EmployeeFamilyData[];
  
  // Skills
  skills: EmployeeSkillsData[];
  
  // Devices
  devices: EmployeeDeviceData[];
  
  // Projects
  projects: EmployeeProjectData[];
  
  // Vehicles
  vehicles: EmployeeVehicleData[];
  
  // Health Records
  healthRecords: EmployeeHealthData[];
}

export interface EmployeeEducationData {
  id: number;
  educationLevel: string;
  degree: string;
  major?: string;
  institution: string;
  graduationYear: string;
  grade?: string;
}

export interface EmployeeExperienceData {
  id: number;
  companyName: string;
  position: string;
  startDate: string;
  endDate?: string;
  description?: string;
  salary?: string;
  reasonForLeaving?: string;
}

export interface EmployeeFamilyData {
  id: number;
  name: string;
  dateOfBirth?: string;
  relationship: string;
  gender?: string;
  cnic?: string;
  occupation?: string;
  employer?: string;
  contactNumber?: string;
  type: string; // 'spouse', 'child', 'dependent'
}

export interface EmployeeSkillsData {
  id: number;
  professionalSkills?: string;
  technicalSkills?: string;
  certifications?: string;
  languages?: string;
}

export interface EmployeeDeviceData {
  id: number;
  deviceType: string;
  deviceBrand?: string;
  deviceModel?: string;
  serialNumber?: string;
  assignedDate: string;
  returnDate?: string;
  condition?: string;
  notes?: string;
}

export interface EmployeeProjectData {
  id: number;
  projectName: string;
  role: string;
  startDate: string;
  endDate?: string;
  description?: string;
  status: string;
}

export interface EmployeeVehicleData {
  id: number;
  vehicleType: string;
  make?: string;
  model?: string;
  year?: string;
  licensePlate?: string;
  assignedDate: string;
  returnDate?: string;
}

export interface EmployeeHealthData {
  id: number;
  recordType: string;
  recordDate: string;
  description?: string;
  doctorName?: string;
  hospitalName?: string;
  medication?: string;
  notes?: string;
}

export interface EmployeeLeaveData {
  balances: {
    leaveType: string;
    total: number;
    used: number;
    pending: number;
    remaining: number;
    carryForward?: number;
    expiryDate?: string;
  }[];
  recentRequests: {
    id: number;
    type: string;
    from: string;
    to: string;
    status: string;
    days: number;
    reason?: string;
  }[];
  pendingRequestsCount: number;
  totalDaysUsed: number;
  totalDaysRemaining: number;
}

export interface EmployeePayrollData {
  lastPayment: {
    date: string;
    amount: string;
    currency: string;
  };
  ytdEarnings: string;
  payslips: {
    id: string;
    period: string;
    date: string;
    amount: string;
    status: string;
    downloadUrl?: string;
  }[];
  bankDetails?: {
    bankName: string;
    accountNumber: string;
  };
}

export interface EmployeePerformanceData {
  currentRating?: number;
  lastReviewDate?: string;
  nextReviewDate?: string;
  goals: {
    id: number;
    title: string;
    status: 'pending' | 'in_progress' | 'completed';
    progress: number;
    deadline?: string;
  }[];
  achievements: {
    id: number;
    title: string;
    description: string;
    date: string;
    type: 'award' | 'recognition' | 'milestone';
  }[];
}

export interface EmployeeDocumentData {
  documents: {
    id: number;
    name: string;
    type: string;
    uploadDate: string;
    size: string;
    downloadUrl: string;
  }[];
  totalCount: number;
}

export interface EmployeeAttendanceData {
  summary: {
    currentMonth: {
      totalDays: number;
      presentDays: number;
      absentDays: number;
      leaveDays: number;
      holidayDays: number;
      workingDays: number;
      punctualityScore: number;
    };
    recentAttendance: {
      date: string;
      checkIn: string;
      checkOut: string;
      status: 'present' | 'absent' | 'late' | 'early_out';
      totalHours: number;
    }[];
  };
}

export interface EmployeeBenefitsData {
  salary: {
    totalSalary: string;
    salaryTier: string;
    salaryTrench: string;
    cashAmount: string;
    bankAmount: string;
    paymentMode: string;
  };
  allowances: {
    foodAllowanceInSalary: string;
    fuelAllowanceInSalary: string;
    numberOfMeals: number;
    fuelInLiters: number;
    fuelAmount: string;
    foodProvidedByCompany: boolean;
  };
  insurance: {
    healthInsuranceProvider?: string;
    healthInsurancePolicyNumber?: string;
    healthInsuranceExpiryDate?: string;
    lifeInsuranceProvider?: string;
    lifeInsurancePolicyNumber?: string;
    lifeInsuranceExpiryDate?: string;
  };
  bankDetails: {
    bankName?: string;
    bankBranch?: string;
    accountNumber?: string;
    accountTitle?: string;
    iban?: string;
  };
  accommodation?: {
    accommodationProvidedByEmployer: boolean;
    accommodationType?: string;
    accommodationAddress?: string;
  };
}

export interface EmployeeTrainingData {
  completedTrainings: {
    id: number;
    title: string;
    completionDate: string;
    certificateUrl?: string;
    score?: number;
  }[];
  upcomingTrainings: {
    id: number;
    title: string;
    startDate: string;
    duration: string;
    mandatory: boolean;
  }[];
  trainingRequirements: string[];
}

export interface EmployeeTeamData {
  reportees: {
    id: number;
    name: string;
    designation: string;
    department: string;
    email: string;
  }[];
  manager?: {
    id: number;
    name: string;
    designation: string;
    department: string;
    email: string;
  };
  colleagues: {
    id: number;
    name: string;
    designation: string;
    department: string;
    email: string;
  }[];
}

class EmployeePortalService {
  private baseUrl = '/api';

  /**
   * Get comprehensive employee profile by email with all relations
   */
  async getEmployeeProfile(userEmail: string): Promise<EmployeePortalProfile | null> {
    try {
      // Special case for admin user
      if (userEmail === '<EMAIL>') {
        return {
          id: 999999,
          employeeId: 'ADMIN-001',
          firstName: 'Super',
          middleName: '',
          lastName: 'Administrator',
          email: userEmail,
          department: 'IT Department',
          designation: 'System Administrator',
          joinDate: 'Not specified',
          employmentType: 'Full Time',
          reportingManager: 'Not specified',
          location: 'Head Office',
          phone: 'Not specified',
          profileImagePath: undefined,
          gender: 'Not specified',
          dateOfBirth: 'Not specified',
          religion: 'Not specified',
          cnicNumber: 'Not specified',
          cnicExpiryDate: '',
          nationality: 'Not specified',
          maritalStatus: 'Not specified',
          bloodType: 'Not specified',
          fatherName: 'Not specified',
          status: 'Active',
          statusDate: '',
          notes: '',
          specialInstructions: '',
          mobileNumber: 'Not specified',
          officialNumber: '',
          officialEmail: userEmail,
          personalEmail: '',
          permanentAddress: 'Not specified',
          currentAddress: 'Not specified',
          emergencyContactName: 'Not specified',
          emergencyContactPhone: 'Not specified',
          emergencyContactRelationship: 'Not specified',
          linkedinProfile: '',
          otherSocialProfiles: '',
          education: [],
          experience: [],
          family: [],
          skills: [],
          devices: [],
          projects: [],
          vehicles: [],
          healthRecords: []
        };
      }

      // Step 1: Get all employees to find the employee by email
      const employeesResponse = await employeeApi.getAll();
      
      if (!employeesResponse.success) {
        console.error('Failed to fetch employees');
        return null;
      }

      const employees = employeesResponse.data || employeesResponse.employees || [];
      
      // Find employee by email
      const employee = employees.find((emp: any) => {
        const officialEmail = emp.contact?.officialEmail || emp.officialEmail;
        const personalEmail = emp.contact?.personalEmail || emp.personalEmail;
        return officialEmail === userEmail || personalEmail === userEmail || emp.email === userEmail;
      });

      if (!employee) {
        console.error('Employee not found for email:', userEmail);
        return null;
      }

      // Step 2: Fetch detailed employee data with all relations using the comprehensive endpoint
      let detailedEmployee;
      try {
        const detailedResponse = await api.get(`/employees/${employee.id}`);
        detailedEmployee = detailedResponse.data.employee || detailedResponse.data;
        console.log('Fetched detailed employee data:', {
          id: detailedEmployee.id,
          name: `${detailedEmployee.firstName} ${detailedEmployee.lastName}`,
          hasEducation: detailedEmployee.educationEntries?.length > 0,
          hasExperience: detailedEmployee.experienceEntries?.length > 0,
          hasFamily: detailedEmployee.family?.length > 0,
          hasSkills: detailedEmployee.skills?.length > 0,
          hasDevices: detailedEmployee.deviceEntries?.length > 0,
          hasProjects: detailedEmployee.projectEntries?.length > 0,
          hasVehicles: detailedEmployee.vehicles?.length > 0,
          hasHealthRecords: detailedEmployee.healthRecords?.length > 0,
          hasJob: !!detailedEmployee.job,
          hasBenefit: !!detailedEmployee.benefit,
          hasContact: !!detailedEmployee.contact
        });
        
        // Debug: Log the actual structure of related entities
        if (detailedEmployee.job) {
          console.log('Job data structure:', Object.keys(detailedEmployee.job));
        }
        if (detailedEmployee.benefit) {
          console.log('Benefit data structure:', Object.keys(detailedEmployee.benefit));
          console.log('Benefit data sample:', {
            totalSalary: detailedEmployee.benefit.totalSalary,
            salaryTier: detailedEmployee.benefit.salaryTier,
            bankName: detailedEmployee.benefit.bankName,
            paymentMode: detailedEmployee.benefit.paymentMode
          });
        }
        if (detailedEmployee.contact) {
          console.log('Contact data structure:', Object.keys(detailedEmployee.contact));
        }
      } catch (error) {
        console.warn('Could not fetch detailed employee data, using basic data:', error);
        detailedEmployee = employee;
      }

      // Step 3: Build comprehensive profile from the detailed response
      const profile: EmployeePortalProfile = {
        id: detailedEmployee.id,
        employeeId: detailedEmployee.employeeId || `EMP-${detailedEmployee.id}`,
        firstName: detailedEmployee.firstName || 'Not specified',
        middleName: detailedEmployee.middleName || '',
        lastName: detailedEmployee.lastName || 'Not specified',
        email: detailedEmployee.contact?.officialEmail || detailedEmployee.contact?.personalEmail || detailedEmployee.officialEmail || detailedEmployee.personalEmail || detailedEmployee.email || userEmail,
        department: detailedEmployee.job?.department || detailedEmployee.department || 'Not specified',
        designation: detailedEmployee.job?.designation || detailedEmployee.designation || 'Not specified',
        joinDate: detailedEmployee.job?.joinDate || detailedEmployee.joinDate || 'Not specified',
        employmentType: detailedEmployee.job?.employmentType || detailedEmployee.employmentType || 'Full Time',
        reportingManager: detailedEmployee.job?.reportingTo || detailedEmployee.reportingManager || 'Not specified',
        location: detailedEmployee.job?.location || detailedEmployee.location || 'Not specified',
        phone: detailedEmployee.contact?.mobileNumber || detailedEmployee.mobileNumber || 'Not specified',
        profileImagePath: detailedEmployee.profileImagePath,
        
        // Personal Information
        gender: detailedEmployee.gender || 'Not specified',
        dateOfBirth: detailedEmployee.dateOfBirth || 'Not specified',
        religion: detailedEmployee.religion || 'Not specified',
        cnicNumber: detailedEmployee.cnicNumber || 'Not specified',
        cnicExpiryDate: detailedEmployee.cnicExpiryDate || '',
        nationality: detailedEmployee.nationality || 'Not specified',
        maritalStatus: detailedEmployee.maritalStatus || 'Not specified',
        bloodType: detailedEmployee.bloodType || 'Not specified',
        fatherName: detailedEmployee.fatherName || 'Not specified',
        status: detailedEmployee.status || detailedEmployee.employmentStatus || 'Active',
        statusDate: detailedEmployee.statusDate || '',
        notes: detailedEmployee.notes || '',
        specialInstructions: detailedEmployee.specialInstructions || '',
        
        // Contact Information
        mobileNumber: detailedEmployee.contact?.mobileNumber || detailedEmployee.mobileNumber || 'Not specified',
        officialNumber: detailedEmployee.contact?.officialNumber || detailedEmployee.officialNumber || '',
        officialEmail: detailedEmployee.contact?.officialEmail || detailedEmployee.officialEmail || '',
        personalEmail: detailedEmployee.contact?.personalEmail || detailedEmployee.personalEmail || '',
        permanentAddress: detailedEmployee.contact?.permanentAddress || detailedEmployee.permanentAddress || 'Not specified',
        currentAddress: detailedEmployee.contact?.currentAddress || detailedEmployee.currentAddress || 'Not specified',
        emergencyContactName: detailedEmployee.contact?.emergencyContactName || detailedEmployee.emergencyContactName || 'Not specified',
        emergencyContactPhone: detailedEmployee.contact?.emergencyContactPhone || detailedEmployee.emergencyContactPhone || 'Not specified',
        emergencyContactRelationship: detailedEmployee.contact?.emergencyContactRelationship || detailedEmployee.emergencyContactRelationship || 'Not specified',
        linkedinProfile: detailedEmployee.contact?.linkedinProfile || detailedEmployee.linkedinProfile || '',
        otherSocialProfiles: detailedEmployee.contact?.otherSocialProfiles || detailedEmployee.otherSocialProfiles || '',
        
        // Education - Use educationEntries from backend response
        education: (detailedEmployee.educationEntries || detailedEmployee.education || []).map((edu: any) => ({
          id: edu.id,
          educationLevel: edu.educationLevel || 'Not specified',
          degree: edu.degree || 'Not specified',
          major: edu.major || '',
          institution: edu.institution || 'Not specified',
          graduationYear: edu.graduationYear || 'Not specified',
          grade: edu.grade || ''
        })),
        
        // Experience - Use experienceEntries from backend response
        experience: (detailedEmployee.experienceEntries || detailedEmployee.experience || []).map((exp: any) => ({
          id: exp.id,
          companyName: exp.companyName || 'Not specified',
          position: exp.position || 'Not specified',
          startDate: exp.startDate || 'Not specified',
          endDate: exp.endDate || '',
          description: exp.description || '',
          salary: exp.salary || '',
          reasonForLeaving: exp.reasonForLeaving || ''
        })),
        
        // Family - Backend splits family into children and dependents, but we need the original family array
        family: [
          ...(detailedEmployee.children || []),
          ...(detailedEmployee.dependents || []),
          // Add spouse if exists
          ...(detailedEmployee.spouseName ? [{
            id: Date.now(),
            name: detailedEmployee.spouseName,
            dateOfBirth: detailedEmployee.spouseDateOfBirth || '',
            relationship: 'spouse',
            gender: '',
            cnic: detailedEmployee.spouseCNIC || '',
            occupation: detailedEmployee.spouseOccupation || '',
            employer: detailedEmployee.spouseEmployer || '',
            contactNumber: detailedEmployee.spouseContactNumber || '',
            type: 'spouse'
          }] : [])
        ].map((fam: any) => ({
          id: fam.id,
          name: fam.name || 'Not specified',
          dateOfBirth: fam.dateOfBirth || '',
          relationship: fam.relationship || 'Not specified',
          gender: fam.gender || '',
          cnic: fam.cnic || '',
          occupation: fam.occupation || '',
          employer: fam.employer || '',
          contactNumber: fam.contactNumber || '',
          type: fam.type || 'dependent'
        })),
        
        // Skills - Backend flattens skills to top level fields
        skills: [{
          id: 1,
          professionalSkills: detailedEmployee.professionalSkills || '',
          technicalSkills: detailedEmployee.technicalSkills || '',
          certifications: detailedEmployee.certifications || '',
          languages: detailedEmployee.languages || ''
        }],
        
        // Devices - Use deviceEntries from backend response
        devices: (detailedEmployee.deviceEntries || detailedEmployee.devices || []).map((device: any) => ({
          id: device.id,
          deviceType: device.deviceType || 'Not specified',
          deviceBrand: device.deviceBrand || '',
          deviceModel: device.deviceModel || '',
          serialNumber: device.serialNumber || '',
          assignedDate: device.assignedDate || 'Not specified',
          returnDate: device.returnDate || '',
          condition: device.condition || '',
          notes: device.notes || ''
        })),
        
        // Projects - Use projectEntries from backend response
        projects: (detailedEmployee.projectEntries || detailedEmployee.projects || []).map((project: any) => ({
          id: project.id,
          projectName: project.projectName || 'Not specified',
          role: project.role || 'Not specified',
          startDate: project.startDate || 'Not specified',
          endDate: project.endDate || '',
          description: project.description || '',
          status: project.status || 'Unknown'
        })),
        
        // Vehicles - Backend flattens vehicles to top level fields
        vehicles: detailedEmployee.vehicleType ? [{
          id: 1,
          vehicleType: detailedEmployee.vehicleType || 'Not specified',
          make: '',
          model: detailedEmployee.vehicleMakeModel || '',
          year: '',
          licensePlate: detailedEmployee.registrationNumber || '',
          assignedDate: detailedEmployee.handingOverDate || 'Not specified',
          returnDate: detailedEmployee.returnDate || ''
        }] : [],
        
        // Health Records - Backend flattens health records to top level fields
        healthRecords: detailedEmployee.medicalHistory ? [{
          id: 1,
          recordType: 'Medical History',
          recordDate: new Date().toISOString().split('T')[0],
          description: detailedEmployee.medicalHistory || '',
          doctorName: '',
          hospitalName: '',
          medication: detailedEmployee.regularMedications || '',
          notes: `Blood Group: ${detailedEmployee.bloodGroup || 'N/A'}, Allergies: ${detailedEmployee.allergies || 'N/A'}, Chronic Conditions: ${detailedEmployee.chronicConditions || 'N/A'}`
        }] : [],

        // Employment Details - Additional fields from job relation
        employmentStatus: detailedEmployee.job?.employmentStatus || detailedEmployee.employmentStatus,
        employeeLevel: detailedEmployee.job?.employeeLevel || detailedEmployee.employeeLevel,
        probationEndDate: detailedEmployee.job?.probationEndDate || detailedEmployee.probationEndDate,
        noticePeriod: detailedEmployee.job?.noticePeriod || detailedEmployee.noticePeriod,
        reportingTo: detailedEmployee.job?.reportingTo || detailedEmployee.reportingTo,
        remoteWorkEligible: detailedEmployee.job?.remoteWorkEligible || detailedEmployee.remoteWorkEligible,
        nextReviewDate: detailedEmployee.job?.nextReviewDate || detailedEmployee.nextReviewDate,
        trainingRequirements: detailedEmployee.job?.trainingRequirements || detailedEmployee.trainingRequirements,
        workSchedule: detailedEmployee.job?.workSchedule || detailedEmployee.workSchedule,
        shiftType: detailedEmployee.job?.shiftType || detailedEmployee.shiftType,
        project: detailedEmployee.job?.project || detailedEmployee.project,

        // Compensation & Benefits - From benefit relation
        totalSalary: detailedEmployee.benefit?.totalSalary || detailedEmployee.totalSalary,
        salaryTier: detailedEmployee.benefit?.salaryTier || detailedEmployee.salaryTier,
        salaryTrench: detailedEmployee.benefit?.salaryTrench || detailedEmployee.salaryTrench,
        cashAmount: detailedEmployee.benefit?.cashAmount || detailedEmployee.cashAmount,
        bankAmount: detailedEmployee.benefit?.bankAmount || detailedEmployee.bankAmount,
        paymentMode: detailedEmployee.benefit?.paymentMode || detailedEmployee.paymentMode,

        // Allowances - From benefit relation
        foodAllowanceInSalary: detailedEmployee.benefit?.foodAllowanceInSalary || detailedEmployee.foodAllowanceInSalary,
        fuelAllowanceInSalary: detailedEmployee.benefit?.fuelAllowanceInSalary || detailedEmployee.fuelAllowanceInSalary,
        numberOfMeals: detailedEmployee.benefit?.numberOfMeals || detailedEmployee.numberOfMeals,
        fuelInLiters: detailedEmployee.benefit?.fuelInLiters || detailedEmployee.fuelInLiters,
        fuelAmount: detailedEmployee.benefit?.fuelAmount || detailedEmployee.fuelAmount,
        foodProvidedByCompany: detailedEmployee.benefit?.foodProvidedByCompany || detailedEmployee.foodProvidedByCompany,

        // Bank Details - From benefit relation
        bankName: detailedEmployee.benefit?.bankName || detailedEmployee.bankName,
        bankBranch: detailedEmployee.benefit?.bankBranch || detailedEmployee.bankBranch,
        accountNumber: detailedEmployee.benefit?.accountNumber || detailedEmployee.accountNumber,
        accountTitle: detailedEmployee.benefit?.accountTitle || detailedEmployee.accountTitle,
        iban: detailedEmployee.benefit?.iban || detailedEmployee.iban,

        // Insurance - From benefit relation
        healthInsuranceProvider: detailedEmployee.benefit?.healthInsuranceProvider || detailedEmployee.healthInsuranceProvider,
        healthInsurancePolicyNumber: detailedEmployee.benefit?.healthInsurancePolicyNumber || detailedEmployee.healthInsurancePolicyNumber,
        healthInsuranceExpiryDate: detailedEmployee.benefit?.healthInsuranceExpiryDate || detailedEmployee.healthInsuranceExpiryDate,
        lifeInsuranceProvider: detailedEmployee.benefit?.lifeInsuranceProvider || detailedEmployee.lifeInsuranceProvider,
        lifeInsurancePolicyNumber: detailedEmployee.benefit?.lifeInsurancePolicyNumber || detailedEmployee.lifeInsurancePolicyNumber,
        lifeInsuranceExpiryDate: detailedEmployee.benefit?.lifeInsuranceExpiryDate || detailedEmployee.lifeInsuranceExpiryDate,

        // Accommodation - From benefit relation
        accommodationProvidedByEmployer: detailedEmployee.benefit?.accommodationProvidedByEmployer || detailedEmployee.accommodationProvidedByEmployer,
        accommodationType: detailedEmployee.benefit?.accommodationType || detailedEmployee.accommodationType,
        accommodationAddress: detailedEmployee.benefit?.accommodationAddress || detailedEmployee.accommodationAddress
      };

      console.log('Built comprehensive profile with:', {
        educationCount: profile.education.length,
        experienceCount: profile.experience.length,
        familyCount: profile.family.length,
        skillsCount: profile.skills.length,
        devicesCount: profile.devices.length,
        projectsCount: profile.projects.length,
        vehiclesCount: profile.vehicles.length,
        healthRecordsCount: profile.healthRecords.length,
        compensation: {
          totalSalary: profile.totalSalary,
          salaryTier: profile.salaryTier,
          bankName: profile.bankName,
          paymentMode: profile.paymentMode,
          foodAllowanceInSalary: profile.foodAllowanceInSalary,
          fuelAllowanceInSalary: profile.fuelAllowanceInSalary
        }
      });

      return profile;
      
    } catch (error) {
      console.error('Error fetching comprehensive employee profile:', error);
      return null;
    }
  }

  /**
   * Get employee leave data including balances and recent requests
   */
  async getEmployeeLeaveData(employeeId: number): Promise<EmployeeLeaveData> {
    try {
      // Fetch leave balances
      const balancesResponse = await leaveBalancesApi.getByEmployeeId(employeeId);
      
      // Fetch recent leave requests - for now using getAll and filtering client-side
      const requestsResponse = await leaveRequestsApi.getAll();
      
      const balances = balancesResponse.data || [];
      const allRequests = requestsResponse.data || [];
      const requests = allRequests.filter((req: any) => req.employeeId === employeeId);

      // Calculate totals
      const totalDaysUsed = balances.reduce((sum, balance) => sum + (balance.used || 0), 0);
      const totalDaysRemaining = balances.reduce((sum, balance) => sum + (balance.remaining || 0), 0);
      const pendingRequestsCount = requests.filter((req: any) => req.status === 'pending').length;

      // Get recent requests (last 5)
      const recentRequests = requests
        .sort((a: any, b: any) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime())
        .slice(0, 5)
        .map((req: any) => ({
          id: req.id,
          type: req.leaveType,
          from: req.startDate,
          to: req.endDate,
          status: req.status,
          days: req.totalDays || 1,
          reason: req.reason
        }));

      return {
        balances: balances.map(balance => ({
          leaveType: balance.leaveType,
          total: balance.total || 0,
          used: balance.used || 0,
          pending: balance.pending || 0,
          remaining: balance.remaining || 0,
          carryForward: balance.carryForward,
          expiryDate: balance.expiryDate
        })),
        recentRequests,
        pendingRequestsCount,
        totalDaysUsed,
        totalDaysRemaining
      };
    } catch (error) {
      console.error('Error fetching employee leave data:', error);
      return {
        balances: [],
        recentRequests: [],
        pendingRequestsCount: 0,
        totalDaysUsed: 0,
        totalDaysRemaining: 0
      };
    }
  }

  /**
   * Get employee payroll data
   */
  async getEmployeePayrollData(employeeId: number): Promise<EmployeePayrollData> {
    try {
      // For now, return mock data structure but with API integration ready
      // TODO: Replace with actual payroll API when available
      const response = await api.get(`/employees/${employeeId}/payroll`);
      
      if (response.data) {
        return response.data;
      }
      
      // Fallback to structured mock data
      return {
        lastPayment: {
          date: new Date().toISOString().split('T')[0],
          amount: '0.00',
          currency: 'PKR'
        },
        ytdEarnings: '0.00',
        payslips: [],
        bankDetails: undefined
      };
    } catch (error) {
      console.error('Error fetching employee payroll data:', error);
      // Return empty structure
      return {
        lastPayment: {
          date: 'N/A',
          amount: 'N/A',
          currency: 'PKR'
        },
        ytdEarnings: 'N/A',
        payslips: []
      };
    }
  }

  /**
   * Get employee performance data
   */
  async getEmployeePerformanceData(employeeId: number): Promise<EmployeePerformanceData> {
    try {
      // TODO: Replace with actual performance API when available
      const response = await api.get(`/employees/${employeeId}/performance`);
      
      if (response.data) {
        return response.data;
      }
      
      // Return structured empty data
      return {
        goals: [],
        achievements: []
      };
    } catch (error) {
      console.error('Error fetching employee performance data:', error);
      return {
        goals: [],
        achievements: []
      };
    }
  }

  /**
   * Get employee documents
   */
  async getEmployeeDocuments(employeeId: number): Promise<EmployeeDocumentData> {
    try {
      const response = await api.get(`/employees/${employeeId}/documents`);
      
      if (response.data && response.data.documents) {
        return {
          documents: response.data.documents.map((doc: any) => ({
            id: doc.id,
            name: doc.fileName || doc.name,
            type: doc.documentType || 'Document',
            uploadDate: doc.uploadDate || doc.createdAt,
            size: doc.fileSize || 'N/A',
            downloadUrl: doc.filePath || '#'
          })),
          totalCount: response.data.documents.length
        };
      }
      
      return {
        documents: [],
        totalCount: 0
      };
    } catch (error) {
      console.error('Error fetching employee documents:', error);
      return {
        documents: [],
        totalCount: 0
      };
    }
  }

  /**
   * Update employee profile information
   */
  async updateEmployeeProfile(employeeId: number, profileData: Partial<EmployeePortalProfile>): Promise<boolean> {
    try {
      const response = await api.put(`/employees/${employeeId}`, profileData);
      return response.data?.success || false;
    } catch (error) {
      console.error('Error updating employee profile:', error);
      return false;
    }
  }

  /**
   * Submit leave request
   */
  async submitLeaveRequest(employeeId: number, leaveData: {
    leaveType: string;
    startDate: string;
    endDate: string;
    reason: string;
    totalDays: number;
  }): Promise<boolean> {
    try {
      // Direct API call since create method doesn't exist in leaveRequestsApi
      const token = localStorage.getItem('authToken');
      const response = await api.post('/api/leave-requests', {
        employeeId,
        ...leaveData
      }, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
        }
      });
      return response.data?.success || false;
    } catch (error) {
      console.error('Error submitting leave request:', error);
      return false;
    }
  }

  /**
   * Get comprehensive employee attendance data
   */
  async getEmployeeAttendanceData(employeeId: number): Promise<EmployeeAttendanceData> {
    try {
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1;
      const currentYear = currentDate.getFullYear();
      
      // Fetch attendance summary and recent records
      const [summaryResponse, attendanceResponse] = await Promise.all([
        api.get(`/api/attendance/summary?employeeId=${employeeId}&month=${currentMonth}&year=${currentYear}`),
        api.get(`/api/attendance/employee/${employeeId}?limit=30`) // Last 30 records
      ]);
      
      const summary = summaryResponse.data || {};
      const attendanceRecords = attendanceResponse.data || [];
      
      // Process recent attendance data
      const recentAttendance = attendanceRecords.slice(0, 10).map((record: any) => ({
        date: record.date,
        checkIn: record.checkInTime || 'N/A',
        checkOut: record.checkOutTime || 'N/A',
        status: record.status || 'present',
        totalHours: record.totalHours || 0
      }));
      
      return {
        summary: {
          currentMonth: {
            totalDays: summary.totalDays || 0,
            presentDays: summary.presentDays || 0,
            absentDays: summary.absentDays || 0,
            leaveDays: summary.leaveDays || 0,
            holidayDays: summary.holidayDays || 0,
            workingDays: summary.workingDays || 0,
            punctualityScore: summary.punctualityScore || 0
          },
          recentAttendance
        }
      };
    } catch (error) {
      console.error('Error fetching employee attendance data:', error);
      return {
        summary: {
          currentMonth: {
            totalDays: 0,
            presentDays: 0,
            absentDays: 0,
            leaveDays: 0,
            holidayDays: 0,
            workingDays: 0,
            punctualityScore: 0
          },
          recentAttendance: []
        }
      };
    }
  }

  /**
   * Get employee benefits and salary data
   */
  async getEmployeeBenefitsData(employeeId: number): Promise<EmployeeBenefitsData> {
    try {
      // Get employee details with benefits info
      const response = await employeeApi.getById(employeeId);
      
      if (!response.success || !response.data) {
        throw new Error('Employee data not found');
      }
      
      const employee = response.data as any;
      const benefit = employee.benefit || {};
      
      return {
        salary: {
          totalSalary: benefit.totalSalary || '0',
          salaryTier: benefit.salaryTier || 'N/A',
          salaryTrench: benefit.salaryTrench || 'N/A',
          cashAmount: benefit.cashAmount || '0',
          bankAmount: benefit.bankAmount || '0',
          paymentMode: benefit.paymentMode || 'N/A'
        },
        allowances: {
          foodAllowanceInSalary: benefit.foodAllowanceInSalary || '0',
          fuelAllowanceInSalary: benefit.fuelAllowanceInSalary || '0',
          numberOfMeals: benefit.numberOfMeals || 0,
          fuelInLiters: benefit.fuelInLiters || 0,
          fuelAmount: benefit.fuelAmount || '0',
          foodProvidedByCompany: benefit.foodProvidedByCompany || false
        },
        insurance: {
          healthInsuranceProvider: benefit.healthInsuranceProvider,
          healthInsurancePolicyNumber: benefit.healthInsurancePolicyNumber,
          healthInsuranceExpiryDate: benefit.healthInsuranceExpiryDate,
          lifeInsuranceProvider: benefit.lifeInsuranceProvider,
          lifeInsurancePolicyNumber: benefit.lifeInsurancePolicyNumber,
          lifeInsuranceExpiryDate: benefit.lifeInsuranceExpiryDate
        },
        bankDetails: {
          bankName: benefit.bankName,
          bankBranch: benefit.bankBranch,
          accountNumber: benefit.accountNumber,
          accountTitle: benefit.accountTitle,
          iban: benefit.iban
        },
        accommodation: {
          accommodationProvidedByEmployer: benefit.accommodationProvidedByEmployer || false,
          accommodationType: benefit.accommodationType,
          accommodationAddress: benefit.accommodationAddress
        }
      };
    } catch (error) {
      console.error('Error fetching employee benefits data:', error);
      return {
        salary: {
          totalSalary: 'N/A',
          salaryTier: 'N/A',
          salaryTrench: 'N/A',
          cashAmount: 'N/A',
          bankAmount: 'N/A',
          paymentMode: 'N/A'
        },
        allowances: {
          foodAllowanceInSalary: 'N/A',
          fuelAllowanceInSalary: 'N/A',
          numberOfMeals: 0,
          fuelInLiters: 0,
          fuelAmount: 'N/A',
          foodProvidedByCompany: false
        },
        insurance: {},
        bankDetails: {},
        accommodation: {
          accommodationProvidedByEmployer: false
        }
      };
    }
  }

  /**
   * Get employee training data
   */
  async getEmployeeTrainingData(employeeId: number): Promise<EmployeeTrainingData> {
    try {
      // Get employee details with training requirements
      const response = await employeeApi.getById(employeeId);
      
      if (!response.success || !response.data) {
        throw new Error('Employee data not found');
      }
      
      const employee = response.data as any;
      const trainingRequirements = (employee as any).job?.trainingRequirements 
        ? (employee as any).job.trainingRequirements.split(',').map((req: string) => req.trim()).filter(Boolean)
        : [];
      
      // For now, return structured data with training requirements from job
      // TODO: Integrate with actual training management system when available
      return {
        completedTrainings: [],
        upcomingTrainings: [],
        trainingRequirements
      };
    } catch (error) {
      console.error('Error fetching employee training data:', error);
      return {
        completedTrainings: [],
        upcomingTrainings: [],
        trainingRequirements: []
      };
    }
  }

  /**
   * Get employee team data (manager, reportees, colleagues)
   */
  async getEmployeeTeamData(employeeId: number): Promise<EmployeeTeamData> {
    try {
      // Get all employees to find team relationships
      const response = await employeeApi.getAll();
      
      if (!response.success) {
        throw new Error('Failed to fetch employees');
      }
      
      const employees = response.data || response.employees || [];
      const currentEmployee = employees.find((emp: any) => emp.id === employeeId);
      
      if (!currentEmployee) {
        throw new Error('Current employee not found');
      }
      
      const currentDepartment = (currentEmployee as any).job?.department || (currentEmployee as any).department;
      const reportingTo = (currentEmployee as any).job?.reportingTo;
      
      // Find manager by name (if reportingTo is specified)
      const manager = reportingTo 
        ? employees.find((emp: any) => 
            `${emp.firstName} ${emp.lastName}`.toLowerCase().includes(reportingTo.toLowerCase())
          )
        : undefined;
      
      // Find reportees (employees who report to this employee)
      const reportees = employees.filter((emp: any) => {
        const empReportingTo = emp.job?.reportingTo;
        const currentEmployeeName = `${currentEmployee.firstName} ${currentEmployee.lastName}`;
        return empReportingTo && empReportingTo.toLowerCase().includes(currentEmployeeName.toLowerCase());
      });
      
      // Find colleagues (same department, excluding self and reportees)
      const colleagues = employees.filter((emp: any) => {
        const empDepartment = emp.job?.department || emp.department;
        return emp.id !== employeeId && 
               empDepartment === currentDepartment &&
               !reportees.some((reportee: any) => reportee.id === emp.id);
      }).slice(0, 10); // Limit to 10 colleagues
      
      const formatEmployee = (emp: any) => ({
        id: emp.id,
        name: `${emp.firstName} ${emp.lastName}`,
        designation: emp.job?.designation || emp.designation || 'N/A',
        department: emp.job?.department || emp.department || 'N/A',
        email: emp.contact?.officialEmail || emp.officialEmail || 'N/A'
      });
      
      return {
        reportees: reportees.map(formatEmployee),
        manager: manager ? formatEmployee(manager) : undefined,
        colleagues: colleagues.map(formatEmployee)
      };
    } catch (error) {
      console.error('Error fetching employee team data:', error);
      return {
        reportees: [],
        manager: undefined,
        colleagues: []
      };
    }
  }

  /**
   * Enhanced payroll data fetching with real employee benefit data
   */
  async getEmployeePayrollDataEnhanced(employeeId: number): Promise<EmployeePayrollData> {
    try {
      // Get employee details with benefits info
      const response = await employeeApi.getById(employeeId);
      
      if (!response.success || !response.data) {
        throw new Error('Employee data not found');
      }
      
      const employee = response.data as any;
      const benefit = employee.benefit || {};
      
      return {
        lastPayment: {
          date: new Date().toISOString().split('T')[0],
          amount: benefit.totalSalary || '0',
          currency: 'PKR'
        },
        ytdEarnings: (parseFloat(benefit.totalSalary || '0') * 12).toString(),
        payslips: [], // TODO: Integrate with actual payroll system
        bankDetails: {
          bankName: benefit.bankName || 'N/A',
          accountNumber: benefit.accountNumber || 'N/A'
        }
      };
    } catch (error) {
      console.error('Error fetching enhanced payroll data:', error);
      return {
        lastPayment: {
          date: 'N/A',
          amount: 'N/A',
          currency: 'PKR'
        },
        ytdEarnings: 'N/A',
        payslips: []
      };
    }
  }
}

export const employeePortalService = new EmployeePortalService();
export default employeePortalService; 