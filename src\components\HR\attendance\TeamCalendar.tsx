import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Users, CalendarDays, Calendar as CalendarIcon, Filter, Download } from 'lucide-react';
import { Attendance, AttendanceStatus, Shift } from '../../../types/attendance';

interface TeamCalendarProps {
  attendances: Attendance[];
  shifts: Shift[];
  employees?: any[];
  holidays?: Array<{
    date: string;
    name: string;
    type?: 'public' | 'company' | 'religious' | 'optional';
  }>;
  weekendDays?: number[];
  shiftAssignments?: Array<{
    employeeId: number;
    shiftId: number;
    startDate: string;
    endDate?: string;
    isPermanent?: boolean;
  }>;
  onDateChange?: (date: string) => void;
}

interface EmployeeAttendanceData {
  employeeId: number;
  employeeName: string;
  department: string;
  position?: string;
  profileImagePath?: string;
  attendances: Record<string, Attendance>;
}

const TeamCalendar: React.FC<TeamCalendarProps> = ({ attendances, shifts, employees = [], holidays = [], weekendDays = [], shiftAssignments = [], onDateChange }) => {
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [calendarDays, setCalendarDays] = useState<Date[]>([]);
  const [employeeData, setEmployeeData] = useState<EmployeeAttendanceData[]>([]);
  const [filteredEmployeeData, setFilteredEmployeeData] = useState<EmployeeAttendanceData[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDepartment, setFilterDepartment] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<AttendanceStatus | ''>('');
  const [departments, setDepartments] = useState<string[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState<string>('This Month');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Helper function to format date as YYYY-MM-DD in local timezone
  const formatLocalDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Helper function to check if a date is a weekend
  const isWeekend = (date: Date): boolean => {
    // Use configurable weekend days, fallback to default [0, 6] (Sunday, Saturday)
    const defaultWeekends = [0, 6];
    const weekends = weekendDays.length > 0 ? weekendDays : defaultWeekends;
    return weekends.includes(date.getDay());
  };

  // Helper function to get employee's shift for a specific date
  const getEmployeeShift = (employeeId: number, date: Date): Shift | null => {
    const dateStr = formatLocalDate(date);
    
    // Find the active shift assignment for this employee on this date
    const assignment = shiftAssignments.find(sa => {
      if (sa.employeeId !== employeeId) return false;
      
      // Check if the date falls within the assignment period
      const startDate = new Date(sa.startDate);
      const endDate = sa.endDate ? new Date(sa.endDate) : null;
      
      if (date < startDate) return false;
      if (endDate && date > endDate) return false;
      
      return true;
    });
    
    if (!assignment) return null;
    
    // Find the actual shift
    return shifts.find(s => s.id === assignment.shiftId) || null;
  };

  // Helper function to check if a date is a weekend for a specific employee (based on their shift)
  const isWeekendForEmployee = (employeeId: number, date: Date): boolean => {
    const shift = getEmployeeShift(employeeId, date);
    
    if (!shift) {
      // No shift assigned, use configured weekends
      const defaultWeekends = [0, 6]; // Sunday, Saturday
      const configuredWeekends = weekendDays.length > 0 ? weekendDays : defaultWeekends;
      return configuredWeekends.includes(date.getDay());
    }
    
    // Check if this day is NOT in the shift's working days (making it a weekend for this shift)
    const dayOfWeek = date.getDay();
    return !shift.workingDays.includes(dayOfWeek);
  };

  // Helper function to check if a date is a weekend (global check for headers)
  const isGlobalWeekend = (date: Date): boolean => {
    const dayOfWeek = date.getDay();
    
    // Get all employees who have shift assignments for this date
    const employeesWithShifts = filteredEmployeeData.filter(emp => {
      const shift = getEmployeeShift(emp.employeeId, date);
      return shift !== null;
    });
    
    // If no employees have shifts, use configured weekends
    if (employeesWithShifts.length === 0) {
      const defaultWeekends = [0, 6]; // Sunday, Saturday
      const configuredWeekends = weekendDays.length > 0 ? weekendDays : defaultWeekends;
      const result = configuredWeekends.includes(dayOfWeek);
      
      // Debug weekend detection (sample only)
      if (Math.random() < 0.1) { // 10% sampling
        console.log('🗓️ Weekend check:', {
          date: date.toDateString(),
          dayOfWeek,
          weekendDaysConfig: weekendDays,
          effectiveWeekends: configuredWeekends,
          isWeekend: result
        });
      }
      
      return result;
    }
    
    // Check if this day is a non-working day for ANY of the assigned shifts
    const isWeekendForAnyShift = employeesWithShifts.some(emp => {
      const shift = getEmployeeShift(emp.employeeId, date);
      return shift && !shift.workingDays.includes(dayOfWeek);
    });
    
    return isWeekendForAnyShift;
  };

  // Helper function to get weekend type for better display
  const getWeekendType = (employeeId: number, date: Date): { isWeekend: boolean; type: 'common' | 'shift-specific' | null; shift?: Shift } => {
    const shift = getEmployeeShift(employeeId, date);
    const dayOfWeek = date.getDay();
    const defaultWeekends = [0, 6]; // Sunday, Saturday
    const commonWeekends = weekendDays.length > 0 ? weekendDays : defaultWeekends;
    
    if (!shift) {
      // No shift assigned, use configured weekends
      return {
        isWeekend: commonWeekends.includes(dayOfWeek),
        type: commonWeekends.includes(dayOfWeek) ? 'common' : null
      };
    }
    
    const isShiftWeekend = !shift.workingDays.includes(dayOfWeek);
    const isCommonWeekend = commonWeekends.includes(dayOfWeek);
    
    if (isShiftWeekend) {
      return {
        isWeekend: true,
        type: isCommonWeekend ? 'common' : 'shift-specific',
        shift: shift
      };
    }
    
    return { isWeekend: false, type: null };
  };

  // Helper function to check if a date is a holiday
  const isHoliday = (date: Date): { isHoliday: boolean; holiday?: any } => {
    const dateStr = formatLocalDate(date);
    
    // Debug logging for first few calls to understand the issue
    if (holidays.length > 0 && Math.random() < 0.01) { // Random sampling to avoid spam
      console.log('🔍 Holiday Debug:', {
        checkingDate: dateStr,
        totalHolidays: holidays.length,
        holidayDates: holidays.map(h => h.date),
        exactMatch: holidays.find(h => h.date === dateStr)
      });
    }
    
    // Check for exact date match first
    let holiday = holidays.find(h => h.date === dateStr);
    
    // If no exact match, check for multi-day holidays
    if (!holiday) {
      holiday = holidays.find(h => {
        const duration = (h as any).metadata?.duration || 1;
        if (duration <= 1) return false; // Skip single-day holidays
        
        const holidayStartDate = new Date(h.date);
        const holidayEndDate = new Date(holidayStartDate);
        holidayEndDate.setDate(holidayStartDate.getDate() + duration - 1);
        
        // Check if current date falls within the holiday range
        return date >= holidayStartDate && date <= holidayEndDate;
      });
    }
    
    return {
      isHoliday: !!holiday,
      holiday: holiday
    };
  };

  // Helper function to get holiday type class
  const getHolidayTypeClass = (type?: string): string => {
    switch (type) {
      case 'public':
        return 'bg-red-100 dark:bg-red-900/30 border-red-300 dark:border-red-700';
      case 'company':
        return 'bg-blue-100 dark:bg-blue-900/30 border-blue-300 dark:border-blue-700';
      case 'religious':
      case 'optional':
        return 'bg-purple-100 dark:bg-purple-900/30 border-purple-300 dark:border-purple-700';
      default:
        return 'bg-orange-100 dark:bg-orange-900/30 border-orange-300 dark:border-orange-700';
    }
  };

  // Function to handle period changes
  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period);
    const today = new Date();
    let targetDate = new Date();

    switch (period) {
      case 'Today':
        targetDate = today;
        // For "Today", always show current month
        setCurrentMonth(new Date(today.getFullYear(), today.getMonth(), 1));
        break;
      case 'Yesterday':
        targetDate = new Date(today);
        targetDate.setDate(today.getDate() - 1);
        // Show the month containing yesterday
        setCurrentMonth(new Date(targetDate.getFullYear(), targetDate.getMonth(), 1));
        break;
      case 'This Week':
        // Go to the start of current week (Sunday)
        targetDate = new Date(today);
        targetDate.setDate(today.getDate() - today.getDay());
        // Show the month containing the start of this week
        setCurrentMonth(new Date(targetDate.getFullYear(), targetDate.getMonth(), 1));
        break;
      case 'Last Week':
        // Go to the start of last week
        targetDate = new Date(today);
        targetDate.setDate(today.getDate() - today.getDay() - 7);
        // Show the month containing the start of last week
        setCurrentMonth(new Date(targetDate.getFullYear(), targetDate.getMonth(), 1));
        break;
      case 'This Month':
        targetDate = new Date(today.getFullYear(), today.getMonth(), 1);
        setCurrentMonth(new Date(targetDate.getFullYear(), targetDate.getMonth(), 1));
        break;
      case 'Last Month':
        targetDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        setCurrentMonth(new Date(targetDate.getFullYear(), targetDate.getMonth(), 1));
        break;
      case 'This Year':
        targetDate = new Date(today.getFullYear(), 0, 1);
        setCurrentMonth(new Date(targetDate.getFullYear(), targetDate.getMonth(), 1));
        break;
      default:
        targetDate = today;
        setCurrentMonth(new Date(today.getFullYear(), today.getMonth(), 1));
    }
    
    // Trigger onDateChange if provided
    if (onDateChange) {
      onDateChange(formatLocalDate(targetDate));
    }
  };

  // Generate calendar days for the current month
  useEffect(() => {
    const days: Date[] = [];
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    
    // --- Use AttendanceCalendar logic for calendar grid generation ---
    const firstDayOfMonth = new Date(year, month, 1);
    const lastDayOfMonth = new Date(year, month + 1, 0);
    const firstDayOfWeek = firstDayOfMonth.getDay();
    const daysInMonth = lastDayOfMonth.getDate();
    const daysFromPrevMonth = firstDayOfWeek;

    // Add previous month's days
    for (let i = daysFromPrevMonth; i > 0; i--) {
      const date = new Date(year, month, 1 - i);
      days.push(date);
    }

    // Add current month's days
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      days.push(date);
    }

    // Add next month's days
    const totalDays = 42; // 6 rows × 7 days
    const remainingDays = totalDays - days.length;
    for (let day = 1; day <= remainingDays; day++) {
      const date = new Date(year, month + 1, day);
      days.push(date);
    }

    // --- FIX: Ensure the first day is always Sunday ---
    while (days.length > 0 && days[0].getDay() !== 0) {
      const prev = new Date(days[0]);
      prev.setDate(prev.getDate() - 1);
      days.unshift(prev);
    }
    
    // Debug first week to verify alignment
    console.log('🗓️ First week alignment check:', days.slice(0, 7).map((day, index) => ({
      index,
      dayName: day.toLocaleDateString('en-US', { weekday: 'short' }),
      date: day.getDate(),
      dayOfWeek: day.getDay(),
      monthYear: `${day.getMonth() + 1}/${day.getFullYear()}`,
      expectedDay: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][index]
    })));
    
    console.log('🗓️ Generated', days.length, 'calendar days total');
    
    setCalendarDays(days);
  }, [currentMonth]);

  // After generating calendarDays, log the first week for alignment
  useEffect(() => {
    if (calendarDays.length > 0) {
      console.log('🟦 TeamCalendar: First week days:', calendarDays.slice(0, 7).map((d, i) => ({
        index: i,
        date: d.toISOString().split('T')[0],
        dayOfWeek: d.getDay(),
        weekday: d.toLocaleDateString('en-US', { weekday: 'short' })
      })));
    }
  }, [calendarDays]);

  // Process attendance data by employee
  useEffect(() => {
    // Extract unique employees and their data
    const employeeMap = new Map<number, EmployeeAttendanceData>();
    const departmentSet = new Set<string>();
    
    // First, initialize employee data from the employees database
    employees.forEach(employee => {
      const employeeName = `${employee.firstName || ''} ${employee.lastName || ''}`.trim() || 'Unknown';
      const department = employee.department || 'N/A';
      
      departmentSet.add(department);
      
      employeeMap.set(employee.id, {
        employeeId: employee.id,
        employeeName: employeeName,
        department: department,
        position: employee.designation || 'N/A',
        profileImagePath: employee.profileImagePath,
        attendances: {}
      });
    });
    
    // Then, process attendance records and merge with employee data
    attendances.forEach(attendance => {
      // Add to departments set for filtering
      if (attendance.department) {
        departmentSet.add(attendance.department);
      }
      
      // Use the attendance date directly (should already be in YYYY-MM-DD format)
      const dateKey = attendance.date; // Attendance.date is already in YYYY-MM-DD format
      
      // Debug log for date processing
      if (Math.random() < 0.01) { // 1% sampling
        console.log('📅 Processing attendance date:', {
          originalDate: attendance.date,
          dateKey,
          employeeName: attendance.employeeName,
          status: attendance.status
        });
      }
      
      // If employee doesn't exist in database, create from attendance record
      if (!employeeMap.has(attendance.employeeId)) {
        const department = attendance.department || 'N/A';
        departmentSet.add(department);
        
        employeeMap.set(attendance.employeeId, {
          employeeId: attendance.employeeId,
          employeeName: attendance.employeeName,
          department: department,
          position: attendance.position || 'N/A',
          profileImagePath: undefined, // No profile image for attendance-only records
          attendances: {}
        });
      }
      
      const employee = employeeMap.get(attendance.employeeId)!;
      employee.attendances[dateKey] = attendance;
    });
    
    setDepartments(Array.from(departmentSet).sort());
    setEmployeeData(Array.from(employeeMap.values()));
  }, [attendances, employees]);

  // Apply filters
  useEffect(() => {
    let filtered = employeeData;
    
    // Search term filter (name or ID)
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(employee => 
        employee.employeeName.toLowerCase().includes(term) || 
        employee.employeeId.toString().includes(term)
      );
    }
    
    // Department filter
    if (filterDepartment) {
      filtered = filtered.filter(employee => 
        employee.department === filterDepartment
      );
    }
    
    // Status filter (has to be applied to the dates)
    if (filterStatus) {
      filtered = filtered.filter(employee => {
        // Convert current month to YYYY-MM format for faster filtering
        const yearMonth = `${currentMonth.getFullYear()}-${(currentMonth.getMonth() + 1).toString().padStart(2, '0')}`;
        
        // Check if any attendance in the current month has the filtered status
        return Object.entries(employee.attendances).some(([date, attendance]) => 
          date.startsWith(yearMonth) && attendance.status === filterStatus
        );
      });
    }
    
    setFilteredEmployeeData(filtered);
  }, [employeeData, searchTerm, filterDepartment, filterStatus, currentMonth]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filterDepartment, filterStatus]);

  // Calculate pagination
  const totalPages = Math.ceil(filteredEmployeeData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, filteredEmployeeData.length);
  const paginatedEmployeeData = filteredEmployeeData.slice(startIndex, endIndex);

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Change month handlers
  const today = () => {
    setCurrentMonth(new Date());
    setSelectedPeriod('Today'); // Set to "Today" instead of "This Month"
  };

  // Enhanced prev/next month functions to update period
  const prevMonth = () => {
    const newMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1);
    setCurrentMonth(newMonth);
    
    // Update period selection based on the new month
    const today = new Date();
    if (newMonth.getMonth() === today.getMonth() && newMonth.getFullYear() === today.getFullYear()) {
      setSelectedPeriod('This Month');
    } else if (newMonth.getMonth() === today.getMonth() - 1 && newMonth.getFullYear() === today.getFullYear()) {
      setSelectedPeriod('Last Month');
    } else {
      setSelectedPeriod('Custom');
    }
  };

  const nextMonth = () => {
    const newMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1);
    setCurrentMonth(newMonth);
    
    // Update period selection based on the new month
    const today = new Date();
    if (newMonth.getMonth() === today.getMonth() && newMonth.getFullYear() === today.getFullYear()) {
      setSelectedPeriod('This Month');
    } else if (newMonth.getMonth() === today.getMonth() - 1 && newMonth.getFullYear() === today.getFullYear()) {
      setSelectedPeriod('Last Month');
    } else {
      setSelectedPeriod('Custom');
    }
  };

  const getAttendanceStatusClass = (status: AttendanceStatus) => {
    switch (status) {
      case AttendanceStatus.PRESENT:
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400';
      case AttendanceStatus.ABSENT:
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400';
      case AttendanceStatus.LATE:
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400';
      case AttendanceStatus.HALF_DAY:
        return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-400';
      case AttendanceStatus.LEAVE:
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400';
      case AttendanceStatus.WORK_FROM_HOME:
        return 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-400';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
    }
  };

  const formatAttendanceStatus = (status: AttendanceStatus) => {
    switch (status) {
      case AttendanceStatus.PRESENT:
        return 'Present';
      case AttendanceStatus.ABSENT:
        return 'Absent';
      case AttendanceStatus.LATE:
        return 'Late';
      case AttendanceStatus.HALF_DAY:
        return 'Half Day';
      case AttendanceStatus.LEAVE:
        return 'Leave';
      case AttendanceStatus.WORK_FROM_HOME:
        return 'WFH';
      case AttendanceStatus.ON_DUTY:
        return 'On Duty';
      default:
        const statusValue = status as string;
        return statusValue.split('_').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        ).join(' ');
    }
  };

  const renderCellContent = (employee: EmployeeAttendanceData, date: Date) => {
    const dateKey = formatLocalDate(date);
    const attendance = employee.attendances[dateKey];
    
    // Enhanced debug logging for first few employees to track the issue
    const shouldDebug = Math.random() < 0.05; // 5% sampling for debug logs
    if (shouldDebug) {
      console.log('🔍 TeamCalendar Debug:', {
        employee: employee.employeeName,
        originalDate: date.toString(),
        dateKey,
        dayOfWeek: date.getDay(),
        weekday: date.toLocaleDateString('en-US', { weekday: 'short' }),
        attendance: attendance ? {
          status: attendance.status,
          originalDate: attendance.date,
          employeeName: attendance.employeeName
        } : null,
        availableKeys: Object.keys(employee.attendances).slice(0, 5), // First 5 keys
        totalAttendanceRecords: Object.keys(employee.attendances).length
      });
    }
    const weekendInfo = getWeekendType(employee.employeeId, date);
    const holiday = isHoliday(date);
    
    // No attendance data for this date
    if (!attendance) {
      // Today's date - highlight but no attendance
      if (isToday(date)) {
        return (
          <div className="h-full flex items-center justify-center">
            <span className="w-5 h-5 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center text-[10px] text-blue-500 dark:text-blue-400">
              {date.getDate()}
            </span>
          </div>
        );
      }
      
      // Weekend for this employee - show employee-specific weekend
      if (weekendInfo.isWeekend) {
        const isCommonWeekend = weekendInfo.type === 'common';
        const isShiftSpecific = weekendInfo.type === 'shift-specific';
        
        return (
          <div className="h-full flex items-center justify-center">
            <div className="flex flex-col items-center">
              <span className={`text-[10px] font-medium ${
                isCommonWeekend 
                  ? 'text-amber-600 dark:text-amber-400' 
                  : 'text-orange-600 dark:text-orange-400'
              }`}>
                {date.getDate()}
              </span>
              <span className={`text-[8px] font-medium ${
                isCommonWeekend 
                  ? 'text-amber-500 dark:text-amber-400' 
                  : 'text-orange-500 dark:text-orange-400'
              }`} 
              title={
                isCommonWeekend 
                  ? 'Common Weekend' 
                  : `${weekendInfo.shift?.name} - Non-working day`
              }>
                {isCommonWeekend ? 'W' : 'S'}
              </span>
            </div>
          </div>
        );
      }
      
      // Holiday - highlight
      if (holiday.isHoliday) {
        return (
          <div className="h-full flex items-center justify-center">
            <div className="flex flex-col items-center">
              <span className="text-red-600 dark:text-red-400 text-[10px] font-medium">{date.getDate()}</span>
              <span className="text-[8px] text-red-500 dark:text-red-400 font-medium">H</span>
            </div>
          </div>
        );
      }
      
      // Regular day - just show the date
      return (
        <div className="h-full flex items-center justify-center">
          <span className="text-gray-500 dark:text-gray-400 text-[10px]">{date.getDate()}</span>
        </div>
      );
    }
    
    // Has attendance data
    const statusClass = getAttendanceStatusClass(attendance.status);
    const isRemote = attendance.isRemote;
    const isCommonWeekend = weekendInfo.type === 'common';
    const isShiftSpecific = weekendInfo.type === 'shift-specific';
    
    // Special handling for weekends
    if (weekendInfo.isWeekend) {
      // If absent on weekend, just show weekend indicator (don't show "Absent")
      if (attendance.status === AttendanceStatus.ABSENT) {
        return (
          <div className="h-full flex items-center justify-center">
            <div className="flex flex-col items-center">
              <span className={`text-[10px] font-medium ${
                isCommonWeekend 
                  ? 'text-amber-600 dark:text-amber-400' 
                  : 'text-orange-600 dark:text-orange-400'
              }`}>
                {date.getDate()}
              </span>
              <span className={`text-[8px] font-medium ${
                isCommonWeekend 
                  ? 'text-amber-500 dark:text-amber-400' 
                  : 'text-orange-500 dark:text-orange-400'
              }`} 
              title={
                isCommonWeekend 
                  ? 'Common Weekend - Absent (Expected)' 
                  : `${weekendInfo.shift?.name} - Non-working day - Absent (Expected)`
              }>
                {isCommonWeekend ? 'W' : 'S'}
              </span>
            </div>
          </div>
        );
      }
      
      // If present on weekend, just show status (no W indicator)
      return (
        <div className="h-full p-0.5 flex flex-col items-center justify-center">
          <div className={`text-[9px] px-1.5 py-0.5 rounded-full ${statusClass} ${isToday(date) ? 'ring-1 ring-blue-300 dark:ring-blue-500' : ''} ${holiday.isHoliday ? 'ring-1 ring-red-300 dark:ring-red-500' : ''}`}>
            {formatAttendanceStatus(attendance.status)}
          </div>
          {holiday.isHoliday && (
            <div className="text-[8px] text-red-600 dark:text-red-400 font-bold">
              H
            </div>
          )}
        </div>
      );
    }
    
    // Regular weekday attendance
    return (
      <div className="h-full p-0.5 flex flex-col items-center justify-center">
        <div className={`text-[9px] px-1.5 py-0.5 rounded-full ${statusClass} ${isToday(date) ? 'ring-1 ring-blue-300 dark:ring-blue-500' : ''} ${holiday.isHoliday ? 'ring-1 ring-red-300 dark:ring-red-500' : ''}`}>
          {formatAttendanceStatus(attendance.status)}
        </div>
        {holiday.isHoliday && (
          <div className="text-[8px] text-red-600 dark:text-red-400 font-bold">
            H
          </div>
        )}
      </div>
    );
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();
  };

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentMonth.getMonth();
  };

  const exportToCSV = () => {
    // Generate CSV data
    const headers = ['Employee ID', 'Name', 'Department'];
    
    // Add date headers
    calendarDays.forEach(day => {
      headers.push(formatLocalDate(day));
    });
    
    // Add employee data rows
    const rows = filteredEmployeeData.map(employee => {
      const row: string[] = [
        employee.employeeId.toString(),
        employee.employeeName,
        employee.department || ''
      ];
      
      // Add status for each day
      calendarDays.forEach(day => {
        const dateKey = formatLocalDate(day);
        const attendance = employee.attendances[dateKey];
        row.push(attendance ? attendance.status : '');
      });
      
      return row;
    });
    
    // Create CSV content
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');
    
    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `team-attendance-${currentMonth.toISOString().slice(0, 7)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 overflow-hidden max-w-full transition-colors duration-200">
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
        <div className="flex items-center">
          <div className="h-10 w-10 rounded-lg bg-blue-600 dark:bg-blue-500 mr-3 flex items-center justify-center">
            <Users className="text-white h-6 w-6" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white">Team Calendar View</h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              View attendance for all team members at a glance
              {selectedPeriod !== 'This Month' && (
                <span className="ml-2 text-blue-600 dark:text-blue-400 font-medium">
                  • {selectedPeriod}
                </span>
              )}
            </p>
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2 w-full lg:w-auto">
          <div className="relative flex-grow lg:flex-grow-0">
            <input
              type="text"
              placeholder="Search by name or ID"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full lg:w-auto pl-8 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Users className="h-4 w-4 text-gray-400 dark:text-gray-500" />
            </div>
          </div>
          
          <select
            value={filterDepartment}
            onChange={(e) => setFilterDepartment(e.target.value)}
            className="w-full sm:w-auto pl-3 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">All Departments</option>
            {departments.map(dept => (
              <option key={dept} value={dept}>{dept}</option>
            ))}
          </select>
          
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as AttendanceStatus | '')}
            className="w-full sm:w-auto pl-3 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">All Statuses</option>
            <option value={AttendanceStatus.PRESENT}>Present</option>
            <option value={AttendanceStatus.ABSENT}>Absent</option>
            <option value={AttendanceStatus.LATE}>Late</option>
            <option value={AttendanceStatus.LEAVE}>Leave</option>
            <option value={AttendanceStatus.WORK_FROM_HOME}>Work From Home</option>
            <option value={AttendanceStatus.HALF_DAY}>Half Day</option>
          </select>
          
          <div className="flex items-center">
            <button
              onClick={exportToCSV}
              className="w-full sm:w-auto px-3 py-2 bg-green-600 dark:bg-green-500 text-white rounded text-sm hover:bg-green-700 dark:hover:bg-green-600 flex items-center justify-center transition-colors duration-200"
            >
              <Download className="h-4 w-4 mr-1" />
              Export
            </button>
          </div>
        </div>
      </div>
      
      {/* Pagination Controls */}
      {filteredEmployeeData.length > 0 && (
        <div className="mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Show:</span>
              <select
                value={itemsPerPage}
                onChange={(e) => handleItemsPerPageChange(parseInt(e.target.value))}
                className="border border-gray-300 dark:border-gray-600 rounded text-sm py-1 px-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 dark:text-white"
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
              <span className="text-sm text-gray-600 dark:text-gray-400">per page</span>
            </div>
            
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Showing <span className="font-medium text-gray-900 dark:text-white">{startIndex + 1}</span> to{' '}
              <span className="font-medium text-gray-900 dark:text-white">{endIndex}</span> of{' '}
              <span className="font-medium text-gray-900 dark:text-white">{filteredEmployeeData.length}</span> employees
            </div>
          </div>

          {totalPages > 1 && (
            <div className="flex items-center gap-2">
              <button
                onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                Previous
              </button>

              {/* Page numbers */}
              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                  let page;
                  if (totalPages <= 5) {
                    page = i + 1;
                  } else if (currentPage <= 3) {
                    page = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    page = totalPages - 4 + i;
                  } else {
                    page = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-3 py-1 text-sm border rounded transition-colors duration-200 ${
                        currentPage === page
                          ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                          : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                      }`}
                    >
                      {page}
                    </button>
                  );
                })}
              </div>

              <button
                onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                Next
              </button>
            </div>
          )}
        </div>
      )}
      
      <div className="border border-gray-200 dark:border-gray-600 rounded-lg mb-6">
        {/* Date Period Selection */}
        <div className="px-4 py-3 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
          <div className="flex flex-wrap items-center gap-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">Quick Select:</span>
            {['Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month', 'This Year'].map((period) => (
              <button
                key={period}
                onClick={() => handlePeriodChange(period)}
                className={`px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 ${
                  selectedPeriod === period
                    ? 'bg-blue-600 dark:bg-blue-500 text-white shadow-sm'
                    : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                {period}
              </button>
            ))}
          </div>
        </div>
        
        {/* Month Navigation Header */}
        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
          <button 
            onClick={prevMonth}
            className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            <ChevronLeft className="h-5 w-5 text-gray-600 dark:text-gray-300" />
          </button>
          
          <div className="flex items-center gap-3">
            <h3 className="text-lg font-medium text-gray-800 dark:text-white">
              {currentMonth.toLocaleString('default', { month: 'long', year: 'numeric' })}
            </h3>
            {selectedPeriod !== 'This Month' && (
              <span className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 rounded-full">
                {selectedPeriod}
              </span>
            )}
          </div>
          
          <div className="flex items-center">
            {selectedPeriod !== 'This Month' && selectedPeriod !== 'Today' ? (
              <button 
                onClick={today}
                className="mr-2 px-3 py-1 text-xs border border-blue-300 dark:border-blue-600 rounded bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-400 transition-colors duration-200"
              >
                Back to Current Month
              </button>
            ) : (
              <button 
                onClick={today}
                className="mr-2 px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-white transition-colors duration-200"
              >
                Today
              </button>
            )}
            <button 
              onClick={nextMonth}
              className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
            >
              <ChevronRight className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            </button>
          </div>
        </div>
        
        <div className="overflow-x-auto" style={{ maxWidth: '100%' }}>
          <div className="min-w-max">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-700 z-10 border-r border-gray-200 dark:border-gray-600 min-w-[160px] max-w-[180px]">
                    Employee
                  </th>
                  {calendarDays.map((day, index) => {
                    const holiday = isHoliday(day);
                    const isGlobalWeekendDay = isGlobalWeekend(day);
                    const isTodayDate = isToday(day);
                    const isCurrentMonthDay = isCurrentMonth(day);
                    
                    // Determine background class with priority: holiday > today > weekend > current month
                    let bgClass = '';
                    let textClass = 'text-gray-500 dark:text-gray-300';
                    let borderClass = '';
                    
                    if (!isCurrentMonthDay) {
                      bgClass = 'bg-gray-100 dark:bg-gray-600';
                      textClass = 'text-gray-400 dark:text-gray-500';
                    } else if (holiday.isHoliday) {
                      bgClass = getHolidayTypeClass(holiday.holiday?.type);
                      textClass = 'text-red-700 dark:text-red-300 font-semibold';
                      borderClass = 'border-2';
                    } else if (isTodayDate) {
                      bgClass = 'bg-blue-50 dark:bg-blue-900/30';
                      textClass = 'text-blue-700 dark:text-blue-300 font-semibold';
                      borderClass = 'border-2 border-blue-300 dark:border-blue-500';
                    } else if (isGlobalWeekendDay) {
                      bgClass = 'bg-amber-50 dark:bg-amber-900/20';
                      textClass = 'text-amber-700 dark:text-amber-300 font-medium';
                      borderClass = 'border border-amber-200 dark:border-amber-700';
                    }
                    
                    return (
                      <th 
                        key={index}
                        className={`px-1 py-3 text-center text-xs font-medium uppercase tracking-wider min-w-[40px] ${bgClass} ${textClass} ${borderClass}`}
                        title={holiday.isHoliday ? `Holiday: ${holiday.holiday?.name}` : isGlobalWeekendDay ? 'Weekend/Non-working day' : ''}
                      >
                        <div className="flex flex-col items-center">
                          <span className={`text-[9px] ${!isCurrentMonthDay ? 'text-gray-400 dark:text-gray-500' : ''}`}>
                            {day.toLocaleDateString('en-US', { weekday: 'short' })}
                          </span>
                          <span className={`text-xs ${isTodayDate ? 'font-bold' : holiday.isHoliday ? 'font-semibold' : ''}`}>
                            {day.getDate()}
                          </span>
                          {holiday.isHoliday && (
                            <span className="text-[8px] text-red-600 dark:text-red-400 font-bold">
                              H
                            </span>
                          )}
                          {isGlobalWeekendDay && !holiday.isHoliday && (
                            <span className="text-[8px] text-amber-600 dark:text-amber-400 font-medium">
                              W
                            </span>
                          )}
                        </div>
                      </th>
                    );
                  })}
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                {paginatedEmployeeData.length === 0 ? (
                  <tr>
                    <td colSpan={calendarDays.length + 1} className="px-6 py-12 text-center text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-600">
                      <CalendarDays className="mx-auto h-12 w-12 text-gray-300 dark:text-gray-600 mb-3" />
                      {filteredEmployeeData.length === 0 ? (
                        <>
                          <p className="text-gray-600 dark:text-gray-300 font-medium">No employees found</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            {employeeData.length === 0 
                              ? 'Try importing attendance data or adding employees'
                              : 'Try adjusting your search or filter criteria'
                            }
                          </p>
                        </>
                      ) : (
                        <>
                          <p className="text-gray-600 dark:text-gray-300 font-medium">No employees on this page</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            Found {filteredEmployeeData.length} employees, but none on page {currentPage}
                          </p>
                          <button 
                            onClick={() => handlePageChange(1)}
                            className="mt-3 px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white text-sm rounded hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors duration-200"
                          >
                            Go to First Page
                          </button>
                        </>
                      )}
                    </td>
                  </tr>
                ) : (
                  paginatedEmployeeData.map((employee) => (
                    <tr key={employee.employeeId} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                      <td className="px-2 py-2 whitespace-nowrap sticky left-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-600 z-10">
                        <div className="flex items-center">
                          <div className="h-6 w-6 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center text-xs text-gray-600 dark:text-gray-300 mr-2 overflow-hidden">
                            {employee.profileImagePath ? (
                              <img 
                                src={employee.profileImagePath} 
                                alt={employee.employeeName} 
                                className="h-full w-full object-cover rounded-full" 
                              />
                            ) : (
                              employee.employeeName.split(' ').map(n => n[0]).join('')
                            )}
                          </div>
                          <div className="min-w-0 max-w-[120px]">
                            <div className="text-xs font-medium text-gray-900 dark:text-white truncate">{employee.employeeName}</div>
                            <div className="text-[9px] text-gray-500 dark:text-gray-400 truncate">
                              {employee.department}
                            </div>
                            {(() => {
                              // Get employee's current shift assignment
                              const currentDate = new Date();
                              const currentShift = getEmployeeShift(employee.employeeId, currentDate);
                              if (currentShift) {
                                return (
                                  <div className="flex items-center mt-0.5">
                                    <div 
                                      className="w-2 h-2 rounded-full mr-1" 
                                      style={{ backgroundColor: currentShift.color }}
                                    ></div>
                                    <span className="text-[8px] text-gray-600 dark:text-gray-400 truncate">
                                      {currentShift.name}
                                    </span>
                                  </div>
                                );
                              }
                              return (
                                <div className="text-[8px] text-gray-400 dark:text-gray-500 mt-0.5">
                                  No shift assigned
                                </div>
                              );
                            })()}
                          </div>
                        </div>
                      </td>
                      {calendarDays.map((day, index) => {
                        const holiday = isHoliday(day);
                        const isGlobalWeekendDay = isGlobalWeekend(day);
                        const isTodayDate = isToday(day);
                        const isCurrentMonthDay = isCurrentMonth(day);
                        
                        // Check if this employee has a shift-specific weekend on this day
                        const employeeWeekendInfo = getWeekendType(employee.employeeId, day);
                        const isShiftSpecificWeekend = employeeWeekendInfo.type === 'shift-specific';
                        
                        // Determine background class with priority: holiday > today > shift weekend > common weekend
                        let cellBgClass = '';
                        
                        if (!isCurrentMonthDay) {
                          cellBgClass = 'bg-gray-50 dark:bg-gray-700';
                        } else if (holiday.isHoliday) {
                          cellBgClass = getHolidayTypeClass(holiday.holiday?.type);
                        } else if (isTodayDate) {
                          cellBgClass = 'bg-blue-50 dark:bg-blue-900/30';
                        } else if (isShiftSpecificWeekend) {
                          cellBgClass = 'bg-orange-50 dark:bg-orange-900/20';
                        } else if (isGlobalWeekendDay) {
                          cellBgClass = 'bg-amber-50 dark:bg-amber-900/20';
                        }
                        
                        return (
                          <td 
                            key={index} 
                            className={`px-1 py-2 text-center border-r border-gray-100 dark:border-gray-600 ${cellBgClass}`}
                            style={{ height: '35px', minWidth: '40px' }}
                            title={
                              holiday.isHoliday ? `Holiday: ${holiday.holiday?.name}` : 
                              isShiftSpecificWeekend ? `${employeeWeekendInfo.shift?.name} - Non-working day` :
                              isGlobalWeekendDay ? 'Common Weekend' : ''
                            }
                          >
                            {renderCellContent(employee, day)}
                          </td>
                        );
                      })}
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <div className="flex flex-wrap gap-3 mb-4">
        <div className="text-sm text-gray-700 dark:text-gray-300 font-medium">Status Legend:</div>
        <div className="flex items-center">
          <span className="w-3 h-3 rounded-full bg-green-100 dark:bg-green-900/30 mr-1"></span>
          <span className="text-xs text-gray-600 dark:text-gray-400">Present</span>
        </div>
        <div className="flex items-center">
          <span className="w-3 h-3 rounded-full bg-red-100 dark:bg-red-900/30 mr-1"></span>
          <span className="text-xs text-gray-600 dark:text-gray-400">Absent</span>
        </div>
        <div className="flex items-center">
          <span className="w-3 h-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 mr-1"></span>
          <span className="text-xs text-gray-600 dark:text-gray-400">Late</span>
        </div>
        <div className="flex items-center">
          <span className="w-3 h-3 rounded-full bg-orange-100 dark:bg-orange-900/30 mr-1"></span>
          <span className="text-xs text-gray-600 dark:text-gray-400">Half Day</span>
        </div>
        <div className="flex items-center">
          <span className="w-3 h-3 rounded-full bg-blue-100 dark:bg-blue-900/30 mr-1"></span>
          <span className="text-xs text-gray-600 dark:text-gray-400">Leave</span>
        </div>
        <div className="flex items-center">
          <span className="w-3 h-3 rounded-full bg-purple-100 dark:bg-purple-900/30 mr-1"></span>
          <span className="text-xs text-gray-600 dark:text-gray-400">Work From Home</span>
        </div>
      </div>
      
      <div className="text-sm text-gray-500 dark:text-gray-400">
        {paginatedEmployeeData.length} of {filteredEmployeeData.length} employees shown • 
        {calendarDays.filter(isCurrentMonth).length} days in {currentMonth.toLocaleString('default', { month: 'long' })}
        {employees.length > 0 && (
          <span className="ml-2">
            • Connected to {employees.length} database employee{employees.length !== 1 ? 's' : ''}
          </span>
        )}
        {filteredEmployeeData.length !== employeeData.length && (
          <span className="ml-2 text-yellow-600 dark:text-yellow-400">
            • {employeeData.length - filteredEmployeeData.length} filtered out
          </span>
        )}
      </div>
      
      {/* Bottom Pagination Controls */}
      {filteredEmployeeData.length > itemsPerPage && (
        <div className="mt-4 flex justify-center">
          <div className="flex items-center gap-2">
            <button
              onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              Previous
            </button>

            <span className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400">
              Page {currentPage} of {totalPages}
            </span>

            <button
              onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              Next
            </button>
          </div>
        </div>
      )}
      
      {/* Summary Statistics for the selected period */}
      {paginatedEmployeeData.length > 0 && (
        <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {selectedPeriod} Summary - {currentMonth.toLocaleString('default', { month: 'long', year: 'numeric' })}
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-500 dark:text-gray-400">Filtered Employees:</span>
              <span className="ml-2 font-medium text-gray-900 dark:text-white">{filteredEmployeeData.length}</span>
              {filteredEmployeeData.length !== employeeData.length && (
                <span className="ml-1 text-xs text-gray-500 dark:text-gray-400">of {employeeData.length}</span>
              )}
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Working Days:</span>
              <span className="ml-2 font-medium text-gray-900 dark:text-white">
                {calendarDays.filter(day => 
                  isCurrentMonth(day) && 
                  day.getDay() !== 0 && 
                  day.getDay() !== 6
                ).length}
              </span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Attendance Records:</span>
              <span className="ml-2 font-medium text-gray-900 dark:text-white">
                {(() => {
                  const periodAttendances = attendances.filter(att => {
                    const attDate = new Date(att.date);
                    return attDate.getMonth() === currentMonth.getMonth() && 
                           attDate.getFullYear() === currentMonth.getFullYear();
                  });
                  return periodAttendances.length;
                })()}
              </span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Departments:</span>
              <span className="ml-2 font-medium text-gray-900 dark:text-white">{departments.length}</span>
            </div>
          </div>
          
          {/* Additional period-specific insights */}
          {selectedPeriod !== 'This Month' && (
            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
              <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                <span className="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400">
                  {selectedPeriod} View
                </span>
                <span>Showing data for the selected time period</span>
              </div>
            </div>
          )}
          
          {/* Pagination info in summary */}
          {totalPages > 1 && (
            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
              <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                <span className="inline-flex items-center px-2 py-1 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                  Page {currentPage} of {totalPages}
                </span>
                <span>Displaying {paginatedEmployeeData.length} employees</span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TeamCalendar; 