import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useLocation, useNavigate } from 'react-router-dom';
import api from '../services/api';

interface Ticket {
  id: number;
  ticketNumber: string;
  title: string;
  status: string;
  priority: string;
  project?: string;
  location?: string;
  createdAt: string;
  createdBy: {
    name: string;
    department: string;
  };
}

export const TicketsPage: React.FC = () => {
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const initialFilter = location.state?.initialFilter || 'all';
  
  const [statusFilter, setStatusFilter] = useState(initialFilter);
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [projectFilter, setProjectFilter] = useState('all');
  const [locationFilter, setLocationFilter] = useState('all');
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTickets = async () => {
      try {
        setLoading(true);
        let queryParams = new URLSearchParams();

        // Handle different filter types from dashboard
        if (statusFilter === 'critical') {
          queryParams.append('priority', 'CRITICAL');
        } else if (statusFilter !== 'all') {
          queryParams.append('status', statusFilter.toUpperCase());
        }

        // Add other filters
        if (priorityFilter !== 'all') {
          queryParams.append('priority', priorityFilter.toUpperCase());
        }
        
        if (departmentFilter !== 'all') {
          queryParams.append('department', departmentFilter.toUpperCase());
        }
        
        if (projectFilter !== 'all') {
          queryParams.append('project', projectFilter);
        }
        
        if (locationFilter !== 'all') {
          queryParams.append('location', locationFilter);
        }

        const response = await api.get<Ticket[]>(`/tickets?${queryParams.toString()}`);
        console.log('Fetched tickets:', response.data);
        setTickets(response.data);
      } catch (error: any) {
        console.error('Error fetching tickets:', error);
        setError(error.message || 'Failed to fetch tickets');
      } finally {
        setLoading(false);
      }
    };

    fetchTickets();
  }, [statusFilter, priorityFilter, departmentFilter, projectFilter, locationFilter]);

  // Add listener for custom notification:data-refresh events
  useEffect(() => {
    const handleDataRefresh = (event: Event) => {
      const customEvent = event as CustomEvent<{type: string, data: any}>;
      const { type } = customEvent.detail;
      
      console.log('TicketsPage: Received data refresh event:', type);
      
      if (type === 'ticket') {
        // Refresh the ticket list when we receive a ticket notification
        console.log('TicketsPage: Refreshing ticket list due to notification');
        
        // Use the current status filter when refreshing
        let queryParams = new URLSearchParams();
        if (statusFilter === 'critical') {
          queryParams.append('priority', 'CRITICAL');
        } else if (statusFilter !== 'all') {
          queryParams.append('status', statusFilter.toUpperCase());
        }

        // Add other filters
        if (priorityFilter !== 'all') {
          queryParams.append('priority', priorityFilter.toUpperCase());
        }
        
        if (departmentFilter !== 'all') {
          queryParams.append('department', departmentFilter.toUpperCase());
        }
        
        if (projectFilter !== 'all') {
          queryParams.append('project', projectFilter);
        }
        
        if (locationFilter !== 'all') {
          queryParams.append('location', locationFilter);
        }

        setLoading(true);
        api.get<Ticket[]>(`/tickets?${queryParams.toString()}`)
          .then(response => {
            console.log('TicketsPage: Refreshed tickets:', response.data);
            setTickets(response.data);
          })
          .catch(error => {
            console.error('TicketsPage: Error refreshing tickets:', error);
          })
          .finally(() => {
            setLoading(false);
          });
      }
    };
    
    // Add event listener for custom refresh events
    window.addEventListener('notification:data-refresh', handleDataRefresh);
    
    // Clean up
    return () => {
      window.removeEventListener('notification:data-refresh', handleDataRefresh);
    };
  }, [statusFilter, priorityFilter, departmentFilter, projectFilter, locationFilter]);

  // Handle row click to view ticket details
  const handleRowClick = (ticketId: number) => {
    navigate(`/tickets/${ticketId}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Tickets</h1>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
          Create Ticket
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <input
            type="text"
            placeholder="Search tickets..."
            className="border rounded-md p-2"
          />
          <select 
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="border rounded-md p-2"
          >
            <option value="all">All Status</option>
            <option value="open">Open</option>
            <option value="in_progress">In Progress</option>
            <option value="resolved">Resolved</option>
          </select>
          <select 
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value)}
            className="border rounded-md p-2"
          >
            <option value="all">All Priorities</option>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="critical">Critical</option>
          </select>
          <select 
            value={departmentFilter}
            onChange={(e) => setDepartmentFilter(e.target.value)}
            className="border rounded-md p-2"
          >
            <option value="all">All Departments</option>
            <option value="it">IT</option>
            <option value="hr">HR</option>
            <option value="finance">Finance</option>
          </select>
          <select 
            value={projectFilter}
            onChange={(e) => setProjectFilter(e.target.value)}
            className="border rounded-md p-2"
          >
            <option value="all">All Projects</option>
            <option value="eurobiz">Eurobiz Corporations</option>
            <option value="guardian_intl">Guardian International</option>
            <option value="guardian_dev">Guardian Developers</option>
            <option value="grand_dev">Grand Developers</option>
            <option value="ard_dev">ARD Developers</option>
          </select>
          <select 
            value={locationFilter}
            onChange={(e) => setLocationFilter(e.target.value)}
            className="border rounded-md p-2"
          >
            <option value="all">All Locations</option>
            <option value="hq">Headquarters</option>
            <option value="branch1">Branch Office 1</option>
            <option value="branch2">Branch Office 2</option>
            <option value="remote">Remote</option>
          </select>
        </div>
      </div>

      {/* Tickets Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Ticket ID
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Title
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Priority
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Project
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Department
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Location
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {tickets.length === 0 ? (
              <tr>
                <td colSpan={8} className="px-6 py-4 text-center text-gray-500">
                  No tickets found
                </td>
              </tr>
            ) : (
              tickets.map((ticket) => (
                <tr 
                  key={ticket.id} 
                  className="hover:bg-gray-50 cursor-pointer transition-colors duration-150"
                  onClick={() => handleRowClick(ticket.id)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{ticket.ticketNumber}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">{ticket.title}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      ticket.status === 'OPEN' ? 'bg-yellow-100 text-yellow-800' :
                      ticket.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                      ticket.status === 'RESOLVED' ? 'bg-green-100 text-green-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {ticket.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      ticket.priority === 'LOW' ? 'bg-green-100 text-green-800' :
                      ticket.priority === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                      ticket.priority === 'HIGH' ? 'bg-orange-100 text-orange-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {ticket.priority}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{ticket.project}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{ticket.createdBy.department}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{ticket.location}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">
                      {new Date(ticket.createdAt).toLocaleDateString()}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TicketsPage; 