import api from './api';

// Helper function to handle API errors
const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
};

// Enhanced Employee interface for leave management integration
export interface Employee {
  id: number;
  employeeId: string; // Employee code like "GC-0001"
  firstName: string;
  lastName: string;
  officialEmail?: string;
  personalEmail?: string;
  department: string;
  designation: string;
  joinDate: string;
  employmentStatus: string;
  mobileNumber?: string;
  profileImagePath?: string;
  gender?: string;
  dateOfBirth?: string;
  // Additional fields for leave management
  status?: string;
  manager?: {
    id: number;
    name: string;
  };
}

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  count?: number;
  employees?: T[]; // For backward compatibility with employee listing API
}

// Employee API with enhanced leave management support
export const employeeApi = {
  // Get all employees with enhanced filtering for leave management
  getAll: async (filters?: {
    department?: string;
    designation?: string;
    isActive?: boolean;
    status?: string;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<Employee>> => {
    try {
      const queryParams = new URLSearchParams();
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined) {
            queryParams.append(key, value.toString());
          }
        });
      }
      
      const response = await fetch(`/api/employees/listing?${queryParams.toString()}`);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch employees');
      }
      
      return {
        success: data.success,
        data: data.employees || data.data || [],
        count: data.count,
        employees: data.employees
      };
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Get active employees only (for leave management)
  getActiveEmployees: async (): Promise<PaginatedResponse<Employee>> => {
    try {
      const response = await fetch('/api/employees/listing');
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch employees');
      }

      // Filter only active employees
      const activeEmployees = (data.employees || []).filter((emp: Employee) => {
        const status = emp.status || emp.employmentStatus || 'active';
        return status.toLowerCase() === 'active';
      });
      
      return {
        success: true,
        data: activeEmployees,
        count: activeEmployees.length,
        employees: activeEmployees
      };
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Get employee by ID
  getById: async (id: number): Promise<ApiResponse<Employee>> => {
    try {
      const response = await fetch(`/api/employees/${id}`);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Employee not found');
      }
      
      return data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Get employee by employee code
  getByCode: async (employeeCode: string): Promise<ApiResponse<Employee>> => {
    try {
      const response = await fetch(`/api/employees/code/${employeeCode}`);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Employee not found');
      }
      
      return data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Get unique departments list
  getDepartments: async (): Promise<ApiResponse<string[]>> => {
    try {
      const employeesResponse = await employeeApi.getActiveEmployees();
      if (employeesResponse.success && employeesResponse.data) {
        const departments = [...new Set(
          employeesResponse.data
            .map((emp: Employee) => emp.department)
            .filter((dept: string | undefined): dept is string => dept != null && dept.trim() !== '')
        )].sort() as string[];
        
        return {
          success: true,
          data: departments
        };
      }
      
      return {
        success: true,
        data: []
      };
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Get unique designations/roles list
  getDesignations: async (): Promise<ApiResponse<string[]>> => {
    try {
      const employeesResponse = await employeeApi.getActiveEmployees();
      if (employeesResponse.success && employeesResponse.data) {
        const designations = [...new Set(
          employeesResponse.data
            .map((emp: Employee) => emp.designation)
            .filter((designation: string | undefined): designation is string => designation != null && designation.trim() !== '')
        )].sort() as string[];
        
        return {
          success: true,
          data: designations
        };
      }
      
      return {
        success: true,
        data: []
      };
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Get employees by department (for leave management filtering)
  getByDepartment: async (department: string): Promise<PaginatedResponse<Employee>> => {
    try {
      const allEmployeesResponse = await employeeApi.getActiveEmployees();
      if (allEmployeesResponse.success && allEmployeesResponse.data) {
        const departmentEmployees = allEmployeesResponse.data.filter(
          (emp: Employee) => emp.department?.toLowerCase() === department.toLowerCase()
        );
        
        return {
          success: true,
          data: departmentEmployees,
          count: departmentEmployees.length,
          employees: departmentEmployees
        };
      }
      
      return {
        success: true,
        data: [],
        count: 0,
        employees: []
      };
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
};

export default employeeApi; 