import React from 'react';
import { Shield, Star, AlertTriangle, CheckCircle, Clock, Eye } from 'lucide-react';

interface Trademark {
  id: string;
  trademarkName: string;
  description?: string;
  status: 'pending' | 'registered' | 'opposed' | 'abandoned' | 'expired' | 'renewed';
  registrationNumber?: string;
  jurisdiction: string;
  expiryDate?: string;
  trademarkImageUrl?: string;
  isPrimary: boolean;
}

interface CompanyTrademarkSummaryProps {
  onViewAll: () => void;
}

const CompanyTrademarkSummary: React.FC<CompanyTrademarkSummaryProps> = ({ onViewAll }) => {
  // Mock data - in real app, this would come from props or API
  const trademarks: Trademark[] = [
    {
      id: '1',
      trademarkName: 'InfraSpine',
      description: 'IT Management System Platform',
      status: 'registered',
      registrationNumber: 'TM-2024-001',
      jurisdiction: 'Pakistan',
      expiryDate: '2034-01-20',
      trademarkImageUrl: '/images/infraspine-logo.png',
      isPrimary: true
    },
    {
      id: '2',
      trademarkName: 'InfraSpine Pro',
      description: 'Premium IT Management Suite',
      status: 'pending',
      jurisdiction: 'United States',
      isPrimary: false
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'registered':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'opposed':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'expired':
      case 'abandoned':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'renewed':
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'registered':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'opposed':
        return 'bg-orange-100 text-orange-800';
      case 'expired':
      case 'abandoned':
        return 'bg-red-100 text-red-800';
      case 'renewed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const isExpiringSoon = (expiryDate?: string) => {
    if (!expiryDate) return false;
    const today = new Date();
    const expiry = new Date(expiryDate);
    const timeDiff = expiry.getTime() - today.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
    return daysDiff <= 90 && daysDiff > 0;
  };

  const registeredCount = trademarks.filter(tm => tm.status === 'registered').length;
  const pendingCount = trademarks.filter(tm => tm.status === 'pending').length;
  const expiringCount = trademarks.filter(tm => isExpiringSoon(tm.expiryDate)).length;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <Shield className="h-5 w-5 text-blue-600" />
          Company Trademarks
        </h3>
        <button
          onClick={onViewAll}
          className="flex items-center gap-1 text-blue-600 hover:text-blue-700 text-sm font-medium"
        >
          <Eye className="h-4 w-4" />
          View All
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-green-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600">Registered</p>
              <p className="text-2xl font-bold text-green-700">{registeredCount}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-yellow-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-yellow-600">Pending</p>
              <p className="text-2xl font-bold text-yellow-700">{pendingCount}</p>
            </div>
            <Clock className="h-8 w-8 text-yellow-500" />
          </div>
        </div>
        
        <div className="bg-orange-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-orange-600">Expiring Soon</p>
              <p className="text-2xl font-bold text-orange-700">{expiringCount}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Trademark List */}
      <div className="space-y-4">
        {trademarks.slice(0, 3).map((trademark) => (
          <div key={trademark.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3">
              {trademark.trademarkImageUrl ? (
                <img
                  src={trademark.trademarkImageUrl}
                  alt={trademark.trademarkName}
                  className="h-10 w-10 object-contain bg-white rounded-lg p-1"
                />
              ) : (
                <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Shield className="h-5 w-5 text-blue-600" />
                </div>
              )}
              <div>
                <h4 className="font-medium text-gray-900 flex items-center gap-2">
                  {trademark.trademarkName}
                  {trademark.isPrimary && (
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  )}
                </h4>
                <p className="text-sm text-gray-600">{trademark.description}</p>
                <div className="flex items-center gap-2 mt-1">
                  <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(trademark.status)}`}>
                    {getStatusIcon(trademark.status)}
                    {trademark.status.toUpperCase()}
                  </span>
                  <span className="text-xs text-gray-500">{trademark.jurisdiction}</span>
                </div>
              </div>
            </div>
            
            <div className="text-right">
              {trademark.registrationNumber && (
                <p className="text-sm font-medium text-gray-900">{trademark.registrationNumber}</p>
              )}
              {trademark.expiryDate && (
                <p className={`text-xs ${isExpiringSoon(trademark.expiryDate) ? 'text-orange-600 font-medium' : 'text-gray-500'}`}>
                  Expires: {new Date(trademark.expiryDate).toLocaleDateString()}
                  {isExpiringSoon(trademark.expiryDate) && (
                    <AlertTriangle className="inline h-3 w-3 text-orange-500 ml-1" />
                  )}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>

      {trademarks.length === 0 && (
        <div className="text-center py-8">
          <Shield className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No trademarks</h3>
          <p className="mt-1 text-sm text-gray-500">
            Add your company's trademarks to protect your intellectual property.
          </p>
          <div className="mt-4">
            <button
              onClick={onViewAll}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <Shield className="h-4 w-4 mr-2" />
              Add Trademark
            </button>
          </div>
        </div>
      )}

      {trademarks.length > 3 && (
        <div className="mt-4 text-center">
          <button
            onClick={onViewAll}
            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
          >
            View {trademarks.length - 3} more trademarks
          </button>
        </div>
      )}
    </div>
  );
};

export default CompanyTrademarkSummary;
