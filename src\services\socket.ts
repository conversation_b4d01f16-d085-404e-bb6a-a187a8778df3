import { Manager } from 'socket.io-client';
import { toast } from 'react-hot-toast';

export interface NotificationData {
  id: string;
  type: 'ticket' | 'escalation' | 'email' | 'system';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
  link?: string;
  destinationPage?: string;
  ticketData?: {
    ticketId: string;
    subject: string;
    status?: string;
    priority?: string;
    department?: string;
    assignedTo?: string;
    updatedBy?: string;
  };
}

class SocketService {
  private socket: any | null = null;
  private connecting = false;
  private notificationHandlers: ((data: NotificationData) => void)[] = [];
  private typingTimeouts: Map<string, number> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: number | null = null;
  private isConnected = false;
  private connectionPromise: Promise<any> | null = null;
  private token: string | null = null;
  private processedNotifications = new Set<string>();
  private isConnecting = false;
  private subscriptions = new Map<string, boolean>();
  private lastConnectionAttempt = 0;
  private heartbeatInterval: number | null = null;
  private heartbeatFailCount = 0;

  setToken(token: string | null) {
    this.token = token;
    
    // If token is set and we're not already connected, connect
    if (token && !this.socket?.connected && !this.isConnecting) {
      this.connect();
    } else if (!token && this.socket) {
      // If token is removed, disconnect
      this.disconnect();
    }
  }

  async connect(): Promise<any> {
    console.log('Socket service: Connecting to socket server');
    
    // Prevent rapid reconnection attempts
    const now = Date.now();
    if (now - this.lastConnectionAttempt < 5000) { // Increased to 5 seconds
      console.log('Socket service: Throttling connection attempts');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
    this.lastConnectionAttempt = Date.now();
    
    // If already connected, just return the socket
    if (this.socket && this.socket.connected) {
      console.log('Socket service: Already connected');
      this.isConnected = true;
      return this.socket;
    }

    // If connection is in progress, return the existing promise
    if (this.connecting && this.connectionPromise) {
      console.log('Socket service: Connection already in progress');
      return this.connectionPromise;
    }

    this.connecting = true;
    this.isConnecting = true;

    // Create a timeout promise that will reject after 15 seconds
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Connection timeout after 15 seconds')), 15000);
    });

    this.connectionPromise = Promise.race([
      new Promise(async (resolve, reject) => {
        try {
          // Get token from localStorage
          const token = this.token || localStorage.getItem('authToken');
          
          // Try to get user data for debugging
          const userDataStr = localStorage.getItem('userData');
          let userData = null;
          try {
            if (userDataStr) {
              userData = JSON.parse(userDataStr);
              console.log('Socket service: User data found:', userData.id ? 'ID: ' + userData.id : 'No ID');
            }
          } catch (e) {
            console.error('Socket service: Error parsing user data:', e);
          }
          
          console.log('Socket service: Token found:', token ? 'Yes' : 'No');
          
          if (!token) {
            console.error('Socket service: No authentication token found in localStorage');
            this.connecting = false;
            this.isConnecting = false;
            reject(new Error('No authentication token found'));
            return;
          }

          // Use the correct API URL
          // Try to use the environment variable if available
          let apiUrl = 'http://localhost:5000';
          // Check if we're in a browser environment and if VITE_API_URL is defined in window
          if (typeof window !== 'undefined') {
            if ((window as any).VITE_API_URL) {
              apiUrl = (window as any).VITE_API_URL;
            } else if (window.location) {
              // Extract the origin from the current URL
              const currentOrigin = window.location.origin;
              // If we're not on localhost:3000 (development), use the current origin
              if (!currentOrigin.includes('localhost:3000')) {
                apiUrl = currentOrigin;
              }
            }
          }
          console.log('Socket service: Using API URL:', apiUrl);
          
          try {
            const options = {
              autoConnect: false,
              reconnection: true,
              reconnectionAttempts: 5,
              reconnectionDelay: 1000,
              reconnectionDelayMax: 5000,
              timeout: 20000, // Increase timeout to 20 seconds
              query: { token },
              auth: { token },
              transports: ['polling', 'websocket'], // Try polling first, then websocket
              path: '/socket.io/',
              forceNew: true,
              withCredentials: true, // Enable credentials since the server supports them now with specific origin
              upgrade: true,
              rememberUpgrade: true
            };
            
            console.log('Socket service: Connection options:', JSON.stringify({
              ...options,
              query: { token: token ? token.substring(0, 10) + '...' : null },
              auth: { token: token ? token.substring(0, 10) + '...' : null }
            }));
            
            // Clean up any existing socket
            if (this.socket) {
              console.log('Socket service: Cleaning up existing socket');
              try {
                this.socket.disconnect();
              } catch (e) {
                console.error('Socket service: Error disconnecting socket:', e);
              }
              this.socket = null;
            }
            
            try {
              const manager = new Manager(apiUrl, options);
              
              // Prevent errors from causing page refreshes
              manager.on('error', (err: any) => {
                console.error('Socket manager error:', err);
              });
              
              manager.on('reconnect_error', (err: any) => {
                console.error('Socket manager reconnect error:', err);
              });
              
              this.socket = manager.socket('/notifications');
              
              // Set up a connection timeout
              const connectionTimeout = setTimeout(() => {
                if (this.socket && !this.socket.connected) {
                  console.error('Socket service: Connection timed out');
                  this.socket.disconnect();
                  reject(new Error('Connection timed out'));
                }
              }, 10000);
              
              this.socket.on('connect', () => {
                console.log('Socket service: Connected with ID:', this.socket?.id);
                clearTimeout(connectionTimeout);
                this.reconnectAttempts = 0;
                this.isConnected = true;
                this.connecting = false;
                this.isConnecting = false;
                
                // Resubscribe to all active subscriptions after connection
                this.subscriptions.forEach((active, userId) => {
                  if (active) {
                    console.log(`Socket service: Resubscribing to notifications for user ${userId} after connection`);
                    this.socket?.emit('subscribe', { userId });
                  }
                });
                
                resolve(this.socket);
              });
              
              this.socket.on('connect_error', (error: Error) => {
                console.error('Socket connection error:', error.message, error);
                clearTimeout(connectionTimeout);
                this.connecting = false;
                this.isConnecting = false;
                this.isConnected = false;
                
                // Only log non-network related errors
                if (!error.message.includes('xhr poll error') && 
                    !error.message.includes('timeout') && 
                    !error.message.includes('transport error')) {
                  console.error(`Notification service error: ${error.message}`);
                }
                
                // Try to reconnect with polling if websocket failed
                if (error.message.includes('websocket')) {
                  console.log('Websocket transport failed, trying polling...');
                  if (this.socket && this.socket.io) {
                    this.socket.io.opts.transports = ['polling'];
                  }
                }
                
                this.handleReconnect();
                reject(error);
              });
              
              this.socket.on('disconnect', (reason: string) => {
                console.log('Socket disconnected:', reason);
                this.isConnected = false;
                
                // Only log certain disconnect reasons
                if (reason === 'io server disconnect' || reason === 'io client disconnect') {
                  console.warn('Disconnected from notification service:', reason);
                }
                
                // Don't refresh the page on disconnect
                if (reason !== 'io client disconnect') {
                  this.handleReconnect();
                }
              });
              
              this.socket.on('error', (error: Error) => {
                console.error('Socket error:', error.message);
              });
              
              this.setupNotificationListeners();
              
              console.log('Socket service: Connecting to socket...');
              this.socket.connect();
            } catch (e) {
              console.error('Socket service: Error creating socket:', e);
              this.connecting = false;
              this.isConnecting = false;
              reject(e);
            }
          } catch (error) {
            console.error('Error creating socket connection:', error);
            this.connecting = false;
            this.isConnecting = false;
            this.connectionPromise = null;
            reject(error);
          }
        } catch (error) {
          console.error('Socket service: Unexpected error during connection:', error);
          this.connecting = false;
          this.isConnecting = false;
          reject(error);
        }
      }),
      timeoutPromise
    ]).catch(error => {
      console.error('Socket service: Connection failed or timed out:', error.message);
      this.connecting = false;
      this.isConnecting = false;
      this.connectionPromise = null;
      throw error;
    });

    return this.connectionPromise;
  }

  private handleReconnect() {
    console.log('Socket service: Attempting to reconnect');
    
    // Clear any existing reconnect timeout
    if (this.reconnectTimeout !== null) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    // Increase reconnect attempts
    this.reconnectAttempts++;
    
    // If we've exceeded the maximum number of attempts, stop trying
    if (this.reconnectAttempts > this.maxReconnectAttempts) {
      console.error(`Socket service: Failed to reconnect after ${this.maxReconnectAttempts} attempts`);
      this.reconnectAttempts = 0;
      return;
    }
    
    // Calculate backoff delay (exponential backoff with jitter)
    const baseDelay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    const jitter = Math.random() * 1000;
    const delay = baseDelay + jitter;
    
    console.log(`Socket service: Reconnecting in ${Math.round(delay / 1000)} seconds (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    // Set a timeout to reconnect
    this.reconnectTimeout = window.setTimeout(() => {
      console.log('Socket service: Attempting reconnection now');
      this.connect().catch(error => {
        console.error('Socket service: Reconnection failed:', error);
        this.handleReconnect();
      });
    }, delay);
  }

  disconnect() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    
    this.reconnectAttempts = 0;
    this.isConnected = false;
    this.connectionPromise = null;
    this.connecting = false;
    this.isConnecting = false;
    this.subscriptions.clear();
    this.processedNotifications.clear();
  }

  private setupHeartbeat() {
    if (!this.socket) return;
    
    // Clear any existing heartbeat interval
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    
    // Reset heartbeat failure count
    this.heartbeatFailCount = 0;
    
    // Set up a new heartbeat interval with a longer interval (30 seconds instead of default)
    this.heartbeatInterval = window.setInterval(() => {
      // Skip heartbeat if socket is not connected
      if (!this.socket || !this.socket.connected) {
        // If socket is not connected, don't try to send heartbeat
        if (this.socket && !this.socket.connected) {
          console.log('Socket service: Socket disconnected, attempting to reconnect');
          // Don't wait for the connect promise to resolve/reject
          this.connect().catch(error => {
            console.error('Socket service: Reconnection failed:', error);
            this.handleReconnect();
          });
        }
        return;
      }
      
      console.log('Socket service: Sending heartbeat');
      try {
        // Use a timeout to prevent hanging - increased from 5000ms to 15000ms
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Heartbeat timeout')), 15000);
        });
        
        // Send the heartbeat with a timeout
        Promise.race([
          new Promise(resolve => {
            this.socket?.emit('heartbeat', { timestamp: Date.now() }, resolve);
          }),
          timeoutPromise
        ]).catch(error => {
          // Only log the error if it's not a timeout
          if (error.message !== 'Heartbeat timeout') {
            console.warn('Socket service: Heartbeat failed:', error.message);
          } else {
            // For timeout errors, only log once every 5 failures to reduce console spam
            if (!this.heartbeatFailCount) this.heartbeatFailCount = 0;
            this.heartbeatFailCount++;
            
            if (this.heartbeatFailCount % 5 === 1) {
              console.warn('Socket service: Heartbeat timeout (suppressing further logs)');
            }
            
            // After 3 consecutive failures, try to reconnect
            if (this.heartbeatFailCount >= 3) {
              console.log('Socket service: Multiple heartbeat failures, attempting to reconnect');
              this.socket?.disconnect();
              this.connect().catch(error => {
                console.error('Socket service: Reconnection failed after heartbeat failures:', error);
              });
              // Reset the failure count after attempting reconnect
              this.heartbeatFailCount = 0;
            }
          }
        });
      } catch (error) {
        console.error('Socket service: Error sending heartbeat:', error);
      }
    }, 30000); // Increased from default (likely 10000ms) to 30000ms
  }

  private setupNotificationListeners() {
    if (!this.socket) return;

    // Only remove the notification handler, keep any custom ticket:update handlers
    this.socket.off('notification');
    this.socket.off('typing');
    
    // Set up heartbeat
    this.setupHeartbeat();
    
    const processNotification = (data: NotificationData) => {
      // Check if we've already processed this notification
      if (this.processedNotifications.has(data.id)) {
        console.log('Socket service: Ignoring duplicate notification:', data.id);
        return;
      }
      
      console.log('Socket service: Processing notification:', data);
      
      // Add to processed set to avoid duplicates
      this.processedNotifications.add(data.id);
      
      // Limit the size of the processed set to avoid memory leaks
      if (this.processedNotifications.size > 100) {
        // Remove the oldest entries (convert to array, slice, convert back to set)
        const entries = Array.from(this.processedNotifications);
        this.processedNotifications = new Set(entries.slice(-50));
      }
      
      // Show toast notification
      if (data.type === 'ticket') {
        // For ticket status updates, show a more specific toast
        if (data.title.includes('Status Updated')) {
          const statusMatch = data.message.match(/status changed from .* to (\w+)/);
          const newStatus = statusMatch ? statusMatch[1] : 'updated';
          
          if (newStatus === 'RESOLVED') {
            toast.success(data.message, { icon: '✅' });
          } else if (newStatus === 'IN_PROGRESS') {
            toast.success(data.message, { icon: '🔄' });
          } else {
            toast.success(data.message);
          }
        } else {
          toast.success(data.message);
        }
      } else if (data.type === 'escalation') {
        toast.error(data.message);
      } else {
        toast(data.message);
      }
      
      // Notify all handlers
      this.notificationHandlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error('Socket service: Error in notification handler:', error);
        }
      });
      
      // Dispatch a custom event for components to listen to
      try {
        const refreshEvent = new CustomEvent('notification:data-refresh', {
          detail: {
            type: 'ticket',
            data: data.ticketData || { ticketId: '0' }
          }
        });
        window.dispatchEvent(refreshEvent);
      } catch (error) {
        console.error('Socket service: Error dispatching refresh event:', error);
      }
    };

    this.socket.on('notification', (data: NotificationData) => {
      console.log('Socket service: Received notification:', data);
      processNotification(data);
    });

    this.socket.on('typing', (data: { userId: string, ticketId: string, isTyping: boolean }) => {
      console.log('Socket service: Typing event:', data);
    });
    
    // Set up ticket update listeners
    const ticketUpdateListeners: ((data: any) => void)[] = [];
    
    if (ticketUpdateListeners.length === 0) {
      this.socket.on('ticket:update', (data: any) => {
        console.log('Socket service: Received ticket update (default handler):', data);
        
        // Check if this is already a properly formatted notification
        if (data.id && data.type && data.title && data.message) {
          processNotification(data as NotificationData);
          return;
        }
        
        // Convert ticket update to notification format
        const notification: NotificationData = {
          id: `ticket-${data.ticketId}-${Date.now()}`,
          type: 'ticket',
          title: `Ticket Status Updated`,
          message: `Ticket #T${data.ticketId} status changed from ${data.status || 'UNKNOWN'} to ${data.newStatus || data.status}`,
          timestamp: new Date(),
          read: false,
          priority: data.priority?.toLowerCase() as any || 'medium',
          link: `/tickets/${data.ticketId}`,
          destinationPage: 'tickets',
          ticketData: {
            ticketId: data.ticketId.toString(),
            subject: data.subject || 'Ticket Update',
            status: data.newStatus || data.status,
            priority: data.priority,
            department: data.department,
            assignedTo: data.assignedTo,
            updatedBy: data.updatedBy
          }
        };
        
        console.log('Socket service: Created notification from ticket update:', notification);
        
        // Process the notification
        processNotification(notification);
      });
    }

    // Handle reconnection events
    this.socket.on('reconnect', (attemptNumber: number) => {
      console.log(`Socket service: Reconnected after ${attemptNumber} attempts`);
      
      // Resubscribe to all active subscriptions
      this.subscriptions.forEach((active, userId) => {
        if (active) {
          console.log(`Socket service: Resubscribing to notifications for user ${userId} after reconnection`);
          this.socket?.emit('subscribe', { userId });
        }
      });
    });
    
    // Add a handler for subscription acknowledgment
    this.socket.on('subscribed', (data: { userId: string }) => {
      console.log(`Socket service: Successfully subscribed to notifications for user ${data.userId}`);
    });
    
    // Add a handler for subscription errors
    this.socket.on('subscription_error', (error: any) => {
      console.error('Socket service: Subscription error:', error);
    });
  }

  onNotification(handler: (data: NotificationData) => void) {
    console.log('Socket service: Adding notification handler');
    this.notificationHandlers.push(handler);
    return () => {
      console.log('Socket service: Removing notification handler');
      this.notificationHandlers = this.notificationHandlers.filter(h => h !== handler);
    };
  }

  // Add a method to handle ticket update events specifically
  onTicketUpdate(handler: (data: any) => void) {
    console.log('Socket service: Adding ticket update handler');
    
    if (!this.socket) {
      console.warn('Socket service: Cannot add ticket update handler: socket not initialized');
      return () => {}; // Return empty function
    }
    
    // Remove any existing handlers to avoid duplicates
    this.socket.off('ticket:update');
    
    // Add the new handler
    this.socket.on('ticket:update', (data: any) => {
      console.log('Socket service: Received ticket update:', data);
      handler(data);
    });
    
    // Return unsubscribe function
    return () => {
      console.log('Socket service: Removing ticket update handler');
      if (this.socket) {
        this.socket.off('ticket:update');
      }
    };
  }

  subscribeToNotifications(userIdOrHandler: string | ((data: NotificationData) => void)) {
    // If the parameter is a function, it's a notification handler
    if (typeof userIdOrHandler === 'function') {
      const handler = userIdOrHandler;
      console.log('Socket service: Adding notification handler');
      this.notificationHandlers.push(handler);
      
      // Return unsubscribe function
      return () => {
        console.log('Socket service: Removing notification handler');
        this.notificationHandlers = this.notificationHandlers.filter(h => h !== handler);
      };
    }
    
    // Otherwise, it's a userId
    const userId = userIdOrHandler;
    
    // Check if already subscribed to prevent duplicate subscriptions
    if (this.subscriptions.get(userId)) {
      console.log(`Socket service: Already subscribed to notifications for user ${userId}`);
      return () => {
        console.log(`Socket service: Unsubscribing from notifications for user ${userId} (existing subscription)`);
        this.subscriptions.set(userId, false);
        if (this.socket && this.socket.connected) {
          this.unsubscribeFromNotifications(userId);
        }
      };
    }
    
    console.log(`Socket service: Setting up subscription for user ${userId}`);
    
    // Mark this subscription as active
    this.subscriptions.set(userId, true);
    
    if (!this.socket || !this.socket.connected) {
      console.warn('Socket service: Cannot subscribe to notifications: socket not connected');
      
      this.connect()
        .then(() => {
          if (this.socket && this.socket.connected && this.subscriptions.get(userId)) {
            console.log(`Socket service: Connected successfully, now subscribing to notifications for user ${userId}`);
            this.socket.emit('subscribe', { userId });
          } else {
            console.warn('Socket service: Socket still not connected after connect() call or subscription was cancelled');
          }
        })
        .catch(err => console.error('Socket service: Failed to connect for subscription:', err));
      
      return () => {
        console.log(`Socket service: Unsubscribing from notifications for user ${userId} (from connect path)`);
        this.subscriptions.set(userId, false);
        if (this.socket && this.socket.connected) {
          this.unsubscribeFromNotifications(userId);
        }
      };
    }
    
    console.log(`Socket service: Subscribing to notifications for user ${userId} (socket already connected)`);
    this.socket.emit('subscribe', { userId });
    
    return () => {
      console.log(`Socket service: Unsubscribing from notifications for user ${userId} (from direct path)`);
      this.subscriptions.set(userId, false);
      if (this.socket && this.socket.connected) {
        this.unsubscribeFromNotifications(userId);
      }
    };
  }

  unsubscribeFromNotifications(userId: string) {
    this.subscriptions.set(userId, false);
    
    if (!this.socket || !this.socket.connected) {
      console.warn('Cannot unsubscribe from notifications: socket not connected');
      return;
    }

    console.log('Unsubscribing from notifications for user:', userId);
    this.socket.emit('unsubscribe', { userId });
  }

  emitTyping(ticketId: string, userId: string, isTyping: boolean) {
    if (!this.socket || !this.socket.connected) {
      console.log('Socket functionality temporarily disabled');
      return;
    }

    const key = `${userId}-${ticketId}`;
    
    if (this.typingTimeouts.has(key)) {
      clearTimeout(this.typingTimeouts.get(key)!);
      this.typingTimeouts.delete(key);
    }

    this.socket.emit('typing', { ticketId, userId, isTyping });

    if (isTyping) {
      const timeout = window.setTimeout(() => {
        this.emitTyping(ticketId, userId, false);
      }, 5000);
      
      this.typingTimeouts.set(key, timeout);
    }
  }

  markNotificationAsRead(notificationId: string) {
    if (!this.socket || !this.socket.connected) {
      console.warn('Cannot mark notification as read: socket not connected');
      return;
    }
    
    this.socket.emit('notification:mark_read', { notificationId });
  }

  getNotificationPreferences() {
    if (!this.socket || !this.socket.connected) {
      return Promise.reject('Socket not connected');
    }
    
    return new Promise((resolve) => {
      this.socket.emit('notification:get_preferences', {}, (preferences: any) => {
        resolve(preferences);
      });
    });
  }

  updateNotificationPreferences(preferences: any) {
    if (!this.socket || !this.socket.connected) {
      return Promise.reject('Socket not connected');
    }
    
    return new Promise((resolve) => {
      this.socket.emit('notification:update_preferences', preferences, (response: any) => {
        resolve(response);
      });
    });
  }

  // Helper method to manually add a notification (for testing)
  addTestNotification(notification: Partial<NotificationData>) {
    const defaultNotification: NotificationData = {
      id: `test-${Date.now()}`,
      type: 'system',
      title: 'Test Notification',
      message: 'This is a test notification',
      timestamp: new Date(),
      read: false,
      priority: 'medium',
      destinationPage: 'dashboard' // Default destination page
    };
    
    const fullNotification = { ...defaultNotification, ...notification };
    
    // Process through the same pipeline as socket notifications
    this.notificationHandlers.forEach(handler => {
      try {
        handler(fullNotification);
      } catch (error) {
        console.error('Error in notification handler:', error);
      }
    });
    
    return fullNotification;
  }

  subscribeToMessages() {
    console.log('Socket functionality temporarily disabled');
    return () => {};
  }

  subscribeToTyping() {
    console.log('Socket functionality temporarily disabled');
    return () => {};
  }

  markAsRead(ticketId: string, messageIds: string[]) {
    console.log('Socket functionality temporarily disabled');
  }

  subscribeToReadReceipts(callback: (data: { messageId: string; readBy: string[] }) => void) {
    console.log('Socket functionality temporarily disabled');
  }

  addReaction(ticketId: string, messageId: string, reaction: string) {
    console.log('Socket functionality temporarily disabled');
  }

  removeReaction(ticketId: string, messageId: string, reaction: string) {
    console.log('Socket functionality temporarily disabled');
  }

  subscribeToReplies(callback: (reply: any) => void) {
    console.log('Socket functionality temporarily disabled');
  }

  updatePresence(status: 'online' | 'away' | 'offline') {
    console.log('Socket functionality temporarily disabled');
  }

  subscribeToPresence(callback: (data: { userId: string; status: string }) => void) {
    console.log('Socket functionality temporarily disabled');
  }

  subscribeToOnlineUsers() {
    console.log('Socket functionality temporarily disabled');
    return () => {};
  }

  emitStopTyping() {
    console.log('Socket functionality temporarily disabled');
  }

  getSocket(): any | null {
    return this.socket;
  }
}

export const socketService = new SocketService();

// Make the socket service available in the window object for testing
if (typeof window !== 'undefined') {
  (window as any).socketService = socketService;
} 