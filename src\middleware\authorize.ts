import { Response, NextFunction } from 'express';
import { UserRole } from '../entities/User';
import { AppError } from './errorHandler';
import { AuthRequest } from './auth';
import logger from '../utils/logger';

export const authorize = (allowedRoles: UserRole[]) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      const user = req.user;
      
      if (!user) {
        throw new AppError(401, 'UNAUTHORIZED', 'User not authenticated');
      }

      if (!allowedRoles.includes(user.role)) {
        logger.warn(`Unauthorized access attempt by user ${user.id} with role ${user.role}`);
        throw new AppError(403, 'FORBIDDEN', 'You do not have permission to perform this action');
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};