import { AppDataSource } from '../config/database';
import { AddFatherNameField1746000000020 } from '../migrations/1746000000020-AddFatherNameField';

async function runFatherNameMigration() {
  console.log('Starting father name field migration...');
  
  try {
    // Initialize the data source
    if (!AppDataSource.isInitialized) {
      console.log('Initializing database connection...');
      await AppDataSource.initialize();
      console.log('Database connection initialized');
    }
    
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    
    // Start transaction
    await queryRunner.startTransaction();
    
    try {
      console.log('Running migration to add fatherName field to employees table...');
      const migration = new AddFatherNameField1746000000020();
      await migration.up(queryRunner);
      
      // Commit transaction
      await queryRunner.commitTransaction();
      console.log('Migration completed successfully');
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();
      console.error('Error during migration:', error);
      throw error;
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
    
  } catch (error) {
    console.error('Migration process failed:', error);
  } finally {
    // Close the database connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('Database connection closed');
    }
  }
}

// Run the migration
runFatherNameMigration().then(() => {
  console.log('Father name field migration script completed');
}).catch(error => {
  console.error('Father name field migration script failed:', error);
}); 