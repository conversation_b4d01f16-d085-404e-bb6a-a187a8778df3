import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Unique
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsEnum, IsDateString, Length } from 'class-validator';
import { User } from './User';
import { Project } from './Project';

export enum ProjectRole {
  OWNER = 'owner',
  MANAGER = 'manager',
  LEAD = 'lead',
  DEVELOPER = 'developer',
  DESIGNER = 'designer',
  TESTER = 'tester',
  ANALYST = 'analyst',
  MEMBER = 'member',
  VIEWER = 'viewer'
}

@Entity('project_members')
@Unique(['projectId', 'userId'])
export class ProjectMember {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: ProjectRole,
    default: ProjectRole.MEMBER
  })
  @IsEnum(ProjectRole, { message: 'Invalid project role' })
  role: ProjectRole;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid join date format' })
  joinedDate: string;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid leave date format' })
  leftDate: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  notes: string;

  // Relations
  @ManyToOne(() => Project, project => project.members, { nullable: false, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @Column({ type: 'int' })
  projectId: number;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'uuid' })
  userId: string;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'addedById' })
  addedBy: User;

  @Column({ type: 'uuid' })
  addedById: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;
}
