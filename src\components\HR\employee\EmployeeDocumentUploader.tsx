import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-hot-toast';

interface Document {
  id: number;
  documentType: string;
  verificationStatus: 'verified' | 'rejected';
  filePath: string;
  preview: string;
  createdAt: string;
}

interface EmployeeDocumentUploaderProps {
  employeeId: string | number;
  onDocumentChange?: () => void;
}

const EmployeeDocumentUploader: React.FC<EmployeeDocumentUploaderProps> = ({ 
  employeeId,
  onDocumentChange 
}) => {
  const [loading, setLoading] = useState(false);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Form state
  const [documentType, setDocumentType] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  
  // Document types
  const documentTypes = [
    'National ID/CNIC',
    'Passport',
    'Driving License',
    'Educational Certificate',
    'Experience Certificate',
    'Bank Account Details',
    'Tax Documents',
    'Medical Certificate',
    'Police Clearance',
    'CV/Resume',
    'Affidavit',
    'Other'
  ];
  
  // Fetch documents on load
  useEffect(() => {
    fetchDocuments();
  }, [employeeId]);
  
  const fetchDocuments = async () => {
    try {
      setLoading(true);
      // Add timestamp to prevent cache issues
      const response = await axios.get(`/api/employees/${employeeId}/documents?t=${Date.now()}`);
      
      if (response.data.success) {
        console.log('Documents fetched successfully:', response.data.documents.length);
        setDocuments(response.data.documents);
      } else {
        setError('Failed to fetch documents');
      }
    } catch (err) {
      setError('Error fetching documents');
      console.error('Error fetching documents:', err);
    } finally {
      setLoading(false);
    }
  };
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedFile) {
      setError('Please select a file');
      return;
    }
    
    if (!documentType) {
      setError('Please select document type');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      // Display uploading message
      const toastId = toast.loading('Uploading document...');
      
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('documentType', documentType);
      formData.append('verificationStatus', 'verified');
      
      const response = await axios.post(
        `/api/employees/${employeeId}/documents`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      
      // Dismiss the loading toast
      toast.dismiss(toastId);
      
      if (response.data.success) {
        // Show success toast
        toast.success('Document uploaded successfully. Reload the page to see it in the document list.');
        
        // Log the uploaded document details
        console.log('Document uploaded successfully:', response.data.document);
        
        setSuccess('Document uploaded successfully. Please reload the page to see all your documents.');
        // Reset form
        setDocumentType('');
        setSelectedFile(null);
        
        // Force a fresh document fetch
        setTimeout(() => {
          fetchDocuments();
        }, 1000); // Add a small delay to ensure server has processed the upload
        
        // Notify parent component if needed
        if (onDocumentChange) {
          onDocumentChange();
        }
      } else {
        // Show error toast
        toast.error(response.data.message || 'Failed to upload document');
        
        setError(response.data.message || 'Failed to upload document');
      }
    } catch (err: any) {
      toast.error(err.response?.data?.message || 'Error uploading document');
      setError(err.response?.data?.message || 'Error uploading document');
      console.error('Error uploading document:', err);
    } finally {
      setLoading(false);
    }
  };
  
  const handleDelete = async (documentId: number) => {
    if (!confirm('Are you sure you want to delete this document?')) {
      return;
    }
    
    try {
      setLoading(true);
      
      // Show deletion in progress
      const toastId = toast.loading('Deleting document...');
      
      const response = await axios.delete(`/api/employees/${employeeId}/documents/${documentId}`);
      
      // Dismiss the loading toast
      toast.dismiss(toastId);
      
      if (response.data.success) {
        // Show success toast
        toast.success('Document deleted successfully');
        
        setSuccess('Document deleted successfully. The document list has been updated.');
        
        // Remove document from local state immediately
        setDocuments(prevDocuments => prevDocuments.filter(doc => doc.id !== documentId));
        
        // Also force a fresh fetch from server to ensure data is in sync
        setTimeout(() => {
          fetchDocuments();
        }, 500);
        
        // Notify parent component if needed
        if (onDocumentChange) {
          onDocumentChange();
        }
      } else {
        // Show error toast
        toast.error(response.data.message || 'Failed to delete document');
        setError(response.data.message || 'Failed to delete document');
      }
    } catch (err: any) {
      toast.error(err.response?.data?.message || 'Error deleting document');
      setError(err.response?.data?.message || 'Error deleting document');
      console.error('Error deleting document:', err);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="employee-documents">
      <h3>Employee Documents</h3>
      
      {/* Error and success messages */}
      {error && <div className="alert alert-danger">{error}</div>}
      {success && (
        <div className="alert alert-success">
          {success}
          <div className="mt-2">
            <button 
              className="btn btn-sm btn-outline-primary" 
              onClick={() => window.location.reload()}
            >
              Reload Page
            </button>
          </div>
        </div>
      )}
      
      {/* Upload form */}
      <form onSubmit={handleSubmit} className="document-upload-form mb-4">
        <div className="row">
          <div className="col-md-6 mb-3">
            <label htmlFor="documentType" className="form-label">Document Type *</label>
            <select
              id="documentType"
              className="form-select"
              value={documentType}
              onChange={(e) => setDocumentType(e.target.value)}
              required
            >
              <option value="">Select Document Type</option>
              {documentTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>
          
          <div className="col-md-6 mb-3">
            <label htmlFor="documentFile" className="form-label">Upload Document *</label>
            <input
              type="file"
              id="documentFile"
              className="form-control"
              onChange={handleFileChange}
              accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
              required
            />
            {selectedFile && (
              <div className="mt-2">
                <small>Selected file: {selectedFile.name} ({Math.round(selectedFile.size / 1024)} KB)</small>
              </div>
            )}
          </div>
          
          <div className="col-12">
            <button 
              type="submit" 
              className="btn btn-primary" 
              disabled={loading || !selectedFile}
            >
              {loading ? 'Uploading...' : 'Upload Document'}
            </button>
          </div>
        </div>
      </form>
      
      {/* Documents list */}
      <div className="documents-list">
        <h4>Uploaded Documents ({documents.length})</h4>
        
        {loading && <p>Loading documents...</p>}
        
        {documents.length === 0 && !loading && (
          <p>No documents uploaded yet.</p>
        )}
        
        <div className="row">
          {documents.map(doc => (
            <div key={doc.id} className="col-md-6 col-lg-4 mb-3">
              <div className="card h-100">
                <div className="card-body">
                  <h5 className="card-title">{doc.documentType}</h5>
                  
                  <p className="card-text">
                    <small className="text-muted">
                      Uploaded: {new Date(doc.createdAt).toLocaleDateString()}
                    </small>
                  </p>
                  
                  <span className={`badge bg-${
                    doc.verificationStatus === 'verified' ? 'success' : 'danger'
                  }`}>
                    {doc.verificationStatus === 'verified' ? 'Verified' : 'Rejected'}
                  </span>
                </div>
                
                <div className="card-footer bg-transparent d-flex justify-content-between">
                  <a 
                    href={doc.preview} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="btn btn-sm btn-primary"
                  >
                    View
                  </a>
                  <a 
                    href={`/api/employees/${employeeId}/documents/${doc.id}`}
                    download
                    className="btn btn-sm btn-secondary"
                  >
                    Download
                  </a>
                  <button
                    onClick={() => handleDelete(doc.id)}
                    className="btn btn-sm btn-danger"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EmployeeDocumentUploader; 