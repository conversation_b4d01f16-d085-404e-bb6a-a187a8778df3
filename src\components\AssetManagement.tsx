import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { 
  Search, Filter, Plus, Monitor, Tag, MapPin, Building2, 
  MoreVertical, AlertCircle, Laptop, Smartphone, Server,
  Printer, Network, Database, HardDrive, Settings,
  Calendar, Clock, DollarSign, Package, Shield,
  CheckCircle, XCircle, AlertTriangle, User, Save,
  Eye, X, FileText, Users, Trash2, Barcode, Box, TrendingDown, TrendingUp, Columns
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import AssetForm from './AssetForm';
import { toast } from 'react-hot-toast';
import { Table } from './Table';
import { ViewAssetModal } from './ViewAssetModal';
import { DeleteConfirmationModal } from './DeleteConfirmationModal';
import { useNavigate } from 'react-router-dom';
import AssetTable from './AssetTable';
import api, { safelyHandleResponse } from '../services/api';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import ColumnSelector from './ColumnSelector';
import type { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';

// Define helper functions
const formatPKR = (amount: number) => {
  if (isNaN(amount) || amount === null) return 'Rs 0';
  const formattedNumber = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
    useGrouping: true
  }).format(Number(amount));
  return `Rs ${formattedNumber}`;
};

// Define asset types and categories
const categoryMap: Record<string, string[]> = {
  'Computing': ['Desktop', 'Laptop', 'Server', 'Workstation', 'Mini PC', 'Embedded System', 'Other'],
  'Mobile & Tablet': ['Smartphone', 'Tablet', 'Feature Phone', 'PDAs & Rugged Devices', 'Wearables', 'Other'],
  'Networking & Communication': ['Router & Modem', 'Switch', 'Access Point', 'VoIP Phone', 'Gateway', 'Firewall', 'Load Balancer', 'Controller', 'Cables & Connectors', 'PBX', 'Other'],
  'Printing': ['Inkjet Printer', 'Laser Printer', '3D Printer', 'Scanner', 'Photo Copier', 'Plotter', 'Label Printer', 'Dot Matrix Printer', 'Other'],
  'Display & Multimedia': ['Monitor', 'Projector', 'TV', 'LED Display', 'Digital Signage', 'Speakers & Microphones', 'Conference Equipment', 'Other'],
  'Security & Surveillance': ['CCTV Camera', 'IP Camera', 'NVR', 'DVR', 'Biometric Device', 'RFID Scanner', 'Access Control', 'Alarm System', 'Other'],
  'Peripherals & Accessories': ['Keyboard', 'Mouse', 'Webcam', 'Docking Station', 'External Storage', 'Headset', 'Other'],
  'Other Equipment': ['Other']
};

const assetTypes = Object.keys(categoryMap);

// Define FilterState interface
interface FilterState {
  assetType: string;
  status: string;
  department: string;
  category: string;
  project: string;
  location: string;
  manufacturer: string;
}

// Define initial filter state
const initialFilterState: FilterState = {
  assetType: '',
  status: '',
  department: '',
  category: '',
  project: '',
  location: '',
  manufacturer: ''
};

// Define AssetFrontend interface
export interface AssetFrontend {
  id?: number;
  assetType: string;
  category: string;
  manufacturer: string;
  model: string;
  serialNumber: string;
  status: string;
  condition: string;
  location: string;
  department: string;
  purchaseDate?: string;
  warrantyExpiry?: string;
  cost?: number;
  vendor?: string;
  project?: string;
  internetAccess?: boolean;
  ipAddress?: string;
  attributes: Record<string, any>;
  notes?: string;
  assignedTo?: {
    id: string;
    name: string;
    department: string;
    assignedAt: string;
  }[];
  assignedToId?: string | null;
  maintenanceHistory?: {
    id: string;
    type: string;
    date: string;
    description: string;
    cost: number;
    performedBy: string;
  }[];
  nextMaintenance?: string;
  maintenanceCost?: number;
  createdAt: string;
  updatedAt: string;
  isLocalOnly?: boolean;
  assetTag?: string;
}

// Add type guard for categoryMap keys
function isValidAssetType(key: string): key is keyof typeof categoryMap {
  return key in categoryMap;
}

// Define ExportModal props interface
interface ExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (format: string, scope: string) => void;
}

// Define ExportModal component
const ExportModal: React.FC<ExportModalProps> = ({ isOpen, onClose, onExport }) => {
  const [selectedFormat, setSelectedFormat] = useState('excel');
  const [selectedScope, setSelectedScope] = useState('current');

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-96">
        <h3 className="text-lg font-semibold mb-4">Export Assets</h3>
        
        {/* Scope Selection */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Export Scope</label>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                value="current"
                checked={selectedScope === 'current'}
                onChange={(e) => setSelectedScope(e.target.value)}
                className="mr-2"
              />
              <span className="text-sm text-gray-600">Current View (Filtered)</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                value="all"
                checked={selectedScope === 'all'}
                onChange={(e) => setSelectedScope(e.target.value)}
                className="mr-2"
              />
              <span className="text-sm text-gray-600">All Assets</span>
            </label>
          </div>
        </div>

        {/* Format Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">Export Format</label>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                value="excel"
                checked={selectedFormat === 'excel'}
                onChange={(e) => setSelectedFormat(e.target.value)}
                className="mr-2"
              />
              <span className="text-sm text-gray-600">Excel (.xlsx)</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                value="csv"
                checked={selectedFormat === 'csv'}
                onChange={(e) => setSelectedFormat(e.target.value)}
                className="mr-2"
              />
              <span className="text-sm text-gray-600">CSV (.csv)</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                value="pdf"
                checked={selectedFormat === 'pdf'}
                onChange={(e) => setSelectedFormat(e.target.value)}
                className="mr-2"
              />
              <span className="text-sm text-gray-600">PDF (.pdf)</span>
            </label>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-2">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
          >
            Cancel
          </button>
          <button
            onClick={() => onExport(selectedFormat, selectedScope)}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700"
          >
            Export
          </button>
        </div>
      </div>
    </div>
  );
};

function AssetManagement() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [assets, setAssets] = useState<AssetFrontend[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const redirectedRef = React.useRef(false);
  const [filters, setFilters] = useState<FilterState>(initialFilterState);
  
  // Define fetchAssets function
  const fetchAssets = async () => {
    setIsLoading(true);
    try {
      const response = await api.get('/assets');
      if (response.data) {
        setAssets(response.data);
      }
    } catch (error) {
      console.error('Error fetching assets:', error);
      setError('Failed to fetch assets');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Check if user has permission to access this page
  useEffect(() => {
    // Check if an access error has already been shown
    if (!window.hasOwnProperty('accessErrorShown')) {
      (window as any).accessErrorShown = false;
    }
    
    if (user && !['IT_ADMIN', 'IT_STAFF'].includes(user.role) && !redirectedRef.current && !(window as any).accessErrorShown) {
      redirectedRef.current = true;
      (window as any).accessErrorShown = true;
      toast.error("You don't have permission to access asset management");
      navigate('/dashboard');
    }
  }, [user, navigate]);
  
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<AssetFrontend | undefined>(undefined);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilters, setActiveFilters] = useState<string[]>([
    'assetType',
    'status',
    'department',
    'category',
    'manufacturer',  // Add manufacturer to default active filters
    'project',
    'location'
  ]); // Default active filters
  const [isFilterMenuOpen, setIsFilterMenuOpen] = useState(false);
  const [selectedAssets, setSelectedAssets] = useState<number[]>([]);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    maintenance: 0,
    retired: 0,
    totalCost: 0,
    maintenanceCost: 0,
    depreciationValue: 0,
    currentValue: 0,
    totalAssigned: 0,
    requireAttention: 0
  });
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [assetToDelete, setAssetToDelete] = useState<AssetFrontend | undefined>(undefined);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [apiStatus, setApiStatus] = useState<{ status: string; message: string }>({ status: 'unknown', message: 'Checking...' });
  const [isColumnSelectorOpen, setIsColumnSelectorOpen] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState<string[]>([
    'serialNo',
    'assignedTo',
    'department',
    'assetType',
    'category',
    'manufacturer',
    'model',
    'status',
    'location',
    'cost',
    'actions'
  ]);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);

  // Add refs for the dropdown containers
  const columnSelectorRef = useRef<HTMLDivElement>(null);
  const filterMenuRef = useRef<HTMLDivElement>(null);
  const columnButtonRef = useRef<HTMLButtonElement>(null);
  const filterButtonRef = useRef<HTMLButtonElement>(null);

  // Add useEffect for handling clicks outside the dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Handle Column Selector
      if (isColumnSelectorOpen &&
          columnSelectorRef.current &&
          columnButtonRef.current &&
          !columnSelectorRef.current.contains(event.target as Node) &&
          !columnButtonRef.current.contains(event.target as Node)) {
        setIsColumnSelectorOpen(false);
      }

      // Handle Filter Menu
      if (isFilterMenuOpen &&
          filterMenuRef.current &&
          filterButtonRef.current &&
          !filterMenuRef.current.contains(event.target as Node) &&
          !filterButtonRef.current.contains(event.target as Node)) {
        setIsFilterMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isColumnSelectorOpen, isFilterMenuOpen]);

  interface FilterOption {
    label: string;
    icon: React.FC<{ className?: string }>;
  }

  // Update the available filters to match the first image
  const availableFilters: Record<string, FilterOption> = {
    assetType: { label: 'Asset Type', icon: Monitor },
    status: { label: 'Status', icon: Clock },
    department: { label: 'Department', icon: Building2 },
    category: { label: 'Category', icon: Tag },
    manufacturer: { label: 'Manufacturer', icon: Box },  // Add manufacturer filter
    project: { label: 'Project', icon: FileText },
    location: { label: 'Location', icon: MapPin }
  };

  // Update the toggleFilter function to only manage the filter state
  const toggleFilter = (filterKey: string) => {
    setActiveFilters((prev: string[]) => {
      const newFilters = prev.includes(filterKey)
        ? prev.filter(f => f !== filterKey)
        : [...prev, filterKey];
      return newFilters;
    });
  };

  // Add useEffect to handle filter visibility
  // useEffect(() => {
  //   const filterDropdowns = document.querySelectorAll('.filter-dropdown');
  //   filterDropdowns.forEach((dropdown) => {
  //     const filterKey = dropdown.getAttribute('data-filter');
  //     if (filterKey) {
  //       const isActive = activeFilters.includes(filterKey);
  //       (dropdown as HTMLElement).style.display = isActive ? 'block' : 'none';
  //     }
  //   });
  // }, [activeFilters]);

  // Update the filter handling in filteredAssets
  const filteredAssets = useMemo(() => {
    if (!Array.isArray(assets)) {
      console.warn('Assets is not an array in filteredAssets:', assets);
      return [];
    }

    return assets.filter(asset => {
      // First check search term
      const matchesSearch = 
        searchTerm === '' ||
        asset.model?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        asset.serialNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        asset.manufacturer?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        asset.assetType?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        asset.category?.toLowerCase().includes(searchTerm.toLowerCase());

      // Then check active filters independently
      const matchesFilters = activeFilters.every(filterKey => {
        const filterValue = filters[filterKey as keyof FilterState];
        if (!filterValue) return true;
        
        const assetValue = asset[filterKey as keyof AssetFrontend];
        if (!assetValue) return false;
        
        return assetValue.toString().toLowerCase() === filterValue.toLowerCase();
      });

      return matchesSearch && matchesFilters;
    });
  }, [assets, searchTerm, filters, activeFilters]);

  useEffect(() => {
    fetchAssets();
  }, []);

  useEffect(() => {
    calculateStats();
  }, [filteredAssets]);

  const calculateStats = () => {
    // Ensure filteredAssets is an array before using reduce
    if (!Array.isArray(filteredAssets)) {
      console.warn('FilteredAssets is not an array in calculateStats:', filteredAssets);
      setStats({
        total: 0,
        active: 0,
        maintenance: 0,
        retired: 0,
        totalCost: 0,
        maintenanceCost: 0,
        depreciationValue: 0,
        currentValue: 0,
        totalAssigned: 0,
        requireAttention: 0
      });
      return;
    }

    console.log('Calculating depreciation for assets:', filteredAssets);

    const newStats = filteredAssets.reduce((acc, asset) => {
      // Increment total count
      acc.total += 1;
      
      // Count by status
      if (asset.status?.toLowerCase() === 'active') acc.active += 1;
      if (asset.status?.toLowerCase() === 'maintenance' || asset.status?.toLowerCase() === 'under repair') acc.maintenance += 1;
      if (asset.status?.toLowerCase() === 'retired') acc.retired += 1;
      
      // Calculate costs and depreciation
      const cost = Number(parseFloat(asset.cost?.toString() || '0').toFixed(2));
      if (!isNaN(cost)) {
        acc.totalCost = Number((acc.totalCost + cost).toFixed(2));
        
        // Calculate depreciation if purchase date exists
        if (asset.purchaseDate) {
          console.log(`\nCalculating depreciation for asset:`, {
            id: asset.id,
            manufacturer: asset.manufacturer,
            model: asset.model,
            cost: cost,
            purchaseDate: asset.purchaseDate
          });

          const purchaseDate = new Date(asset.purchaseDate);
          const today = new Date();
          
          // Calculate age in years more precisely
          const ageInYears = (today.getTime() - purchaseDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000);
          console.log('Age in years:', ageInYears);
          
          // Using straight-line depreciation with 5-year useful life (20% per year)
          const yearlyDepreciation = cost * 0.20; // 20% per year
          console.log('Yearly depreciation rate:', yearlyDepreciation);
          
          // Calculate total depreciation based on age, capped at 90% of cost
          let totalDepreciation = yearlyDepreciation * ageInYears;
          console.log('Initial total depreciation:', totalDepreciation);
          
          // Cap depreciation at 90% of cost
          totalDepreciation = Math.min(totalDepreciation, cost * 0.9);
          console.log('Capped total depreciation:', totalDepreciation);
          
          // Round to 2 decimal places
          totalDepreciation = Number(totalDepreciation.toFixed(2));
          console.log('Final rounded depreciation:', totalDepreciation);
          
          acc.depreciationValue = Number((acc.depreciationValue + totalDepreciation).toFixed(2));
          acc.currentValue = Number((acc.currentValue + (cost - totalDepreciation)).toFixed(2));

          console.log('Running totals:', {
            depreciationValue: acc.depreciationValue,
            currentValue: acc.currentValue
          });
        } else {
          console.log(`\nNo purchase date for asset:`, {
            id: asset.id,
            manufacturer: asset.manufacturer,
            model: asset.model,
            cost: cost
          });
          // If no purchase date, add to current value without depreciation
          acc.currentValue = Number((acc.currentValue + cost).toFixed(2));
        }
      }
      
      // Count assigned assets
      if (asset.assignedToId) {
        acc.totalAssigned += 1;
      }

      // Count assets requiring maintenance
      const nextMaintenanceDate = asset.nextMaintenance ? new Date(asset.nextMaintenance) : null;
      if (nextMaintenanceDate && nextMaintenanceDate <= new Date()) {
        acc.requireAttention += 1;
      }
      
      return acc;
    }, {
      total: 0,
      active: 0,
      maintenance: 0,
      retired: 0,
      totalCost: 0,
      maintenanceCost: 0,
      depreciationValue: 0,
      currentValue: 0,
      totalAssigned: 0,
      requireAttention: 0
    });

    console.log('\nFinal statistics:', newStats);

    // Ensure final values are properly rounded
    newStats.totalCost = Number(newStats.totalCost.toFixed(2));
    newStats.depreciationValue = Number(newStats.depreciationValue.toFixed(2));
    newStats.currentValue = Number(newStats.currentValue.toFixed(2));

    setStats(newStats);
  };

  // Pagination is now handled by TanStack Table
  const totalPages = Math.ceil(filteredAssets.length / itemsPerPage);

  // Change page
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  // Add a function to get the authentication token
  const getAuthToken = () => {
    // Get the token from localStorage or cookies
    return localStorage.getItem('auth_token') || '';
  };

  // Format dates properly for the backend
  const formatDateForBackend = (dateString?: string): string | undefined => {
    if (!dateString) return undefined;
    
    try {
      // Parse the date string and format it as YYYY-MM-DD
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('Error formatting date:', error);
      return undefined;
    }
  };

  // Helper function to check if a string is a date
  const isDateString = (value: string): boolean => {
    if (!value) return false;
    const date = new Date(value);
    return !isNaN(date.getTime());
  };

  // Helper function to handle API errors (outside handleAssetSubmit)
  const handleApiError = (error: any) => {
    if (error.response) {
      console.error(`Server responded with ${error.response.status}:`, error.response.data);
      
      // Special handling for known error cases
      if (error.response.status === 400 && error.response.data?.message?.includes('date')) {
        toast.error('There was an error with date formatting. Please check date fields are in YYYY-MM-DD format.');
      } else {
        toast.error(`Server error (${error.response.status}): ${error.response.data?.message || 'Unknown error'}`);
      }
    } else if (error.request) {
      console.error('No response received from server');
      toast.error('No response received from server. Please check if the server is running.');
    } else {
      console.error('Error message:', error.message);
      toast.error(`Error: ${error.message || 'Unknown error'}`);
    }
  };

  const handleAssetSubmit = async (asset: AssetFrontend, formData?: FormData): Promise<boolean> => {
    setIsLoading(true);
    
    try {
      console.log('Submitting asset data:', asset);
      console.log('assignedTo in asset data:', asset.assignedTo);
      console.log('assignedToId in asset data:', asset.assignedToId);
      
      // Format the asset data for the backend
      const formattedAsset = {
        ...asset,
        // Format dates for backend
        purchaseDate: formatDateForBackend(asset.purchaseDate),
        warrantyExpiry: formatDateForBackend(asset.warrantyExpiry),
      } as any; // Use type assertion to avoid TypeScript errors
      
      // Extract userId from assignedTo array for backend API
      if (asset.assignedTo && asset.assignedTo.length > 0) {
        console.log('Found assignedTo data with length:', asset.assignedTo.length);
        console.log('First user in assignedTo:', asset.assignedTo[0]);
        console.log('First user ID before conversion:', asset.assignedTo[0].id);
        console.log('Type of first user ID:', typeof asset.assignedTo[0].id);
        
        // Make sure we're sending the ID as a string
        formattedAsset.assignedToId = String(asset.assignedTo[0].id);
        console.log('Setting assignedToId for backend:', formattedAsset.assignedToId);
        console.log('Type of assignedToId:', typeof formattedAsset.assignedToId);
      } else {
        console.log('No assignedTo data found in asset');
        // Ensure we clear any existing assignment if no user is selected
        formattedAsset.assignedToId = null;
        console.log('Setting assignedToId to null');
      }
      
      console.log('Formatted asset for submission:', formattedAsset);
      
      if (asset.id) {
        // This is an update to an existing asset
        console.log('Updating existing asset with ID:', asset.id);
        
        try {
          // Make sure assignedToId is included in the update request
          console.log('Update request data:', formattedAsset);
          console.log('assignedToId for update:', formattedAsset.assignedToId);
          console.log('Type of assignedToId for update:', typeof formattedAsset.assignedToId);
          
          // Create a simplified update object with only the necessary fields
          const updateData = {
            assetType: formattedAsset.assetType,
            category: formattedAsset.category,
            manufacturer: formattedAsset.manufacturer,
            model: formattedAsset.model,
            serialNumber: formattedAsset.serialNumber,
            status: formattedAsset.status,
            condition: formattedAsset.condition,
            location: formattedAsset.location,
            department: formattedAsset.department,
            // Explicitly include assignedToId field
            assignedToId: formattedAsset.assignedToId,
            // Include other fields
            purchaseDate: formattedAsset.purchaseDate,
            warrantyExpiry: formattedAsset.warrantyExpiry,
            cost: formattedAsset.cost,
            vendor: formattedAsset.vendor,
            internetAccess: formattedAsset.internetAccess,
            ipAddress: formattedAsset.ipAddress,
            notes: formattedAsset.notes,
            attributes: formattedAsset.attributes || {}
          };
          
          console.log('Final update data being sent:', updateData);
          console.log('assignedToId in update data:', updateData.assignedToId);
          console.log('Type of assignedToId in update data:', typeof updateData.assignedToId);
          
          const response = await api.put(`/assets/${asset.id}`, updateData);
          console.log('Asset update response:', response);
          console.log('Response data:', response.data);
          console.log('Response status:', response.status);
          
          // Refresh the assets list
          await fetchAssets();
          return true;
        } catch (updateError: any) {
          console.error('Error updating asset:', updateError);
          const errorMessage = updateError.response?.data?.error || updateError.message || 'Unknown error';
          toast.error(`Error updating asset: ${errorMessage}`, {
            duration: 5000
          });
          return false;
        }
      } else {
        // This is a new asset creation
        console.log('Creating new asset using simple endpoint');
        
        try {
          // For creation, we need to ensure all required fields are present and properly formatted
          // Create a simplified object with only the necessary fields
          const createData = {
            assetType: formattedAsset.assetType || 'OtherEquipment', // Default value if empty
            category: formattedAsset.category || 'General', // Default value if empty
            manufacturer: formattedAsset.manufacturer || 'Unknown', // Default value if empty
            model: formattedAsset.model || 'Generic Model', // Default value if empty
            serialNumber: formattedAsset.serialNumber || '', // Keep as is or empty string
            status: formattedAsset.status || 'Active', // Default value if empty
            condition: formattedAsset.condition || 'Good', // Default value if empty
            location: formattedAsset.location || 'Main Office', // Default value if empty
            department: formattedAsset.department || 'IT', // Default value if empty
            // Include assignedToId field for user assignment
            assignedToId: formattedAsset.assignedToId,
            // Include cost field
            cost: formattedAsset.cost,
            // Include other fields that might be available
            purchaseDate: formattedAsset.purchaseDate,
            warrantyExpiry: formattedAsset.warrantyExpiry,
            vendor: formattedAsset.vendor,
            internetAccess: formattedAsset.internetAccess || false,
            ipAddress: formattedAsset.ipAddress,
            notes: formattedAsset.notes,
            attributes: formattedAsset.attributes || {}
          };
          
          console.log('Sending create data:', createData);
          
          // Send the asset data to the regular endpoint
          const response = await api.post('/assets', createData);
          console.log('Asset creation response:', response);
          
          // Refresh the assets list
          await fetchAssets();
          return true;
        } catch (createError: any) {
          console.error('Error creating asset:', createError);
          let errorMessage = 'Unknown error';
          
          if (createError.response) {
            console.error('Error response data:', createError.response.data);
            errorMessage = createError.response.data?.error || 
                          createError.response.data?.message || 
                          createError.response.data?.details || 
                          'Server error';
          } else if (createError.message) {
            errorMessage = createError.message;
          }
          
          toast.error(`Error creating asset: ${errorMessage}`, {
            duration: 5000
          });
          return false;
        }
      }
    } catch (error: any) {
      console.error('Error in handleAssetSubmit:', error);
      toast.error(`Submission error: ${error.message || 'Unknown error'}`);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Helper functions to convert values to match backend enums
  const convertAssetType = (type: string): string => {
    const assetTypeMap: Record<string, string> = {
      'Computing': 'Computing',
      'Mobile & Tablet': 'MobileTablet',
      'Networking & Communication': 'NetworkingCommunication',
      'Printing': 'Printing',
      'Display & Multimedia': 'DisplayMultimedia',
      'Security & Surveillance': 'SecuritySurveillance',
      'Peripherals & Accessories': 'PeripheralsAccessories',
      'Other Equipment': 'OtherEquipment'
    };
    
    return assetTypeMap[type] || type;
  };
  
  const convertAssetStatus = (status: string): string => {
    const statusMap: Record<string, string> = {
      'Active': 'Active',
      'Reserved': 'Reserved',
      'Under Repair': 'UnderRepair',
      'Retired': 'Retired',
      'Lost/Stolen': 'LostStolen',
      'Decommissioned': 'Decommissioned',
      'In Transit': 'InTransit',
      'Other': 'Other'
    };
    
    return statusMap[status] || status;
  };
  
  const convertAssetCondition = (condition: string): string => {
    const conditionMap: Record<string, string> = {
      'New': 'New',
      'Good': 'Good',
      'Fair': 'Fair',
      'Needs Repair': 'NeedsRepair',
      'Retired': 'Retired',
      'Other': 'Other'
    };
    
    return conditionMap[condition] || condition;
  };

  const handleClose = () => {
    setIsFormOpen(false);
    setIsEditModalOpen(false);
    setSelectedAsset(undefined);
  };

  const handleSaveSuccess = () => {
    setIsFormOpen(false);
    // Optionally refresh the assets list
    // fetchAssets();
  };

  const handleOpenForm = () => {
    setIsFormOpen(true);
    setIsViewModalOpen(false);
    setIsEditModalOpen(false);
    setSelectedAsset(undefined);
    
    // Log to confirm we're not passing any initial data
    console.log('Opening new asset form with no initial data');
  };

  const handleDeleteAsset = async (assetId: number | undefined) => {
    if (assetId === undefined) return;
    
    setIsDeleteModalOpen(false);
    setIsLoading(true);
    
    try {
      await api.delete(`/assets/${assetId}`);
      await fetchAssets();
      toast.success('Asset deleted successfully');
    } catch (error: any) {
      console.error('Error deleting asset:', error);
      const errorMessage = error.response?.data?.error || error.message || 'Unknown error';
      toast.error(`Error deleting asset: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Add a function to show the delete confirmation modal
  const showDeleteConfirmation = (asset: AssetFrontend) => {
    setAssetToDelete(asset);
    setIsDeleteModalOpen(true);
  };

  // Add a helper function to standardize status display
  const formatStatus = (status: string): string => {
    if (!status) return '';
    // Capitalize first letter, lowercase the rest
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  };

  // Toggle dropdown function
  const toggleDropdown = (id: string | number | undefined) => {
    if (id === undefined) return;
    const idString = id.toString();
    setActiveDropdown(activeDropdown === idString ? null : idString);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setActiveDropdown(null);
    };
    
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  interface Column {
    id: string;
    header: string;
    accessorKey?: string;
    size?: number;
    cell?: (info: any) => React.ReactNode;
  }

  const columns: Column[] = [
    {
      id: 'serialNo',
      header: 'S.NO',
      accessorKey: 'serialNo',
      size: 70,
      cell: (info: any) => {
        const rowIndex = info.row.index;
        const serialNo = ((currentPage - 1) * itemsPerPage) + rowIndex + 1;
        return <span className="text-center block">{serialNo}</span>;
      },
    },
    {
      id: 'assignedTo',
      header: 'ASSIGNED TO',
      accessorKey: 'assignedTo',
      cell: (info: any) => {
        const asset = info.row.original;
        let assignedUser;
        if (Array.isArray(asset.assignedTo)) {
          assignedUser = asset.assignedTo[0];
        } else if (asset.assignedTo && typeof asset.assignedTo === 'object') {
          assignedUser = asset.assignedTo;
        }
        return (
          <span className="font-medium text-center block">
            {assignedUser && assignedUser.name ? assignedUser.name : '-'}
          </span>
        );
      },
    },
    {
      id: 'department',
      header: 'DEPARTMENT',
      accessorKey: 'department',
    },
    {
      id: 'assetType',
      header: 'ASSET TYPE',
      accessorKey: 'assetType',
    },
    {
      id: 'category',
      header: 'CATEGORY',
      accessorKey: 'category',
    },
    {
      id: 'manufacturer',
      header: 'MANUFACTURER',
      accessorKey: 'manufacturer',
    },
    {
      id: 'model',
      header: 'MODEL',
      accessorKey: 'model',
    },
    {
      id: 'serialNumber',
      header: 'SERIAL NUMBER',
      accessorKey: 'serialNumber',
    },
    {
      id: 'attributes',
      header: 'SPECIFICATIONS',
      accessorKey: 'attributes',
      cell: (info: any) => {
        const attributes = info.getValue();
        if (!attributes || typeof attributes !== 'object') return '-';

        // Define technical specification keys to look for (case-insensitive)
        const techSpecs = [
          { key: ['processor', 'cpu'], label: 'Processor' },
          { key: ['ram', 'memory', 'ramcapacity'], label: 'RAM' },
          { key: ['storage', 'hdd', 'ssd', 'storagecapacity'], label: 'Storage' },
          { key: ['graphics', 'gpu', 'graphicscard'], label: 'Graphics' },
          { key: ['display', 'screensize'], label: 'Display' },
          { key: ['resolution'], label: 'Resolution' }
        ];

        // Convert all keys to lowercase for case-insensitive matching
        const normalizedAttributes = Object.entries(attributes).reduce((acc, [key, value]) => {
          acc[key.toLowerCase()] = value;
          return acc;
        }, {} as Record<string, any>);

        // Collect all matching specifications
        const specs = techSpecs
          .map(spec => {
            // Find the first matching key for this specification
            const matchingKey = spec.key.find(k => 
              normalizedAttributes[k.toLowerCase()] && 
              normalizedAttributes[k.toLowerCase()].toString().trim() !== ''
            );
            
            if (matchingKey) {
              const value = normalizedAttributes[matchingKey.toLowerCase()];
              return `${spec.label}: ${value}`;
            }
            return null;
          })
          .filter(spec => spec !== null)
          .join(' | ');

        return specs || '-';
      }
    },
    {
      id: 'status',
      header: 'STATUS',
      accessorKey: 'status',
      cell: (info: any) => {
        const status = formatStatus(info.getValue());
        return (
          <div className="flex justify-center">
            <span className={`px-3 py-1.5 rounded-full text-xs font-medium flex items-center gap-1.5 w-fit ${
              info.row.original.isLocalOnly ? 'bg-yellow-100 text-yellow-800' :
                status.toLowerCase() === 'active' ? 'bg-green-100 text-green-800' :
                status.toLowerCase() === 'maintenance' || status.toLowerCase() === 'under repair' ? 'bg-yellow-100 text-yellow-800' :
                status.toLowerCase() === 'retired' ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {status}
              {info.row.original.isLocalOnly && <span className="text-xs">(Local)</span>}
            </span>
          </div>
        );
      },
    },
    {
      id: 'location',
      header: 'LOCATION',
      accessorKey: 'location',
    },
    {
      id: 'cost',
      header: 'COST',
      accessorKey: 'cost',
      cell: (info: any) => (
        <span className="font-medium text-center block">
          {formatPKR(info.getValue() || 0)}
        </span>
      ),
    },
    {
      id: 'actions',
      header: 'ACTIONS',
      cell: (info: any) => {
        const asset = info.row.original;
        return (
          <div className="flex justify-center">
            <button
              onClick={(e) => {
                e.stopPropagation();
                if (asset.id !== undefined) {
                  toggleDropdown(asset.id.toString());
                }
              }}
              className="text-gray-600 hover:text-blue-600 p-1 rounded-full hover:bg-gray-100 transition-colors"
              title="Asset Actions"
            >
              <Settings className="h-5 w-5" />
            </button>
            
            {activeDropdown === (asset.id?.toString() || '') && (
              <div className="absolute right-0 mt-8 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                <div className="py-1">
                  <button
                    onClick={() => {
                      handleViewAsset(asset);
                      setActiveDropdown(null);
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                  >
                    <Eye className="h-4 w-4 mr-2 text-blue-500" />
                    View Details
                  </button>
                  <button
                    onClick={() => {
                      handleEditAsset(asset);
                      setActiveDropdown(null);
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                  >
                    <Settings className="h-4 w-4 mr-2 text-indigo-500" />
                    Edit Asset
                  </button>
                  <button
                    onClick={() => {
                      showDeleteConfirmation(asset);
                      setActiveDropdown(null);
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                  >
                    <Trash2 className="h-4 w-4 mr-2 text-red-500" />
                    Delete Asset
                  </button>
                </div>
              </div>
            )}
          </div>
        );
      },
    },
    {
      id: 'purchaseDate',
      header: 'PURCHASE',
      accessorKey: 'purchaseDate',
      cell: (info: any) => {
        const date = info.getValue();
        return date ? new Date(date).toLocaleDateString() : '-';
      },
    },
    {
      id: 'warrantyExpiry',
      header: 'WARRANTY',
      accessorKey: 'warrantyExpiry',
      cell: (info: any) => {
        const date = info.getValue();
        if (!date) return '-';
        const expiryDate = new Date(date);
        const today = new Date();
        const className = expiryDate < today ? 'text-red-600' : 
                         expiryDate < new Date(today.setDate(today.getDate() + 90)) ? 'text-yellow-600' : 
                         'text-green-600';
        return <span className={className}>{expiryDate.toLocaleDateString()}</span>;
      },
    },
    {
      id: 'condition',
      header: 'CONDITION',
      accessorKey: 'condition',
      cell: (info: any) => {
        const condition = info.getValue();
        const getConditionColor = (condition: string) => {
          switch (condition?.toLowerCase()) {
            case 'new': return 'bg-green-100 text-green-800';
            case 'good': return 'bg-blue-100 text-blue-800';
            case 'fair': return 'bg-yellow-100 text-yellow-800';
            case 'needs repair': return 'bg-orange-100 text-orange-800';
            case 'retired': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
          }
        };
        return condition ? (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getConditionColor(condition)}`}>
            {condition}
          </span>
        ) : '-';
      },
    },
    {
      id: 'nextMaintenance',
      header: 'NEXT MAINTENANCE',
      accessorKey: 'nextMaintenance',
      cell: (info: any) => {
        const date = info.getValue();
        if (!date) return '-';
        const maintenanceDate = new Date(date);
        const today = new Date();
        const className = maintenanceDate < today ? 'text-red-600' : 
                         maintenanceDate < new Date(today.setDate(today.getDate() + 30)) ? 'text-yellow-600' : 
                         'text-green-600';
        return <span className={className}>{maintenanceDate.toLocaleDateString()}</span>;
      },
    },
    {
      id: 'vendor',
      header: 'VENDOR',
      accessorKey: 'vendor',
    },
    {
      id: 'project',
      header: 'PROJECT',
      accessorKey: 'project',
    },
    {
      id: 'ipAddress',
      header: 'IP ADDRESS',
      accessorKey: 'ipAddress',
      cell: (info: any) => {
        const ipAddress = info.getValue();
        return ipAddress || '-';
      },
    },
    {
      id: 'createdAt',
      header: 'CREATED AT',
      accessorKey: 'createdAt',
      cell: (info: any) => {
        const date = info.getValue();
        return date ? new Date(date).toLocaleDateString() : '-';
      },
    },
    {
      id: 'updatedAt',
      header: 'LAST UPDATED',
      accessorKey: 'updatedAt',
      cell: (info: any) => {
        const date = info.getValue();
        return date ? new Date(date).toLocaleDateString() : '-';
      },
    },
  ];

  const handleViewAsset = (asset: AssetFrontend) => {
    setSelectedAsset(asset);
    setIsViewModalOpen(true);
    setIsFormOpen(false);
    setIsEditModalOpen(false);
  };

  const handleEditAsset = (asset: AssetFrontend) => {
    setSelectedAsset(asset);
    setIsEditModalOpen(true);
    setIsFormOpen(true);
    setIsViewModalOpen(false);
  };

  const handleColumnToggle = (columnId: string) => {
    setVisibleColumns(prev => {
      if (prev.includes(columnId)) {
        // Don't allow hiding all columns
        if (prev.length === 1) return prev;
        return prev.filter(id => id !== columnId);
      }
      return [...prev, columnId];
    });
  };

  const filteredColumns = columns.filter(column => visibleColumns.includes(column.id));

  // Update the initial visible columns state to include only the essential columns
  useEffect(() => {
    setVisibleColumns([
      'serialNo',
      'assignedTo',
      'department',
      'assetType',
      'category',
      'manufacturer',
      'model',
      'status',
      'location',
      'cost',
      'actions'
    ]);
  }, []);

  const handleExportReport = (format: string, scope: string) => {
    const assetsToExport = scope === 'current' ? filteredAssets : assets;
    
    // Create headers for the export - using Title Case for better readability
    const headers = [
      'S.No',
      'Assigned To',
      'Department',
      'Asset Type',
      'Category',
      'Manufacturer',
      'Model',
      'Serial No',
      'Specifications',
      'Status',
      'Condition',
      'Project',
      'Location',
      'Purchase',
      'Warranty',
      'Cost',
      'Vendor'
    ];

    // Create rows from assets data
    const rows = assetsToExport.map((asset, index) => {
      // Get the assigned user's name
      let assignedName = '';
      if (asset.assignedTo && Array.isArray(asset.assignedTo) && asset.assignedTo.length > 0) {
        assignedName = asset.assignedTo[0].name;
      } else if (asset.assignedTo && typeof asset.assignedTo === 'object' && 'name' in asset.assignedTo) {
        assignedName = (asset.assignedTo as { name: string }).name;
      }

      // Format specifications for export
      let specsString = '';
      if (asset.attributes && typeof asset.attributes === 'object') {
        const techSpecs = [
          { key: ['processor', 'cpu'], label: 'Processor' },
          { key: ['ram', 'memory', 'ramcapacity'], label: 'Memory' },
          { key: ['storage', 'hdd', 'ssd', 'storagecapacity'], label: 'Storage' },
          { key: ['graphics', 'gpu', 'graphicscard'], label: 'Graphics' },
          { key: ['display', 'screensize'], label: 'Display' },
          { key: ['resolution'], label: 'Resolution' }
        ];

        const normalizedAttributes = Object.entries(asset.attributes).reduce((acc, [key, value]) => {
          acc[key.toLowerCase()] = value;
          return acc;
        }, {} as Record<string, any>);

        specsString = techSpecs
          .map(spec => {
            const matchingKey = spec.key.find(k => 
              normalizedAttributes[k.toLowerCase()] && 
              normalizedAttributes[k.toLowerCase()].toString().trim() !== ''
            );
            
            if (matchingKey) {
              const value = normalizedAttributes[matchingKey.toLowerCase()];
              return `${spec.label}: ${value}`;
            }
            return null;
          })
          .filter(spec => spec !== null)
          .join(' | ');
      }

      // Use full location names
      const getFullLocation = (location: string): string => {
        return location
          .replace('GC-HO', 'Grand City Head Office')
          .replace('GC-SO', 'Grand City Site Office')
          .replace('ARD-HO', 'ARD Head Office')
          .replace('ARD-SO', 'ARD Site Office');
      };

      // Helper function to capitalize first letter only
      const capitalizeFirstLetter = (str: string): string => {
        if (!str) return '';
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
      };

      return [
        (index + 1).toString(),
        assignedName || '-',
        asset.department || '-',
        asset.assetType || '-',
        asset.category || '-',
        asset.manufacturer || '-',
        asset.model || '-',
        asset.serialNumber || '-',
        specsString || '-',
        capitalizeFirstLetter(asset.status || ''), // Capitalize first letter of status
        capitalizeFirstLetter(asset.condition || ''), // Capitalize first letter of condition
        asset.project || '-',
        getFullLocation(asset.location || '-'),
        asset.purchaseDate ? new Date(asset.purchaseDate).toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: '2-digit' }) : '-',
        asset.warrantyExpiry ? new Date(asset.warrantyExpiry).toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: '2-digit' }) : '-',
        asset.cost !== undefined ? formatPKR(asset.cost) : '-',
        asset.vendor || '-'
      ];
    });

    if (format === 'pdf') {
      import('jspdf').then(module => {
        const jsPDF = module.default;
        const doc = new jsPDF({
          orientation: 'landscape',
          unit: 'pt',
          format: 'a3'
        });

        import('jspdf-autotable').then(() => {
          // Add company logo and header
          const pageWidth = doc.internal.pageSize.width;
          
          // Add title section with gradient-like background
          doc.setFillColor(240, 244, 248);
          doc.rect(0, 0, pageWidth, 80, 'F');
          
          // Add title
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(24);
          doc.setTextColor(30, 41, 59);
          const title = 'IT Asset Inventory Report';
          const titleWidth = doc.getStringUnitWidth(title) * 24 / doc.internal.scaleFactor;
          doc.text(title, (pageWidth - titleWidth) / 2, 45);

          // Add report metadata
          doc.setFont('helvetica', 'normal');
          doc.setFontSize(10);
          doc.setTextColor(71, 85, 105);
          const dateStr = new Date().toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });
          doc.text(`Generated on: ${dateStr}`, 40, 70);
          doc.text(`Total Assets: ${rows.length}`, pageWidth - 200, 70);

          // Add summary section
          const summaryY = 100;
          doc.setFillColor(248, 250, 252);
          doc.rect(40, summaryY, pageWidth - 80, 60, 'F');
          
          // Add summary content
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(12);
          doc.setTextColor(30, 41, 59);
          doc.text('Summary', 60, summaryY + 20);

          // Calculate summary statistics
          const activeAssets = rows.filter(row => row[9] === 'ACTIVE').length;
          const totalCost = rows.reduce((sum, row) => {
            const cost = row[15].replace('Rs ', '').replace(/,/g, '');
            return sum + (parseFloat(cost) || 0);
          }, 0);

          // Add summary statistics
          doc.setFont('helvetica', 'normal');
          doc.setFontSize(10);
          doc.setTextColor(71, 85, 105);
          
          // First column
          doc.text(`Active Assets: ${activeAssets}`, 60, summaryY + 40);
          doc.text(`Total Assets: ${rows.length}`, 60, summaryY + 55);
          
          // Second column
          doc.text(`Total Value: ${formatPKR(totalCost)}`, pageWidth/2, summaryY + 40);
          doc.text(`Departments: ${new Set(rows.map(row => row[2])).size}`, pageWidth/2, summaryY + 55);

          // Configure table
          const tableConfig = {
            startY: summaryY + 80,
            head: [headers],
            body: rows,
            columnStyles: {
              0: { cellWidth: 30, halign: 'center' },  // S.No
              1: { cellWidth: 80, halign: 'left' },    // Assigned To
              2: { cellWidth: 70, halign: 'left' },    // Department
              3: { cellWidth: 60, halign: 'left' },    // Asset Type
              4: { cellWidth: 60, halign: 'left' },    // Category
              5: { cellWidth: 70, halign: 'left' },    // Manufacturer
              6: { cellWidth: 60, halign: 'left' },    // Model
              7: { cellWidth: 60, halign: 'left' },    // Serial No
              8: { cellWidth: 120, halign: 'left' },   // Specifications
              9: { cellWidth: 45, halign: 'left' },    // Status
              10: { cellWidth: 50, halign: 'left' },   // Condition - increased width
              11: { cellWidth: 60, halign: 'left' },   // Project
              12: { cellWidth: 100, halign: 'left' },  // Location
              13: { cellWidth: 45, halign: 'left' },   // Purchase
              14: { cellWidth: 45, halign: 'left' },   // Warranty
              15: { cellWidth: 60, halign: 'left' },   // Cost - changed to left align
              16: { cellWidth: 60, halign: 'left' }    // Vendor
            },
            headStyles: {
              fillColor: [51, 65, 85],
              textColor: [255, 255, 255],
              fontSize: 9,
              fontStyle: 'bold',
              minCellHeight: 20,
              cellPadding: { top: 5, right: 2, bottom: 5, left: 2 },
              overflow: 'ellipsize',
              lineWidth: 1,
              halign: 'left',  // Default left alignment for all headers
              valign: 'middle'
            },
            styles: {
              font: 'helvetica',
              fontSize: 8,
              cellPadding: { top: 4, right: 2, bottom: 4, left: 2 },
              overflow: 'linebreak',
              minCellHeight: 16,
              valign: 'middle',
              lineWidth: 0.5,
              lineColor: [226, 232, 240]
            },
            margin: { top: 30, right: 15, bottom: 30, left: 15 },
            didParseCell: function(data: any) {
              if (data.section === 'head') {
                data.cell.styles.cellWidth = 'wrap';
                data.cell.styles.overflow = 'ellipsize';
                data.cell.styles.minCellHeight = 20;
                
                // Only center S.No header
                if (data.column.index === 0) {
                  data.cell.styles.halign = 'center';
                }
              } else if (data.section === 'body') {
                // Center S.No column and right-align Cost values
                if (data.column.index === 0) {
                  data.cell.styles.halign = 'center';
                } else if (data.column.index === 15) { // Cost column
                  data.cell.styles.halign = 'right';
                }
              }
            }
          };

          // Add the table to the PDF
          (doc as any).autoTable(tableConfig);

          doc.save(`it-asset-inventory-${new Date().toISOString().split('T')[0]}.pdf`);
        });
      });
    } else if (format === 'excel') {
      // Enhanced Excel export with Microsoft Excel-like behavior
      import('xlsx').then(XLSX => {
        const ws = XLSX.utils.aoa_to_sheet([headers, ...rows]);
        
        // Calculate optimal column widths based on content
        const getExcelWidth = (text: string) => {
          // Excel width units are based on the width of '0' character
          // Adding extra width for padding and better readability
          return Math.min(Math.max(text.length * 1.2, 8), 60);
        };

        // Get maximum width needed for each column
        const colWidths = headers.map((_, colIndex) => {
          let maxWidth = getExcelWidth(headers[colIndex]);
          rows.forEach(row => {
            const cellContent = row[colIndex]?.toString() || '';
            maxWidth = Math.max(maxWidth, getExcelWidth(cellContent));
          });
          return { wch: maxWidth };
        });

        ws['!cols'] = colWidths;

        // Set consistent row height
        const rowHeights = Array(rows.length + 1).fill({ hpt: 20 });
        ws['!rows'] = rowHeights;

        // Apply Excel-like styling
        const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
        for (let R = range.s.r; R <= range.e.r; R++) {
          for (let C = range.s.c; C <= range.e.c; C++) {
            const cell_address = { c: C, r: R };
            const cell_ref = XLSX.utils.encode_cell(cell_address);
            
            if (!ws[cell_ref]) {
              ws[cell_ref] = { t: 's', v: '' };
            }

            // Initialize style object if it doesn't exist
            if (!ws[cell_ref].s) {
              ws[cell_ref].s = {};
            }

            // Excel-like cell styling
              ws[cell_ref].s = {
              ...ws[cell_ref].s,
              alignment: {
                horizontal: 'left',
                vertical: 'center',
                wrapText: false,
                shrinkToFit: true  // Excel's shrink to fit feature
              },
                font: { 
                name: 'Calibri',
                sz: 11,
                color: { rgb: R === 0 ? "FFFFFF" : "000000" }
                },
                fill: { 
                fgColor: { rgb: R === 0 ? "4472C4" : "FFFFFF" }
                },
                border: {
                top: { style: 'thin', color: { rgb: "D4D4D4" } },
                bottom: { style: 'thin', color: { rgb: "D4D4D4" } },
                left: { style: 'thin', color: { rgb: "D4D4D4" } },
                right: { style: 'thin', color: { rgb: "D4D4D4" } }
              }
            };
          }
        }

        // Set print settings
        ws['!printHeader'] = [{ text: 'IT Asset Inventory Report', fontSize: 14, bold: true }];
        ws['!margins'] = { 
          left: 0.7, 
          right: 0.7,
          top: 0.75,
          bottom: 0.75,
          header: 0.3,
          footer: 0.3
        };

        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'IT Asset Inventory');
        
        // Write the file without custom workbook properties
        XLSX.writeFile(wb, `it-asset-inventory-${new Date().toISOString().split('T')[0]}.xlsx`);
      });
    } else {
      // Enhanced CSV export with BOM for Excel compatibility
      const csvContent = '\ufeff' + [
        headers.join(','),
        ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `it-asset-inventory-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    setIsExportModalOpen(false);
  };

  return (
    <div className="p-6">
      {isFormOpen ? (
        <div>
          <AssetForm 
            onSubmit={handleAssetSubmit} 
            onClose={handleClose}
            onSaveSuccess={handleSaveSuccess}
            initialData={isEditModalOpen ? selectedAsset : undefined}
          />
        </div>
      ) : (
        <>
          {/* Warning for locally saved assets */}
          {assets.some(asset => asset.isLocalOnly) && (
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-5 w-5 text-yellow-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    <span className="font-medium">Warning:</span> Some assets are only saved locally and will be lost after page refresh. 
                    This happens when the server is unavailable or returns an error.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {/* Total Assets Card */}
            <div className="bg-white rounded-lg shadow p-4 sm:p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Total Assets</h3>
                  <p className="text-3xl font-bold mt-2">{stats.total}</p>
                </div>
                <div className="bg-blue-50 p-3 rounded-lg">
                  <Monitor className="h-6 w-6 text-blue-500" />
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-green-600 flex items-center">
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Active
                  </span>
                  <span className="font-medium">{stats.active}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-red-600 flex items-center">
                    <XCircle className="h-4 w-4 mr-1" />
                    Retired
                  </span>
                  <span className="font-medium">{stats.retired}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-yellow-600 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    Under Maintenance
                  </span>
                  <span className="font-medium">{stats.maintenance}</span>
                </div>
              </div>
            </div>

            {/* Assigned Assets Card */}
            <div className="bg-white rounded-lg shadow p-4 sm:p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Assigned Assets</h3>
                  <p className="text-3xl font-bold mt-2">{stats.active}</p>
                </div>
                <div className="bg-purple-50 p-3 rounded-lg">
                  <Users className="h-6 w-6 text-purple-500" />
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600 flex items-center">
                    <User className="h-4 w-4 mr-1" />
                    Assigned Users
                  </span>
                  <span className="font-medium">{stats.totalAssigned}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-green-600 flex items-center">
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Active Assets
                  </span>
                  <span className="font-medium">{stats.active}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-blue-600 flex items-center">
                    <Building2 className="h-4 w-4 mr-1" />
                    Departments
                  </span>
                  <span className="font-medium">
                    {filteredAssets
                      .map(asset => asset.department)
                      .filter((dept): dept is string => !!dept)
                      .filter((dept, index, self) => self.indexOf(dept) === index)
                      .length}
                  </span>
                </div>
              </div>
            </div>

            {/* Total Cost Card */}
            <div className="bg-white rounded-lg shadow p-4 sm:p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Total Cost</h3>
                  <p className="text-3xl font-bold mt-2">{formatPKR(stats.totalCost)}</p>
                </div>
                <div className="bg-green-50 p-3 rounded-lg">
                  <DollarSign className="h-6 w-6 text-green-500" />
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600 flex items-center">
                    <Package className="h-4 w-4 mr-1" />
                    Asset Value
                  </span>
                  <span className="font-medium">{formatPKR(stats.totalCost)}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-red-600 flex items-center">
                    <TrendingDown className="h-4 w-4 mr-1" />
                    Depreciation
                  </span>
                  <span className="font-medium">{formatPKR(stats.depreciationValue)}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-green-600 flex items-center">
                    <TrendingUp className="h-4 w-4 mr-1" />
                    Current Value
                  </span>
                  <span className="font-medium">{formatPKR(stats.currentValue)}</span>
                </div>
              </div>
            </div>

            {/* Maintenance Card */}
            <div className="bg-white rounded-lg shadow p-4 sm:p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Maintenance</h3>
                  <p className="text-3xl font-bold mt-2">{stats.requireAttention}</p>
                </div>
                <div className="bg-yellow-50 p-3 rounded-lg">
                  <Settings className="h-6 w-6 text-yellow-500" />
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-yellow-600 flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    Require Attention
                  </span>
                  <span className="font-medium">{stats.requireAttention}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-blue-600 flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    Upcoming
                  </span>
                  <span className="font-medium">{filteredAssets.filter(asset => {
                    const nextMaintenance = asset.nextMaintenance ? new Date(asset.nextMaintenance) : null;
                    const today = new Date();
                    const thirtyDaysFromNow = new Date();
                    thirtyDaysFromNow.setDate(today.getDate() + 30);
                    return nextMaintenance && nextMaintenance > today && nextMaintenance <= thirtyDaysFromNow;
                  }).length}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-orange-600 flex items-center">
                    <Shield className="h-4 w-4 mr-1" />
                    Warranty Expiring
                  </span>
                  <span className="font-medium">{filteredAssets.filter(asset => {
                    const warrantyExpiry = asset.warrantyExpiry ? new Date(asset.warrantyExpiry) : null;
                    const today = new Date();
                    const ninetyDaysFromNow = new Date();
                    ninetyDaysFromNow.setDate(today.getDate() + 90);
                    return warrantyExpiry && warrantyExpiry > today && warrantyExpiry <= ninetyDaysFromNow;
                  }).length}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Asset Management Section */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-4 sm:p-6">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0 mb-6">
                <div>
                  <h2 className="text-lg sm:text-xl font-bold text-gray-900">Asset Management</h2>
                  <p className="text-sm text-gray-500">Manage your organization's assets</p>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => setIsExportModalOpen(true)}
                    className="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    <FileText className="h-5 w-5 mr-2" />
                    Export Report
                  </button>
                  <button
                    onClick={handleOpenForm}
                    className="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    <Plus className="h-5 w-5 mr-2" />
                    Add Asset
                  </button>
                </div>
              </div>

              {/* Search and Filters */}
              <div className="flex flex-col space-y-2 mb-4">
                <div className="flex flex-col md:flex-row gap-2">
                  {/* Search Bar */}
                  <div className="relative flex-grow">
                    <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                      <Search className="h-4 w-4 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search assets by name, type, serial number..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="block w-full pl-8 pr-3 py-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    {/* Column Selector Button */}
                    <div className="relative">
                      <button
                        ref={columnButtonRef}
                        onClick={() => setIsColumnSelectorOpen(!isColumnSelectorOpen)}
                        className={`flex items-center px-3 py-1.5 border rounded-lg text-sm font-medium transition-all duration-200 ${
                          isColumnSelectorOpen 
                            ? 'bg-blue-50 border-blue-200 text-blue-600' 
                            : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <Columns className={`h-4 w-4 mr-1.5 ${isColumnSelectorOpen ? 'text-blue-600' : 'text-gray-500'}`} />
                        Columns
                      </button>
                      {isColumnSelectorOpen && (
                        <div ref={columnSelectorRef}>
                          <ColumnSelector
                            columns={columns}
                            onColumnToggle={handleColumnToggle}
                            onClose={() => setIsColumnSelectorOpen(false)}
                            visibleColumns={visibleColumns}
                          />
                        </div>
                      )}
                    </div>

                    {/* Filter Button */}
                    <div className="relative">
                      <button
                        ref={filterButtonRef}
                        onClick={() => setIsFilterMenuOpen(!isFilterMenuOpen)}
                        className={`flex items-center px-3 py-1.5 border rounded-lg text-sm font-medium transition-all duration-200 ${
                          isFilterMenuOpen 
                            ? 'bg-blue-50 border-blue-200 text-blue-600' 
                            : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <Filter className={`h-4 w-4 mr-1.5 ${isFilterMenuOpen ? 'text-blue-600' : 'text-gray-500'}`} />
                        Customize Filters
                      </button>
                      {isFilterMenuOpen && (
                        <div ref={filterMenuRef} className="absolute right-0 mt-1 w-64 bg-white rounded-lg shadow-lg z-50 border border-gray-200">
                          <div className="p-2">
                            <div className="flex justify-between items-center mb-2">
                              <h3 className="text-sm font-semibold text-gray-900">Select Filters</h3>
                              <button
                                onClick={() => setIsFilterMenuOpen(false)}
                                className="text-gray-400 hover:text-gray-500 transition-colors"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            </div>
                            <div className="space-y-1">
                              {Object.entries(availableFilters).map(([key, { label, icon: Icon }]) => (
                                <label key={key} className="flex items-center space-x-2 hover:bg-gray-50 px-2 py-1 rounded cursor-pointer">
                                  <input
                                    type="checkbox"
                                    checked={activeFilters.includes(key)}
                                    onChange={() => toggleFilter(key)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-3.5 w-3.5"
                                  />
                                  <span className="flex items-center text-sm text-gray-700">
                                    <Icon className="h-3.5 w-3.5 mr-1.5 text-gray-500" />
                                    {label}
                                  </span>
                                </label>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Active Filters */}
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-7 gap-2">
                  {/* Asset Type Filter */}
                    <select
                      value={filters.assetType}
                    onChange={(e) => setFilters(prev => ({ ...prev, assetType: e.target.value, category: '', manufacturer: '' }))}
                      className="block w-full px-2 py-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">All Types</option>
                      {assetTypes.map(type => (
                        <option key={type} value={type}>
                          {type}
                        </option>
                      ))}
                    </select>

                  {/* Category Filter */}
                    <select
                      value={filters.category}
                    onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value, manufacturer: '' }))}
                      className="block w-full px-2 py-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">All Categories</option>
                    {filters.assetType && isValidAssetType(filters.assetType) ? 
                        categoryMap[filters.assetType].map(category => (
                          <option key={category} value={category}>
                            {category}
                          </option>
                        ))
                      : 
                      Object.values(categoryMap).flat().filter((value, index, self) => 
                        self.indexOf(value) === index
                      ).map(category => (
                          <option key={category} value={category}>
                            {category}
                          </option>
                        ))
                    }
                    </select>

                  {/* Manufacturer Filter */}
                  <select
                    value={filters.manufacturer}
                    onChange={(e) => setFilters(prev => ({ ...prev, manufacturer: e.target.value }))}
                    className="block w-full px-2 py-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Manufacturers</option>
                    {Array.from(new Set(assets
                      .filter(asset => asset.manufacturer && asset.manufacturer.trim() !== '')
                      .map(asset => asset.manufacturer)
                    ))
                    .sort((a, b) => a.localeCompare(b))
                    .map(manufacturer => (
                      <option key={manufacturer} value={manufacturer}>
                        {manufacturer}
                      </option>
                    ))}
                  </select>

                  {/* Status Filter */}
                    <select
                      value={filters.status}
                      onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                      className="block w-full px-2 py-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">All Status</option>
                      <option value="Active">Active</option>
                      <option value="Under Repair">Under Repair</option>
                      <option value="Retired">Retired</option>
                      <option value="Reserved">Reserved</option>
                      <option value="Lost/Stolen">Lost/Stolen</option>
                      <option value="Decommissioned">Decommissioned</option>
                      <option value="In Transit">In Transit</option>
                    </select>

                  {/* Department Filter */}
                    <select
                      value={filters.department}
                      onChange={(e) => setFilters(prev => ({ ...prev, department: e.target.value }))}
                      className="block w-full px-2 py-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">All Departments</option>
                      <option value="CSD">CSD</option>
                      <option value="FINANCE">FINANCE</option>
                      <option value="HR">HR</option>
                      <option value="IT">IT</option>
                      <option value="LAND">LAND</option>
                      <option value="LEGAL">LEGAL</option>
                      <option value="MANAGEMENT">MANAGEMENT</option>
                      <option value="MARKETING">MARKETING</option>
                      <option value="OPERATIONS">OPERATIONS</option>
                      <option value="PND">PND</option>
                      <option value="SALES">SALES</option>
                    </select>

                  {/* Project Filter */}
                    <select
                      value={filters.project}
                      onChange={(e) => setFilters(prev => ({ ...prev, project: e.target.value }))}
                      className="block w-full px-2 py-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">All Projects</option>
                      <option value="Eurobiz Corporations">Eurobiz Corporations</option>
                      <option value="Guardian International">Guardian International</option>
                      <option value="Guardian Developers">Guardian Developers</option>
                      <option value="Grand Developers">Grand Developers</option>
                      <option value="ARD Developers">ARD Developers</option>
                    </select>

                  {/* Location Filter */}
                    <select
                      value={filters.location}
                      onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                      className="block w-full px-2 py-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">All Locations</option>
                      <option value="Grand City Head Office Lahore">Grand City Head Office Lahore</option>
                      <option value="Grand City Site Office Kharian">Grand City Site Office Kharian</option>
                      <option value="Grand City Site Office Arifwala">Grand City Site Office Arifwala</option>
                      <option value="Grand City Site Office Vehari">Grand City Site Office Vehari</option>
                      <option value="Grand City Site Office Faisalabad">Grand City Site Office Faisalabad</option>
                      <option value="Grand City Site Office Murree">Grand City Site Office Murree</option>
                      <option value="ARD Head Office Lahore">ARD Head Office Lahore</option>
                      <option value="ARD Site Office RUDA">ARD Site Office RUDA</option>
                    </select>
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="p-4 bg-red-50 text-red-500 rounded-lg">
                  {error}
                </div>
              )}

              {/* Loading State */}
              {isLoading ? (
                    <div className="flex justify-center items-center p-8">
                      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
              ) : (
                    <>
                      {/* Asset Table or Cards (Responsive) */}
                      {isMobile ? (
                        /* Mobile Card View */
                        <div className="space-y-4">
                          {filteredAssets.length === 0 ? (
                            <div className="bg-white p-4 rounded-lg shadow text-center text-gray-500">
                              No assets found. Try adjusting your filters or add a new asset.
                </div>
                          ) : (
                            filteredAssets.map((asset) => (
                              <div key={asset.id} className="bg-white rounded-lg shadow overflow-hidden">
                                <div className="p-4 flex justify-between items-center border-b">
                                  <div className="flex flex-col">
                                    <h3 className="font-medium">{asset.manufacturer} {asset.model}</h3>
                                    <div className="text-sm text-gray-500">{asset.assetType}</div>
                                  </div>
                                  <div className="relative">
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        if (asset.id !== undefined) {
                                          toggleDropdown(asset.id.toString());
                                        }
                                      }}
                                      className="p-2 text-gray-400 hover:text-gray-500 rounded-full"
                                    >
                                      <MoreVertical className="h-5 w-5" />
                                    </button>
                                    
                                    {activeDropdown === (asset.id?.toString() || '') && (
                                      <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                                        <div className="py-1">
                                          <button
                                            onClick={() => {
                                              handleViewAsset(asset);
                                              setActiveDropdown(null);
                                            }}
                                            className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                                          >
                                            <Eye className="h-4 w-4 mr-2 text-blue-500" />
                                            View Details
                                          </button>
                                          <button
                                            onClick={() => {
                                              handleEditAsset(asset);
                                              setActiveDropdown(null);
                                            }}
                                            className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                                          >
                                            <Settings className="h-4 w-4 mr-2 text-indigo-500" />
                                            Edit Asset
                                          </button>
                                          <button
                                            onClick={() => {
                                              showDeleteConfirmation(asset);
                                              setActiveDropdown(null);
                                            }}
                                            className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                                          >
                                            <Trash2 className="h-4 w-4 mr-2 text-red-500" />
                                            Delete Asset
                                          </button>
                </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                <div className="px-4 py-3 grid grid-cols-2 gap-2 text-sm">
                                  <div>
                                    <p className="text-gray-500">Type</p>
                                    <p className="font-medium">{asset.assetType}</p>
                                  </div>
                                  <div>
                                    <p className="text-gray-500">Category</p>
                                    <p className="font-medium">{asset.category}</p>
                                  </div>
                                  <div>
                                    <p className="text-gray-500">Serial Number</p>
                                    <p className="font-medium">
                                      {asset.serialNumber || '-'}
                                    </p>
                                  </div>
                                  <div>
                                    <p className="text-gray-500">Status</p>
                                    <p>
                                      <span className={`px-2 py-1 rounded-full text-xs font-medium inline-block ${
                                        asset.status?.toLowerCase() === 'active' ? 'bg-green-100 text-green-800' :
                                        asset.status?.toLowerCase() === 'under repair' ? 'bg-yellow-100 text-yellow-800' :
                                        asset.status?.toLowerCase() === 'retired' ? 'bg-red-100 text-red-800' :
                                        'bg-gray-100 text-gray-800'
                                      }`}>
                                        {formatStatus(asset.status || '')}
                                      </span>
                                    </p>
                                  </div>
                                  <div>
                                    <p className="text-gray-500">Location</p>
                                    <p className="font-medium">{asset.location}</p>
                                  </div>
                                  <div>
                                    <p className="text-gray-500">Department</p>
                                    <p className="font-medium">{asset.department}</p>
                                  </div>
                                </div>
                              </div>
                            ))
                          )}
                        </div>
                      ) : (
                        /* Desktop Table View */
                        <AssetTable 
                          data={filteredAssets}
                          columns={filteredColumns}
                          currentPage={currentPage}
                          setCurrentPage={paginate}
                          itemsPerPage={itemsPerPage}
                          setItemsPerPage={setItemsPerPage}
                        />
                      )}
                    </>
                  )}
                </div>
              </div>

              {/* Modals */}
          {isViewModalOpen && selectedAsset && (
            <ViewAssetModal
              asset={selectedAsset}
              onClose={() => {
                setIsViewModalOpen(false);
                setSelectedAsset(undefined);
              }}
              onEdit={() => {
                setIsViewModalOpen(false);
                handleEditAsset(selectedAsset);
              }}
            />
          )}

          {isDeleteModalOpen && assetToDelete && (
            <DeleteConfirmationModal
              title="Delete Asset"
              message={`Are you sure you want to delete ${assetToDelete.manufacturer} ${assetToDelete.model}? This action cannot be undone.`}
              onConfirm={() => {
                handleDeleteAsset(assetToDelete.id);
                setIsDeleteModalOpen(false);
                setAssetToDelete(undefined);
              }}
              onCancel={() => {
                setIsDeleteModalOpen(false);
                setAssetToDelete(undefined);
              }}
            />
          )}

          {/* Add the Export Modal */}
          <ExportModal
            isOpen={isExportModalOpen}
            onClose={() => setIsExportModalOpen(false)}
            onExport={handleExportReport}
          />
        </>
      )}
    </div>
  );
}

// Export as both named and default export for compatibility
export { AssetManagement };
export default AssetManagement;