import React, { useState, useEffect } from 'react';
import AttendanceNavigation from './AttendanceNavigation';
import AdvancedTimeAttendanceWrapper from './AdvancedTimeAttendanceWrapper';
import AttendanceForm from './AttendanceForm';
import AttendanceManagement from './AttendanceManagement';
import attendanceService from '../../../services/AttendanceService';
import employeeService from '../../../services/EmployeeService';
import { Attendance, AttendanceStatus } from '../../../types/attendance';
import { 
  hrCardStyle, 
  hrSectionTitleStyle,
  hrInfoAlertStyle,
  hrSuccessAlertStyle,
  hrErrorAlertStyle
} from '../../../styles/hrWorkflow';
import { Info, Calendar, CheckCircle, AlertCircle, Clock } from 'lucide-react';

// Placeholder components - in a real implementation, these would be separate files
const MyAttendance: React.FC<{ employeeId: number }> = ({ employeeId }) => {
  const [attendanceRecords, setAttendanceRecords] = useState<Attendance[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentMonth, setCurrentMonth] = useState(new Date().toISOString().split('T')[0].substring(0, 7));

  useEffect(() => {
    const fetchMyAttendance = async () => {
      try {
        setLoading(true);
        const startDate = `${currentMonth}-01`;
        const endDate = `${currentMonth}-31`;
        
        console.log('🔍 MyAttendance: Fetching attendance for employeeId:', employeeId);
        console.log('🔍 MyAttendance: Date range:', startDate, 'to', endDate);
        
        // Fetch attendance records for the current employee and month
        const response = await attendanceService.getAttendances({
          employeeId,
          startDate,
          endDate
        });
        
        console.log('📊 MyAttendance: Found', response.data?.length || 0, 'attendance records for employee', employeeId, 'in month', currentMonth);
        
        if (response.data && !response.error) {
          setAttendanceRecords(response.data || []);
        } else {
          console.error('❌ MyAttendance: Error fetching attendance:', response.error);
          setAttendanceRecords([]);
        }
      } catch (error) {
        console.error('❌ MyAttendance: Error fetching attendance records:', error);
        setAttendanceRecords([]);
      } finally {
        setLoading(false);
      }
    };

    fetchMyAttendance();
  }, [employeeId, currentMonth]);

  if (loading) {
    return (
      <div className="p-8 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="text-gray-500 mt-2">Loading your attendance records...</p>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status: AttendanceStatus) => {
    const statusColors = {
      [AttendanceStatus.PRESENT]: 'bg-green-100 text-green-800',
      [AttendanceStatus.ABSENT]: 'bg-red-100 text-red-800',
      [AttendanceStatus.LATE]: 'bg-yellow-100 text-yellow-800',
      [AttendanceStatus.HALF_DAY]: 'bg-blue-100 text-blue-800',
      [AttendanceStatus.LEAVE]: 'bg-purple-100 text-purple-800',
      [AttendanceStatus.WORK_FROM_HOME]: 'bg-indigo-100 text-indigo-800',
      [AttendanceStatus.HOLIDAY]: 'bg-orange-100 text-orange-800',
      [AttendanceStatus.WEEKEND]: 'bg-gray-100 text-gray-800',
      [AttendanceStatus.ON_DUTY]: 'bg-teal-100 text-teal-800',
      [AttendanceStatus.PAID_TIME_OFF]: 'bg-violet-100 text-violet-800',
      [AttendanceStatus.UNPAID_LEAVE]: 'bg-red-100 text-red-800',
      [AttendanceStatus.COMP_OFF]: 'bg-emerald-100 text-emerald-800',
      [AttendanceStatus.SICK_LEAVE]: 'bg-rose-100 text-rose-800',
      [AttendanceStatus.ANNUAL_LEAVE]: 'bg-sky-100 text-sky-800',
      [AttendanceStatus.MATERNITY_LEAVE]: 'bg-pink-100 text-pink-800',
      [AttendanceStatus.PATERNITY_LEAVE]: 'bg-cyan-100 text-cyan-800'
    };
    
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusColors[status] || 'bg-gray-100 text-gray-800'}`}>
        {status.replace('_', ' ')}
      </span>
    );
  };

  const currentMonthSummary = {
    present: attendanceRecords.filter(record => record.status === AttendanceStatus.PRESENT).length,
    absent: attendanceRecords.filter(record => record.status === AttendanceStatus.ABSENT).length,
    totalHours: attendanceRecords.reduce((sum, record) => sum + (record.workHours || 0), 0)
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center">
          <Calendar className="h-6 w-6 mr-2 text-blue-500" />
          My Attendance
        </h2>
        <div>
          <input
            type="month"
            value={currentMonth}
            onChange={(e) => setCurrentMonth(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            max={new Date().toISOString().split('T')[0].substring(0, 7)}
          />
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-sm p-4 border">
          <div className="flex items-center justify-between mb-2">
            <div className="text-sm font-medium text-gray-500">Present Days</div>
            <CheckCircle className="h-5 w-5 text-green-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">{currentMonthSummary.present}</div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4 border">
          <div className="flex items-center justify-between mb-2">
            <div className="text-sm font-medium text-gray-500">Absent Days</div>
            <AlertCircle className="h-5 w-5 text-red-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">{currentMonthSummary.absent}</div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4 border">
          <div className="flex items-center justify-between mb-2">
            <div className="text-sm font-medium text-gray-500">Total Hours</div>
            <Clock className="h-5 w-5 text-blue-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">{currentMonthSummary.totalHours.toFixed(1)}</div>
        </div>
      </div>

      {/* Attendance Records Table */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Attendance Records</h3>
        </div>
        
        {attendanceRecords.length === 0 ? (
          <div className="p-8 text-center">
            <div className="text-gray-400 mb-2">
              <Calendar className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No attendance records found</h3>
            <p className="text-gray-500">No attendance records found for the selected month. Start marking your attendance to see records here.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check In</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check Out</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hours</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {attendanceRecords.map((record) => (
                  <tr key={record.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatDate(record.date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.checkInTime || '—'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.checkOutTime || '—'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.workHours ? record.workHours.toFixed(2) : '0.00'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(record.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.location || '—'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

const TimeSheet = () => <div className="p-4">My Timesheet View</div>;
const RequestView = () => <div className="p-4">Request/Regularization View</div>;
const TeamAttendance = () => <div className="p-4">Team Attendance View</div>;
const ApprovalView = () => <div className="p-4">Attendance Approvals View</div>;
const TeamCalendarView = () => <div className="p-4">Team Calendar View</div>;
const ReportsView = () => <div className="p-4">Reports & Analytics View</div>;
const ShiftManagement = () => <div className="p-4">Shift Management View</div>;
const HolidaysView = () => <div className="p-4">Holidays View</div>;
const ConfigurationView = () => <div className="p-4">Attendance Configuration View</div>;

// Functional ClockInOut component that integrates with the attendance system
const ClockInOut: React.FC<{ employeeId: number; employeeName: string }> = ({ employeeId, employeeName }) => {
  const [employees, setEmployees] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);

  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        // Add cache-busting timestamp
        const response = await employeeService.getEmployees({ t: Date.now() });
        
        if (response.data && !response.error) {
          // The API response structure is { success: true, count: number, employees: [...] }
          const employeesData = response.data.employees || response.data || [];
          
          console.log('🔍 Total employees fetched:', employeesData.length);
          
          const mappedEmployees = employeesData.map((emp: any) => {
            // Handle the actual API response structure from getEmployeesForListing
            const firstName = emp.firstName ? emp.firstName.toString().trim() : '';
            const lastName = emp.lastName ? emp.lastName.toString().trim() : '';
            
            // Debug specific employee
            if (emp.employeeId === 'GC-0403') {
              console.log('🐛 DEBUG GC-0403 Employee:', {
                rawFirstName: emp.firstName,
                rawLastName: emp.lastName,
                processedFirstName: firstName,
                processedLastName: lastName,
                hasFirstName: !!firstName,
                hasLastName: !!lastName,
                department: emp.department
              });
            }
            
            // Build the full name - just concatenate if we have either name
            let employeeName = '';
            if (firstName || lastName) {
              employeeName = `${firstName} ${lastName}`.trim();
            }
            
            // Only use fallback if we truly have no name at all
            if (!employeeName) {
              employeeName = emp.employeeId ? `Employee ${emp.employeeId}` : `Employee ID: ${emp.id}`;
            }
            
            // Debug the final name
            if (emp.employeeId === 'GC-0403') {
              console.log('🐛 Final name for GC-0403:', employeeName);
            }
            
            return {
              id: emp.id,
              name: employeeName,
              department: emp.department || 'Unknown',
              position: emp.designation || 'Unknown',
              avatar: emp.profileImagePath || emp.photo || emp.profileImage,
              location: emp.location, // Direct from API response
              project: emp.project, // Direct from API response
              employeeId: emp.employeeId,
              email: emp.officialEmail || emp.personalEmail
            };
          });
          
          setEmployees(mappedEmployees);
        }
      } catch (error) {
        console.error('Error fetching employees:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchEmployees();
  }, []);

  const handleAttendanceSubmit = async (attendanceData: Omit<Attendance, 'id'>) => {
    try {
      setMessage(null);
      
      console.log('🚀 Submitting attendance data:', attendanceData);
      console.log('🔍 Expected employeeId:', employeeId);
      console.log('🔍 Submitted employeeId:', attendanceData.employeeId);
      console.log('📅 Submitted date:', attendanceData.date);
      console.log('📅 Current date (browser):', new Date().toISOString().split('T')[0]);
      console.log('📅 Current date (local):', new Date().toLocaleDateString());
      
      const response = await attendanceService.createAttendance(attendanceData);
      
      console.log('📊 Attendance submission response:', response);
      
      if (response.data && !response.error) {
        setMessage({
          type: 'success',
          text: `Attendance marked successfully for ${attendanceData.employeeName} on ${attendanceData.date}`
        });
        
        console.log('✅ Attendance submitted successfully, record ID:', response.data.data?.id);
      } else {
        setMessage({
          type: 'error',
          text: response.error || 'Failed to mark attendance'
        });
        console.error('❌ Attendance submission failed:', response.error);
      }
    } catch (error) {
      console.error('❌ Error submitting attendance:', error);
      setMessage({
        type: 'error',
        text: 'Failed to mark attendance. Please try again.'
      });
    }

    // Clear message after 5 seconds
    setTimeout(() => {
      setMessage(null);
    }, 5000);
  };

  if (loading) {
    return (
      <div className="p-4 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="text-gray-500 mt-2">Loading employees...</p>
      </div>
    );
  }

  return (
    <div className="p-4">
      {/* Message notifications */}
      {message && (
        <div className={message.type === 'success' ? hrSuccessAlertStyle : hrErrorAlertStyle + " mb-4"}>
          <div className="flex items-start">
            {message.type === 'success' ? (
              <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
            )}
            <p>{message.text}</p>
          </div>
        </div>
      )}

      <AttendanceForm
        onSubmit={handleAttendanceSubmit}
        employees={employees}
        departments={['IT', 'HR', 'Finance', 'Marketing', 'Operations', 'Sales']}
        shifts={[
          { id: 1, name: 'Day Shift', startTime: '09:00', endTime: '17:00' },
          { id: 2, name: 'Night Shift', startTime: '21:00', endTime: '05:00' }
        ]}
      />
    </div>
  );
};

interface AttendanceModuleProps {
  employeeId: number;
  employeeName: string;
  userRole: 'employee' | 'manager' | 'admin';
}

const AttendanceModule: React.FC<AttendanceModuleProps> = ({ 
  employeeId, 
  employeeName,
  userRole = 'employee'
}) => {
  const [activeTab, setActiveTab] = useState<string>('clock-in-out');
  const [message, setMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);
  const [pendingRegularizations, setPendingRegularizations] = useState<number>(0);

  // Fetch pending regularizations - this would be an API call in a real implementation
  useEffect(() => {
    // Simulate fetching pending regularizations
    const fetchPendingRegularizations = () => {
      // In a real app, this would be an API call
      setTimeout(() => {
        setPendingRegularizations(2);
      }, 1000);
    };

    fetchPendingRegularizations();
  }, []);

  // Get the component to render based on the active tab
  const getTabContent = () => {
    switch (activeTab) {
      case 'clock-in-out':
        return <ClockInOut employeeId={employeeId} employeeName={employeeName} />;
      case 'daily':
        return <MyAttendance employeeId={employeeId} />;
      case 'timesheet':
        return <TimeSheet />;
      case 'requests':
        return <RequestView />;
      case 'team':
        return userRole !== 'employee' ? <TeamAttendance /> : <div className="p-4 text-gray-500">Access restricted</div>;
      case 'approvals':
        return userRole === 'admin' ? <ApprovalView /> : <div className="p-4 text-gray-500">Access restricted</div>;
      case 'calendar':
        return <TeamCalendarView />;
      case 'reports':
        return userRole !== 'employee' ? <ReportsView /> : <div className="p-4 text-gray-500">Access restricted</div>;
      case 'shifts':
        return userRole === 'admin' ? <ShiftManagement /> : <div className="p-4 text-gray-500">Access restricted</div>;
      case 'holidays':
        return <HolidaysView />;
      case 'management':
        return userRole !== 'employee' ? <AttendanceManagement /> : <div className="p-4 text-gray-500">Access restricted</div>;
      case 'config':
        return userRole === 'admin' ? <ConfigurationView /> : <div className="p-4 text-gray-500">Access restricted</div>;
      case 'advanced':
        return (
          <AdvancedTimeAttendanceWrapper 
            employeeId={employeeId}
            employeeName={employeeName}
          />
        );
      default:
        return <MyAttendance employeeId={employeeId} />;
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <h1 className={hrSectionTitleStyle}>
              <Calendar className="h-6 w-6 mr-2 text-blue-500" />
              Attendance Management
            </h1>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-500">
                Welcome, <span className="font-medium text-gray-900">{employeeName}</span>
              </div>
              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-800 font-medium">
                {employeeName.charAt(0)}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <div className="flex-1 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 flex">
        {/* Navigation sidebar */}
        <div className="w-64 mr-8">
          <div className="sticky top-6">
            <AttendanceNavigation 
              activeTab={activeTab}
              onTabChange={setActiveTab}
              pendingRegularizations={pendingRegularizations}
              userRole={userRole}
            />
          </div>
        </div>

        {/* Main content area */}
        <div className="flex-1">
          {/* Welcome message */}
          {activeTab === 'clock-in-out' && (
            <div className={hrInfoAlertStyle + " mb-6"}>
              <div className="flex items-start">
                <Info className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                <div>
                  <h3 className="font-medium text-blue-800">Welcome to the new Attendance Management System</h3>
                  <p className="text-sm text-blue-700 mt-1">
                    We've redesigned the attendance module to better follow standard HR workflows and improve your experience.
                    Navigate using the sidebar to access different attendance features.
                  </p>
                  <button 
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium mt-2"
                    onClick={() => setActiveTab('daily')}
                  >
                    Dismiss
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Tab content */}
          <div className={hrCardStyle}>
            {getTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AttendanceModule; 