import { Router } from 'express';
import { RoleConfigurationService } from '../services/RoleConfigurationService';
import { PermissionEvaluationService } from '../services/PermissionEvaluationService';
import { requirePermission, commonPermissions } from '../middleware/dynamicPermission';
import { auth } from '../middleware/auth';
import logger from '../utils/logger';

const router = Router();

// Apply authentication to all routes
router.use(auth);

/**
 * Get all roles with permissions
 */
router.get('/roles', 
  requirePermission(commonPermissions.system.admin, {
    errorMessage: 'System administration access required to view roles'
  }),
  async (req, res) => {
    try {
      const roles = await RoleConfigurationService.getAllRoles();
      res.json({
        success: true,
        data: roles,
        count: roles.length
      });
    } catch (error) {
      logger.error('Error fetching roles:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch roles',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

/**
 * Create a new role
 */
router.post('/roles',
  requirePermission(commonPermissions.system.admin),
  async (req, res) => {
    try {
      const { name, description, category, permissions, parentRoleId, rules, metadata } = req.body;

      // Validate input
      const validation = await RoleConfigurationService.validateRoleConfiguration(req.body);
      if (!validation.valid) {
        return res.status(400).json({
          success: false,
          message: 'Invalid role configuration',
          errors: validation.errors
        });
      }

      const role = await RoleConfigurationService.createRole({
        name,
        description,
        category,
        permissions: permissions || [],
        parentRoleId,
        rules,
        metadata
      });

      res.status(201).json({
        success: true,
        message: 'Role created successfully',
        data: role
      });
    } catch (error) {
      logger.error('Error creating role:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create role',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

/**
 * Update an existing role
 */
router.put('/roles/:roleId',
  requirePermission(commonPermissions.system.admin),
  async (req, res) => {
    try {
      const { roleId } = req.params;
      const updates = req.body;

      const role = await RoleConfigurationService.updateRole(roleId, updates);

      res.json({
        success: true,
        message: 'Role updated successfully',
        data: role
      });
    } catch (error) {
      logger.error('Error updating role:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update role',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

/**
 * Delete a role
 */
router.delete('/roles/:roleId',
  requirePermission(commonPermissions.system.admin),
  async (req, res) => {
    try {
      const { roleId } = req.params;
      await RoleConfigurationService.deleteRole(roleId);

      res.json({
        success: true,
        message: 'Role deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting role:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete role',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

/**
 * Clone an existing role
 */
router.post('/roles/:roleId/clone',
  requirePermission(commonPermissions.system.admin),
  async (req, res) => {
    try {
      const { roleId } = req.params;
      const { newName, newDescription } = req.body;

      if (!newName) {
        return res.status(400).json({
          success: false,
          message: 'New role name is required'
        });
      }

      const clonedRole = await RoleConfigurationService.cloneRole(roleId, newName, newDescription);

      res.status(201).json({
        success: true,
        message: 'Role cloned successfully',
        data: clonedRole
      });
    } catch (error) {
      logger.error('Error cloning role:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to clone role',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

/**
 * Get all permissions grouped by category
 */
router.get('/permissions',
  requirePermission(commonPermissions.system.admin),
  async (req, res) => {
    try {
      const permissions = await RoleConfigurationService.getAllPermissions();
      res.json({
        success: true,
        data: permissions
      });
    } catch (error) {
      logger.error('Error fetching permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch permissions',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

/**
 * Create a new permission
 */
router.post('/permissions',
  requirePermission(commonPermissions.system.admin),
  async (req, res) => {
    try {
      const { name, description, category, resource, action, metadata } = req.body;

      if (!name || !description || !category || !resource || !action) {
        return res.status(400).json({
          success: false,
          message: 'Name, description, category, resource, and action are required'
        });
      }

      const permission = await RoleConfigurationService.createPermission({
        name,
        description,
        category,
        resource,
        action,
        metadata
      });

      res.status(201).json({
        success: true,
        message: 'Permission created successfully',
        data: permission
      });
    } catch (error) {
      logger.error('Error creating permission:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create permission',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

/**
 * Assign role to user
 */
router.post('/users/:userId/roles',
  requirePermission(commonPermissions.system.admin),
  async (req, res) => {
    try {
      const { userId } = req.params;
      const { roleId, isPrimary, expiresAt, scope } = req.body;
      const assignedBy = req.user?.id || 'system';

      if (!roleId) {
        return res.status(400).json({
          success: false,
          message: 'Role ID is required'
        });
      }

      const assignment = await RoleConfigurationService.assignRoleToUser(userId, roleId, {
        isPrimary,
        expiresAt: expiresAt ? new Date(expiresAt) : undefined,
        assignedBy,
        scope
      });

      res.status(201).json({
        success: true,
        message: 'Role assigned successfully',
        data: assignment
      });
    } catch (error) {
      logger.error('Error assigning role:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to assign role',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

/**
 * Remove role from user
 */
router.delete('/users/:userId/roles/:roleId',
  requirePermission(commonPermissions.system.admin),
  async (req, res) => {
    try {
      const { userId, roleId } = req.params;
      await RoleConfigurationService.removeRoleFromUser(userId, roleId);

      res.json({
        success: true,
        message: 'Role removed successfully'
      });
    } catch (error) {
      logger.error('Error removing role:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to remove role',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

/**
 * Get user's role assignments
 */
router.get('/users/:userId/roles',
  requirePermission(commonPermissions.users.read),
  async (req, res) => {
    try {
      const { userId } = req.params;
      const assignments = await RoleConfigurationService.getUserRoleAssignments(userId);

      res.json({
        success: true,
        data: assignments
      });
    } catch (error) {
      logger.error('Error fetching user roles:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch user roles',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

/**
 * Check user permissions
 */
router.post('/users/:userId/check-permissions',
  requirePermission(commonPermissions.users.read),
  async (req, res) => {
    try {
      const { userId } = req.params;
      const { actions, resource } = req.body;

      if (!actions || !Array.isArray(actions)) {
        return res.status(400).json({
          success: false,
          message: 'Actions array is required'
        });
      }

      const results = await PermissionEvaluationService.checkMultiplePermissions(
        userId,
        actions,
        resource
      );

      res.json({
        success: true,
        data: results
      });
    } catch (error) {
      logger.error('Error checking permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to check permissions',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

/**
 * Get role hierarchy
 */
router.get('/roles/hierarchy',
  requirePermission(commonPermissions.system.admin),
  async (req, res) => {
    try {
      const hierarchy = await RoleConfigurationService.getRoleHierarchy();
      res.json({
        success: true,
        data: hierarchy
      });
    } catch (error) {
      logger.error('Error fetching role hierarchy:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch role hierarchy',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

export default router;
