import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsNumber, Min, <PERSON>, Length } from 'class-validator';
import { JobApplication } from './JobApplication';
import { User } from './User';

@Entity('application_evaluations')
export class ApplicationEvaluation {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int', default: 0 })
  @IsNumber({}, { message: 'Overall rating must be a number' })
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must be at most 5' })
  overallRating: number;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Technical skills rating must be a number' })
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must be at most 5' })
  technicalSkillsRating: number;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Communication rating must be a number' })
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must be at most 5' })
  communicationRating: number;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Experience rating must be a number' })
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must be at most 5' })
  experienceRating: number;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Cultural fit rating must be a number' })
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must be at most 5' })
  culturalFitRating: number;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Education rating must be a number' })
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must be at most 5' })
  educationRating: number;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @Length(0, 2000, { message: 'Strengths must be less than 2000 characters' })
  strengths: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @Length(0, 2000, { message: 'Weaknesses must be less than 2000 characters' })
  weaknesses: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @Length(0, 2000, { message: 'Comments must be less than 2000 characters' })
  comments: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @Length(0, 1000, { message: 'Recommendation must be less than 1000 characters' })
  recommendation: string;

  @Column({ type: 'boolean', default: false })
  recommendForHire: boolean;

  @Column({ type: 'boolean', default: false })
  recommendForInterview: boolean;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  customRatings: Record<string, number>;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  skillsAssessment: Array<{
    skill: string;
    rating: number;
    notes?: string;
  }>;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  internalNotes: string;

  // Relations
  @ManyToOne(() => JobApplication, application => application.evaluations, { nullable: false })
  @JoinColumn({ name: 'applicationId' })
  application: JobApplication;

  @Column({ type: 'int' })
  applicationId: number;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'evaluatorId' })
  evaluator: User;

  @Column({ type: 'uuid' })
  evaluatorId: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Virtual properties
  get averageRating(): number {
    const ratings = [
      this.technicalSkillsRating,
      this.communicationRating,
      this.experienceRating,
      this.culturalFitRating,
      this.educationRating
    ].filter(rating => rating && rating > 0);

    if (ratings.length === 0) return this.overallRating;
    
    return ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
  }

  get hasDetailedRatings(): boolean {
    return !!(this.technicalSkillsRating || this.communicationRating || 
              this.experienceRating || this.culturalFitRating || this.educationRating);
  }

  get evaluationSummary(): string {
    if (this.overallRating >= 4) return 'Excellent candidate';
    if (this.overallRating >= 3) return 'Good candidate';
    if (this.overallRating >= 2) return 'Average candidate';
    return 'Below expectations';
  }
}
