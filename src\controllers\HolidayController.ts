import { Request, Response } from 'express';
import { Repository } from 'typeorm';
import { Holiday, HolidayType } from '../entities/Holiday';
import { HolidayConfiguration } from '../entities/HolidayConfiguration';
import { AppDataSource } from '../config/database';
import { validate } from 'class-validator';

export class HolidayController {
  private holidayRepository: Repository<Holiday>;
  private configRepository: Repository<HolidayConfiguration>;

  constructor() {
    // Initialize repositories when they're first accessed
  }

  private getHolidayRepository(): Repository<Holiday> {
    if (!this.holidayRepository) {
      this.holidayRepository = AppDataSource.getRepository(Holiday);
    }
    return this.holidayRepository;
  }

  private getConfigRepository(): Repository<HolidayConfiguration> {
    if (!this.configRepository) {
      this.configRepository = AppDataSource.getRepository(HolidayConfiguration);
    }
    return this.configRepository;
  }

  // Get all holidays with optional filtering
  async getHolidays(req: Request, res: Response): Promise<void> {
    try {
      console.log('🎯 Holiday API called - fetching holidays...');
      
      const { year, type, isActive } = req.query;
      
      const holidayRepo = this.getHolidayRepository();
      let query = holidayRepo.createQueryBuilder('holiday');
      
      if (year) {
        query = query.where('YEAR(holiday.date) = :year', { year });
      }
      
      if (type) {
        query = query.andWhere('holiday.type = :type', { type });
      }
      
      if (isActive !== undefined) {
        query = query.andWhere('holiday.isActive = :isActive', { isActive: isActive === 'true' });
      }
      
      const holidays = await query
        .orderBy('holiday.date', 'ASC')
        .getMany();

      console.log(`✅ Found ${holidays.length} holidays`);

      res.json({
        success: true,
        data: holidays,
        count: holidays.length
      });
    } catch (error) {
      console.error('❌ Error fetching holidays:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch holidays',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Get a single holiday by ID
  async getHoliday(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      const holidayRepo = this.getHolidayRepository();
      const holiday = await holidayRepo.findOne({
        where: { id: parseInt(id) }
      });

      if (!holiday) {
        res.status(404).json({
          success: false,
          error: 'Holiday not found'
        });
        return;
      }

      res.json({
        success: true,
        data: holiday
      });
    } catch (error) {
      console.error('Error fetching holiday:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch holiday'
      });
    }
  }

  // Create a new holiday
  async createHoliday(req: Request, res: Response): Promise<void> {
    try {
      const holidayData = req.body;
      
      // Create new holiday instance
      const holiday = new Holiday();
      Object.assign(holiday, holidayData);
      
      // Add audit information
      if (req.user) {
        holiday.createdBy = (req.user as any).name || (req.user as any).email;
      }

      // Validate the entity
      const errors = await validate(holiday);
      if (errors.length > 0) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.map(error => ({
            property: error.property,
            constraints: error.constraints
          }))
        });
        return;
      }

      const holidayRepo = this.getHolidayRepository();

      // Check for duplicate holidays on the same date
      const existingHoliday = await holidayRepo.findOne({
        where: { 
          date: holiday.date,
          name: holiday.name
        }
      });

      if (existingHoliday) {
        res.status(409).json({
          success: false,
          error: 'A holiday with this name already exists on this date'
        });
        return;
      }

      const savedHoliday = await holidayRepo.save(holiday);

      res.status(201).json({
        success: true,
        data: savedHoliday,
        message: 'Holiday created successfully'
      });
    } catch (error) {
      console.error('Error creating holiday:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create holiday'
      });
    }
  }

  // Update a holiday
  async updateHoliday(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const holidayRepo = this.getHolidayRepository();
      const holiday = await holidayRepo.findOne({
        where: { id: parseInt(id) }
      });

      if (!holiday) {
        res.status(404).json({
          success: false,
          error: 'Holiday not found'
        });
        return;
      }

      // Update holiday data
      Object.assign(holiday, updateData);
      
      // Add audit information
      if (req.user) {
        holiday.updatedBy = (req.user as any).name || (req.user as any).email;
      }

      // Validate the updated entity
      const errors = await validate(holiday);
      if (errors.length > 0) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.map(error => ({
            property: error.property,
            constraints: error.constraints
          }))
        });
        return;
      }

      const savedHoliday = await holidayRepo.save(holiday);

      res.json({
        success: true,
        data: savedHoliday,
        message: 'Holiday updated successfully'
      });
    } catch (error) {
      console.error('Error updating holiday:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update holiday'
      });
    }
  }

  // Delete a holiday
  async deleteHoliday(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const holidayRepo = this.getHolidayRepository();
      const holiday = await holidayRepo.findOne({
        where: { id: parseInt(id) }
      });

      if (!holiday) {
        res.status(404).json({
          success: false,
          error: 'Holiday not found'
        });
        return;
      }

      await holidayRepo.remove(holiday);

      res.json({
        success: true,
        message: 'Holiday deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting holiday:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete holiday'
      });
    }
  }

  // Toggle holiday status
  async toggleHolidayStatus(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const holidayRepo = this.getHolidayRepository();
      const holiday = await holidayRepo.findOne({
        where: { id: parseInt(id) }
      });

      if (!holiday) {
        res.status(404).json({
          success: false,
          error: 'Holiday not found'
        });
        return;
      }

      holiday.isActive = !holiday.isActive;
      
      // Add audit information
      if (req.user) {
        holiday.updatedBy = (req.user as any).name || (req.user as any).email;
      }

      const savedHoliday = await holidayRepo.save(holiday);

      res.json({
        success: true,
        data: savedHoliday,
        message: `Holiday ${savedHoliday.isActive ? 'activated' : 'deactivated'} successfully`
      });
    } catch (error) {
      console.error('Error toggling holiday status:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to toggle holiday status'
      });
    }
  }

  // Get upcoming holidays
  async getUpcomingHolidays(req: Request, res: Response): Promise<void> {
    try {
      const { days = 30 } = req.query;
      const today = new Date();
      const futureDate = new Date();
      futureDate.setDate(today.getDate() + parseInt(days as string));

      const holidayRepo = this.getHolidayRepository();
      const holidays = await holidayRepo
        .createQueryBuilder('holiday')
        .where('holiday.date >= :today', { today: today.toISOString().split('T')[0] })
        .andWhere('holiday.date <= :futureDate', { futureDate: futureDate.toISOString().split('T')[0] })
        .andWhere('holiday.isActive = :isActive', { isActive: true })
        .orderBy('holiday.date', 'ASC')
        .getMany();

      res.json({
        success: true,
        data: holidays,
        count: holidays.length
      });
    } catch (error) {
      console.error('Error fetching upcoming holidays:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch upcoming holidays'
      });
    }
  }

  // Import multiple holidays
  async importHolidays(req: Request, res: Response): Promise<void> {
    try {
      const { holidays } = req.body;

      if (!Array.isArray(holidays)) {
        res.status(400).json({
          success: false,
          error: 'Holidays must be an array'
        });
        return;
      }

      const holidayRepo = this.getHolidayRepository();
      const createdHolidays = [];
      const errors = [];

      for (const holidayData of holidays) {
        try {
          const holiday = new Holiday();
          Object.assign(holiday, holidayData);
          
          // Add audit information
          if (req.user) {
            holiday.createdBy = (req.user as any).name || (req.user as any).email;
          }

          // Validate the entity
          const validationErrors = await validate(holiday);
          if (validationErrors.length > 0) {
            errors.push({
              holidayData,
              errors: validationErrors.map(error => ({
                property: error.property,
                constraints: error.constraints
              }))
            });
            continue;
          }

          // Check for duplicates
          const existingHoliday = await holidayRepo.findOne({
            where: { 
              date: holiday.date,
              name: holiday.name
            }
          });

          if (existingHoliday) {
            errors.push({
              holidayData,
              errors: ['Holiday already exists on this date']
            });
            continue;
          }

          const savedHoliday = await holidayRepo.save(holiday);
          createdHolidays.push(savedHoliday);
        } catch (error) {
          errors.push({
            holidayData,
            errors: [(error as Error).message]
          });
        }
      }

      res.status(201).json({
        success: true,
        data: createdHolidays,
        message: `Successfully imported ${createdHolidays.length} holidays`,
        summary: {
          total: holidays.length,
          imported: createdHolidays.length,
          failed: errors.length
        },
        errors: errors.length > 0 ? errors : undefined
      });
    } catch (error) {
      console.error('Error importing holidays:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to import holidays'
      });
    }
  }

  // Get holiday configuration
  async getHolidayConfiguration(req: Request, res: Response): Promise<void> {
    try {
      const { organizationId = 'default' } = req.query;

      const configRepo = this.getConfigRepository();
      const config = await configRepo.findOne({
        where: { 
          organizationId: organizationId as string,
          isActive: true
        }
      });

      if (!config) {
        // Return default configuration if none exists
        res.json({
          success: true,
          data: {
            holidays: [],
            weekendDays: [0, 6], // Sunday, Saturday
            timezone: 'UTC'
          }
        });
        return;
      }

      // Get associated holidays
      const holidayRepo = this.getHolidayRepository();
      const holidays = await holidayRepo.find({
        where: { isActive: true },
        order: { date: 'ASC' }
      });

      res.json({
        success: true,
        data: {
          holidays,
          weekendDays: config.weekendDays,
          timezone: config.timezone,
          workingHours: config.workingHours,
          holidaySettings: config.holidaySettings
        }
      });
    } catch (error) {
      console.error('Error fetching holiday configuration:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch holiday configuration'
      });
    }
  }

  // Save holiday configuration
  async saveHolidayConfiguration(req: Request, res: Response): Promise<void> {
    try {
      const { holidays, weekendDays, organizationId = 'default', ...otherSettings } = req.body;

      const configRepo = this.getConfigRepository();
      
      // Find or create configuration
      let config = await configRepo.findOne({
        where: { 
          organizationId,
          isActive: true
        }
      });

      if (!config) {
        config = new HolidayConfiguration();
        config.organizationId = organizationId;
        config.name = `${organizationId} Configuration`;
      }

      // Update configuration
      config.weekendDays = weekendDays || [0, 6];
      Object.assign(config, otherSettings);
      
      // Add audit information
      if (req.user) {
        const userName = (req.user as any).name || (req.user as any).email;
        if (!config.id) {
          config.createdBy = userName;
        }
        config.updatedBy = userName;
      }

      const savedConfig = await configRepo.save(config);

      // Handle holidays if provided
      let savedHolidays = [];
      if (holidays && Array.isArray(holidays)) {
        const holidayRepo = this.getHolidayRepository();
        // This is a simplified approach - in production, you might want more sophisticated holiday management
        for (const holidayData of holidays) {
          if (holidayData.id) {
            // Update existing holiday
            const holiday = await holidayRepo.findOne({
              where: { id: holidayData.id }
            });
            if (holiday) {
              Object.assign(holiday, holidayData);
              if (req.user) {
                holiday.updatedBy = (req.user as any).name || (req.user as any).email;
              }
              savedHolidays.push(await holidayRepo.save(holiday));
            }
          } else {
            // Create new holiday
            const holiday = new Holiday();
            Object.assign(holiday, holidayData);
            if (req.user) {
              holiday.createdBy = (req.user as any).name || (req.user as any).email;
            }
            
            // Validate before saving
            const errors = await validate(holiday);
            if (errors.length === 0) {
              savedHolidays.push(await holidayRepo.save(holiday));
            }
          }
        }
      }

      res.json({
        success: true,
        data: {
          configuration: savedConfig,
          holidays: savedHolidays
        },
        message: 'Holiday configuration saved successfully'
      });
    } catch (error) {
      console.error('Error saving holiday configuration:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to save holiday configuration'
      });
    }
  }
} 