import { Request, Response } from 'express';
import { AppDataSource } from '../config/database';
import { JobPosting, JobPostingStatus, JobType, ExperienceLevel, WorkLocation } from '../entities/JobPosting';
import { JobApplication, ApplicationStatus, ApplicationSource } from '../entities/JobApplication';
import { Interview, InterviewStatus, InterviewType, InterviewResult } from '../entities/Interview';
import { ApplicationEvaluation } from '../entities/ApplicationEvaluation';
import { InterviewFeedback, FeedbackDecision } from '../entities/InterviewFeedback';
import { User } from '../entities/User';
import { validate } from 'class-validator';
import { Like, In, Between, IsNull, Not } from 'typeorm';

// Job Posting Controllers
export const createJobPosting = async (req: Request, res: Response) => {
  try {
    const jobPostingRepository = AppDataSource.getRepository(JobPosting);
    const userRepository = AppDataSource.getRepository(User);

    const jobPosting = new JobPosting();
    Object.assign(jobPosting, req.body);
    jobPosting.createdById = req.user.id;

    // Validate hiring manager if provided
    if (req.body.hiringManagerId) {
      const hiringManager = await userRepository.findOne({
        where: { id: req.body.hiringManagerId }
      });
      if (!hiringManager) {
        return res.status(400).json({ error: 'Invalid hiring manager' });
      }
    }

    const errors = await validate(jobPosting);
    if (errors.length > 0) {
      return res.status(400).json({ errors: errors.map(e => e.constraints) });
    }

    const savedJobPosting = await jobPostingRepository.save(jobPosting);
    
    // Load relations for response
    const fullJobPosting = await jobPostingRepository.findOne({
      where: { id: savedJobPosting.id },
      relations: ['createdBy', 'hiringManager']
    });

    res.status(201).json({
      message: 'Job posting created successfully',
      jobPosting: fullJobPosting
    });
  } catch (error) {
    console.error('Error creating job posting:', error);
    res.status(500).json({ error: 'Failed to create job posting' });
  }
};

export const getJobPostings = async (req: Request, res: Response) => {
  try {
    const jobPostingRepository = AppDataSource.getRepository(JobPosting);
    
    const {
      page = 1,
      limit = 10,
      status,
      department,
      jobType,
      experienceLevel,
      workLocation,
      search,
      isActive,
      isUrgent,
      isFeatured,
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = req.query;

    const queryBuilder = jobPostingRepository.createQueryBuilder('jobPosting')
      .leftJoinAndSelect('jobPosting.createdBy', 'createdBy')
      .leftJoinAndSelect('jobPosting.hiringManager', 'hiringManager')
      .loadRelationCountAndMap('jobPosting.applicationCount', 'jobPosting.applications');

    // Apply filters
    if (status) {
      queryBuilder.andWhere('jobPosting.status = :status', { status });
    }
    if (department) {
      queryBuilder.andWhere('jobPosting.department = :department', { department });
    }
    if (jobType) {
      queryBuilder.andWhere('jobPosting.jobType = :jobType', { jobType });
    }
    if (experienceLevel) {
      queryBuilder.andWhere('jobPosting.experienceLevel = :experienceLevel', { experienceLevel });
    }
    if (workLocation) {
      queryBuilder.andWhere('jobPosting.workLocation = :workLocation', { workLocation });
    }
    if (isActive !== undefined) {
      queryBuilder.andWhere('jobPosting.isActive = :isActive', { isActive: isActive === 'true' });
    }
    if (isUrgent !== undefined) {
      queryBuilder.andWhere('jobPosting.isUrgent = :isUrgent', { isUrgent: isUrgent === 'true' });
    }
    if (isFeatured !== undefined) {
      queryBuilder.andWhere('jobPosting.isFeatured = :isFeatured', { isFeatured: isFeatured === 'true' });
    }
    if (search) {
      queryBuilder.andWhere(
        '(jobPosting.title LIKE :search OR jobPosting.description LIKE :search OR jobPosting.requirements LIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`jobPosting.${sortBy}`, sortOrder as 'ASC' | 'DESC');

    // Apply pagination
    const skip = (Number(page) - 1) * Number(limit);
    queryBuilder.skip(skip).take(Number(limit));

    const [jobPostings, total] = await queryBuilder.getManyAndCount();

    res.json({
      jobPostings,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching job postings:', error);
    res.status(500).json({ error: 'Failed to fetch job postings' });
  }
};

export const getJobPostingById = async (req: Request, res: Response) => {
  try {
    const jobPostingRepository = AppDataSource.getRepository(JobPosting);
    const { id } = req.params;

    const jobPosting = await jobPostingRepository.findOne({
      where: { id: Number(id) },
      relations: ['createdBy', 'hiringManager', 'applications']
    });

    if (!jobPosting) {
      return res.status(404).json({ error: 'Job posting not found' });
    }

    // Increment view count
    await jobPostingRepository.update(jobPosting.id, {
      viewCount: jobPosting.viewCount + 1
    });

    res.json({ jobPosting });
  } catch (error) {
    console.error('Error fetching job posting:', error);
    res.status(500).json({ error: 'Failed to fetch job posting' });
  }
};

export const updateJobPosting = async (req: Request, res: Response) => {
  try {
    const jobPostingRepository = AppDataSource.getRepository(JobPosting);
    const { id } = req.params;

    const jobPosting = await jobPostingRepository.findOne({
      where: { id: Number(id) }
    });

    if (!jobPosting) {
      return res.status(404).json({ error: 'Job posting not found' });
    }

    Object.assign(jobPosting, req.body);

    const errors = await validate(jobPosting);
    if (errors.length > 0) {
      return res.status(400).json({ errors: errors.map(e => e.constraints) });
    }

    const updatedJobPosting = await jobPostingRepository.save(jobPosting);

    const fullJobPosting = await jobPostingRepository.findOne({
      where: { id: updatedJobPosting.id },
      relations: ['createdBy', 'hiringManager']
    });

    res.json({
      message: 'Job posting updated successfully',
      jobPosting: fullJobPosting
    });
  } catch (error) {
    console.error('Error updating job posting:', error);
    res.status(500).json({ error: 'Failed to update job posting' });
  }
};

export const deleteJobPosting = async (req: Request, res: Response) => {
  try {
    const jobPostingRepository = AppDataSource.getRepository(JobPosting);
    const { id } = req.params;

    const jobPosting = await jobPostingRepository.findOne({
      where: { id: Number(id) },
      relations: ['applications']
    });

    if (!jobPosting) {
      return res.status(404).json({ error: 'Job posting not found' });
    }

    // Check if there are applications
    if (jobPosting.applications && jobPosting.applications.length > 0) {
      return res.status(400).json({ 
        error: 'Cannot delete job posting with existing applications. Please close it instead.' 
      });
    }

    await jobPostingRepository.remove(jobPosting);

    res.json({ message: 'Job posting deleted successfully' });
  } catch (error) {
    console.error('Error deleting job posting:', error);
    res.status(500).json({ error: 'Failed to delete job posting' });
  }
};

export const publishJobPosting = async (req: Request, res: Response) => {
  try {
    const jobPostingRepository = AppDataSource.getRepository(JobPosting);
    const { id } = req.params;

    const jobPosting = await jobPostingRepository.findOne({
      where: { id: Number(id) }
    });

    if (!jobPosting) {
      return res.status(404).json({ error: 'Job posting not found' });
    }

    jobPosting.status = JobPostingStatus.PUBLISHED;
    jobPosting.publishedAt = new Date();
    jobPosting.isActive = true;

    await jobPostingRepository.save(jobPosting);

    res.json({
      message: 'Job posting published successfully',
      jobPosting
    });
  } catch (error) {
    console.error('Error publishing job posting:', error);
    res.status(500).json({ error: 'Failed to publish job posting' });
  }
};

export const closeJobPosting = async (req: Request, res: Response) => {
  try {
    const jobPostingRepository = AppDataSource.getRepository(JobPosting);
    const { id } = req.params;

    const jobPosting = await jobPostingRepository.findOne({
      where: { id: Number(id) }
    });

    if (!jobPosting) {
      return res.status(404).json({ error: 'Job posting not found' });
    }

    jobPosting.status = JobPostingStatus.CLOSED;
    jobPosting.closedAt = new Date();
    jobPosting.isActive = false;

    await jobPostingRepository.save(jobPosting);

    res.json({
      message: 'Job posting closed successfully',
      jobPosting
    });
  } catch (error) {
    console.error('Error closing job posting:', error);
    res.status(500).json({ error: 'Failed to close job posting' });
  }
};

// Job Application Controllers
export const createJobApplication = async (req: Request, res: Response) => {
  try {
    const applicationRepository = AppDataSource.getRepository(JobApplication);
    const jobPostingRepository = AppDataSource.getRepository(JobPosting);

    // Verify job posting exists and is accepting applications
    const jobPosting = await jobPostingRepository.findOne({
      where: { id: req.body.jobPostingId }
    });

    if (!jobPosting) {
      return res.status(404).json({ error: 'Job posting not found' });
    }

    if (!jobPosting.canReceiveApplications) {
      return res.status(400).json({ error: 'This job posting is not accepting applications' });
    }

    // Check for duplicate application
    const existingApplication = await applicationRepository.findOne({
      where: {
        email: req.body.email,
        jobPostingId: req.body.jobPostingId
      }
    });

    if (existingApplication) {
      return res.status(400).json({ error: 'You have already applied for this position' });
    }

    const application = new JobApplication();
    Object.assign(application, req.body);

    const errors = await validate(application);
    if (errors.length > 0) {
      return res.status(400).json({ errors: errors.map(e => e.constraints) });
    }

    const savedApplication = await applicationRepository.save(application);

    // Update job posting application count
    await jobPostingRepository.increment(
      { id: jobPosting.id },
      'applicationCount',
      1
    );

    const fullApplication = await applicationRepository.findOne({
      where: { id: savedApplication.id },
      relations: ['jobPosting', 'assignedTo']
    });

    res.status(201).json({
      message: 'Application submitted successfully',
      application: fullApplication
    });
  } catch (error) {
    console.error('Error creating job application:', error);
    res.status(500).json({ error: 'Failed to submit application' });
  }
};

export const getJobApplications = async (req: Request, res: Response) => {
  try {
    const applicationRepository = AppDataSource.getRepository(JobApplication);

    const {
      page = 1,
      limit = 10,
      status,
      jobPostingId,
      assignedToId,
      source,
      search,
      rating,
      isStarred,
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = req.query;

    const queryBuilder = applicationRepository.createQueryBuilder('application')
      .leftJoinAndSelect('application.jobPosting', 'jobPosting')
      .leftJoinAndSelect('application.assignedTo', 'assignedTo')
      .leftJoinAndSelect('application.lastReviewedBy', 'lastReviewedBy')
      .loadRelationCountAndMap('application.interviewCount', 'application.interviews')
      .loadRelationCountAndMap('application.evaluationCount', 'application.evaluations');

    // Apply filters
    if (status) {
      queryBuilder.andWhere('application.status = :status', { status });
    }
    if (jobPostingId) {
      queryBuilder.andWhere('application.jobPostingId = :jobPostingId', { jobPostingId });
    }
    if (assignedToId) {
      queryBuilder.andWhere('application.assignedToId = :assignedToId', { assignedToId });
    }
    if (source) {
      queryBuilder.andWhere('application.source = :source', { source });
    }
    if (rating) {
      queryBuilder.andWhere('application.rating = :rating', { rating });
    }
    if (isStarred !== undefined) {
      queryBuilder.andWhere('application.isStarred = :isStarred', { isStarred: isStarred === 'true' });
    }
    if (search) {
      queryBuilder.andWhere(
        '(application.firstName LIKE :search OR application.lastName LIKE :search OR application.email LIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`application.${sortBy}`, sortOrder as 'ASC' | 'DESC');

    // Apply pagination
    const skip = (Number(page) - 1) * Number(limit);
    queryBuilder.skip(skip).take(Number(limit));

    const [applications, total] = await queryBuilder.getManyAndCount();

    res.json({
      applications,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching job applications:', error);
    res.status(500).json({ error: 'Failed to fetch applications' });
  }
};

// Get job posting statistics
export const getJobPostingStats = async (req: Request, res: Response) => {
  try {
    const jobPostingRepository = AppDataSource.getRepository(JobPosting);
    const applicationRepository = AppDataSource.getRepository(JobApplication);

    const totalPostings = await jobPostingRepository.count();
    const activePostings = await jobPostingRepository.count({
      where: { status: JobPostingStatus.PUBLISHED, isActive: true }
    });
    const draftPostings = await jobPostingRepository.count({
      where: { status: JobPostingStatus.DRAFT }
    });
    const closedPostings = await jobPostingRepository.count({
      where: { status: JobPostingStatus.CLOSED }
    });

    const totalApplications = await applicationRepository.count();
    const pendingApplications = await applicationRepository.count({
      where: { status: In([ApplicationStatus.SUBMITTED, ApplicationStatus.UNDER_REVIEW]) }
    });

    // Get applications by status
    const applicationsByStatus = await applicationRepository
      .createQueryBuilder('application')
      .select('application.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('application.status')
      .getRawMany();

    // Get postings by department
    const postingsByDepartment = await jobPostingRepository
      .createQueryBuilder('posting')
      .select('posting.department', 'department')
      .addSelect('COUNT(*)', 'count')
      .groupBy('posting.department')
      .getRawMany();

    res.json({
      overview: {
        totalPostings,
        activePostings,
        draftPostings,
        closedPostings,
        totalApplications,
        pendingApplications
      },
      applicationsByStatus,
      postingsByDepartment
    });
  } catch (error) {
    console.error('Error fetching job posting stats:', error);
    res.status(500).json({ error: 'Failed to fetch statistics' });
  }
};
